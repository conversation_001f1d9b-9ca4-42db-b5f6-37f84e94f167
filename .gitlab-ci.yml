variables:
  PROJECT: 'accounting-angular'
  DEV_FILES_PATH: '/result/frontend/dev'
  TEST_FILES_PATH: '/result/frontend/tz'
  STAGING1_FILES_PATH: '/result/frontend/stg1'
  STAGING2_FILES_PATH: '/result/frontend/stg2'
  PRODUCTION_FILES_PATH: '/result/frontend/prod'
  MODULE_FILE_PATH: "modules/accounting/new-accounting-module"
  BUILD_DIRECTORY: './dist/accounting-angular'
  TRACKING_FILE: 'tracking.txt'
  REPO_CONT_PATH: "/erp_project"
  REPO_PROD_LINK: "https://oauth2:${CLONE_KEY}@gitlab.teljoy.io/erp-front-end/magnamedia-core.git"
  REPO_PROD_BRANCH: "accounting__master"
  REPO_STG1_LINK: "https://oauth2:${CLONE_KEY}@gitlab.teljoy.io/erp-front-end/magnamedia-core.git"
  REPO_STG1_BRANCH: "accounting__STAGING_1_2025-01-12"
  REPO_STG2_LINK: "https://oauth2:${CLONE_KEY}@gitlab.teljoy.io/erp-front-end/magnamedia-core.git"
  REPO_STG2_BRANCH: "accounting__STAGING_2_2025-02-09"
  ERP_PROJECT_FOLDER: "magnamedia-core"
  NPM_REPO: "verdaccio.teljoy.io"

stages:
  - DEV
  - TZ
  - NEW_DEV
  - NEW_TZ
  - STG1
  - NEW_STG1
  - STG2
  - NEW_STG2
  - PROD
  - NEW_PROD

DEV:
  stage: DEV
  when: manual
  before_script:
    - cp /shared_resources/verdaccio_token/.npmrc .
    - npm install
    - npm i @maids/cc-shared-components@alpha -f @maids/cc-lib@alpha -f @maids/cc-erp-services@teljoy -f
    - touch $TRACKING_FILE
    - echo "Commit Ref Name - $CI_COMMIT_REF_NAME" >> $TRACKING_FILE
    - echo "Commit SHA - $CI_COMMIT_SHA" >> $TRACKING_FILE
    - echo "CI Job URL - $CI_JOB_URL" >> $TRACKING_FILE
    - echo "CI Pipeline URL - $CI_PIPELINE_URL" >> $TRACKING_FILE
  script:
    - npm run build
    - mkdir -p $DEV_FILES_PATH/$MODULE_FILE_PATH/
    - rm -rf $DEV_FILES_PATH/$MODULE_FILE_PATH/*
    - cp $TRACKING_FILE $BUILD_DIRECTORY/$TRACKING_FILE
    - cp -r $BUILD_DIRECTORY/* $DEV_FILES_PATH/$MODULE_FILE_PATH/
  tags:
    - node_16_20_2_builder
  only:
    - /^teljoy_micro$/

NEW_DEV:
  stage: NEW_DEV
  when: manual
  variables:
    PACKAGES_TO_INSTALL: "@maids/cc-shared-components@alpha @maids/cc-lib@alpha @maids/cc-erp-services@teljoy"
    NG_ARGS: "-c=dev --output-hashing=all"
    WORKING_DIR: $CI_PROJECT_DIR
    BUILD_DIRECTORY: "./dist/accounting-angular"
    PROJECT_DIR: "accounting"
    POST_BUILD_SCRIPTS: "node scripts/extract-routes.js && node scripts/post-build.js"
    SEC_FILES: "./dist/routes-accounting.json ./dist/security-accounting.json"
  script:
    - bash /shared_resources/deployment_scripts/gui/dev/deploy.sh
  tags:
    - node_16_20_2_builder
  only:
    - /^new-erp$/
    - /^teljoy_micro$/

TZ:
  stage: TZ
  when: manual
  before_script:
    - cp /shared_resources/verdaccio_token/.npmrc .
    - npm install
    - npm install @maids/cc-lib@latest @maids/cc-erp-services@latest @maids/cc-shared-components@latest --force
  script:
    - npm run build
    - mkdir -p $TEST_FILES_PATH/$MODULE_FILE_PATH/
    - rm -rf $TEST_FILES_PATH/$MODULE_FILE_PATH/*
    - cp -r $BUILD_DIRECTORY/* $TEST_FILES_PATH/$MODULE_FILE_PATH/
  tags:
    - node_16_20_2_builder
  only:
    - /^testzone_micro$/

NEW_TZ:
  stage: NEW_TZ
  when: manual
  variables:
    PACKAGES_TO_INSTALL: "@maids/cc-shared-components@beta @maids/cc-lib@beta @maids/cc-erp-services@testzone"
    NG_ARGS: "-c=tz --output-hashing=all"
    WORKING_DIR: $CI_PROJECT_DIR
    BUILD_DIRECTORY: "./dist/accounting-angular"
    PROJECT_DIR: "accounting"
    POST_BUILD_SCRIPTS: "node scripts/extract-routes.js && node scripts/post-build.js"
    SEC_FILES: "./dist/routes-accounting.json ./dist/security-accounting.json"
  script:
    - bash /shared_resources/deployment_scripts/gui/test/deploy.sh
  tags:
    - node_16_20_2_builder
  only:
    - /^testzone_micro$/
    - /^new-erp$/

STG1:
  stage: STG1
  when: manual
  before_script:
    - cp /shared_resources/verdaccio_token/.npmrc .
    - npm install
    - npm i @maids/cc-shared-components@latest -f @maids/cc-lib@latest -f @maids/cc-erp-services@latest -f
  script:
    - npm run build
    - mkdir -p $STAGING1_FILES_PATH/$MODULE_FILE_PATH/
    - rm -rf $STAGING1_FILES_PATH/$MODULE_FILE_PATH/*
    - cp -r $BUILD_DIRECTORY/* $STAGING1_FILES_PATH/$MODULE_FILE_PATH/

    # upload to ERP front repo
    - mkdir -p $REPO_CONT_PATH
    - rm -rf $REPO_CONT_PATH/*
    - cd $REPO_CONT_PATH
    - git clone $REPO_STG1_LINK
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "gitlab-runner"
    - cd $ERP_PROJECT_FOLDER
    - if git ls-remote --exit-code --heads origin $REPO_STG1_BRANCH; then
      git checkout -B $REPO_STG1_BRANCH origin/$REPO_STG1_BRANCH;
      else
      git checkout -B $REPO_STG1_BRANCH master;
      fi
    - mkdir -p $MODULE_FILE_PATH/
    - rm -rf $MODULE_FILE_PATH/*
    - cp -r $STAGING1_FILES_PATH/$MODULE_FILE_PATH/* $MODULE_FILE_PATH/
    - git add .
    - git commit --allow-empty -m "Deploy $PROJECT on Staging"
    - git push origin $REPO_STG1_BRANCH

  tags:
    - node_16_20_2_builder
  only:
    - /^staging1_micro$/

NEW_STG1:
  stage: NEW_STG1
  when: manual
  variables:
     PACKAGES_TO_INSTALL: "@maids/cc-shared-components@staging @maids/cc-lib@staging @maids/cc-erp-services@staging"
     NG_ARGS: "-c=staging --output-hashing=all"
     WORKING_DIR: $CI_PROJECT_DIR
     BUILD_DIRECTORY: "./dist/accounting-angular"
     PROJECT_DIR: "accounting"
     POST_BUILD_SCRIPTS: "node scripts/extract-routes.js && node scripts/post-build.js"
     SEC_FILES: "./dist/routes-accounting.json ./dist/security-accounting.json"
  script:
    - bash /shared_resources/deployment_scripts/gui/staging/deploy.sh
  tags:
    - node_16_20_2_builder
  only:
    - /^staging1_micro$/
    - /^master$/

STG2:
  stage: STG2
  when: manual
  before_script:
    - cp /shared_resources/verdaccio_token/.npmrc .
    - npm install
    - npm i @maids/cc-shared-components@latest -f @maids/cc-lib@latest -f @maids/cc-erp-services@latest -f
  script:
    - npm run build
    - mkdir -p $STAGING2_FILES_PATH/$MODULE_FILE_PATH/
    - rm -rf $STAGING2_FILES_PATH/$MODULE_FILE_PATH/*
    - cp -r $BUILD_DIRECTORY/* $STAGING2_FILES_PATH/$MODULE_FILE_PATH/

    # upload to ERP front repo
    - mkdir -p $REPO_CONT_PATH
    - rm -rf $REPO_CONT_PATH/*
    - cd $REPO_CONT_PATH
    - git clone $REPO_STG2_LINK
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "gitlab-runner"
    - cd $ERP_PROJECT_FOLDER
    - if git ls-remote --exit-code --heads origin $REPO_STG2_BRANCH; then
      git checkout -B $REPO_STG2_BRANCH origin/$REPO_STG2_BRANCH;
      else
      git checkout -B $REPO_STG2_BRANCH master;
      fi
    - mkdir -p $MODULE_FILE_PATH/
    - rm -rf $MODULE_FILE_PATH/*
    - cp -r $STAGING2_FILES_PATH/$MODULE_FILE_PATH/* $MODULE_FILE_PATH/
    - git add .
    - git commit --allow-empty -m "Deploy $PROJECT on Staging2"
    - git push origin $REPO_STG2_BRANCH

  tags:
    - node_16_20_2_builder
  only:
    - /^staging2_micro$/

NEW_STG2:
  stage: NEW_STG2
  when: manual
  variables:
     PACKAGES_TO_INSTALL: "@maids/cc-shared-components@staging @maids/cc-lib@staging @maids/cc-erp-services@staging2"
     NG_ARGS: "-c=staging2 --output-hashing=all"
     WORKING_DIR: $CI_PROJECT_DIR
     BUILD_DIRECTORY: "./dist/accounting-angular"
     PROJECT_DIR: "accounting"
     POST_BUILD_SCRIPTS: "node scripts/extract-routes.js && node scripts/post-build.js"
     SEC_FILES: "./dist/routes-accounting.json ./dist/security-accounting.json"
  script:
    - bash /shared_resources/deployment_scripts/gui/staging2/deploy.sh
  tags:
    - node_16_20_2_builder
  only:
    - /^staging2_micro$/
    - /^master$/

PROD:
  stage: PROD
  when: manual
  before_script:
    - cp /shared_resources/verdaccio_token/.npmrc .
    - npm install --force
    - npm install @maids/cc-lib@latest @maids/cc-erp-services@latest --force @maids/cc-shared-components@latest --force
  script:
    - npm run build
    - mkdir -p $PRODUCTION_FILES_PATH/$MODULE_FILE_PATH/
    - rm -rf $PRODUCTION_FILES_PATH/$MODULE_FILE_PATH/*
    - cp -r $BUILD_DIRECTORY/* $PRODUCTION_FILES_PATH/$MODULE_FILE_PATH/

    # upload to ERP front repo
    - mkdir -p $REPO_CONT_PATH
    - rm -rf $REPO_CONT_PATH/*
    - cd $REPO_CONT_PATH
    - git clone $REPO_PROD_LINK
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "gitlab-runner"
    - cd $ERP_PROJECT_FOLDER
    - if git ls-remote --exit-code --heads origin $REPO_PROD_BRANCH; then
          git checkout -B $REPO_PROD_BRANCH origin/$REPO_PROD_BRANCH;
      else
          git checkout -B $REPO_PROD_BRANCH master;
      fi
    - rm -rf $MODULE_FILE_PATH/*
    - cp -r $PRODUCTION_FILES_PATH/$MODULE_FILE_PATH/* $MODULE_FILE_PATH/
    - git add .
    - git commit --allow-empty -m "Deploy $PROJECT on prod"
    - git push origin $REPO_PROD_BRANCH

  tags:
    - node_16_20_2_builder
  only:
    - /^master_accounting$/

NEW_PROD:
  stage: NEW_PROD
  when: manual
  variables:
     PACKAGES_TO_INSTALL: "@maids/cc-shared-components@latest @maids/cc-lib@latest @maids/cc-erp-services@latest"
     NG_ARGS: "-c=new-prod --output-hashing=all"
     WORKING_DIR: $CI_PROJECT_DIR
     BUILD_DIRECTORY: "./dist/accounting-angular"
     PROJECT_DIR: "accounting"
     POST_BUILD_SCRIPTS: "node scripts/extract-routes.js && node scripts/post-build.js"
     SEC_FILES: "./dist/routes-accounting.json ./dist/security-accounting.json"
  script:
    - bash /shared_resources/deployment_scripts/gui/prod/deploy.sh
  tags:
    - node_16_20_2_builder
  only:
    - /^master_accounting$/
