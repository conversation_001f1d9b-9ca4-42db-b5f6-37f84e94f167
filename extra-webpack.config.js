const singleSpaAngularWebpack =
  require("single-spa-angular/lib/webpack").default;
const webpackMerge = require("webpack-merge");
const MiniCssExtractPlugin = require("mini-css-extract-plugin");
module.exports = (config, options) => {
  const singleSpaWebpackConfig = singleSpaAngularWebpack(config, options);
  //trying to fix CORS ERROR
  const merged = webpackMerge.merge(singleSpaWebpackConfig, {
    // entry:{
    //   style:'src/styles.scss'
    // },
    output: {
      publicPath: "./modules/accounting/new-accounting-module/",
      filename: (pathData) => {
        // Check if the chunk is the main entry point
        return pathData.chunk.name === "main"
          ? "[name].js"
          : "[name].[contenthash].js";
      },
    },
    module: {
      rules: [
        {
          test: /\.scss$/,
          exclude: /src[\\/]+app[\\/]+/,
          use: [
            // Extract and save the final CSS.
            MiniCssExtractPlugin.loader,
            // Load the CSS, set url = false to prevent following urls to fonts and images.
            { loader: "css-loader", options: { url: false, importLoaders: 1 } },
            // Add browser prefixes and minify CSS.
            {
              loader: "postcss-loader",
              options: {
                postcssOptions: {
                  plugins: {
                    "postcss-prefix-selector": {
                      prefix: ".angular-app",
                    },
                  },
                },
              },
            },
            // Load the SCSS/SASS
            { loader: "sass-loader" },
          ],
        },
      ],
    },
    plugins: [
      // Define the filename pattern for CSS.
      new MiniCssExtractPlugin({
        filename: (pathData) => {
          // Check if the chunk is the main entry point
          return pathData.chunk.name === "main"
            ? "[name].css"
            : "[name].[contenthash].css";
        },
        chunkFilename: (pathData) => {
          // Check if the chunk is the main entry point
          return pathData.chunk.name === "main"
            ? "[name].css"
            : "[name].[contenthash].css";
        },
      }),
    ],
  });

  return merged;
  // Feel free to modify this webpack config however you'd like to
  // return singleSpaWebpackConfig;
};
