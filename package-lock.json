{"name": "accounting-angular", "version": "0.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "accounting-angular", "version": "0.0.0", "dependencies": {"@angular/animations": "~13.3.0", "@angular/common": "~13.3.0", "@angular/compiler": "~13.3.0", "@angular/core": "~13.3.0", "@angular/forms": "~13.3.0", "@angular/platform-browser": "~13.3.0", "@angular/platform-browser-dynamic": "~13.3.0", "@angular/router": "~13.3.0", "@maids/cc-erp-services": "^2.3.31-local.0", "@maids/cc-lib": "^2.6.34", "@maids/cc-shared-components": "latest", "accounting-angular": "file:", "intl-tel-input": "^17.0.12", "ngx-dropzone-wrapper": "^10.0.1", "rxjs": "~7.5.0", "single-spa": ">=4.0.0", "single-spa-angular": "^6.3.1", "tslib": "^2.3.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-builders/custom-webpack": "13.1.0", "@angular-devkit/build-angular": "~13.3.11", "@angular/cli": "~13.3.11", "@angular/compiler-cli": "~13.3.0", "@types/intl-tel-input": "^17.0.1", "@types/jasmine": "~3.10.0", "@types/node": "^12.20.55", "jasmine-core": "~4.0.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.1.0", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "postcss": "^8.4.35", "postcss-loader": "^8.1.1", "postcss-prefix-selector": "^1.16.0", "replace-in-file-webpack-plugin": "^1.0.6", "style-loader": "^3.3.1", "typescript": "~4.6.2", "webpack": "^5.69.1"}}, "node_modules/@ampproject/remapping": {"version": "2.2.0", "license": "Apache-2.0", "dependencies": {"@jridgewell/gen-mapping": "^0.1.0", "@jridgewell/trace-mapping": "^0.3.9"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@angular-builders/custom-webpack": {"version": "13.1.0", "resolved": "https://registry.npmjs.org/@angular-builders/custom-webpack/-/custom-webpack-13.1.0.tgz", "integrity": "sha512-qhtnAv1i7agk14zeKZZfXjrckYt37OZ+3tsTBLhf3ZFbwREK8L1SNi8xhZ1j1JLGsf2Dp0GEcZrSYeFDweo0WA==", "dev": true, "license": "MIT", "dependencies": {"@angular-devkit/architect": ">=0.1300.0 < 0.1400.0", "@angular-devkit/build-angular": "^13.0.0", "@angular-devkit/core": "^13.0.0", "lodash": "^4.17.15", "ts-node": "^10.0.0", "tsconfig-paths": "^3.9.0", "webpack-merge": "^5.7.3"}, "engines": {"node": ">=12.20.0"}}, "node_modules/@angular-devkit/architect": {"version": "0.1303.11", "license": "MIT", "dependencies": {"@angular-devkit/core": "13.3.11", "rxjs": "6.6.7"}, "engines": {"node": "^12.20.0 || ^14.15.0 || >=16.10.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}}, "node_modules/@angular-devkit/architect/node_modules/rxjs": {"version": "6.6.7", "license": "Apache-2.0", "dependencies": {"tslib": "^1.9.0"}, "engines": {"npm": ">=2.0.0"}}, "node_modules/@angular-devkit/architect/node_modules/tslib": {"version": "1.14.1", "license": "0BSD"}, "node_modules/@angular-devkit/build-angular": {"version": "13.3.11", "license": "MIT", "dependencies": {"@ampproject/remapping": "2.2.0", "@angular-devkit/architect": "0.1303.11", "@angular-devkit/build-webpack": "0.1303.11", "@angular-devkit/core": "13.3.11", "@babel/core": "7.16.12", "@babel/generator": "7.16.8", "@babel/helper-annotate-as-pure": "7.16.7", "@babel/plugin-proposal-async-generator-functions": "7.16.8", "@babel/plugin-transform-async-to-generator": "7.16.8", "@babel/plugin-transform-runtime": "7.16.10", "@babel/preset-env": "7.16.11", "@babel/runtime": "7.16.7", "@babel/template": "7.16.7", "@discoveryjs/json-ext": "0.5.6", "@ngtools/webpack": "13.3.11", "ansi-colors": "4.1.1", "babel-loader": "8.2.5", "babel-plugin-istanbul": "6.1.1", "browserslist": "^4.9.1", "cacache": "15.3.0", "circular-dependency-plugin": "5.2.2", "copy-webpack-plugin": "10.2.1", "core-js": "3.20.3", "critters": "0.0.16", "css-loader": "6.5.1", "esbuild-wasm": "0.14.22", "glob": "7.2.0", "https-proxy-agent": "5.0.0", "inquirer": "8.2.0", "jsonc-parser": "3.0.0", "karma-source-map-support": "1.4.0", "less": "4.1.2", "less-loader": "10.2.0", "license-webpack-plugin": "4.0.2", "loader-utils": "3.2.1", "mini-css-extract-plugin": "2.5.3", "minimatch": "3.0.5", "open": "8.4.0", "ora": "5.4.1", "parse5-html-rewriting-stream": "6.0.1", "piscina": "3.2.0", "postcss": "8.4.5", "postcss-import": "14.0.2", "postcss-loader": "6.2.1", "postcss-preset-env": "7.2.3", "regenerator-runtime": "0.13.9", "resolve-url-loader": "5.0.0", "rxjs": "6.6.7", "sass": "1.49.9", "sass-loader": "12.4.0", "semver": "7.3.5", "source-map-loader": "3.0.1", "source-map-support": "0.5.21", "stylus": "0.56.0", "stylus-loader": "6.2.0", "terser": "5.14.2", "text-table": "0.2.0", "tree-kill": "1.2.2", "tslib": "2.3.1", "webpack": "5.76.1", "webpack-dev-middleware": "5.3.0", "webpack-dev-server": "4.7.3", "webpack-merge": "5.8.0", "webpack-subresource-integrity": "5.1.0"}, "engines": {"node": "^12.20.0 || ^14.15.0 || >=16.10.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}, "optionalDependencies": {"esbuild": "0.14.22"}, "peerDependencies": {"@angular/compiler-cli": "^13.0.0 || ^13.3.0-rc.0", "@angular/localize": "^13.0.0 || ^13.3.0-rc.0", "@angular/service-worker": "^13.0.0 || ^13.3.0-rc.0", "karma": "^6.3.0", "ng-packagr": "^13.0.0", "protractor": "^7.0.0", "tailwindcss": "^2.0.0 || ^3.0.0", "typescript": ">=4.4.3 <4.7"}, "peerDependenciesMeta": {"@angular/localize": {"optional": true}, "@angular/service-worker": {"optional": true}, "karma": {"optional": true}, "ng-packagr": {"optional": true}, "protractor": {"optional": true}, "tailwindcss": {"optional": true}}}, "node_modules/@angular-devkit/build-angular/node_modules/@types/estree": {"version": "0.0.51", "license": "MIT"}, "node_modules/@angular-devkit/build-angular/node_modules/@webassemblyjs/ast": {"version": "1.11.1", "license": "MIT", "dependencies": {"@webassemblyjs/helper-numbers": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1"}}, "node_modules/@angular-devkit/build-angular/node_modules/@webassemblyjs/floating-point-hex-parser": {"version": "1.11.1", "license": "MIT"}, "node_modules/@angular-devkit/build-angular/node_modules/@webassemblyjs/helper-api-error": {"version": "1.11.1", "license": "MIT"}, "node_modules/@angular-devkit/build-angular/node_modules/@webassemblyjs/helper-buffer": {"version": "1.11.1", "license": "MIT"}, "node_modules/@angular-devkit/build-angular/node_modules/@webassemblyjs/helper-numbers": {"version": "1.11.1", "license": "MIT", "dependencies": {"@webassemblyjs/floating-point-hex-parser": "1.11.1", "@webassemblyjs/helper-api-error": "1.11.1", "@xtuc/long": "4.2.2"}}, "node_modules/@angular-devkit/build-angular/node_modules/@webassemblyjs/helper-wasm-bytecode": {"version": "1.11.1", "license": "MIT"}, "node_modules/@angular-devkit/build-angular/node_modules/@webassemblyjs/helper-wasm-section": {"version": "1.11.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/helper-buffer": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1", "@webassemblyjs/wasm-gen": "1.11.1"}}, "node_modules/@angular-devkit/build-angular/node_modules/@webassemblyjs/ieee754": {"version": "1.11.1", "license": "MIT", "dependencies": {"@xtuc/ieee754": "^1.2.0"}}, "node_modules/@angular-devkit/build-angular/node_modules/@webassemblyjs/leb128": {"version": "1.11.1", "license": "Apache-2.0", "dependencies": {"@xtuc/long": "4.2.2"}}, "node_modules/@angular-devkit/build-angular/node_modules/@webassemblyjs/utf8": {"version": "1.11.1", "license": "MIT"}, "node_modules/@angular-devkit/build-angular/node_modules/@webassemblyjs/wasm-edit": {"version": "1.11.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/helper-buffer": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1", "@webassemblyjs/helper-wasm-section": "1.11.1", "@webassemblyjs/wasm-gen": "1.11.1", "@webassemblyjs/wasm-opt": "1.11.1", "@webassemblyjs/wasm-parser": "1.11.1", "@webassemblyjs/wast-printer": "1.11.1"}}, "node_modules/@angular-devkit/build-angular/node_modules/@webassemblyjs/wasm-gen": {"version": "1.11.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1", "@webassemblyjs/ieee754": "1.11.1", "@webassemblyjs/leb128": "1.11.1", "@webassemblyjs/utf8": "1.11.1"}}, "node_modules/@angular-devkit/build-angular/node_modules/@webassemblyjs/wasm-opt": {"version": "1.11.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/helper-buffer": "1.11.1", "@webassemblyjs/wasm-gen": "1.11.1", "@webassemblyjs/wasm-parser": "1.11.1"}}, "node_modules/@angular-devkit/build-angular/node_modules/@webassemblyjs/wasm-parser": {"version": "1.11.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.11.1", "@webassemblyjs/helper-api-error": "1.11.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.1", "@webassemblyjs/ieee754": "1.11.1", "@webassemblyjs/leb128": "1.11.1", "@webassemblyjs/utf8": "1.11.1"}}, "node_modules/@angular-devkit/build-angular/node_modules/@webassemblyjs/wast-printer": {"version": "1.11.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.11.1", "@xtuc/long": "4.2.2"}}, "node_modules/@angular-devkit/build-angular/node_modules/ajv": {"version": "6.12.6", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/@angular-devkit/build-angular/node_modules/ajv-keywords": {"version": "3.5.2", "license": "MIT", "peerDependencies": {"ajv": "^6.9.1"}}, "node_modules/@angular-devkit/build-angular/node_modules/es-module-lexer": {"version": "0.9.3", "license": "MIT"}, "node_modules/@angular-devkit/build-angular/node_modules/json-schema-traverse": {"version": "0.4.1", "license": "MIT"}, "node_modules/@angular-devkit/build-angular/node_modules/postcss": {"version": "8.4.5", "license": "MIT", "dependencies": {"nanoid": "^3.1.30", "picocolors": "^1.0.0", "source-map-js": "^1.0.1"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}}, "node_modules/@angular-devkit/build-angular/node_modules/postcss-loader": {"version": "6.2.1", "license": "MIT", "dependencies": {"cosmiconfig": "^7.0.0", "klona": "^2.0.5", "semver": "^7.3.5"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"postcss": "^7.0.0 || ^8.0.1", "webpack": "^5.0.0"}}, "node_modules/@angular-devkit/build-angular/node_modules/rxjs": {"version": "6.6.7", "license": "Apache-2.0", "dependencies": {"tslib": "^1.9.0"}, "engines": {"npm": ">=2.0.0"}}, "node_modules/@angular-devkit/build-angular/node_modules/rxjs/node_modules/tslib": {"version": "1.14.1", "license": "0BSD"}, "node_modules/@angular-devkit/build-angular/node_modules/schema-utils": {"version": "3.3.0", "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.8", "ajv": "^6.12.5", "ajv-keywords": "^3.5.2"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/@angular-devkit/build-angular/node_modules/tslib": {"version": "2.3.1", "license": "0BSD"}, "node_modules/@angular-devkit/build-angular/node_modules/webpack": {"version": "5.76.1", "license": "MIT", "dependencies": {"@types/eslint-scope": "^3.7.3", "@types/estree": "^0.0.51", "@webassemblyjs/ast": "1.11.1", "@webassemblyjs/wasm-edit": "1.11.1", "@webassemblyjs/wasm-parser": "1.11.1", "acorn": "^8.7.1", "acorn-import-assertions": "^1.7.6", "browserslist": "^4.14.5", "chrome-trace-event": "^1.0.2", "enhanced-resolve": "^5.10.0", "es-module-lexer": "^0.9.0", "eslint-scope": "5.1.1", "events": "^3.2.0", "glob-to-regexp": "^0.4.1", "graceful-fs": "^4.2.9", "json-parse-even-better-errors": "^2.3.1", "loader-runner": "^4.2.0", "mime-types": "^2.1.27", "neo-async": "^2.6.2", "schema-utils": "^3.1.0", "tapable": "^2.1.1", "terser-webpack-plugin": "^5.1.3", "watchpack": "^2.4.0", "webpack-sources": "^3.2.3"}, "bin": {"webpack": "bin/webpack.js"}, "engines": {"node": ">=10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependenciesMeta": {"webpack-cli": {"optional": true}}}, "node_modules/@angular-devkit/build-angular/node_modules/webpack-merge": {"version": "5.8.0", "license": "MIT", "dependencies": {"clone-deep": "^4.0.1", "wildcard": "^2.0.0"}, "engines": {"node": ">=10.0.0"}}, "node_modules/@angular-devkit/build-webpack": {"version": "0.1303.11", "license": "MIT", "dependencies": {"@angular-devkit/architect": "0.1303.11", "rxjs": "6.6.7"}, "engines": {"node": "^12.20.0 || ^14.15.0 || >=16.10.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}, "peerDependencies": {"webpack": "^5.30.0", "webpack-dev-server": "^4.0.0"}}, "node_modules/@angular-devkit/build-webpack/node_modules/rxjs": {"version": "6.6.7", "license": "Apache-2.0", "dependencies": {"tslib": "^1.9.0"}, "engines": {"npm": ">=2.0.0"}}, "node_modules/@angular-devkit/build-webpack/node_modules/tslib": {"version": "1.14.1", "license": "0BSD"}, "node_modules/@angular-devkit/core": {"version": "13.3.11", "license": "MIT", "dependencies": {"ajv": "8.9.0", "ajv-formats": "2.1.1", "fast-json-stable-stringify": "2.1.0", "magic-string": "0.25.7", "rxjs": "6.6.7", "source-map": "0.7.3"}, "engines": {"node": "^12.20.0 || ^14.15.0 || >=16.10.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}, "peerDependencies": {"chokidar": "^3.5.2"}, "peerDependenciesMeta": {"chokidar": {"optional": true}}}, "node_modules/@angular-devkit/core/node_modules/rxjs": {"version": "6.6.7", "license": "Apache-2.0", "dependencies": {"tslib": "^1.9.0"}, "engines": {"npm": ">=2.0.0"}}, "node_modules/@angular-devkit/core/node_modules/tslib": {"version": "1.14.1", "license": "0BSD"}, "node_modules/@angular-devkit/schematics": {"version": "13.3.11", "dev": true, "license": "MIT", "dependencies": {"@angular-devkit/core": "13.3.11", "jsonc-parser": "3.0.0", "magic-string": "0.25.7", "ora": "5.4.1", "rxjs": "6.6.7"}, "engines": {"node": "^12.20.0 || ^14.15.0 || >=16.10.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}}, "node_modules/@angular-devkit/schematics/node_modules/rxjs": {"version": "6.6.7", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^1.9.0"}, "engines": {"npm": ">=2.0.0"}}, "node_modules/@angular-devkit/schematics/node_modules/tslib": {"version": "1.14.1", "dev": true, "license": "0BSD"}, "node_modules/@angular-material-components/datetime-picker": {"version": "7.0.1", "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.3.0"}, "peerDependencies": {"@angular/cdk": "^13.0.1", "@angular/common": "^13.0.1", "@angular/core": "^13.0.1", "@angular/forms": "^13.0.1", "@angular/material": "^13.0.1", "@angular/platform-browser": "^13.0.1"}}, "node_modules/@angular-material-components/moment-adapter": {"version": "7.0.0", "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.3.0"}, "peerDependencies": {"@angular/common": "^13.0.1", "@angular/core": "^13.0.1", "moment": "^2.18.1"}}, "node_modules/@angular/animations": {"version": "13.3.12", "license": "MIT", "dependencies": {"tslib": "^2.3.0"}, "engines": {"node": "^12.20.0 || ^14.15.0 || >=16.10.0"}, "peerDependencies": {"@angular/core": "13.3.12"}}, "node_modules/@angular/cdk": {"version": "13.3.9", "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.3.0"}, "optionalDependencies": {"parse5": "^5.0.0"}, "peerDependencies": {"@angular/common": "^13.0.0 || ^14.0.0-0", "@angular/core": "^13.0.0 || ^14.0.0-0", "rxjs": "^6.5.3 || ^7.4.0"}}, "node_modules/@angular/cli": {"version": "13.3.11", "dev": true, "hasInstallScript": true, "license": "MIT", "dependencies": {"@angular-devkit/architect": "0.1303.11", "@angular-devkit/core": "13.3.11", "@angular-devkit/schematics": "13.3.11", "@schematics/angular": "13.3.11", "@yarnpkg/lockfile": "1.1.0", "ansi-colors": "4.1.1", "debug": "4.3.3", "ini": "2.0.0", "inquirer": "8.2.0", "jsonc-parser": "3.0.0", "npm-package-arg": "8.1.5", "npm-pick-manifest": "6.1.1", "open": "8.4.0", "ora": "5.4.1", "pacote": "12.0.3", "resolve": "1.22.0", "semver": "7.3.5", "symbol-observable": "4.0.0", "uuid": "8.3.2"}, "bin": {"ng": "bin/ng.js"}, "engines": {"node": "^12.20.0 || ^14.15.0 || >=16.10.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}}, "node_modules/@angular/cli/node_modules/@schematics/angular": {"version": "13.3.11", "dev": true, "license": "MIT", "dependencies": {"@angular-devkit/core": "13.3.11", "@angular-devkit/schematics": "13.3.11", "jsonc-parser": "3.0.0"}, "engines": {"node": "^12.20.0 || ^14.15.0 || >=16.10.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}}, "node_modules/@angular/common": {"version": "13.3.12", "license": "MIT", "dependencies": {"tslib": "^2.3.0"}, "engines": {"node": "^12.20.0 || ^14.15.0 || >=16.10.0"}, "peerDependencies": {"@angular/core": "13.3.12", "rxjs": "^6.5.3 || ^7.4.0"}}, "node_modules/@angular/compiler": {"version": "13.3.12", "license": "MIT", "dependencies": {"tslib": "^2.3.0"}, "engines": {"node": "^12.20.0 || ^14.15.0 || >=16.10.0"}}, "node_modules/@angular/compiler-cli": {"version": "13.3.12", "license": "MIT", "dependencies": {"@babel/core": "^7.17.2", "chokidar": "^3.0.0", "convert-source-map": "^1.5.1", "dependency-graph": "^0.11.0", "magic-string": "^0.26.0", "reflect-metadata": "^0.1.2", "semver": "^7.0.0", "sourcemap-codec": "^1.4.8", "tslib": "^2.3.0", "yargs": "^17.2.1"}, "bin": {"ng-xi18n": "bundles/src/bin/ng_xi18n.js", "ngc": "bundles/src/bin/ngc.js", "ngcc": "bundles/ngcc/main-ngcc.js"}, "engines": {"node": "^12.20.0 || ^14.15.0 || >=16.10.0"}, "peerDependencies": {"@angular/compiler": "13.3.12", "typescript": ">=4.4.2 <4.7"}}, "node_modules/@angular/compiler-cli/node_modules/@babel/core": {"version": "7.25.2", "license": "MIT", "dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.24.7", "@babel/generator": "^7.25.0", "@babel/helper-compilation-targets": "^7.25.2", "@babel/helper-module-transforms": "^7.25.2", "@babel/helpers": "^7.25.0", "@babel/parser": "^7.25.0", "@babel/template": "^7.25.0", "@babel/traverse": "^7.25.2", "@babel/types": "^7.25.2", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "node_modules/@angular/compiler-cli/node_modules/@babel/core/node_modules/convert-source-map": {"version": "2.0.0", "license": "MIT"}, "node_modules/@angular/compiler-cli/node_modules/@babel/core/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@angular/compiler-cli/node_modules/@babel/generator": {"version": "7.25.0", "license": "MIT", "dependencies": {"@babel/types": "^7.25.0", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@angular/compiler-cli/node_modules/@babel/generator/node_modules/jsesc": {"version": "2.5.2", "resolved": "https://registry.npmjs.org/jsesc/-/jsesc-2.5.2.tgz", "integrity": "sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==", "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=4"}}, "node_modules/@angular/compiler-cli/node_modules/@babel/template": {"version": "7.25.0", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.24.7", "@babel/parser": "^7.25.0", "@babel/types": "^7.25.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@angular/compiler-cli/node_modules/@jridgewell/gen-mapping": {"version": "0.3.5", "license": "MIT", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@angular/compiler-cli/node_modules/jsesc": {"version": "3.0.2", "resolved": "https://registry.npmjs.org/jsesc/-/jsesc-3.0.2.tgz", "integrity": "sha512-xKqzzWXDttJuOcawBt4KnKHHIf5oQ/Cxax+0PWFG+DFDgHNAdi+TXECADI+RYiFUMmx8792xsMbbgXj4CwnP4g==", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/@angular/compiler-cli/node_modules/magic-string": {"version": "0.26.7", "license": "MIT", "dependencies": {"sourcemap-codec": "^1.4.8"}, "engines": {"node": ">=12"}}, "node_modules/@angular/core": {"version": "13.3.12", "license": "MIT", "dependencies": {"tslib": "^2.3.0"}, "engines": {"node": "^12.20.0 || ^14.15.0 || >=16.10.0"}, "peerDependencies": {"rxjs": "^6.5.3 || ^7.4.0", "zone.js": "~0.11.4"}}, "node_modules/@angular/forms": {"version": "13.3.12", "license": "MIT", "dependencies": {"tslib": "^2.3.0"}, "engines": {"node": "^12.20.0 || ^14.15.0 || >=16.10.0"}, "peerDependencies": {"@angular/common": "13.3.12", "@angular/core": "13.3.12", "@angular/platform-browser": "13.3.12", "rxjs": "^6.5.3 || ^7.4.0"}}, "node_modules/@angular/material": {"version": "13.3.9", "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.3.0"}, "peerDependencies": {"@angular/animations": "^13.0.0 || ^14.0.0-0", "@angular/cdk": "13.3.9", "@angular/common": "^13.0.0 || ^14.0.0-0", "@angular/core": "^13.0.0 || ^14.0.0-0", "@angular/forms": "^13.0.0 || ^14.0.0-0", "@angular/platform-browser": "^13.0.0 || ^14.0.0-0", "rxjs": "^6.5.3 || ^7.4.0"}}, "node_modules/@angular/material-moment-adapter": {"version": "13.3.9", "license": "MIT", "dependencies": {"tslib": "^2.3.0"}, "peerDependencies": {"@angular/core": "^13.0.0 || ^14.0.0-0", "@angular/material": "13.3.9", "moment": "^2.18.1"}}, "node_modules/@angular/platform-browser": {"version": "13.3.12", "license": "MIT", "dependencies": {"tslib": "^2.3.0"}, "engines": {"node": "^12.20.0 || ^14.15.0 || >=16.10.0"}, "peerDependencies": {"@angular/animations": "13.3.12", "@angular/common": "13.3.12", "@angular/core": "13.3.12"}, "peerDependenciesMeta": {"@angular/animations": {"optional": true}}}, "node_modules/@angular/platform-browser-dynamic": {"version": "13.3.12", "license": "MIT", "dependencies": {"tslib": "^2.3.0"}, "engines": {"node": "^12.20.0 || ^14.15.0 || >=16.10.0"}, "peerDependencies": {"@angular/common": "13.3.12", "@angular/compiler": "13.3.12", "@angular/core": "13.3.12", "@angular/platform-browser": "13.3.12"}}, "node_modules/@angular/router": {"version": "13.3.12", "license": "MIT", "dependencies": {"tslib": "^2.3.0"}, "engines": {"node": "^12.20.0 || ^14.15.0 || >=16.10.0"}, "peerDependencies": {"@angular/common": "13.3.12", "@angular/core": "13.3.12", "@angular/platform-browser": "13.3.12", "rxjs": "^6.5.3 || ^7.4.0"}}, "node_modules/@assemblyscript/loader": {"version": "0.10.1", "license": "Apache-2.0"}, "node_modules/@babel/code-frame": {"version": "7.26.2", "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.25.9", "js-tokens": "^4.0.0", "picocolors": "^1.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/compat-data": {"version": "7.25.2", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/core": {"version": "7.16.12", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.16.7", "@babel/generator": "^7.16.8", "@babel/helper-compilation-targets": "^7.16.7", "@babel/helper-module-transforms": "^7.16.7", "@babel/helpers": "^7.16.7", "@babel/parser": "^7.16.12", "@babel/template": "^7.16.7", "@babel/traverse": "^7.16.10", "@babel/types": "^7.16.8", "convert-source-map": "^1.7.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.1.2", "semver": "^6.3.0", "source-map": "^0.5.0"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "node_modules/@babel/core/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/core/node_modules/source-map": {"version": "0.5.7", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/@babel/generator": {"version": "7.16.8", "license": "MIT", "dependencies": {"@babel/types": "^7.16.8", "jsesc": "^2.5.1", "source-map": "^0.5.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/generator/node_modules/source-map": {"version": "0.5.7", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/@babel/helper-annotate-as-pure": {"version": "7.16.7", "license": "MIT", "dependencies": {"@babel/types": "^7.16.7"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-builder-binary-assignment-operator-visitor": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/traverse": "^7.25.9", "@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets": {"version": "7.25.2", "license": "MIT", "dependencies": {"@babel/compat-data": "^7.25.2", "@babel/helper-validator-option": "^7.24.8", "browserslist": "^4.23.1", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/helper-create-class-features-plugin": {"version": "7.25.0", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.24.7", "@babel/helper-member-expression-to-functions": "^7.24.8", "@babel/helper-optimise-call-expression": "^7.24.7", "@babel/helper-replace-supers": "^7.25.0", "@babel/helper-skip-transparent-expression-wrappers": "^7.24.7", "@babel/traverse": "^7.25.0", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-create-class-features-plugin/node_modules/@babel/helper-annotate-as-pure": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-create-class-features-plugin/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/helper-create-regexp-features-plugin": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.25.9", "regexpu-core": "^6.1.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-create-regexp-features-plugin/node_modules/@babel/helper-annotate-as-pure": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-create-regexp-features-plugin/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/helper-define-polyfill-provider": {"version": "0.3.3", "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.17.7", "@babel/helper-plugin-utils": "^7.16.7", "debug": "^4.1.1", "lodash.debounce": "^4.0.8", "resolve": "^1.14.2", "semver": "^6.1.2"}, "peerDependencies": {"@babel/core": "^7.4.0-0"}}, "node_modules/@babel/helper-define-polyfill-provider/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/helper-member-expression-to-functions": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/traverse": "^7.25.9", "@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-imports": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/traverse": "^7.25.9", "@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-transforms": {"version": "7.26.0", "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9", "@babel/traverse": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-optimise-call-expression": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-plugin-utils": {"version": "7.25.9", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-remap-async-to-generator": {"version": "7.25.0", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.24.7", "@babel/helper-wrap-function": "^7.25.0", "@babel/traverse": "^7.25.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-remap-async-to-generator/node_modules/@babel/helper-annotate-as-pure": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-replace-supers": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-member-expression-to-functions": "^7.25.9", "@babel/helper-optimise-call-expression": "^7.25.9", "@babel/traverse": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-simple-access": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/traverse": "^7.25.9", "@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-skip-transparent-expression-wrappers": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/traverse": "^7.25.9", "@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-string-parser": {"version": "7.25.9", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.25.9", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-option": {"version": "7.25.9", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-wrap-function": {"version": "7.25.0", "license": "MIT", "dependencies": {"@babel/template": "^7.25.0", "@babel/traverse": "^7.25.0", "@babel/types": "^7.25.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-wrap-function/node_modules/@babel/template": {"version": "7.25.0", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.24.7", "@babel/parser": "^7.25.0", "@babel/types": "^7.25.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helpers": {"version": "7.25.0", "license": "MIT", "dependencies": {"@babel/template": "^7.25.0", "@babel/types": "^7.25.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helpers/node_modules/@babel/template": {"version": "7.25.0", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.24.7", "@babel/parser": "^7.25.0", "@babel/types": "^7.25.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/parser": {"version": "7.26.2", "license": "MIT", "dependencies": {"@babel/types": "^7.26.0"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": {"version": "7.25.0", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.24.8"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9", "@babel/plugin-transform-optional-chaining": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.13.0"}}, "node_modules/@babel/plugin-proposal-async-generator-functions": {"version": "7.16.8", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.16.7", "@babel/helper-remap-async-to-generator": "^7.16.8", "@babel/plugin-syntax-async-generators": "^7.8.4"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-class-properties": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-class-static-block": {"version": "7.21.0", "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.21.0", "@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-class-static-block": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.12.0"}}, "node_modules/@babel/plugin-proposal-dynamic-import": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-export-namespace-from": {"version": "7.18.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.9", "@babel/plugin-syntax-export-namespace-from": "^7.8.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-json-strings": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-json-strings": "^7.8.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-logical-assignment-operators": {"version": "7.20.7", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-nullish-coalescing-operator": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-numeric-separator": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-numeric-separator": "^7.10.4"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-object-rest-spread": {"version": "7.20.7", "license": "MIT", "dependencies": {"@babel/compat-data": "^7.20.5", "@babel/helper-compilation-targets": "^7.20.7", "@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-transform-parameters": "^7.20.7"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-optional-catch-binding": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-optional-chaining": {"version": "7.21.0", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-skip-transparent-expression-wrappers": "^7.20.0", "@babel/plugin-syntax-optional-chaining": "^7.8.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-private-methods": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-private-property-in-object": {"version": "7.21.11", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.18.6", "@babel/helper-create-class-features-plugin": "^7.21.0", "@babel/helper-plugin-utils": "^7.20.2", "@babel/plugin-syntax-private-property-in-object": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-private-property-in-object/node_modules/@babel/helper-annotate-as-pure": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/plugin-proposal-unicode-property-regex": {"version": "7.18.6", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-async-generators": {"version": "7.8.4", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-class-properties": {"version": "7.12.13", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-class-static-block": {"version": "7.14.5", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-dynamic-import": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-export-namespace-from": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-json-strings": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-logical-assignment-operators": {"version": "7.10.4", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-nullish-coalescing-operator": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-numeric-separator": {"version": "7.10.4", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-object-rest-spread": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-optional-catch-binding": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-optional-chaining": {"version": "7.8.3", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-private-property-in-object": {"version": "7.14.5", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-top-level-await": {"version": "7.14.5", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-arrow-functions": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-async-to-generator": {"version": "7.16.8", "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.16.7", "@babel/helper-plugin-utils": "^7.16.7", "@babel/helper-remap-async-to-generator": "^7.16.8"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-block-scoped-functions": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-block-scoping": {"version": "7.25.0", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-classes": {"version": "7.25.0", "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.24.7", "@babel/helper-compilation-targets": "^7.24.8", "@babel/helper-plugin-utils": "^7.24.8", "@babel/helper-replace-supers": "^7.25.0", "@babel/traverse": "^7.25.0", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-classes/node_modules/@babel/helper-annotate-as-pure": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/plugin-transform-computed-properties": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/template": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-computed-properties/node_modules/@babel/template": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.25.9", "@babel/parser": "^7.25.9", "@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/plugin-transform-destructuring": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-dotall-regex": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-duplicate-keys": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-exponentiation-operator": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-builder-binary-assignment-operator-visitor": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-for-of": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-function-name": {"version": "7.25.1", "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.24.8", "@babel/helper-plugin-utils": "^7.24.8", "@babel/traverse": "^7.25.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-literals": {"version": "7.25.2", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.24.8"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-member-expression-literals": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-amd": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-commonjs": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-simple-access": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-systemjs": {"version": "7.25.0", "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.25.0", "@babel/helper-plugin-utils": "^7.24.8", "@babel/helper-validator-identifier": "^7.24.7", "@babel/traverse": "^7.25.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-umd": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-named-capturing-groups-regex": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-transform-new-target": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-object-super": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-replace-supers": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-optional-chaining": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-parameters": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-property-literals": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-regenerator": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "regenerator-transform": "^0.15.2"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-reserved-words": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-runtime": {"version": "7.16.10", "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.16.7", "@babel/helper-plugin-utils": "^7.16.7", "babel-plugin-polyfill-corejs2": "^0.3.0", "babel-plugin-polyfill-corejs3": "^0.5.0", "babel-plugin-polyfill-regenerator": "^0.3.0", "semver": "^6.3.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-runtime/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/plugin-transform-shorthand-properties": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-spread": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-sticky-regex": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-template-literals": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-typeof-symbol": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-unicode-escapes": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-unicode-regex": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/preset-env": {"version": "7.16.11", "license": "MIT", "dependencies": {"@babel/compat-data": "^7.16.8", "@babel/helper-compilation-targets": "^7.16.7", "@babel/helper-plugin-utils": "^7.16.7", "@babel/helper-validator-option": "^7.16.7", "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": "^7.16.7", "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": "^7.16.7", "@babel/plugin-proposal-async-generator-functions": "^7.16.8", "@babel/plugin-proposal-class-properties": "^7.16.7", "@babel/plugin-proposal-class-static-block": "^7.16.7", "@babel/plugin-proposal-dynamic-import": "^7.16.7", "@babel/plugin-proposal-export-namespace-from": "^7.16.7", "@babel/plugin-proposal-json-strings": "^7.16.7", "@babel/plugin-proposal-logical-assignment-operators": "^7.16.7", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.16.7", "@babel/plugin-proposal-numeric-separator": "^7.16.7", "@babel/plugin-proposal-object-rest-spread": "^7.16.7", "@babel/plugin-proposal-optional-catch-binding": "^7.16.7", "@babel/plugin-proposal-optional-chaining": "^7.16.7", "@babel/plugin-proposal-private-methods": "^7.16.11", "@babel/plugin-proposal-private-property-in-object": "^7.16.7", "@babel/plugin-proposal-unicode-property-regex": "^7.16.7", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-export-namespace-from": "^7.8.3", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.10.4", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-private-property-in-object": "^7.14.5", "@babel/plugin-syntax-top-level-await": "^7.14.5", "@babel/plugin-transform-arrow-functions": "^7.16.7", "@babel/plugin-transform-async-to-generator": "^7.16.8", "@babel/plugin-transform-block-scoped-functions": "^7.16.7", "@babel/plugin-transform-block-scoping": "^7.16.7", "@babel/plugin-transform-classes": "^7.16.7", "@babel/plugin-transform-computed-properties": "^7.16.7", "@babel/plugin-transform-destructuring": "^7.16.7", "@babel/plugin-transform-dotall-regex": "^7.16.7", "@babel/plugin-transform-duplicate-keys": "^7.16.7", "@babel/plugin-transform-exponentiation-operator": "^7.16.7", "@babel/plugin-transform-for-of": "^7.16.7", "@babel/plugin-transform-function-name": "^7.16.7", "@babel/plugin-transform-literals": "^7.16.7", "@babel/plugin-transform-member-expression-literals": "^7.16.7", "@babel/plugin-transform-modules-amd": "^7.16.7", "@babel/plugin-transform-modules-commonjs": "^7.16.8", "@babel/plugin-transform-modules-systemjs": "^7.16.7", "@babel/plugin-transform-modules-umd": "^7.16.7", "@babel/plugin-transform-named-capturing-groups-regex": "^7.16.8", "@babel/plugin-transform-new-target": "^7.16.7", "@babel/plugin-transform-object-super": "^7.16.7", "@babel/plugin-transform-parameters": "^7.16.7", "@babel/plugin-transform-property-literals": "^7.16.7", "@babel/plugin-transform-regenerator": "^7.16.7", "@babel/plugin-transform-reserved-words": "^7.16.7", "@babel/plugin-transform-shorthand-properties": "^7.16.7", "@babel/plugin-transform-spread": "^7.16.7", "@babel/plugin-transform-sticky-regex": "^7.16.7", "@babel/plugin-transform-template-literals": "^7.16.7", "@babel/plugin-transform-typeof-symbol": "^7.16.7", "@babel/plugin-transform-unicode-escapes": "^7.16.7", "@babel/plugin-transform-unicode-regex": "^7.16.7", "@babel/preset-modules": "^0.1.5", "@babel/types": "^7.16.8", "babel-plugin-polyfill-corejs2": "^0.3.0", "babel-plugin-polyfill-corejs3": "^0.5.0", "babel-plugin-polyfill-regenerator": "^0.3.0", "core-js-compat": "^3.20.2", "semver": "^6.3.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/preset-env/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/preset-modules": {"version": "0.1.6", "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-proposal-unicode-property-regex": "^7.4.4", "@babel/plugin-transform-dotall-regex": "^7.4.4", "@babel/types": "^7.4.4", "esutils": "^2.0.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^8.0.0-0 <8.0.0"}}, "node_modules/@babel/runtime": {"version": "7.16.7", "license": "MIT", "dependencies": {"regenerator-runtime": "^0.13.4"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/template": {"version": "7.16.7", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.16.7", "@babel/parser": "^7.16.7", "@babel/types": "^7.16.7"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.25.9", "@babel/generator": "^7.25.9", "@babel/parser": "^7.25.9", "@babel/template": "^7.25.9", "@babel/types": "^7.25.9", "debug": "^4.3.1", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse/node_modules/@babel/generator": {"version": "7.26.2", "license": "MIT", "dependencies": {"@babel/parser": "^7.26.2", "@babel/types": "^7.26.0", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse/node_modules/@babel/template": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.25.9", "@babel/parser": "^7.25.9", "@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse/node_modules/@jridgewell/gen-mapping": {"version": "0.3.5", "license": "MIT", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/traverse/node_modules/jsesc": {"version": "3.0.2", "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/@babel/types": {"version": "7.26.0", "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@colors/colors": {"version": "1.5.0", "devOptional": true, "license": "MIT", "engines": {"node": ">=0.1.90"}}, "node_modules/@cspotcode/source-map-support": {"version": "0.8.1", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/trace-mapping": "0.3.9"}, "engines": {"node": ">=12"}}, "node_modules/@cspotcode/source-map-support/node_modules/@jridgewell/trace-mapping": {"version": "0.3.9", "resolved": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.9.tgz", "integrity": "sha512-3Belt6tdc8bPgAtbcmdtNJlirVoTmEb5e2gC94PnkwEW9jI6CAHUeoG85tjWP5WquqfavoMtMwiG4P926ZKKuQ==", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.0.3", "@jridgewell/sourcemap-codec": "^1.4.10"}}, "node_modules/@csstools/postcss-progressive-custom-properties": {"version": "1.3.0", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "peerDependencies": {"postcss": "^8.3"}}, "node_modules/@csstools/selector-specificity": {"version": "2.2.0", "license": "CC0-1.0", "engines": {"node": "^14 || ^16 || >=18"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss-selector-parser": "^6.0.10"}}, "node_modules/@discoveryjs/json-ext": {"version": "0.5.6", "license": "MIT", "engines": {"node": ">=10.0.0"}}, "node_modules/@gar/promisify": {"version": "1.1.3", "license": "MIT"}, "node_modules/@istanbuljs/load-nyc-config": {"version": "1.1.0", "license": "ISC", "dependencies": {"camelcase": "^5.3.1", "find-up": "^4.1.0", "get-package-type": "^0.1.0", "js-yaml": "^3.13.1", "resolve-from": "^5.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@istanbuljs/schema": {"version": "0.1.3", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.1.1", "license": "MIT", "dependencies": {"@jridgewell/set-array": "^1.0.0", "@jridgewell/sourcemap-codec": "^1.4.10"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/set-array": {"version": "1.2.1", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/source-map": {"version": "0.3.6", "license": "MIT", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}}, "node_modules/@jridgewell/source-map/node_modules/@jridgewell/gen-mapping": {"version": "0.3.5", "license": "MIT", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.0", "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.25", "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@maids/cc-erp-services": {"version": "2.3.31-local.0", "resolved": "https://verdaccio.teljoy.io/@maids/cc-erp-services/-/cc-erp-services-2.3.31-local.0.tgz", "integrity": "sha512-Rhfdu3cD14dKTN5ryGGpJIM88Wpe87ATMF71DldBUKOugg2pCaezJB5HshcbsTlS7GEANGqlI693jDMZsa6HLg==", "dependencies": {"tslib": "^2.3.0"}, "peerDependencies": {"@angular/cdk": "13.x", "@angular/common": "13.x", "@angular/core": "13.x", "@angular/router": "13.x", "ngx-cookie-service": "13.x"}}, "node_modules/@maids/cc-lib": {"version": "2.6.34", "resolved": "https://verdaccio.teljoy.io/@maids/cc-lib/-/cc-lib-2.6.34.tgz", "integrity": "sha512-np7qsS1RVPVWpFdkmASIK+66dOZ8GkdrK+in0/achGZvCrQOVvkVXk+dlvhnQ5voTSgFLT7BmlikcZxP08UD+w==", "dependencies": {"@angular/material-moment-adapter": ">=13.3.1 <14.0.0", "@tinymce/tinymce-angular": ">=6.0.1 <7.0.0", "chart.js": ">=3.8.0 <4.0.0", "cronstrue": "^2.21.0", "ng2-charts": ">=3.0.1 <4.0.0", "ngx-mask": ">=13.2.1 <14.0.0", "tinymce": ">=6.0.2", "tslib": "^2.3.0"}, "peerDependencies": {"@angular-devkit/build-angular": ">=13.0.1 <14.0.0", "@angular-material-components/datetime-picker": ">=7.0.0 <8.0.0", "@angular-material-components/moment-adapter": ">=7.0.0 < 8.0.0", "@angular/animations": ">=13.0.0 <14.0.0", "@angular/cdk": ">13.0.0 <14.0.0", "@angular/common": ">=13.0.0 <14.0.0", "@angular/core": ">=13.0.0 <14.0.0", "@angular/forms": ">=13.0.0 <14.0.0", "@angular/material": ">13.0.0 <14.0.0", "@angular/platform-browser": ">=13.0.0 <14.0.0", "@angular/platform-browser-dynamic": ">=13.0.0 <14.0.0", "@angular/router": ">=13.0.0 <14.0.0", "@ngrx/component-store": ">=13.0.0 <14.0.0", "@ngrx/effects": ">=13.0.0 <14.0.0", "@ngrx/router-store": ">=13.0.0 <14.0.0", "@ngrx/store": ">=13.0.0 <14.0.0", "@ngrx/store-devtools": ">=13.0.0 <14.0.0", "@ngx-formly/core": ">=5.10.18 <6.0.0", "@ngx-translate/core": ">=14.0.0 <15.0.0", "@ngx-translate/http-loader": ">=7.0.0 < 8.0.0", "bootstrap": ">=4.6.0", "immer": ">=9.0.1", "intl-tel-input": ">=17.0.12", "jquery": ">=3.6.0", "jsoneditor": ">=9.5.0", "lodash-es": ">=4.17.21", "moment": ">=2.29.1", "ngx-dropzone-wrapper": ">=10.0.1 <11.0.0", "ngx-material-timepicker": ">=5.5.3 < 6.0.0", "ngx-spinner": ">=13.0.0 <14.0.0"}}, "node_modules/@maids/cc-shared-components": {"version": "1.1.16", "resolved": "https://verdaccio.teljoy.io/@maids/cc-shared-components/-/cc-shared-components-1.1.16.tgz", "integrity": "sha512-4MQBMttALIQVrkgJuclDGPEXDU+Rpw5nhc8oc44BLzlEMpCeAMooLMNCVT8Qh2YTI/Ca9knAkOEzr6U/DSnm0A==", "dependencies": {"intl-tel-input": "^17.0.1", "tslib": "^2.3.0"}, "peerDependencies": {"@angular-devkit/build-angular": ">=13.0.1 <14.0.0", "@angular-material-components/datetime-picker": ">=7.0.0 <8.0.0", "@angular-material-components/moment-adapter": ">=7.0.0 < 8.0.0", "@angular/animations": ">=13.0.0 <14.0.0", "@angular/cdk": ">13.0.0 <14.0.0", "@angular/common": ">=13.0.0 <14.0.0", "@angular/core": ">=13.0.0 <14.0.0", "@angular/forms": ">=13.0.0 <14.0.0", "@angular/material": ">13.0.0 <14.0.0", "@angular/platform-browser": ">=13.0.0 <14.0.0", "@angular/platform-browser-dynamic": ">=13.0.0 <14.0.0", "@angular/router": ">=13.0.0 <14.0.0", "@maids/cc-lib": "*", "@ngrx/component-store": ">=13.0.0 <14.0.0", "@ngrx/effects": ">=13.0.0 <14.0.0", "@ngrx/router-store": ">=13.0.0 <14.0.0", "@ngrx/store": ">=13.0.0 <14.0.0", "@ngrx/store-devtools": ">=13.0.0 <14.0.0", "@ngx-formly/core": ">=5.10.18 <6.0.0", "@ngx-translate/core": ">=14.0.0 <15.0.0", "@ngx-translate/http-loader": ">=7.0.0 < 8.0.0", "bootstrap": ">=4.6.0", "immer": ">=9.0.1", "jquery": ">=3.6.0", "jsoneditor": ">=9.5.0", "lodash-es": ">=4.17.21", "moment": ">=2.29.1", "ngx-dropzone-wrapper": ">=10.0.1 <11.0.0", "ngx-material-timepicker": ">=5.5.3 < 6.0.0", "ngx-spinner": ">=13.0.0 <14.0.0"}}, "node_modules/@maids/cc-lib/node_modules/@maids/cc-lib": {"resolved": "node_modules/@maids/cc-lib", "link": true}, "node_modules/@ngrx/component-store": {"version": "13.2.0", "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.0.0"}, "peerDependencies": {"@angular/core": "^13.0.0", "rxjs": "^6.5.3 || ^7.4.0"}}, "node_modules/@ngrx/effects": {"version": "13.2.0", "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.0.0"}, "peerDependencies": {"@angular/core": "^13.0.0", "@ngrx/store": "13.2.0", "rxjs": "^6.5.3 || ^7.4.0"}}, "node_modules/@ngrx/router-store": {"version": "13.2.0", "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.0.0"}, "peerDependencies": {"@angular/common": "^13.0.0", "@angular/core": "^13.0.0", "@angular/router": "^13.0.0", "@ngrx/store": "13.2.0", "rxjs": "^6.5.3 || ^7.4.0"}}, "node_modules/@ngrx/store": {"version": "13.2.0", "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.0.0"}, "peerDependencies": {"@angular/core": "^13.0.0", "rxjs": "^6.5.3 || ^7.4.0"}}, "node_modules/@ngrx/store-devtools": {"version": "13.2.0", "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.0.0"}, "peerDependencies": {"@ngrx/store": "13.2.0", "rxjs": "^6.5.3 || ^7.4.0"}}, "node_modules/@ngtools/webpack": {"version": "13.3.11", "license": "MIT", "engines": {"node": "^12.20.0 || ^14.15.0 || >=16.10.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}, "peerDependencies": {"@angular/compiler-cli": "^13.0.0", "typescript": ">=4.4.3 <4.7", "webpack": "^5.30.0"}}, "node_modules/@ngx-formly/core": {"version": "5.12.7", "license": "MIT", "peer": true, "dependencies": {"tslib": "^1.7.1"}, "peerDependencies": {"@angular/forms": ">=7.0.0", "rxjs": ">=6.3.0"}}, "node_modules/@ngx-formly/core/node_modules/tslib": {"version": "1.14.1", "license": "0BSD", "peer": true}, "node_modules/@ngx-translate/core": {"version": "14.0.0", "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.3.0"}, "peerDependencies": {"@angular/core": ">=13.0.0", "rxjs": "^6.5.3 || ^7.4.0"}}, "node_modules/@ngx-translate/http-loader": {"version": "7.0.0", "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.3.0"}, "peerDependencies": {"@angular/common": ">=13.0.0", "@ngx-translate/core": ">=14.0.0", "rxjs": "^6.5.3 || ^7.4.0"}}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@npmcli/fs": {"version": "1.1.1", "license": "ISC", "dependencies": {"@gar/promisify": "^1.0.1", "semver": "^7.3.5"}}, "node_modules/@npmcli/git": {"version": "2.1.0", "dev": true, "license": "ISC", "dependencies": {"@npmcli/promise-spawn": "^1.3.2", "lru-cache": "^6.0.0", "mkdirp": "^1.0.4", "npm-pick-manifest": "^6.1.1", "promise-inflight": "^1.0.1", "promise-retry": "^2.0.1", "semver": "^7.3.5", "which": "^2.0.2"}}, "node_modules/@npmcli/git/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@npmcli/git/node_modules/which": {"version": "2.0.2", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/@npmcli/git/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/@npmcli/installed-package-contents": {"version": "1.0.7", "dev": true, "license": "ISC", "dependencies": {"npm-bundled": "^1.1.1", "npm-normalize-package-bin": "^1.0.1"}, "bin": {"installed-package-contents": "index.js"}, "engines": {"node": ">= 10"}}, "node_modules/@npmcli/move-file": {"version": "1.1.2", "license": "MIT", "dependencies": {"mkdirp": "^1.0.4", "rimraf": "^3.0.2"}, "engines": {"node": ">=10"}}, "node_modules/@npmcli/node-gyp": {"version": "1.0.3", "dev": true, "license": "ISC"}, "node_modules/@npmcli/promise-spawn": {"version": "1.3.2", "dev": true, "license": "ISC", "dependencies": {"infer-owner": "^1.0.4"}}, "node_modules/@npmcli/run-script": {"version": "2.0.0", "dev": true, "license": "ISC", "dependencies": {"@npmcli/node-gyp": "^1.0.2", "@npmcli/promise-spawn": "^1.3.2", "node-gyp": "^8.2.0", "read-package-json-fast": "^2.0.1"}}, "node_modules/@popperjs/core": {"version": "2.11.8", "license": "MIT", "peer": true, "funding": {"type": "opencollective", "url": "https://opencollective.com/popperjs"}}, "node_modules/@socket.io/component-emitter": {"version": "3.1.2", "devOptional": true, "license": "MIT"}, "node_modules/@sphinxxxx/color-conversion": {"version": "2.2.2", "license": "ISC", "peer": true}, "node_modules/@tinymce/tinymce-angular": {"version": "6.0.1", "license": "MIT", "dependencies": {"tinymce": "^6.0.0 || ^5.5.0", "tslib": "^2.3.0"}, "peerDependencies": {"@angular/common": ">=13.0.0", "@angular/core": ">=13.0.0", "@angular/forms": ">=13.0.0"}}, "node_modules/@tinymce/tinymce-angular/node_modules/tinymce": {"version": "6.8.5", "license": "MIT"}, "node_modules/@tootallnate/once": {"version": "1.1.2", "dev": true, "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/@tsconfig/node10": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/@tsconfig/node10/-/node10-1.0.11.tgz", "integrity": "sha512-DcRjDCujK/kCk/cUe8Xz8ZSpm8mS3mNNpta+jGCA6USEDfktlNvm1+IuZ9eTcDbNk41BHwpHHeW+N1lKCz4zOw==", "dev": true, "license": "MIT"}, "node_modules/@tsconfig/node12": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/@tsconfig/node12/-/node12-1.0.11.tgz", "integrity": "sha512-cqefuRsh12pWyGsIoBKJA9luFu3mRxCA+ORZvA4ktLSzIuCUtWVxGIuXigEwO5/ywWFMZ2QEGKWvkZG1zDMTag==", "dev": true, "license": "MIT"}, "node_modules/@tsconfig/node14": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/@tsconfig/node14/-/node14-1.0.3.tgz", "integrity": "sha512-ysT8mhdixWK6Hw3i1V2AeRqZ5WfXg1G43mqoYlM2nc6388Fq5jcXyr5mRsqViLx/GJYdoL0bfXD8nmF+Zn/Iow==", "dev": true, "license": "MIT"}, "node_modules/@tsconfig/node16": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/@tsconfig/node16/-/node16-1.0.4.tgz", "integrity": "sha512-vxhUy4J8lyeyinH7Azl1pdd43GJhZH/tP2weN8TntQblOY+A0XbT8DJk1/oCPuOOyg/Ja757rG0CgHcWC8OfMA==", "dev": true, "license": "MIT"}, "node_modules/@types/body-parser": {"version": "1.19.5", "license": "MIT", "dependencies": {"@types/connect": "*", "@types/node": "*"}}, "node_modules/@types/bonjour": {"version": "3.5.13", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/connect": {"version": "3.4.38", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/connect-history-api-fallback": {"version": "1.5.4", "license": "MIT", "dependencies": {"@types/express-serve-static-core": "*", "@types/node": "*"}}, "node_modules/@types/cookie": {"version": "0.4.1", "devOptional": true, "license": "MIT"}, "node_modules/@types/cors": {"version": "2.8.17", "devOptional": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/eslint": {"version": "9.6.1", "license": "MIT", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}}, "node_modules/@types/eslint-scope": {"version": "3.7.7", "license": "MIT", "dependencies": {"@types/eslint": "*", "@types/estree": "*"}}, "node_modules/@types/estree": {"version": "1.0.6", "license": "MIT"}, "node_modules/@types/express": {"version": "4.17.21", "license": "MIT", "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.33", "@types/qs": "*", "@types/serve-static": "*"}}, "node_modules/@types/express-serve-static-core": {"version": "5.0.1", "license": "MIT", "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*", "@types/send": "*"}}, "node_modules/@types/express/node_modules/@types/express-serve-static-core": {"version": "4.19.6", "license": "MIT", "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*", "@types/send": "*"}}, "node_modules/@types/http-errors": {"version": "2.0.4", "license": "MIT"}, "node_modules/@types/http-proxy": {"version": "1.17.15", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/intl-tel-input": {"version": "17.0.6", "dev": true, "license": "MIT", "dependencies": {"@types/jquery": "*"}}, "node_modules/@types/jasmine": {"version": "3.10.18", "dev": true, "license": "MIT"}, "node_modules/@types/jquery": {"version": "3.5.32", "dev": true, "license": "MIT", "dependencies": {"@types/sizzle": "*"}}, "node_modules/@types/json-schema": {"version": "7.0.15", "license": "MIT"}, "node_modules/@types/json5": {"version": "0.0.29", "resolved": "https://registry.npmjs.org/@types/json5/-/json5-0.0.29.tgz", "integrity": "sha512-dRLjCWHYg4oaA77cxO64oO+7JwCwnIzkZPdrrC71jQmQtlhM556pwKo5bUzqvZndkVbeFLIIi+9TC40JNF5hNQ==", "dev": true, "license": "MIT"}, "node_modules/@types/luxon": {"version": "1.11.1", "license": "MIT", "peer": true}, "node_modules/@types/mime": {"version": "1.3.5", "license": "MIT"}, "node_modules/@types/node": {"version": "12.20.55", "license": "MIT"}, "node_modules/@types/node-forge": {"version": "1.3.11", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/parse-json": {"version": "4.0.2", "license": "MIT"}, "node_modules/@types/qs": {"version": "6.9.17", "license": "MIT"}, "node_modules/@types/range-parser": {"version": "1.2.7", "license": "MIT"}, "node_modules/@types/retry": {"version": "0.12.0", "license": "MIT"}, "node_modules/@types/send": {"version": "0.17.4", "license": "MIT", "dependencies": {"@types/mime": "^1", "@types/node": "*"}}, "node_modules/@types/serve-index": {"version": "1.9.4", "license": "MIT", "dependencies": {"@types/express": "*"}}, "node_modules/@types/serve-static": {"version": "1.15.7", "license": "MIT", "dependencies": {"@types/http-errors": "*", "@types/node": "*", "@types/send": "*"}}, "node_modules/@types/sizzle": {"version": "2.3.9", "dev": true, "license": "MIT"}, "node_modules/@types/sockjs": {"version": "0.3.36", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/ws": {"version": "8.5.12", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@webassemblyjs/ast": {"version": "1.14.1", "license": "MIT", "dependencies": {"@webassemblyjs/helper-numbers": "1.13.2", "@webassemblyjs/helper-wasm-bytecode": "1.13.2"}}, "node_modules/@webassemblyjs/floating-point-hex-parser": {"version": "1.13.2", "license": "MIT"}, "node_modules/@webassemblyjs/helper-api-error": {"version": "1.13.2", "license": "MIT"}, "node_modules/@webassemblyjs/helper-buffer": {"version": "1.14.1", "license": "MIT"}, "node_modules/@webassemblyjs/helper-numbers": {"version": "1.13.2", "license": "MIT", "dependencies": {"@webassemblyjs/floating-point-hex-parser": "1.13.2", "@webassemblyjs/helper-api-error": "1.13.2", "@xtuc/long": "4.2.2"}}, "node_modules/@webassemblyjs/helper-wasm-bytecode": {"version": "1.13.2", "license": "MIT"}, "node_modules/@webassemblyjs/helper-wasm-section": {"version": "1.14.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/wasm-gen": "1.14.1"}}, "node_modules/@webassemblyjs/ieee754": {"version": "1.13.2", "license": "MIT", "dependencies": {"@xtuc/ieee754": "^1.2.0"}}, "node_modules/@webassemblyjs/leb128": {"version": "1.13.2", "license": "Apache-2.0", "dependencies": {"@xtuc/long": "4.2.2"}}, "node_modules/@webassemblyjs/utf8": {"version": "1.13.2", "license": "MIT"}, "node_modules/@webassemblyjs/wasm-edit": {"version": "1.14.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/helper-wasm-section": "1.14.1", "@webassemblyjs/wasm-gen": "1.14.1", "@webassemblyjs/wasm-opt": "1.14.1", "@webassemblyjs/wasm-parser": "1.14.1", "@webassemblyjs/wast-printer": "1.14.1"}}, "node_modules/@webassemblyjs/wasm-gen": {"version": "1.14.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/ieee754": "1.13.2", "@webassemblyjs/leb128": "1.13.2", "@webassemblyjs/utf8": "1.13.2"}}, "node_modules/@webassemblyjs/wasm-opt": {"version": "1.14.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/wasm-gen": "1.14.1", "@webassemblyjs/wasm-parser": "1.14.1"}}, "node_modules/@webassemblyjs/wasm-parser": {"version": "1.14.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-api-error": "1.13.2", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/ieee754": "1.13.2", "@webassemblyjs/leb128": "1.13.2", "@webassemblyjs/utf8": "1.13.2"}}, "node_modules/@webassemblyjs/wast-printer": {"version": "1.14.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@xtuc/long": "4.2.2"}}, "node_modules/@xtuc/ieee754": {"version": "1.2.0", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@xtuc/long": {"version": "4.2.2", "license": "Apache-2.0"}, "node_modules/@yarnpkg/lockfile": {"version": "1.1.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/abab": {"version": "2.0.6", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/abbrev": {"version": "1.1.1", "dev": true, "license": "ISC"}, "node_modules/accepts": {"version": "1.3.8", "license": "MIT", "dependencies": {"mime-types": "~2.1.34", "negotiator": "0.6.3"}, "engines": {"node": ">= 0.6"}}, "node_modules/accepts/node_modules/negotiator": {"version": "0.6.3", "resolved": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz", "integrity": "sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/accounting-angular": {"resolved": "", "link": true}, "node_modules/ace-builds": {"version": "1.36.2", "license": "BSD-3-<PERSON><PERSON>", "peer": true}, "node_modules/acorn": {"version": "8.14.0", "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-import-assertions": {"version": "1.9.0", "license": "MIT", "peerDependencies": {"acorn": "^8"}}, "node_modules/acorn-walk": {"version": "8.3.4", "resolved": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.3.4.tgz", "integrity": "sha512-ueEepnujpqee2o5aIYnvHU6C0A42MNdsIDeqy5BydrkuC5R1ZuUFnm27EeFJGoEHJQgn3uleRvmTXaJgfXbt4g==", "dev": true, "license": "MIT", "dependencies": {"acorn": "^8.11.0"}, "engines": {"node": ">=0.4.0"}}, "node_modules/adjust-sourcemap-loader": {"version": "4.0.0", "license": "MIT", "dependencies": {"loader-utils": "^2.0.0", "regex-parser": "^2.2.11"}, "engines": {"node": ">=8.9"}}, "node_modules/adjust-sourcemap-loader/node_modules/loader-utils": {"version": "2.0.4", "license": "MIT", "dependencies": {"big.js": "^5.2.2", "emojis-list": "^3.0.0", "json5": "^2.1.2"}, "engines": {"node": ">=8.9.0"}}, "node_modules/agent-base": {"version": "6.0.2", "license": "MIT", "dependencies": {"debug": "4"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/agentkeepalive": {"version": "4.5.0", "dev": true, "license": "MIT", "dependencies": {"humanize-ms": "^1.2.1"}, "engines": {"node": ">= 8.0.0"}}, "node_modules/aggregate-error": {"version": "3.1.0", "license": "MIT", "dependencies": {"clean-stack": "^2.0.0", "indent-string": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/ajv": {"version": "8.9.0", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ajv-formats": {"version": "2.1.1", "license": "MIT", "dependencies": {"ajv": "^8.0.0"}, "peerDependencies": {"ajv": "^8.0.0"}, "peerDependenciesMeta": {"ajv": {"optional": true}}}, "node_modules/ajv-keywords": {"version": "5.1.0", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3"}, "peerDependencies": {"ajv": "^8.8.2"}}, "node_modules/ansi-colors": {"version": "4.1.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/ansi-escapes": {"version": "4.3.2", "license": "MIT", "dependencies": {"type-fest": "^0.21.3"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-html-community": {"version": "0.0.8", "engines": ["node >= 0.8.0"], "license": "Apache-2.0", "bin": {"ansi-html": "bin/ansi-html"}}, "node_modules/ansi-regex": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/anymatch": {"version": "3.1.3", "license": "ISC", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/aproba": {"version": "2.0.0", "dev": true, "license": "ISC"}, "node_modules/are-we-there-yet": {"version": "3.0.1", "dev": true, "license": "ISC", "dependencies": {"delegates": "^1.0.0", "readable-stream": "^3.6.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/arg": {"version": "4.1.3", "resolved": "https://registry.npmjs.org/arg/-/arg-4.1.3.tgz", "integrity": "sha512-58S9QDqG0Xx27YwPSt9fJxivjYl432YCwfDMfZ+71RAqUrZef7LrKQZ3LHLOwCS4FLNBplP533Zx895SeOCHvA==", "dev": true, "license": "MIT"}, "node_modules/argparse": {"version": "1.0.10", "license": "MIT", "dependencies": {"sprintf-js": "~1.0.2"}}, "node_modules/array-flatten": {"version": "2.1.2", "license": "MIT"}, "node_modules/array-union": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/async": {"version": "2.6.4", "license": "MIT", "dependencies": {"lodash": "^4.17.14"}}, "node_modules/atob": {"version": "2.1.2", "license": "(MIT OR Apache-2.0)", "bin": {"atob": "bin/atob.js"}, "engines": {"node": ">= 4.5.0"}}, "node_modules/autoprefixer": {"version": "10.4.20", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/autoprefixer"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"browserslist": "^4.23.3", "caniuse-lite": "^1.0.30001646", "fraction.js": "^4.3.7", "normalize-range": "^0.1.2", "picocolors": "^1.0.1", "postcss-value-parser": "^4.2.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "engines": {"node": "^10 || ^12 || >=14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/babel-loader": {"version": "8.2.5", "license": "MIT", "dependencies": {"find-cache-dir": "^3.3.1", "loader-utils": "^2.0.0", "make-dir": "^3.1.0", "schema-utils": "^2.6.5"}, "engines": {"node": ">= 8.9"}, "peerDependencies": {"@babel/core": "^7.0.0", "webpack": ">=2"}}, "node_modules/babel-loader/node_modules/loader-utils": {"version": "2.0.4", "license": "MIT", "dependencies": {"big.js": "^5.2.2", "emojis-list": "^3.0.0", "json5": "^2.1.2"}, "engines": {"node": ">=8.9.0"}}, "node_modules/babel-plugin-istanbul": {"version": "6.1.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@istanbuljs/load-nyc-config": "^1.0.0", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-instrument": "^5.0.4", "test-exclude": "^6.0.0"}, "engines": {"node": ">=8"}}, "node_modules/babel-plugin-polyfill-corejs2": {"version": "0.3.3", "license": "MIT", "dependencies": {"@babel/compat-data": "^7.17.7", "@babel/helper-define-polyfill-provider": "^0.3.3", "semver": "^6.1.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/babel-plugin-polyfill-corejs2/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/babel-plugin-polyfill-corejs3": {"version": "0.5.3", "license": "MIT", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.3.2", "core-js-compat": "^3.21.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/babel-plugin-polyfill-regenerator": {"version": "0.3.1", "license": "MIT", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.3.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/balanced-match": {"version": "1.0.2", "license": "MIT"}, "node_modules/base64-js": {"version": "1.5.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/base64id": {"version": "2.0.0", "devOptional": true, "license": "MIT", "engines": {"node": "^4.5.0 || >= 5.9"}}, "node_modules/batch": {"version": "0.6.1", "license": "MIT"}, "node_modules/big.js": {"version": "5.2.2", "license": "MIT", "engines": {"node": "*"}}, "node_modules/binary-extensions": {"version": "2.3.0", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/bl": {"version": "4.1.0", "license": "MIT", "dependencies": {"buffer": "^5.5.0", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}}, "node_modules/body-parser": {"version": "1.20.3", "license": "MIT", "dependencies": {"bytes": "3.1.2", "content-type": "~1.0.5", "debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "on-finished": "2.4.1", "qs": "6.13.0", "raw-body": "2.5.2", "type-is": "~1.6.18", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/body-parser/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/body-parser/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/bonjour": {"version": "3.5.0", "license": "MIT", "dependencies": {"array-flatten": "^2.1.0", "deep-equal": "^1.0.1", "dns-equal": "^1.0.0", "dns-txt": "^2.0.2", "multicast-dns": "^6.0.1", "multicast-dns-service-types": "^1.1.0"}}, "node_modules/boolbase": {"version": "1.0.0", "license": "ISC"}, "node_modules/bootstrap": {"version": "5.3.3", "funding": [{"type": "github", "url": "https://github.com/sponsors/twbs"}, {"type": "opencollective", "url": "https://opencollective.com/bootstrap"}], "license": "MIT", "peer": true, "peerDependencies": {"@popperjs/core": "^2.11.8"}}, "node_modules/brace-expansion": {"version": "1.1.11", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "3.0.3", "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/browserslist": {"version": "4.24.2", "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"caniuse-lite": "^1.0.30001669", "electron-to-chromium": "^1.5.41", "node-releases": "^2.0.18", "update-browserslist-db": "^1.1.1"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/buffer": {"version": "5.7.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "node_modules/buffer-from": {"version": "1.1.2", "license": "MIT"}, "node_modules/buffer-indexof": {"version": "1.1.1", "license": "MIT"}, "node_modules/builtins": {"version": "1.0.3", "dev": true, "license": "MIT"}, "node_modules/bytes": {"version": "3.1.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/cacache": {"version": "15.3.0", "license": "ISC", "dependencies": {"@npmcli/fs": "^1.0.0", "@npmcli/move-file": "^1.0.1", "chownr": "^2.0.0", "fs-minipass": "^2.0.0", "glob": "^7.1.4", "infer-owner": "^1.0.4", "lru-cache": "^6.0.0", "minipass": "^3.1.1", "minipass-collect": "^1.0.2", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.2", "mkdirp": "^1.0.3", "p-map": "^4.0.0", "promise-inflight": "^1.0.1", "rimraf": "^3.0.2", "ssri": "^8.0.1", "tar": "^6.0.2", "unique-filename": "^1.1.1"}, "engines": {"node": ">= 10"}}, "node_modules/cacache/node_modules/lru-cache": {"version": "6.0.0", "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/cacache/node_modules/yallist": {"version": "4.0.0", "license": "ISC"}, "node_modules/call-bind": {"version": "1.0.7", "license": "MIT", "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "set-function-length": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/callsites": {"version": "3.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/camelcase": {"version": "5.3.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/caniuse-lite": {"version": "1.0.30001683", "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "node_modules/chalk": {"version": "4.1.2", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/chardet": {"version": "0.7.0", "license": "MIT"}, "node_modules/chart.js": {"version": "3.9.1", "license": "MIT"}, "node_modules/chokidar": {"version": "3.6.0", "license": "MIT", "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "engines": {"node": ">= 8.10.0"}, "funding": {"url": "https://paulmillr.com/funding/"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/chokidar/node_modules/fsevents": {"version": "2.3.3", "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/chownr": {"version": "2.0.0", "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/chrome-trace-event": {"version": "1.0.4", "license": "MIT", "engines": {"node": ">=6.0"}}, "node_modules/circular-dependency-plugin": {"version": "5.2.2", "license": "ISC", "engines": {"node": ">=6.0.0"}, "peerDependencies": {"webpack": ">=4.0.1"}}, "node_modules/clean-stack": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/cli-cursor": {"version": "3.1.0", "license": "MIT", "dependencies": {"restore-cursor": "^3.1.0"}, "engines": {"node": ">=8"}}, "node_modules/cli-spinners": {"version": "2.9.2", "license": "MIT", "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cli-width": {"version": "3.0.0", "license": "ISC", "engines": {"node": ">= 10"}}, "node_modules/cliui": {"version": "8.0.1", "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/clone": {"version": "1.0.4", "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/clone-deep": {"version": "4.0.1", "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4", "kind-of": "^6.0.2", "shallow-clone": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "node_modules/color-support": {"version": "1.1.3", "dev": true, "license": "ISC", "bin": {"color-support": "bin.js"}}, "node_modules/colorette": {"version": "2.0.20", "license": "MIT"}, "node_modules/commander": {"version": "2.20.3", "license": "MIT"}, "node_modules/commondir": {"version": "1.0.1", "license": "MIT"}, "node_modules/compressible": {"version": "2.0.18", "license": "MIT", "dependencies": {"mime-db": ">= 1.43.0 < 2"}, "engines": {"node": ">= 0.6"}}, "node_modules/compression": {"version": "1.7.5", "license": "MIT", "dependencies": {"bytes": "3.1.2", "compressible": "~2.0.18", "debug": "2.6.9", "negotiator": "~0.6.4", "on-headers": "~1.0.2", "safe-buffer": "5.2.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/compression/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/compression/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/concat-map": {"version": "0.0.1", "license": "MIT"}, "node_modules/connect": {"version": "3.7.0", "devOptional": true, "license": "MIT", "dependencies": {"debug": "2.6.9", "finalhandler": "1.1.2", "parseurl": "~1.3.3", "utils-merge": "1.0.1"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/connect-history-api-fallback": {"version": "1.6.0", "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/connect/node_modules/debug": {"version": "2.6.9", "devOptional": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/connect/node_modules/ms": {"version": "2.0.0", "devOptional": true, "license": "MIT"}, "node_modules/console-control-strings": {"version": "1.1.0", "dev": true, "license": "ISC"}, "node_modules/content-disposition": {"version": "0.5.4", "license": "MIT", "dependencies": {"safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-type": {"version": "1.0.5", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/convert-source-map": {"version": "1.9.0", "license": "MIT"}, "node_modules/cookie": {"version": "0.7.2", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie-signature": {"version": "1.0.6", "license": "MIT"}, "node_modules/copy-anything": {"version": "2.0.6", "license": "MIT", "dependencies": {"is-what": "^3.14.1"}, "funding": {"url": "https://github.com/sponsors/mesqueeb"}}, "node_modules/copy-webpack-plugin": {"version": "10.2.1", "license": "MIT", "dependencies": {"fast-glob": "^3.2.7", "glob-parent": "^6.0.1", "globby": "^12.0.2", "normalize-path": "^3.0.0", "schema-utils": "^4.0.0", "serialize-javascript": "^6.0.0"}, "engines": {"node": ">= 12.20.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.1.0"}}, "node_modules/copy-webpack-plugin/node_modules/glob-parent": {"version": "6.0.2", "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/copy-webpack-plugin/node_modules/schema-utils": {"version": "4.2.0", "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.9", "ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/core-js": {"version": "3.20.3", "hasInstallScript": true, "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "node_modules/core-js-compat": {"version": "3.38.0", "license": "MIT", "dependencies": {"browserslist": "^4.23.3"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "node_modules/core-util-is": {"version": "1.0.3", "license": "MIT"}, "node_modules/cors": {"version": "2.8.5", "devOptional": true, "license": "MIT", "dependencies": {"object-assign": "^4", "vary": "^1"}, "engines": {"node": ">= 0.10"}}, "node_modules/cosmiconfig": {"version": "7.1.0", "license": "MIT", "dependencies": {"@types/parse-json": "^4.0.0", "import-fresh": "^3.2.1", "parse-json": "^5.0.0", "path-type": "^4.0.0", "yaml": "^1.10.0"}, "engines": {"node": ">=10"}}, "node_modules/create-require": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/create-require/-/create-require-1.1.1.tgz", "integrity": "sha512-dcKFX3jn0MpIaXjisoRvexIJVEKzaq7z2rZKxf+MSr9TkdmHmsU4m2lcLojrj/FHl8mk5VxMmYA+ftRkP/3oKQ==", "dev": true, "license": "MIT"}, "node_modules/critters": {"version": "0.0.16", "license": "Apache-2.0", "dependencies": {"chalk": "^4.1.0", "css-select": "^4.2.0", "parse5": "^6.0.1", "parse5-htmlparser2-tree-adapter": "^6.0.1", "postcss": "^8.3.7", "pretty-bytes": "^5.3.0"}}, "node_modules/critters/node_modules/parse5": {"version": "6.0.1", "license": "MIT"}, "node_modules/cronstrue": {"version": "2.51.0", "license": "MIT", "bin": {"cronstrue": "bin/cli.js"}}, "node_modules/cross-spawn": {"version": "7.0.4", "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/cross-spawn/node_modules/which": {"version": "2.0.2", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/css": {"version": "3.0.0", "license": "MIT", "dependencies": {"inherits": "^2.0.4", "source-map": "^0.6.1", "source-map-resolve": "^0.6.0"}}, "node_modules/css-blank-pseudo": {"version": "3.0.3", "license": "CC0-1.0", "dependencies": {"postcss-selector-parser": "^6.0.9"}, "bin": {"css-blank-pseudo": "dist/cli.cjs"}, "engines": {"node": "^12 || ^14 || >=16"}, "peerDependencies": {"postcss": "^8.4"}}, "node_modules/css-has-pseudo": {"version": "3.0.4", "license": "CC0-1.0", "dependencies": {"postcss-selector-parser": "^6.0.9"}, "bin": {"css-has-pseudo": "dist/cli.cjs"}, "engines": {"node": "^12 || ^14 || >=16"}, "peerDependencies": {"postcss": "^8.4"}}, "node_modules/css-loader": {"version": "6.5.1", "license": "MIT", "dependencies": {"icss-utils": "^5.1.0", "postcss": "^8.2.15", "postcss-modules-extract-imports": "^3.0.0", "postcss-modules-local-by-default": "^4.0.0", "postcss-modules-scope": "^3.0.0", "postcss-modules-values": "^4.0.0", "postcss-value-parser": "^4.1.0", "semver": "^7.3.5"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.0.0"}}, "node_modules/css-prefers-color-scheme": {"version": "6.0.3", "license": "CC0-1.0", "bin": {"css-prefers-color-scheme": "dist/cli.cjs"}, "engines": {"node": "^12 || ^14 || >=16"}, "peerDependencies": {"postcss": "^8.4"}}, "node_modules/css-select": {"version": "4.3.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0", "css-what": "^6.0.1", "domhandler": "^4.3.1", "domutils": "^2.8.0", "nth-check": "^2.0.1"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "node_modules/css-what": {"version": "6.1.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">= 6"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "node_modules/css/node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/cssdb": {"version": "5.1.0", "license": "CC0-1.0"}, "node_modules/cssesc": {"version": "3.0.0", "license": "MIT", "bin": {"cssesc": "bin/cssesc"}, "engines": {"node": ">=4"}}, "node_modules/custom-event": {"version": "1.0.1", "devOptional": true, "license": "MIT"}, "node_modules/date-format": {"version": "4.0.14", "devOptional": true, "license": "MIT", "engines": {"node": ">=4.0"}}, "node_modules/debug": {"version": "4.3.3", "license": "MIT", "dependencies": {"ms": "2.1.2"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decode-uri-component": {"version": "0.2.2", "license": "MIT", "engines": {"node": ">=0.10"}}, "node_modules/deep-equal": {"version": "1.1.2", "license": "MIT", "dependencies": {"is-arguments": "^1.1.1", "is-date-object": "^1.0.5", "is-regex": "^1.1.4", "object-is": "^1.1.5", "object-keys": "^1.1.1", "regexp.prototype.flags": "^1.5.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/default-gateway": {"version": "6.0.3", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"execa": "^5.0.0"}, "engines": {"node": ">= 10"}}, "node_modules/defaults": {"version": "1.0.4", "license": "MIT", "dependencies": {"clone": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/define-data-property": {"version": "1.1.4", "license": "MIT", "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/define-lazy-prop": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/define-properties": {"version": "1.2.1", "license": "MIT", "dependencies": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/del": {"version": "6.1.1", "license": "MIT", "dependencies": {"globby": "^11.0.1", "graceful-fs": "^4.2.4", "is-glob": "^4.0.1", "is-path-cwd": "^2.2.0", "is-path-inside": "^3.0.2", "p-map": "^4.0.0", "rimraf": "^3.0.2", "slash": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/del/node_modules/array-union": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/del/node_modules/globby": {"version": "11.1.0", "license": "MIT", "dependencies": {"array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.2.9", "ignore": "^5.2.0", "merge2": "^1.4.1", "slash": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/del/node_modules/slash": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/delegates": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/depd": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/dependency-graph": {"version": "0.11.0", "license": "MIT", "engines": {"node": ">= 0.6.0"}}, "node_modules/destroy": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/detect-node": {"version": "2.1.0", "license": "MIT"}, "node_modules/di": {"version": "0.0.1", "devOptional": true, "license": "MIT"}, "node_modules/diff": {"version": "4.0.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.3.1"}}, "node_modules/dir-glob": {"version": "3.0.1", "license": "MIT", "dependencies": {"path-type": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/dns-equal": {"version": "1.0.0", "license": "MIT"}, "node_modules/dns-packet": {"version": "1.3.4", "license": "MIT", "dependencies": {"ip": "^1.1.0", "safe-buffer": "^5.0.1"}}, "node_modules/dns-txt": {"version": "2.0.2", "license": "MIT", "dependencies": {"buffer-indexof": "^1.0.0"}}, "node_modules/dom-serialize": {"version": "2.2.1", "devOptional": true, "license": "MIT", "dependencies": {"custom-event": "~1.0.0", "ent": "~2.2.0", "extend": "^3.0.0", "void-elements": "^2.0.0"}}, "node_modules/dom-serializer": {"version": "1.4.1", "license": "MIT", "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^4.2.0", "entities": "^2.0.0"}, "funding": {"url": "https://github.com/cheeriojs/dom-serializer?sponsor=1"}}, "node_modules/domelementtype": {"version": "2.3.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/fb55"}], "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/domhandler": {"version": "4.3.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"domelementtype": "^2.2.0"}, "engines": {"node": ">= 4"}, "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}}, "node_modules/domutils": {"version": "2.8.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dom-serializer": "^1.0.1", "domelementtype": "^2.2.0", "domhandler": "^4.2.0"}, "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}}, "node_modules/dropzone": {"version": "5.9.3", "license": "MIT"}, "node_modules/ee-first": {"version": "1.1.1", "license": "MIT"}, "node_modules/electron-to-chromium": {"version": "1.5.64", "license": "ISC"}, "node_modules/emoji-regex": {"version": "8.0.0", "license": "MIT"}, "node_modules/emojis-list": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/encodeurl": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/encoding": {"version": "0.1.13", "dev": true, "license": "MIT", "optional": true, "dependencies": {"iconv-lite": "^0.6.2"}}, "node_modules/encoding/node_modules/iconv-lite": {"version": "0.6.3", "dev": true, "license": "MIT", "optional": true, "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/engine.io": {"version": "6.6.2", "devOptional": true, "license": "MIT", "dependencies": {"@types/cookie": "^0.4.1", "@types/cors": "^2.8.12", "@types/node": ">=10.0.0", "accepts": "~1.3.4", "base64id": "2.0.0", "cookie": "~0.7.2", "cors": "~2.8.5", "debug": "~4.3.1", "engine.io-parser": "~5.2.1", "ws": "~8.17.1"}, "engines": {"node": ">=10.2.0"}}, "node_modules/engine.io-parser": {"version": "5.2.3", "devOptional": true, "license": "MIT", "engines": {"node": ">=10.0.0"}}, "node_modules/enhanced-resolve": {"version": "5.17.1", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.4", "tapable": "^2.2.0"}, "engines": {"node": ">=10.13.0"}}, "node_modules/ent": {"version": "2.2.1", "devOptional": true, "license": "MIT", "dependencies": {"punycode": "^1.4.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/entities": {"version": "2.2.0", "license": "BSD-2-<PERSON><PERSON>", "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/env-paths": {"version": "2.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/err-code": {"version": "2.0.3", "dev": true, "license": "MIT"}, "node_modules/errno": {"version": "0.1.8", "license": "MIT", "optional": true, "dependencies": {"prr": "~1.0.1"}, "bin": {"errno": "cli.js"}}, "node_modules/error-ex": {"version": "1.3.2", "license": "MIT", "dependencies": {"is-arrayish": "^0.2.1"}}, "node_modules/es-define-property": {"version": "1.0.0", "license": "MIT", "dependencies": {"get-intrinsic": "^1.2.4"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-module-lexer": {"version": "1.5.4", "license": "MIT"}, "node_modules/esbuild": {"version": "0.14.22", "hasInstallScript": true, "license": "MIT", "optional": true, "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=12"}, "optionalDependencies": {"esbuild-android-arm64": "0.14.22", "esbuild-darwin-64": "0.14.22", "esbuild-darwin-arm64": "0.14.22", "esbuild-freebsd-64": "0.14.22", "esbuild-freebsd-arm64": "0.14.22", "esbuild-linux-32": "0.14.22", "esbuild-linux-64": "0.14.22", "esbuild-linux-arm": "0.14.22", "esbuild-linux-arm64": "0.14.22", "esbuild-linux-mips64le": "0.14.22", "esbuild-linux-ppc64le": "0.14.22", "esbuild-linux-riscv64": "0.14.22", "esbuild-linux-s390x": "0.14.22", "esbuild-netbsd-64": "0.14.22", "esbuild-openbsd-64": "0.14.22", "esbuild-sunos-64": "0.14.22", "esbuild-windows-32": "0.14.22", "esbuild-windows-64": "0.14.22", "esbuild-windows-arm64": "0.14.22"}}, "node_modules/esbuild-wasm": {"version": "0.14.22", "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=12"}}, "node_modules/esbuild/node_modules/esbuild-darwin-arm64": {"version": "0.14.22", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=12"}}, "node_modules/escalade": {"version": "3.2.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-html": {"version": "1.0.3", "license": "MIT"}, "node_modules/escape-string-regexp": {"version": "1.0.5", "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/eslint-scope": {"version": "5.1.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/esprima": {"version": "4.0.1", "license": "BSD-2-<PERSON><PERSON>", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/esrecurse": {"version": "4.3.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/esrecurse/node_modules/estraverse": {"version": "5.3.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "4.3.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/esutils": {"version": "2.0.3", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/etag": {"version": "1.8.1", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/eventemitter-asyncresource": {"version": "1.0.0", "license": "MIT"}, "node_modules/eventemitter3": {"version": "4.0.7", "license": "MIT"}, "node_modules/events": {"version": "3.3.0", "license": "MIT", "engines": {"node": ">=0.8.x"}}, "node_modules/execa": {"version": "5.1.1", "license": "MIT", "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/express": {"version": "4.21.1", "license": "MIT", "dependencies": {"accepts": "~1.3.8", "array-flatten": "1.1.1", "body-parser": "1.20.3", "content-disposition": "0.5.4", "content-type": "~1.0.4", "cookie": "0.7.1", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "2.0.0", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "1.3.1", "fresh": "0.5.2", "http-errors": "2.0.0", "merge-descriptors": "1.0.3", "methods": "~1.1.2", "on-finished": "2.4.1", "parseurl": "~1.3.3", "path-to-regexp": "0.1.10", "proxy-addr": "~2.0.7", "qs": "6.13.0", "range-parser": "~1.2.1", "safe-buffer": "5.2.1", "send": "0.19.0", "serve-static": "1.16.2", "setprototypeof": "1.2.0", "statuses": "2.0.1", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/express/node_modules/array-flatten": {"version": "1.1.1", "license": "MIT"}, "node_modules/express/node_modules/cookie": {"version": "0.7.1", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/express/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/express/node_modules/encodeurl": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/express/node_modules/finalhandler": {"version": "1.3.1", "license": "MIT", "dependencies": {"debug": "2.6.9", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "on-finished": "2.4.1", "parseurl": "~1.3.3", "statuses": "2.0.1", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/express/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/express/node_modules/statuses": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/extend": {"version": "3.0.2", "devOptional": true, "license": "MIT"}, "node_modules/external-editor": {"version": "3.1.0", "license": "MIT", "dependencies": {"chardet": "^0.7.0", "iconv-lite": "^0.4.24", "tmp": "^0.0.33"}, "engines": {"node": ">=4"}}, "node_modules/fast-deep-equal": {"version": "3.1.3", "license": "MIT"}, "node_modules/fast-glob": {"version": "3.3.2", "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.4"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "license": "MIT"}, "node_modules/fastq": {"version": "1.17.1", "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/faye-websocket": {"version": "0.11.4", "license": "Apache-2.0", "dependencies": {"websocket-driver": ">=0.5.1"}, "engines": {"node": ">=0.8.0"}}, "node_modules/figures": {"version": "3.2.0", "license": "MIT", "dependencies": {"escape-string-regexp": "^1.0.5"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/fill-range": {"version": "7.1.1", "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/finalhandler": {"version": "1.1.2", "devOptional": true, "license": "MIT", "dependencies": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "statuses": "~1.5.0", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/finalhandler/node_modules/debug": {"version": "2.6.9", "devOptional": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/finalhandler/node_modules/ms": {"version": "2.0.0", "devOptional": true, "license": "MIT"}, "node_modules/finalhandler/node_modules/on-finished": {"version": "2.3.0", "devOptional": true, "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/find-cache-dir": {"version": "3.3.2", "license": "MIT", "dependencies": {"commondir": "^1.0.1", "make-dir": "^3.0.2", "pkg-dir": "^4.1.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/avajs/find-cache-dir?sponsor=1"}}, "node_modules/find-up": {"version": "4.1.0", "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/flat": {"version": "5.0.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "bin": {"flat": "cli.js"}}, "node_modules/flatted": {"version": "3.3.1", "devOptional": true, "license": "ISC"}, "node_modules/follow-redirects": {"version": "1.15.9", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/forwarded": {"version": "0.2.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fraction.js": {"version": "4.3.7", "license": "MIT", "engines": {"node": "*"}, "funding": {"type": "patreon", "url": "https://github.com/sponsors/rawify"}}, "node_modules/fresh": {"version": "0.5.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fs-extra": {"version": "8.1.0", "devOptional": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}, "engines": {"node": ">=6 <7 || >=8"}}, "node_modules/fs-minipass": {"version": "2.1.0", "license": "ISC", "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/fs-monkey": {"version": "1.0.6", "license": "Unlicense"}, "node_modules/fs.realpath": {"version": "1.0.0", "license": "ISC"}, "node_modules/function-bind": {"version": "1.1.2", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/functions-have-names": {"version": "1.2.3", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gauge": {"version": "4.0.4", "dev": true, "license": "ISC", "dependencies": {"aproba": "^1.0.3 || ^2.0.0", "color-support": "^1.1.3", "console-control-strings": "^1.1.0", "has-unicode": "^2.0.1", "signal-exit": "^3.0.7", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "wide-align": "^1.1.5"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/gensync": {"version": "1.0.0-beta.2", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/get-caller-file": {"version": "2.0.5", "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-intrinsic": {"version": "1.2.4", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2", "has-proto": "^1.0.1", "has-symbols": "^1.0.3", "hasown": "^2.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-package-type": {"version": "0.1.0", "license": "MIT", "engines": {"node": ">=8.0.0"}}, "node_modules/get-stream": {"version": "6.0.1", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/glob": {"version": "7.2.0", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "5.1.2", "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/glob-to-regexp": {"version": "0.4.1", "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/globals": {"version": "11.12.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/globby": {"version": "12.2.0", "license": "MIT", "dependencies": {"array-union": "^3.0.1", "dir-glob": "^3.0.1", "fast-glob": "^3.2.7", "ignore": "^5.1.9", "merge2": "^1.4.1", "slash": "^4.0.0"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/gopd": {"version": "1.0.1", "license": "MIT", "dependencies": {"get-intrinsic": "^1.1.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/graceful-fs": {"version": "4.2.11", "license": "ISC"}, "node_modules/handle-thing": {"version": "2.0.1", "license": "MIT"}, "node_modules/has-flag": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-property-descriptors": {"version": "1.0.2", "license": "MIT", "dependencies": {"es-define-property": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-proto": {"version": "1.0.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.0.3", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-tostringtag": {"version": "1.0.2", "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-unicode": {"version": "2.0.1", "dev": true, "license": "ISC"}, "node_modules/hasown": {"version": "2.0.2", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/hdr-histogram-js": {"version": "2.0.3", "license": "BSD", "dependencies": {"@assemblyscript/loader": "^0.10.1", "base64-js": "^1.2.0", "pako": "^1.0.3"}}, "node_modules/hdr-histogram-percentiles-obj": {"version": "3.0.0", "license": "MIT"}, "node_modules/hosted-git-info": {"version": "4.1.0", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "engines": {"node": ">=10"}}, "node_modules/hosted-git-info/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/hosted-git-info/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/hpack.js": {"version": "2.1.6", "license": "MIT", "dependencies": {"inherits": "^2.0.1", "obuf": "^1.0.0", "readable-stream": "^2.0.1", "wbuf": "^1.1.0"}}, "node_modules/hpack.js/node_modules/readable-stream": {"version": "2.3.8", "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/hpack.js/node_modules/safe-buffer": {"version": "5.1.2", "license": "MIT"}, "node_modules/hpack.js/node_modules/string_decoder": {"version": "1.1.1", "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/html-entities": {"version": "2.5.2", "funding": [{"type": "github", "url": "https://github.com/sponsors/mdevils"}, {"type": "patreon", "url": "https://patreon.com/mdevils"}], "license": "MIT"}, "node_modules/html-escaper": {"version": "2.0.2", "dev": true, "license": "MIT"}, "node_modules/http-cache-semantics": {"version": "4.1.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/http-deceiver": {"version": "1.2.7", "license": "MIT"}, "node_modules/http-errors": {"version": "2.0.0", "license": "MIT", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/http-errors/node_modules/statuses": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/http-parser-js": {"version": "0.5.8", "license": "MIT"}, "node_modules/http-proxy": {"version": "1.18.1", "license": "MIT", "dependencies": {"eventemitter3": "^4.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/http-proxy-agent": {"version": "4.0.1", "dev": true, "license": "MIT", "dependencies": {"@tootallnate/once": "1", "agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/http-proxy-middleware": {"version": "2.0.7", "license": "MIT", "dependencies": {"@types/http-proxy": "^1.17.8", "http-proxy": "^1.18.1", "is-glob": "^4.0.1", "is-plain-obj": "^3.0.0", "micromatch": "^4.0.2"}, "engines": {"node": ">=12.0.0"}, "peerDependencies": {"@types/express": "^4.17.13"}, "peerDependenciesMeta": {"@types/express": {"optional": true}}}, "node_modules/https-proxy-agent": {"version": "5.0.0", "license": "MIT", "dependencies": {"agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/human-signals": {"version": "2.1.0", "license": "Apache-2.0", "engines": {"node": ">=10.17.0"}}, "node_modules/humanize-ms": {"version": "1.2.1", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.0.0"}}, "node_modules/iconv-lite": {"version": "0.4.24", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/icss-utils": {"version": "5.1.0", "license": "ISC", "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/ieee754": {"version": "1.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ignore": {"version": "5.3.2", "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/ignore-walk": {"version": "4.0.1", "dev": true, "license": "ISC", "dependencies": {"minimatch": "^3.0.4"}, "engines": {"node": ">=10"}}, "node_modules/image-size": {"version": "0.5.5", "license": "MIT", "optional": true, "bin": {"image-size": "bin/image-size.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/immer": {"version": "10.1.1", "license": "MIT", "peer": true, "funding": {"type": "opencollective", "url": "https://opencollective.com/immer"}}, "node_modules/immutable": {"version": "4.3.7", "license": "MIT"}, "node_modules/import-fresh": {"version": "3.3.0", "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/import-fresh/node_modules/resolve-from": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/imurmurhash": {"version": "0.1.4", "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/indent-string": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/infer-owner": {"version": "1.0.4", "license": "ISC"}, "node_modules/inflight": {"version": "1.0.6", "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "license": "ISC"}, "node_modules/ini": {"version": "2.0.0", "dev": true, "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/inquirer": {"version": "8.2.0", "license": "MIT", "dependencies": {"ansi-escapes": "^4.2.1", "chalk": "^4.1.1", "cli-cursor": "^3.1.0", "cli-width": "^3.0.0", "external-editor": "^3.0.3", "figures": "^3.0.0", "lodash": "^4.17.21", "mute-stream": "0.0.8", "ora": "^5.4.1", "run-async": "^2.4.0", "rxjs": "^7.2.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0", "through": "^2.3.6"}, "engines": {"node": ">=8.0.0"}}, "node_modules/intl-tel-input": {"version": "17.0.21", "license": "MIT"}, "node_modules/ip": {"version": "1.1.9", "license": "MIT"}, "node_modules/ip-address": {"version": "9.0.5", "dev": true, "license": "MIT", "dependencies": {"jsbn": "1.1.0", "sprintf-js": "^1.1.3"}, "engines": {"node": ">= 12"}}, "node_modules/ip-address/node_modules/sprintf-js": {"version": "1.1.3", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ipaddr.js": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">= 10"}}, "node_modules/is-arguments": {"version": "1.1.1", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-arrayish": {"version": "0.2.1", "license": "MIT"}, "node_modules/is-binary-path": {"version": "2.1.0", "license": "MIT", "dependencies": {"binary-extensions": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/is-core-module": {"version": "2.15.1", "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-date-object": {"version": "1.0.5", "license": "MIT", "dependencies": {"has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-docker": {"version": "2.2.1", "license": "MIT", "bin": {"is-docker": "cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-extglob": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-glob": {"version": "4.0.3", "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-interactive": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-lambda": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/is-number": {"version": "7.0.0", "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-path-cwd": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/is-path-inside": {"version": "3.0.3", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-plain-obj": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-plain-object": {"version": "2.0.4", "license": "MIT", "dependencies": {"isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-regex": {"version": "1.1.4", "license": "MIT", "dependencies": {"call-bind": "^1.0.2", "has-tostringtag": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-stream": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-unicode-supported": {"version": "0.1.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-what": {"version": "3.14.1", "license": "MIT"}, "node_modules/is-wsl": {"version": "2.2.0", "license": "MIT", "dependencies": {"is-docker": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/isarray": {"version": "1.0.0", "license": "MIT"}, "node_modules/isbinaryfile": {"version": "4.0.10", "devOptional": true, "license": "MIT", "engines": {"node": ">= 8.0.0"}, "funding": {"url": "https://github.com/sponsors/gjtorikian/"}}, "node_modules/isexe": {"version": "2.0.0", "license": "ISC"}, "node_modules/isobject": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/istanbul-lib-coverage": {"version": "3.2.2", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-instrument": {"version": "5.2.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/core": "^7.12.3", "@babel/parser": "^7.14.7", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.2.0", "semver": "^6.3.0"}, "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-instrument/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/istanbul-lib-report": {"version": "3.0.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"istanbul-lib-coverage": "^3.0.0", "make-dir": "^4.0.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}}, "node_modules/istanbul-lib-report/node_modules/make-dir": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"semver": "^7.5.3"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/istanbul-lib-report/node_modules/semver": {"version": "7.6.3", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/istanbul-lib-source-maps": {"version": "4.0.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"debug": "^4.1.1", "istanbul-lib-coverage": "^3.0.0", "source-map": "^0.6.1"}, "engines": {"node": ">=10"}}, "node_modules/istanbul-lib-source-maps/node_modules/source-map": {"version": "0.6.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/istanbul-reports": {"version": "3.1.7", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/jasmine-core": {"version": "4.0.1", "dev": true, "license": "MIT"}, "node_modules/javascript-natural-sort": {"version": "0.7.1", "license": "MIT", "peer": true}, "node_modules/jest-worker": {"version": "27.5.1", "license": "MIT", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "engines": {"node": ">= 10.13.0"}}, "node_modules/jest-worker/node_modules/supports-color": {"version": "8.1.1", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/jiti": {"version": "1.21.6", "dev": true, "license": "MIT", "bin": {"jiti": "bin/jiti.js"}}, "node_modules/jmespath": {"version": "0.16.0", "license": "Apache-2.0", "peer": true, "engines": {"node": ">= 0.6.0"}}, "node_modules/jquery": {"version": "3.7.1", "license": "MIT", "peer": true}, "node_modules/js-tokens": {"version": "4.0.0", "license": "MIT"}, "node_modules/js-yaml": {"version": "3.14.1", "license": "MIT", "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsbn": {"version": "1.1.0", "dev": true, "license": "MIT"}, "node_modules/jsesc": {"version": "2.5.2", "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=4"}}, "node_modules/json-parse-even-better-errors": {"version": "2.3.1", "license": "MIT"}, "node_modules/json-schema-traverse": {"version": "1.0.0", "license": "MIT"}, "node_modules/json-source-map": {"version": "0.6.1", "license": "MIT", "peer": true}, "node_modules/json5": {"version": "2.2.3", "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/jsonc-parser": {"version": "3.0.0", "license": "MIT"}, "node_modules/jsoneditor": {"version": "10.1.1", "license": "Apache-2.0", "peer": true, "dependencies": {"ace-builds": "^1.36.2", "ajv": "^6.12.6", "javascript-natural-sort": "^0.7.1", "jmespath": "^0.16.0", "json-source-map": "^0.6.1", "jsonrepair": "^3.8.1", "mobius1-selectr": "^2.4.13", "picomodal": "^3.0.0", "vanilla-picker": "^2.12.3"}}, "node_modules/jsoneditor/node_modules/ajv": {"version": "6.12.6", "license": "MIT", "peer": true, "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/jsoneditor/node_modules/json-schema-traverse": {"version": "0.4.1", "license": "MIT", "peer": true}, "node_modules/jsonfile": {"version": "4.0.0", "devOptional": true, "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/jsonparse": {"version": "1.3.1", "dev": true, "engines": ["node >= 0.2.0"], "license": "MIT"}, "node_modules/jsonrepair": {"version": "3.8.1", "license": "ISC", "peer": true, "bin": {"jsonrepair": "bin/cli.js"}}, "node_modules/karma": {"version": "6.3.20", "devOptional": true, "license": "MIT", "dependencies": {"@colors/colors": "1.5.0", "body-parser": "^1.19.0", "braces": "^3.0.2", "chokidar": "^3.5.1", "connect": "^3.7.0", "di": "^0.0.1", "dom-serialize": "^2.2.1", "glob": "^7.1.7", "graceful-fs": "^4.2.6", "http-proxy": "^1.18.1", "isbinaryfile": "^4.0.8", "lodash": "^4.17.21", "log4js": "^6.4.1", "mime": "^2.5.2", "minimatch": "^3.0.4", "mkdirp": "^0.5.5", "qjobs": "^1.2.0", "range-parser": "^1.2.1", "rimraf": "^3.0.2", "socket.io": "^4.4.1", "source-map": "^0.6.1", "tmp": "^0.2.1", "ua-parser-js": "^0.7.30", "yargs": "^16.1.1"}, "bin": {"karma": "bin/karma"}, "engines": {"node": ">= 10"}}, "node_modules/karma-chrome-launcher": {"version": "3.1.1", "dev": true, "license": "MIT", "dependencies": {"which": "^1.2.1"}}, "node_modules/karma-coverage": {"version": "2.1.1", "dev": true, "license": "MIT", "dependencies": {"istanbul-lib-coverage": "^3.2.0", "istanbul-lib-instrument": "^4.0.3", "istanbul-lib-report": "^3.0.0", "istanbul-lib-source-maps": "^4.0.1", "istanbul-reports": "^3.0.5", "minimatch": "^3.0.4"}, "engines": {"node": ">=10.0.0"}}, "node_modules/karma-coverage/node_modules/istanbul-lib-instrument": {"version": "4.0.3", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/core": "^7.7.5", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.0.0", "semver": "^6.3.0"}, "engines": {"node": ">=8"}}, "node_modules/karma-coverage/node_modules/semver": {"version": "6.3.1", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/karma-jasmine": {"version": "4.0.2", "dev": true, "license": "MIT", "dependencies": {"jasmine-core": "^3.6.0"}, "engines": {"node": ">= 10"}, "peerDependencies": {"karma": "*"}}, "node_modules/karma-jasmine-html-reporter": {"version": "1.7.0", "dev": true, "license": "MIT", "peerDependencies": {"jasmine-core": ">=3.8", "karma": ">=0.9", "karma-jasmine": ">=1.1"}}, "node_modules/karma-jasmine/node_modules/jasmine-core": {"version": "3.99.1", "dev": true, "license": "MIT"}, "node_modules/karma-source-map-support": {"version": "1.4.0", "license": "MIT", "dependencies": {"source-map-support": "^0.5.5"}}, "node_modules/karma/node_modules/cliui": {"version": "7.0.4", "devOptional": true, "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^7.0.0"}}, "node_modules/karma/node_modules/mkdirp": {"version": "0.5.6", "devOptional": true, "license": "MIT", "dependencies": {"minimist": "^1.2.6"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/karma/node_modules/source-map": {"version": "0.6.1", "devOptional": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/karma/node_modules/tmp": {"version": "0.2.3", "devOptional": true, "license": "MIT", "engines": {"node": ">=14.14"}}, "node_modules/karma/node_modules/yargs": {"version": "16.2.0", "devOptional": true, "license": "MIT", "dependencies": {"cliui": "^7.0.2", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.0", "y18n": "^5.0.5", "yargs-parser": "^20.2.2"}, "engines": {"node": ">=10"}}, "node_modules/karma/node_modules/yargs-parser": {"version": "20.2.9", "devOptional": true, "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/kind-of": {"version": "6.0.3", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/klona": {"version": "2.0.6", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/less": {"version": "4.1.2", "license": "Apache-2.0", "dependencies": {"copy-anything": "^2.0.1", "parse-node-version": "^1.0.1", "tslib": "^2.3.0"}, "bin": {"lessc": "bin/lessc"}, "engines": {"node": ">=6"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "needle": "^2.5.2", "source-map": "~0.6.0"}}, "node_modules/less-loader": {"version": "10.2.0", "license": "MIT", "dependencies": {"klona": "^2.0.4"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"less": "^3.5.0 || ^4.0.0", "webpack": "^5.0.0"}}, "node_modules/less/node_modules/make-dir": {"version": "2.1.0", "license": "MIT", "optional": true, "dependencies": {"pify": "^4.0.1", "semver": "^5.6.0"}, "engines": {"node": ">=6"}}, "node_modules/less/node_modules/mime": {"version": "1.6.0", "license": "MIT", "optional": true, "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/less/node_modules/pify": {"version": "4.0.1", "license": "MIT", "optional": true, "engines": {"node": ">=6"}}, "node_modules/less/node_modules/semver": {"version": "5.7.2", "license": "ISC", "optional": true, "bin": {"semver": "bin/semver"}}, "node_modules/less/node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/license-webpack-plugin": {"version": "4.0.2", "license": "ISC", "dependencies": {"webpack-sources": "^3.0.0"}, "peerDependenciesMeta": {"webpack": {"optional": true}, "webpack-sources": {"optional": true}}}, "node_modules/lines-and-columns": {"version": "1.2.4", "license": "MIT"}, "node_modules/loader-runner": {"version": "4.3.0", "license": "MIT", "engines": {"node": ">=6.11.5"}}, "node_modules/loader-utils": {"version": "3.2.1", "license": "MIT", "engines": {"node": ">= 12.13.0"}}, "node_modules/locate-path": {"version": "5.0.0", "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/lodash": {"version": "4.17.21", "license": "MIT"}, "node_modules/lodash-es": {"version": "4.17.21", "license": "MIT"}, "node_modules/lodash.debounce": {"version": "4.0.8", "license": "MIT"}, "node_modules/log-symbols": {"version": "4.1.0", "license": "MIT", "dependencies": {"chalk": "^4.1.0", "is-unicode-supported": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/log4js": {"version": "6.9.1", "devOptional": true, "license": "Apache-2.0", "dependencies": {"date-format": "^4.0.14", "debug": "^4.3.4", "flatted": "^3.2.7", "rfdc": "^1.3.0", "streamroller": "^3.1.5"}, "engines": {"node": ">=8.0"}}, "node_modules/log4js/node_modules/debug": {"version": "4.3.6", "devOptional": true, "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/log4js/node_modules/debug/node_modules/ms": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==", "devOptional": true, "license": "MIT"}, "node_modules/lru-cache": {"version": "5.1.1", "license": "ISC", "dependencies": {"yallist": "^3.0.2"}}, "node_modules/luxon": {"version": "1.24.0", "license": "MIT", "peer": true, "engines": {"node": "*"}}, "node_modules/magic-string": {"version": "0.25.7", "license": "MIT", "dependencies": {"sourcemap-codec": "^1.4.4"}}, "node_modules/make-dir": {"version": "3.1.0", "license": "MIT", "dependencies": {"semver": "^6.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/make-dir/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/make-error": {"version": "1.3.6", "resolved": "https://registry.npmjs.org/make-error/-/make-error-1.3.6.tgz", "integrity": "sha512-s8UhlNe7vPKomQhC1qFelMokr/Sc3AgNbso3n74mVPA5LTZwkB9NlXf4XPamLxJE8h0gh73rM94xvwRT2CVInw==", "dev": true, "license": "ISC"}, "node_modules/make-fetch-happen": {"version": "9.1.0", "dev": true, "license": "ISC", "dependencies": {"agentkeepalive": "^4.1.3", "cacache": "^15.2.0", "http-cache-semantics": "^4.1.0", "http-proxy-agent": "^4.0.1", "https-proxy-agent": "^5.0.0", "is-lambda": "^1.0.1", "lru-cache": "^6.0.0", "minipass": "^3.1.3", "minipass-collect": "^1.0.2", "minipass-fetch": "^1.3.2", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "negotiator": "^0.6.2", "promise-retry": "^2.0.1", "socks-proxy-agent": "^6.0.0", "ssri": "^8.0.0"}, "engines": {"node": ">= 10"}}, "node_modules/make-fetch-happen/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/make-fetch-happen/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/media-typer": {"version": "0.3.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/memfs": {"version": "3.5.3", "license": "Unlicense", "dependencies": {"fs-monkey": "^1.0.4"}, "engines": {"node": ">= 4.0.0"}}, "node_modules/merge-descriptors": {"version": "1.0.3", "license": "MIT", "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/merge-stream": {"version": "2.0.0", "license": "MIT"}, "node_modules/merge2": {"version": "1.4.1", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/methods": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/micromatch": {"version": "4.0.8", "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mime": {"version": "2.6.0", "devOptional": true, "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4.0.0"}}, "node_modules/mime-db": {"version": "1.52.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mimic-fn": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/mini-css-extract-plugin": {"version": "2.5.3", "license": "MIT", "dependencies": {"schema-utils": "^4.0.0"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.0.0"}}, "node_modules/mini-css-extract-plugin/node_modules/schema-utils": {"version": "4.2.0", "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.9", "ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/minimalistic-assert": {"version": "1.0.1", "license": "ISC"}, "node_modules/minimatch": {"version": "3.0.5", "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.8", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/minipass": {"version": "3.3.6", "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/minipass-collect": {"version": "1.0.2", "license": "ISC", "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/minipass-fetch": {"version": "1.4.1", "dev": true, "license": "MIT", "dependencies": {"minipass": "^3.1.0", "minipass-sized": "^1.0.3", "minizlib": "^2.0.0"}, "engines": {"node": ">=8"}, "optionalDependencies": {"encoding": "^0.1.12"}}, "node_modules/minipass-flush": {"version": "1.0.5", "license": "ISC", "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/minipass-json-stream": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"jsonparse": "^1.3.1", "minipass": "^3.0.0"}}, "node_modules/minipass-pipeline": {"version": "1.2.4", "license": "ISC", "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/minipass-sized": {"version": "1.0.3", "dev": true, "license": "ISC", "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/minipass/node_modules/yallist": {"version": "4.0.0", "license": "ISC"}, "node_modules/minizlib": {"version": "2.1.2", "license": "MIT", "dependencies": {"minipass": "^3.0.0", "yallist": "^4.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/minizlib/node_modules/yallist": {"version": "4.0.0", "license": "ISC"}, "node_modules/mkdirp": {"version": "1.0.4", "license": "MIT", "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}, "node_modules/mobius1-selectr": {"version": "2.4.13", "license": "MIT", "peer": true}, "node_modules/moment": {"version": "2.30.1", "license": "MIT", "peer": true, "engines": {"node": "*"}}, "node_modules/ms": {"version": "2.1.2", "license": "MIT"}, "node_modules/multicast-dns": {"version": "6.2.3", "license": "MIT", "dependencies": {"dns-packet": "^1.3.1", "thunky": "^1.0.2"}, "bin": {"multicast-dns": "cli.js"}}, "node_modules/multicast-dns-service-types": {"version": "1.1.0", "license": "MIT"}, "node_modules/mute-stream": {"version": "0.0.8", "license": "ISC"}, "node_modules/nanoid": {"version": "3.3.7", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/needle": {"version": "2.9.1", "license": "MIT", "optional": true, "dependencies": {"debug": "^3.2.6", "iconv-lite": "^0.4.4", "sax": "^1.2.4"}, "bin": {"needle": "bin/needle"}, "engines": {"node": ">= 4.4.x"}}, "node_modules/needle/node_modules/debug": {"version": "3.2.7", "license": "MIT", "optional": true, "dependencies": {"ms": "^2.1.1"}}, "node_modules/negotiator": {"version": "0.6.4", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/neo-async": {"version": "2.6.2", "license": "MIT"}, "node_modules/ng2-charts": {"version": "3.1.2", "license": "ISC", "dependencies": {"lodash-es": "^4.17.15", "tslib": "^2.3.0"}, "peerDependencies": {"@angular/common": ">=11.0.0", "@angular/core": ">=11.0.0", "chart.js": "^3.4.0", "rxjs": "^6.5.3 || ^7.4.0"}}, "node_modules/ngx-cookie-service": {"version": "13.2.1", "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.0.0"}, "peerDependencies": {"@angular/common": "^13.0.0", "@angular/core": "^13.0.0"}}, "node_modules/ngx-dropzone-wrapper": {"version": "10.0.1", "license": "MIT", "dependencies": {"dropzone": "^5.7.0", "tslib": "^2.0.0"}, "peerDependencies": {"@angular/common": ">=9.0.0", "@angular/core": ">=9.0.0"}}, "node_modules/ngx-mask": {"version": "13.2.2", "license": "MIT", "dependencies": {"tslib": "^2.3.0"}, "peerDependencies": {"@angular/common": ">=12.0.0", "@angular/core": ">=12.0.0", "@angular/forms": ">=12.0.0"}}, "node_modules/ngx-material-timepicker": {"version": "5.6.0", "license": "MIT", "peer": true, "dependencies": {"@types/luxon": "1.11.1", "luxon": "1.24.0", "tslib": "^1.9.0"}}, "node_modules/ngx-material-timepicker/node_modules/tslib": {"version": "1.14.1", "license": "0BSD", "peer": true}, "node_modules/ngx-spinner": {"version": "13.1.1", "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.3.0"}, "peerDependencies": {"@angular/common": "^13.0.0", "@angular/core": "^13.0.0"}}, "node_modules/node-addon-api": {"version": "3.2.1", "license": "MIT", "optional": true}, "node_modules/node-forge": {"version": "1.3.1", "license": "(BSD-3-<PERSON><PERSON> OR GPL-2.0)", "engines": {"node": ">= 6.13.0"}}, "node_modules/node-gyp": {"version": "8.4.1", "dev": true, "license": "MIT", "dependencies": {"env-paths": "^2.2.0", "glob": "^7.1.4", "graceful-fs": "^4.2.6", "make-fetch-happen": "^9.1.0", "nopt": "^5.0.0", "npmlog": "^6.0.0", "rimraf": "^3.0.2", "semver": "^7.3.5", "tar": "^6.1.2", "which": "^2.0.2"}, "bin": {"node-gyp": "bin/node-gyp.js"}, "engines": {"node": ">= 10.12.0"}}, "node_modules/node-gyp-build": {"version": "4.8.2", "license": "MIT", "optional": true, "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}}, "node_modules/node-gyp/node_modules/which": {"version": "2.0.2", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/node-releases": {"version": "2.0.18", "license": "MIT"}, "node_modules/nopt": {"version": "5.0.0", "dev": true, "license": "ISC", "dependencies": {"abbrev": "1"}, "bin": {"nopt": "bin/nopt.js"}, "engines": {"node": ">=6"}}, "node_modules/normalize-path": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/normalize-range": {"version": "0.1.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm-bundled": {"version": "1.1.2", "dev": true, "license": "ISC", "dependencies": {"npm-normalize-package-bin": "^1.0.1"}}, "node_modules/npm-install-checks": {"version": "4.0.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"semver": "^7.1.1"}, "engines": {"node": ">=10"}}, "node_modules/npm-normalize-package-bin": {"version": "1.0.1", "dev": true, "license": "ISC"}, "node_modules/npm-package-arg": {"version": "8.1.5", "dev": true, "license": "ISC", "dependencies": {"hosted-git-info": "^4.0.1", "semver": "^7.3.4", "validate-npm-package-name": "^3.0.0"}, "engines": {"node": ">=10"}}, "node_modules/npm-packlist": {"version": "3.0.0", "dev": true, "license": "ISC", "dependencies": {"glob": "^7.1.6", "ignore-walk": "^4.0.1", "npm-bundled": "^1.1.1", "npm-normalize-package-bin": "^1.0.1"}, "bin": {"npm-packlist": "bin/index.js"}, "engines": {"node": ">=10"}}, "node_modules/npm-pick-manifest": {"version": "6.1.1", "dev": true, "license": "ISC", "dependencies": {"npm-install-checks": "^4.0.0", "npm-normalize-package-bin": "^1.0.1", "npm-package-arg": "^8.1.2", "semver": "^7.3.4"}}, "node_modules/npm-registry-fetch": {"version": "12.0.2", "dev": true, "license": "ISC", "dependencies": {"make-fetch-happen": "^10.0.1", "minipass": "^3.1.6", "minipass-fetch": "^1.4.1", "minipass-json-stream": "^1.0.1", "minizlib": "^2.1.2", "npm-package-arg": "^8.1.5"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "node_modules/npm-registry-fetch/node_modules/cacache": {"version": "16.1.3", "dev": true, "license": "ISC", "dependencies": {"@npmcli/fs": "^2.1.0", "@npmcli/move-file": "^2.0.0", "chownr": "^2.0.0", "fs-minipass": "^2.1.0", "glob": "^8.0.1", "infer-owner": "^1.0.4", "lru-cache": "^7.7.1", "minipass": "^3.1.6", "minipass-collect": "^1.0.2", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "mkdirp": "^1.0.4", "p-map": "^4.0.0", "promise-inflight": "^1.0.1", "rimraf": "^3.0.2", "ssri": "^9.0.0", "tar": "^6.1.11", "unique-filename": "^2.0.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/npm-registry-fetch/node_modules/cacache/node_modules/@npmcli/fs": {"version": "2.1.2", "dev": true, "license": "ISC", "dependencies": {"@gar/promisify": "^1.1.3", "semver": "^7.3.5"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/npm-registry-fetch/node_modules/cacache/node_modules/@npmcli/move-file": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"mkdirp": "^1.0.4", "rimraf": "^3.0.2"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/npm-registry-fetch/node_modules/cacache/node_modules/brace-expansion": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/npm-registry-fetch/node_modules/cacache/node_modules/glob": {"version": "8.1.0", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^5.0.1", "once": "^1.3.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/npm-registry-fetch/node_modules/cacache/node_modules/minimatch": {"version": "5.1.6", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=10"}}, "node_modules/npm-registry-fetch/node_modules/cacache/node_modules/unique-filename": {"version": "2.0.1", "dev": true, "license": "ISC", "dependencies": {"unique-slug": "^3.0.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/npm-registry-fetch/node_modules/cacache/node_modules/unique-slug": {"version": "3.0.0", "dev": true, "license": "ISC", "dependencies": {"imurmurhash": "^0.1.4"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/npm-registry-fetch/node_modules/http-proxy-agent": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"@tootallnate/once": "2", "agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/npm-registry-fetch/node_modules/http-proxy-agent/node_modules/@tootallnate/once": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 10"}}, "node_modules/npm-registry-fetch/node_modules/lru-cache": {"version": "7.18.3", "dev": true, "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/npm-registry-fetch/node_modules/make-fetch-happen": {"version": "10.2.1", "dev": true, "license": "ISC", "dependencies": {"agentkeepalive": "^4.2.1", "cacache": "^16.1.0", "http-cache-semantics": "^4.1.0", "http-proxy-agent": "^5.0.0", "https-proxy-agent": "^5.0.0", "is-lambda": "^1.0.1", "lru-cache": "^7.7.1", "minipass": "^3.1.6", "minipass-collect": "^1.0.2", "minipass-fetch": "^2.0.3", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "socks-proxy-agent": "^7.0.0", "ssri": "^9.0.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/npm-registry-fetch/node_modules/make-fetch-happen/node_modules/minipass-fetch": {"version": "2.1.2", "dev": true, "license": "MIT", "dependencies": {"minipass": "^3.1.6", "minipass-sized": "^1.0.3", "minizlib": "^2.1.2"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "optionalDependencies": {"encoding": "^0.1.13"}}, "node_modules/npm-registry-fetch/node_modules/socks-proxy-agent": {"version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"agent-base": "^6.0.2", "debug": "^4.3.3", "socks": "^2.6.2"}, "engines": {"node": ">= 10"}}, "node_modules/npm-registry-fetch/node_modules/ssri": {"version": "9.0.1", "dev": true, "license": "ISC", "dependencies": {"minipass": "^3.1.1"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/npm-run-path": {"version": "4.0.1", "license": "MIT", "dependencies": {"path-key": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/npmlog": {"version": "6.0.2", "dev": true, "license": "ISC", "dependencies": {"are-we-there-yet": "^3.0.0", "console-control-strings": "^1.1.0", "gauge": "^4.0.3", "set-blocking": "^2.0.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/nth-check": {"version": "2.1.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0"}, "funding": {"url": "https://github.com/fb55/nth-check?sponsor=1"}}, "node_modules/object-assign": {"version": "4.1.1", "devOptional": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-inspect": {"version": "1.13.2", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-is": {"version": "1.1.6", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-keys": {"version": "1.1.1", "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/obuf": {"version": "1.1.2", "license": "MIT"}, "node_modules/on-finished": {"version": "2.4.1", "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/on-headers": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/onetime": {"version": "5.1.2", "license": "MIT", "dependencies": {"mimic-fn": "^2.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/open": {"version": "8.4.0", "license": "MIT", "dependencies": {"define-lazy-prop": "^2.0.0", "is-docker": "^2.1.1", "is-wsl": "^2.2.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ora": {"version": "5.4.1", "license": "MIT", "dependencies": {"bl": "^4.1.0", "chalk": "^4.1.0", "cli-cursor": "^3.1.0", "cli-spinners": "^2.5.0", "is-interactive": "^1.0.0", "is-unicode-supported": "^0.1.0", "log-symbols": "^4.1.0", "strip-ansi": "^6.0.0", "wcwidth": "^1.0.1"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/os-tmpdir": {"version": "1.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/p-limit": {"version": "2.3.0", "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "4.1.0", "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/p-map": {"version": "4.0.0", "license": "MIT", "dependencies": {"aggregate-error": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-retry": {"version": "4.6.2", "license": "MIT", "dependencies": {"@types/retry": "0.12.0", "retry": "^0.13.1"}, "engines": {"node": ">=8"}}, "node_modules/p-retry/node_modules/retry": {"version": "0.13.1", "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/p-try": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/pacote": {"version": "12.0.3", "dev": true, "license": "ISC", "dependencies": {"@npmcli/git": "^2.1.0", "@npmcli/installed-package-contents": "^1.0.6", "@npmcli/promise-spawn": "^1.2.0", "@npmcli/run-script": "^2.0.0", "cacache": "^15.0.5", "chownr": "^2.0.0", "fs-minipass": "^2.1.0", "infer-owner": "^1.0.4", "minipass": "^3.1.3", "mkdirp": "^1.0.3", "npm-package-arg": "^8.0.1", "npm-packlist": "^3.0.0", "npm-pick-manifest": "^6.0.0", "npm-registry-fetch": "^12.0.0", "promise-retry": "^2.0.1", "read-package-json-fast": "^2.0.1", "rimraf": "^3.0.2", "ssri": "^8.0.1", "tar": "^6.1.0"}, "bin": {"pacote": "lib/bin.js"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16"}}, "node_modules/pako": {"version": "1.0.11", "license": "(MIT AND Zlib)"}, "node_modules/parent-module": {"version": "1.0.1", "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/parse-json": {"version": "5.2.0", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/parse-node-version": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/parse5": {"version": "5.1.1", "license": "MIT", "optional": true, "peer": true}, "node_modules/parse5-html-rewriting-stream": {"version": "6.0.1", "license": "MIT", "dependencies": {"parse5": "^6.0.1", "parse5-sax-parser": "^6.0.1"}}, "node_modules/parse5-html-rewriting-stream/node_modules/parse5": {"version": "6.0.1", "license": "MIT"}, "node_modules/parse5-htmlparser2-tree-adapter": {"version": "6.0.1", "license": "MIT", "dependencies": {"parse5": "^6.0.1"}}, "node_modules/parse5-htmlparser2-tree-adapter/node_modules/parse5": {"version": "6.0.1", "license": "MIT"}, "node_modules/parse5-sax-parser": {"version": "6.0.1", "license": "MIT", "dependencies": {"parse5": "^6.0.1"}}, "node_modules/parse5-sax-parser/node_modules/parse5": {"version": "6.0.1", "license": "MIT"}, "node_modules/parseurl": {"version": "1.3.3", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/path-exists": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "3.1.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "license": "MIT"}, "node_modules/path-to-regexp": {"version": "0.1.10", "license": "MIT"}, "node_modules/path-type": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/picocolors": {"version": "1.1.1", "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/picomodal": {"version": "3.0.0", "license": "MIT", "peer": true}, "node_modules/pify": {"version": "2.3.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/piscina": {"version": "3.2.0", "license": "MIT", "dependencies": {"eventemitter-asyncresource": "^1.0.0", "hdr-histogram-js": "^2.0.1", "hdr-histogram-percentiles-obj": "^3.0.0"}, "optionalDependencies": {"nice-napi": "^1.0.2"}}, "node_modules/piscina/node_modules/nice-napi": {"version": "1.0.2", "hasInstallScript": true, "license": "MIT", "optional": true, "os": ["!win32"], "dependencies": {"node-addon-api": "^3.0.0", "node-gyp-build": "^4.2.2"}}, "node_modules/pkg-dir": {"version": "4.2.0", "license": "MIT", "dependencies": {"find-up": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/portfinder": {"version": "1.0.32", "license": "MIT", "dependencies": {"async": "^2.6.4", "debug": "^3.2.7", "mkdirp": "^0.5.6"}, "engines": {"node": ">= 0.12.0"}}, "node_modules/portfinder/node_modules/debug": {"version": "3.2.7", "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "node_modules/portfinder/node_modules/mkdirp": {"version": "0.5.6", "license": "MIT", "dependencies": {"minimist": "^1.2.6"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/postcss": {"version": "8.4.40", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.7", "picocolors": "^1.1.0", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/postcss-attribute-case-insensitive": {"version": "5.0.2", "license": "MIT", "dependencies": {"postcss-selector-parser": "^6.0.10"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-color-functional-notation": {"version": "4.2.4", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-color-hex-alpha": {"version": "8.0.4", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.4"}}, "node_modules/postcss-color-rebeccapurple": {"version": "7.1.1", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-custom-media": {"version": "8.0.2", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.3"}}, "node_modules/postcss-custom-properties": {"version": "12.1.11", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-custom-selectors": {"version": "6.0.3", "license": "MIT", "dependencies": {"postcss-selector-parser": "^6.0.4"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.3"}}, "node_modules/postcss-dir-pseudo-class": {"version": "6.0.5", "license": "CC0-1.0", "dependencies": {"postcss-selector-parser": "^6.0.10"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-double-position-gradients": {"version": "3.1.2", "license": "CC0-1.0", "dependencies": {"@csstools/postcss-progressive-custom-properties": "^1.1.0", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-env-function": {"version": "4.0.6", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "peerDependencies": {"postcss": "^8.4"}}, "node_modules/postcss-focus-visible": {"version": "6.0.4", "license": "CC0-1.0", "dependencies": {"postcss-selector-parser": "^6.0.9"}, "engines": {"node": "^12 || ^14 || >=16"}, "peerDependencies": {"postcss": "^8.4"}}, "node_modules/postcss-focus-within": {"version": "5.0.4", "license": "CC0-1.0", "dependencies": {"postcss-selector-parser": "^6.0.9"}, "engines": {"node": "^12 || ^14 || >=16"}, "peerDependencies": {"postcss": "^8.4"}}, "node_modules/postcss-font-variant": {"version": "5.0.0", "license": "MIT", "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-gap-properties": {"version": "3.0.5", "license": "CC0-1.0", "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-image-set-function": {"version": "4.0.7", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-import": {"version": "14.0.2", "license": "MIT", "dependencies": {"postcss-value-parser": "^4.0.0", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "engines": {"node": ">=10.0.0"}, "peerDependencies": {"postcss": "^8.0.0"}}, "node_modules/postcss-initial": {"version": "4.0.1", "license": "MIT", "peerDependencies": {"postcss": "^8.0.0"}}, "node_modules/postcss-lab-function": {"version": "4.2.1", "license": "CC0-1.0", "dependencies": {"@csstools/postcss-progressive-custom-properties": "^1.1.0", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-loader": {"version": "8.1.1", "dev": true, "license": "MIT", "dependencies": {"cosmiconfig": "^9.0.0", "jiti": "^1.20.0", "semver": "^7.5.4"}, "engines": {"node": ">= 18.12.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"@rspack/core": "0.x || 1.x", "postcss": "^7.0.0 || ^8.0.1", "webpack": "^5.0.0"}, "peerDependenciesMeta": {"@rspack/core": {"optional": true}, "webpack": {"optional": true}}}, "node_modules/postcss-loader/node_modules/argparse": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz", "integrity": "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==", "dev": true, "license": "Python-2.0"}, "node_modules/postcss-loader/node_modules/cosmiconfig": {"version": "9.0.0", "resolved": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-9.0.0.tgz", "integrity": "sha512-itvL5h8RETACmOTFc4UfIyB2RfEHi71Ax6E/PivVxq9NseKbOWpeyHEOIbmAw1rs8Ak0VursQNww7lf7YtUwzg==", "dev": true, "license": "MIT", "dependencies": {"env-paths": "^2.2.1", "import-fresh": "^3.3.0", "js-yaml": "^4.1.0", "parse-json": "^5.2.0"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/d-fischer"}, "peerDependencies": {"typescript": ">=4.9.5"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/postcss-loader/node_modules/js-yaml": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz", "integrity": "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==", "dev": true, "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/postcss-loader/node_modules/js-yaml/node_modules/argparse": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz", "integrity": "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==", "dev": true}, "node_modules/postcss-loader/node_modules/semver": {"version": "7.6.3", "resolved": "https://registry.npmjs.org/semver/-/semver-7.6.3.tgz", "integrity": "sha512-oVekP1cKtI+CTDvHWYFUcMtsK/00wmAEfyqKfNdARm8u1wNVhSgaX7A8d4UuIlUI5e84iEwOhs7ZPYRmzU9U6A==", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/postcss-loader/node_modules/typescript": {"version": "5.7.2", "resolved": "https://registry.npmjs.org/typescript/-/typescript-5.7.2.tgz", "integrity": "sha512-i5t66RHxDvVN40HfDd1PsEThGNnlMCMT3jMUuoh9/0TaqWevNontacunWyN02LA9/fIbEWlcHZcgTKb9QoaLfg==", "dev": true, "license": "Apache-2.0", "optional": true, "peer": true, "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/postcss-logical": {"version": "5.0.4", "license": "CC0-1.0", "engines": {"node": "^12 || ^14 || >=16"}, "peerDependencies": {"postcss": "^8.4"}}, "node_modules/postcss-media-minmax": {"version": "5.0.0", "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-modules-extract-imports": {"version": "3.1.0", "license": "ISC", "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-modules-local-by-default": {"version": "4.0.5", "license": "MIT", "dependencies": {"icss-utils": "^5.0.0", "postcss-selector-parser": "^6.0.2", "postcss-value-parser": "^4.1.0"}, "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-modules-scope": {"version": "3.2.0", "license": "ISC", "dependencies": {"postcss-selector-parser": "^6.0.4"}, "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-modules-values": {"version": "4.0.0", "license": "ISC", "dependencies": {"icss-utils": "^5.0.0"}, "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-nesting": {"version": "10.2.0", "license": "CC0-1.0", "dependencies": {"@csstools/selector-specificity": "^2.0.0", "postcss-selector-parser": "^6.0.10"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-overflow-shorthand": {"version": "3.0.4", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-page-break": {"version": "3.0.4", "license": "MIT", "peerDependencies": {"postcss": "^8"}}, "node_modules/postcss-place": {"version": "7.0.5", "license": "CC0-1.0", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-prefix-selector": {"version": "1.16.1", "dev": true, "license": "MIT", "peerDependencies": {"postcss": ">4 <9"}}, "node_modules/postcss-preset-env": {"version": "7.2.3", "license": "CC0-1.0", "dependencies": {"autoprefixer": "^10.4.2", "browserslist": "^4.19.1", "caniuse-lite": "^1.0.30001299", "css-blank-pseudo": "^3.0.2", "css-has-pseudo": "^3.0.3", "css-prefers-color-scheme": "^6.0.2", "cssdb": "^5.0.0", "postcss-attribute-case-insensitive": "^5.0.0", "postcss-color-functional-notation": "^4.2.1", "postcss-color-hex-alpha": "^8.0.2", "postcss-color-rebeccapurple": "^7.0.2", "postcss-custom-media": "^8.0.0", "postcss-custom-properties": "^12.1.2", "postcss-custom-selectors": "^6.0.0", "postcss-dir-pseudo-class": "^6.0.3", "postcss-double-position-gradients": "^3.0.4", "postcss-env-function": "^4.0.4", "postcss-focus-visible": "^6.0.3", "postcss-focus-within": "^5.0.3", "postcss-font-variant": "^5.0.0", "postcss-gap-properties": "^3.0.2", "postcss-image-set-function": "^4.0.4", "postcss-initial": "^4.0.1", "postcss-lab-function": "^4.0.3", "postcss-logical": "^5.0.3", "postcss-media-minmax": "^5.0.0", "postcss-nesting": "^10.1.2", "postcss-overflow-shorthand": "^3.0.2", "postcss-page-break": "^3.0.4", "postcss-place": "^7.0.3", "postcss-pseudo-class-any-link": "^7.0.2", "postcss-replace-overflow-wrap": "^4.0.0", "postcss-selector-not": "^5.0.0"}, "engines": {"node": "^12 || ^14 || >=16"}, "peerDependencies": {"postcss": "^8.4"}}, "node_modules/postcss-pseudo-class-any-link": {"version": "7.1.6", "license": "CC0-1.0", "dependencies": {"postcss-selector-parser": "^6.0.10"}, "engines": {"node": "^12 || ^14 || >=16"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss": "^8.2"}}, "node_modules/postcss-replace-overflow-wrap": {"version": "4.0.0", "license": "MIT", "peerDependencies": {"postcss": "^8.0.3"}}, "node_modules/postcss-selector-not": {"version": "5.0.0", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-selector-parser": {"version": "6.1.2", "license": "MIT", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "engines": {"node": ">=4"}}, "node_modules/postcss-value-parser": {"version": "4.2.0", "license": "MIT"}, "node_modules/pretty-bytes": {"version": "5.6.0", "license": "MIT", "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/process-nextick-args": {"version": "2.0.1", "license": "MIT"}, "node_modules/promise-inflight": {"version": "1.0.1", "license": "ISC"}, "node_modules/promise-retry": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"err-code": "^2.0.2", "retry": "^0.12.0"}, "engines": {"node": ">=10"}}, "node_modules/proxy-addr": {"version": "2.0.7", "license": "MIT", "dependencies": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/proxy-addr/node_modules/ipaddr.js": {"version": "1.9.1", "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/prr": {"version": "1.0.1", "license": "MIT", "optional": true}, "node_modules/punycode": {"version": "1.4.1", "devOptional": true, "license": "MIT"}, "node_modules/qjobs": {"version": "1.2.0", "devOptional": true, "license": "MIT", "engines": {"node": ">=0.9"}}, "node_modules/qs": {"version": "6.13.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.0.6"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/queue-microtask": {"version": "1.2.3", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/randombytes": {"version": "2.1.0", "license": "MIT", "dependencies": {"safe-buffer": "^5.1.0"}}, "node_modules/range-parser": {"version": "1.2.1", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "2.5.2", "license": "MIT", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/read-cache": {"version": "1.0.0", "license": "MIT", "dependencies": {"pify": "^2.3.0"}}, "node_modules/read-package-json-fast": {"version": "2.0.3", "dev": true, "license": "ISC", "dependencies": {"json-parse-even-better-errors": "^2.3.0", "npm-normalize-package-bin": "^1.0.1"}, "engines": {"node": ">=10"}}, "node_modules/readable-stream": {"version": "3.6.2", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/readdirp": {"version": "3.6.0", "license": "MIT", "dependencies": {"picomatch": "^2.2.1"}, "engines": {"node": ">=8.10.0"}}, "node_modules/reflect-metadata": {"version": "0.1.14", "license": "Apache-2.0"}, "node_modules/regenerate": {"version": "1.4.2", "license": "MIT"}, "node_modules/regenerate-unicode-properties": {"version": "10.2.0", "license": "MIT", "dependencies": {"regenerate": "^1.4.2"}, "engines": {"node": ">=4"}}, "node_modules/regenerator-runtime": {"version": "0.13.9", "license": "MIT"}, "node_modules/regenerator-transform": {"version": "0.15.2", "license": "MIT", "dependencies": {"@babel/runtime": "^7.8.4"}}, "node_modules/regex-parser": {"version": "2.3.0", "license": "MIT"}, "node_modules/regexp.prototype.flags": {"version": "1.5.3", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-errors": "^1.3.0", "set-function-name": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/regexpu-core": {"version": "6.1.1", "license": "MIT", "dependencies": {"regenerate": "^1.4.2", "regenerate-unicode-properties": "^10.2.0", "regjsgen": "^0.8.0", "regjsparser": "^0.11.0", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.1.0"}, "engines": {"node": ">=4"}}, "node_modules/regjsgen": {"version": "0.8.0", "license": "MIT"}, "node_modules/regjsparser": {"version": "0.11.2", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"jsesc": "~3.0.2"}, "bin": {"regjsparser": "bin/parser"}}, "node_modules/regjsparser/node_modules/jsesc": {"version": "3.0.2", "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/replace-in-file-webpack-plugin": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/replace-in-file-webpack-plugin/-/replace-in-file-webpack-plugin-1.0.6.tgz", "integrity": "sha512-+KRgNYL2nbc6nza6SeF+wTBNkovuHFTfJF8QIEqZg5MbwkYpU9no0kH2YU354wvY/BK8mAC2UKoJ7q+sJTvciw==", "dev": true, "license": "MIT"}, "node_modules/require-directory": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/require-from-string": {"version": "2.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/requires-port": {"version": "1.0.0", "license": "MIT"}, "node_modules/resolve": {"version": "1.22.0", "license": "MIT", "dependencies": {"is-core-module": "^2.8.1", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-from": {"version": "5.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/resolve-url-loader": {"version": "5.0.0", "license": "MIT", "dependencies": {"adjust-sourcemap-loader": "^4.0.0", "convert-source-map": "^1.7.0", "loader-utils": "^2.0.0", "postcss": "^8.2.14", "source-map": "0.6.1"}, "engines": {"node": ">=12"}}, "node_modules/resolve-url-loader/node_modules/loader-utils": {"version": "2.0.4", "license": "MIT", "dependencies": {"big.js": "^5.2.2", "emojis-list": "^3.0.0", "json5": "^2.1.2"}, "engines": {"node": ">=8.9.0"}}, "node_modules/resolve-url-loader/node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/restore-cursor": {"version": "3.1.0", "license": "MIT", "dependencies": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}, "engines": {"node": ">=8"}}, "node_modules/retry": {"version": "0.12.0", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/reusify": {"version": "1.0.4", "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rfdc": {"version": "1.4.1", "devOptional": true, "license": "MIT"}, "node_modules/rimraf": {"version": "3.0.2", "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/run-async": {"version": "2.4.1", "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/run-parallel": {"version": "1.2.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/rxjs": {"version": "7.5.7", "license": "Apache-2.0", "dependencies": {"tslib": "^2.1.0"}}, "node_modules/safe-buffer": {"version": "5.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/safer-buffer": {"version": "2.1.2", "license": "MIT"}, "node_modules/sass": {"version": "1.49.9", "license": "MIT", "dependencies": {"chokidar": ">=3.0.0 <4.0.0", "immutable": "^4.0.0", "source-map-js": ">=0.6.2 <2.0.0"}, "bin": {"sass": "sass.js"}, "engines": {"node": ">=12.0.0"}}, "node_modules/sass-loader": {"version": "12.4.0", "license": "MIT", "dependencies": {"klona": "^2.0.4", "neo-async": "^2.6.2"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"fibers": ">= 3.1.0", "node-sass": "^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0", "sass": "^1.3.0", "webpack": "^5.0.0"}, "peerDependenciesMeta": {"fibers": {"optional": true}, "node-sass": {"optional": true}, "sass": {"optional": true}}}, "node_modules/sax": {"version": "1.4.1", "license": "ISC", "optional": true}, "node_modules/schema-utils": {"version": "2.7.1", "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.5", "ajv": "^6.12.4", "ajv-keywords": "^3.5.2"}, "engines": {"node": ">= 8.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/schema-utils/node_modules/ajv": {"version": "6.12.6", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/schema-utils/node_modules/ajv-keywords": {"version": "3.5.2", "license": "MIT", "peerDependencies": {"ajv": "^6.9.1"}}, "node_modules/schema-utils/node_modules/json-schema-traverse": {"version": "0.4.1", "license": "MIT"}, "node_modules/select-hose": {"version": "2.0.0", "license": "MIT"}, "node_modules/selfsigned": {"version": "2.4.1", "license": "MIT", "dependencies": {"@types/node-forge": "^1.3.0", "node-forge": "^1"}, "engines": {"node": ">=10"}}, "node_modules/semver": {"version": "7.3.5", "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/semver/node_modules/lru-cache": {"version": "6.0.0", "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/semver/node_modules/yallist": {"version": "4.0.0", "license": "ISC"}, "node_modules/send": {"version": "0.19.0", "license": "MIT", "dependencies": {"debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "2.0.0", "mime": "1.6.0", "ms": "2.1.3", "on-finished": "2.4.1", "range-parser": "~1.2.1", "statuses": "2.0.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/send/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/send/node_modules/debug/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/send/node_modules/mime": {"version": "1.6.0", "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/send/node_modules/ms": {"version": "2.1.3", "license": "MIT"}, "node_modules/send/node_modules/statuses": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/serialize-javascript": {"version": "6.0.2", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"randombytes": "^2.1.0"}}, "node_modules/serve-index": {"version": "1.9.1", "license": "MIT", "dependencies": {"accepts": "~1.3.4", "batch": "0.6.1", "debug": "2.6.9", "escape-html": "~1.0.3", "http-errors": "~1.6.2", "mime-types": "~2.1.17", "parseurl": "~1.3.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/serve-index/node_modules/debug": {"version": "2.6.9", "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/serve-index/node_modules/depd": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/serve-index/node_modules/http-errors": {"version": "1.6.3", "license": "MIT", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.0", "statuses": ">= 1.4.0 < 2"}, "engines": {"node": ">= 0.6"}}, "node_modules/serve-index/node_modules/inherits": {"version": "2.0.3", "license": "ISC"}, "node_modules/serve-index/node_modules/ms": {"version": "2.0.0", "license": "MIT"}, "node_modules/serve-index/node_modules/setprototypeof": {"version": "1.1.0", "license": "ISC"}, "node_modules/serve-static": {"version": "1.16.2", "license": "MIT", "dependencies": {"encodeurl": "~2.0.0", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.19.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/serve-static/node_modules/encodeurl": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/set-blocking": {"version": "2.0.0", "dev": true, "license": "ISC"}, "node_modules/set-function-length": {"version": "1.2.2", "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/set-function-name": {"version": "2.0.2", "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "functions-have-names": "^1.2.3", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/setprototypeof": {"version": "1.2.0", "license": "ISC"}, "node_modules/shallow-clone": {"version": "3.0.1", "license": "MIT", "dependencies": {"kind-of": "^6.0.2"}, "engines": {"node": ">=8"}}, "node_modules/shebang-command": {"version": "2.0.0", "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/side-channel": {"version": "1.0.6", "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.4", "object-inspect": "^1.13.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/signal-exit": {"version": "3.0.7", "license": "ISC"}, "node_modules/single-spa": {"version": "6.0.3", "resolved": "https://registry.npmjs.org/single-spa/-/single-spa-6.0.3.tgz", "integrity": "sha512-pXlwHXHhs3hx/MnGQHBLdFCVAN2rlm9TLBKazbDMyZ25QRxoen2vlUZ47MyXKa9K8Gd5UZs/gkuQad4TGbViwg==", "license": "MIT"}, "node_modules/single-spa-angular": {"version": "6.3.1", "resolved": "https://registry.npmjs.org/single-spa-angular/-/single-spa-angular-6.3.1.tgz", "integrity": "sha512-aKMWHhhU99KUnhEatf7y+DC1fOrmcvKu/Hgh/XWcNJlhhDCc/Ws+JM0V4g7rAGiUnAeXF/y1vzBLf+oUUtEpZg==", "license": "Apache-2.0", "dependencies": {"tslib": "^2.3.0"}, "peerDependencies": {"@angular/core": ">=13", "json5": "*", "single-spa": ">=4.0.0", "style-loader": "^3.3.1"}}, "node_modules/slash": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/smart-buffer": {"version": "4.2.0", "dev": true, "license": "MIT", "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}}, "node_modules/socket.io": {"version": "4.8.1", "devOptional": true, "license": "MIT", "dependencies": {"accepts": "~1.3.4", "base64id": "~2.0.0", "cors": "~2.8.5", "debug": "~4.3.2", "engine.io": "~6.6.0", "socket.io-adapter": "~2.5.2", "socket.io-parser": "~4.2.4"}, "engines": {"node": ">=10.2.0"}}, "node_modules/socket.io-adapter": {"version": "2.5.5", "devOptional": true, "license": "MIT", "dependencies": {"debug": "~4.3.4", "ws": "~8.17.1"}}, "node_modules/socket.io-adapter/node_modules/debug": {"version": "4.3.6", "devOptional": true, "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/socket.io-adapter/node_modules/debug/node_modules/ms": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==", "devOptional": true, "license": "MIT"}, "node_modules/socket.io-parser": {"version": "4.2.4", "devOptional": true, "license": "MIT", "dependencies": {"@socket.io/component-emitter": "~3.1.0", "debug": "~4.3.1"}, "engines": {"node": ">=10.0.0"}}, "node_modules/sockjs": {"version": "0.3.24", "license": "MIT", "dependencies": {"faye-websocket": "^0.11.3", "uuid": "^8.3.2", "websocket-driver": "^0.7.4"}}, "node_modules/socks": {"version": "2.8.3", "dev": true, "license": "MIT", "dependencies": {"ip-address": "^9.0.5", "smart-buffer": "^4.2.0"}, "engines": {"node": ">= 10.0.0", "npm": ">= 3.0.0"}}, "node_modules/socks-proxy-agent": {"version": "6.2.1", "dev": true, "license": "MIT", "dependencies": {"agent-base": "^6.0.2", "debug": "^4.3.3", "socks": "^2.6.2"}, "engines": {"node": ">= 10"}}, "node_modules/source-map": {"version": "0.7.3", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">= 8"}}, "node_modules/source-map-js": {"version": "1.2.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-loader": {"version": "3.0.1", "license": "MIT", "dependencies": {"abab": "^2.0.5", "iconv-lite": "^0.6.3", "source-map-js": "^1.0.1"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.0.0"}}, "node_modules/source-map-loader/node_modules/iconv-lite": {"version": "0.6.3", "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-resolve": {"version": "0.6.0", "license": "MIT", "dependencies": {"atob": "^2.1.2", "decode-uri-component": "^0.2.0"}}, "node_modules/source-map-support": {"version": "0.5.21", "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/source-map-support/node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/sourcemap-codec": {"version": "1.4.8", "license": "MIT"}, "node_modules/spdy": {"version": "4.0.2", "license": "MIT", "dependencies": {"debug": "^4.1.0", "handle-thing": "^2.0.0", "http-deceiver": "^1.2.7", "select-hose": "^2.0.0", "spdy-transport": "^3.0.0"}, "engines": {"node": ">=6.0.0"}}, "node_modules/spdy-transport": {"version": "3.0.0", "license": "MIT", "dependencies": {"debug": "^4.1.0", "detect-node": "^2.0.4", "hpack.js": "^2.1.6", "obuf": "^1.1.2", "readable-stream": "^3.0.6", "wbuf": "^1.7.3"}}, "node_modules/sprintf-js": {"version": "1.0.3", "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ssri": {"version": "8.0.1", "license": "ISC", "dependencies": {"minipass": "^3.1.1"}, "engines": {"node": ">= 8"}}, "node_modules/statuses": {"version": "1.5.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/streamroller": {"version": "3.1.5", "devOptional": true, "license": "MIT", "dependencies": {"date-format": "^4.0.14", "debug": "^4.3.4", "fs-extra": "^8.1.0"}, "engines": {"node": ">=8.0"}}, "node_modules/streamroller/node_modules/debug": {"version": "4.3.6", "devOptional": true, "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/streamroller/node_modules/debug/node_modules/ms": {"version": "2.1.2", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.2.tgz", "integrity": "sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==", "devOptional": true, "license": "MIT"}, "node_modules/string_decoder": {"version": "1.3.0", "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/string-width": {"version": "4.2.3", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-bom": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/strip-final-newline": {"version": "2.0.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/style-loader": {"version": "3.3.4", "resolved": "https://registry.npmjs.org/style-loader/-/style-loader-3.3.4.tgz", "integrity": "sha512-0WqXzrsMTyb8yjZJHDqwmnwRJvhALK9LfRtRc6B4UTWe8AijYLZYZ9thuJTZc2VfQWINADW/j+LiJnfy2RoC1w==", "license": "MIT", "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.0.0"}}, "node_modules/stylus": {"version": "0.56.0", "license": "MIT", "dependencies": {"css": "^3.0.0", "debug": "^4.3.2", "glob": "^7.1.6", "safer-buffer": "^2.1.2", "sax": "~1.2.4", "source-map": "^0.7.3"}, "bin": {"stylus": "bin/stylus"}, "engines": {"node": "*"}}, "node_modules/stylus-loader": {"version": "6.2.0", "license": "MIT", "dependencies": {"fast-glob": "^3.2.7", "klona": "^2.0.4", "normalize-path": "^3.0.0"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"stylus": ">=0.52.4", "webpack": "^5.0.0"}}, "node_modules/stylus/node_modules/sax": {"version": "1.2.4", "license": "ISC"}, "node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/symbol-observable": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10"}}, "node_modules/tapable": {"version": "2.2.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/tar": {"version": "6.2.1", "license": "ISC", "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/tar/node_modules/minipass": {"version": "5.0.0", "license": "ISC", "engines": {"node": ">=8"}}, "node_modules/tar/node_modules/yallist": {"version": "4.0.0", "license": "ISC"}, "node_modules/terser": {"version": "5.14.2", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@jridgewell/source-map": "^0.3.2", "acorn": "^8.5.0", "commander": "^2.20.0", "source-map-support": "~0.5.20"}, "bin": {"terser": "bin/terser"}, "engines": {"node": ">=10"}}, "node_modules/terser-webpack-plugin": {"version": "5.3.10", "license": "MIT", "dependencies": {"@jridgewell/trace-mapping": "^0.3.20", "jest-worker": "^27.4.5", "schema-utils": "^3.1.1", "serialize-javascript": "^6.0.1", "terser": "^5.26.0"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.1.0"}, "peerDependenciesMeta": {"@swc/core": {"optional": true}, "esbuild": {"optional": true}, "uglify-js": {"optional": true}}}, "node_modules/terser-webpack-plugin/node_modules/ajv": {"version": "6.12.6", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/terser-webpack-plugin/node_modules/ajv-keywords": {"version": "3.5.2", "license": "MIT", "peerDependencies": {"ajv": "^6.9.1"}}, "node_modules/terser-webpack-plugin/node_modules/ajv/node_modules/json-schema-traverse": {"version": "0.4.1", "license": "MIT"}, "node_modules/terser-webpack-plugin/node_modules/schema-utils": {"version": "3.3.0", "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.8", "ajv": "^6.12.5", "ajv-keywords": "^3.5.2"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/terser-webpack-plugin/node_modules/terser": {"version": "5.36.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@jridgewell/source-map": "^0.3.3", "acorn": "^8.8.2", "commander": "^2.20.0", "source-map-support": "~0.5.20"}, "bin": {"terser": "bin/terser"}, "engines": {"node": ">=10"}}, "node_modules/test-exclude": {"version": "6.0.0", "license": "ISC", "dependencies": {"@istanbuljs/schema": "^0.1.2", "glob": "^7.1.4", "minimatch": "^3.0.4"}, "engines": {"node": ">=8"}}, "node_modules/text-table": {"version": "0.2.0", "license": "MIT"}, "node_modules/through": {"version": "2.3.8", "license": "MIT"}, "node_modules/thunky": {"version": "1.1.0", "license": "MIT"}, "node_modules/tinymce": {"version": "7.3.0", "license": "GPL-2.0-or-later"}, "node_modules/tmp": {"version": "0.0.33", "license": "MIT", "dependencies": {"os-tmpdir": "~1.0.2"}, "engines": {"node": ">=0.6.0"}}, "node_modules/to-regex-range": {"version": "5.0.1", "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/toidentifier": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/tree-kill": {"version": "1.2.2", "license": "MIT", "bin": {"tree-kill": "cli.js"}}, "node_modules/ts-node": {"version": "10.9.2", "dev": true, "license": "MIT", "dependencies": {"@cspotcode/source-map-support": "^0.8.0", "@tsconfig/node10": "^1.0.7", "@tsconfig/node12": "^1.0.7", "@tsconfig/node14": "^1.0.0", "@tsconfig/node16": "^1.0.2", "acorn": "^8.4.1", "acorn-walk": "^8.1.1", "arg": "^4.1.0", "create-require": "^1.1.0", "diff": "^4.0.1", "make-error": "^1.1.1", "v8-compile-cache-lib": "^3.0.1", "yn": "3.1.1"}, "bin": {"ts-node": "dist/bin.js", "ts-node-cwd": "dist/bin-cwd.js", "ts-node-esm": "dist/bin-esm.js", "ts-node-script": "dist/bin-script.js", "ts-node-transpile-only": "dist/bin-transpile.js", "ts-script": "dist/bin-script-deprecated.js"}, "peerDependencies": {"@swc/core": ">=1.2.50", "@swc/wasm": ">=1.2.50", "@types/node": "*", "typescript": ">=2.7"}, "peerDependenciesMeta": {"@swc/core": {"optional": true}, "@swc/wasm": {"optional": true}}}, "node_modules/tsconfig-paths": {"version": "3.15.0", "dev": true, "license": "MIT", "dependencies": {"@types/json5": "^0.0.29", "json5": "^1.0.2", "minimist": "^1.2.6", "strip-bom": "^3.0.0"}}, "node_modules/tsconfig-paths/node_modules/json5": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/json5/-/json5-1.0.2.tgz", "integrity": "sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==", "dev": true, "license": "MIT", "dependencies": {"minimist": "^1.2.0"}, "bin": {"json5": "lib/cli.js"}}, "node_modules/tslib": {"version": "2.8.1", "license": "0BSD"}, "node_modules/type-fest": {"version": "0.21.3", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/type-is": {"version": "1.6.18", "license": "MIT", "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "engines": {"node": ">= 0.6"}}, "node_modules/typed-assert": {"version": "1.0.9", "license": "MIT"}, "node_modules/typescript": {"version": "4.6.4", "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=4.2.0"}}, "node_modules/ua-parser-js": {"version": "0.7.39", "devOptional": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/ua-parser-js"}, {"type": "paypal", "url": "https://paypal.me/faisalman"}, {"type": "github", "url": "https://github.com/sponsors/faisalman"}], "license": "MIT", "bin": {"ua-parser-js": "script/cli.js"}, "engines": {"node": "*"}}, "node_modules/unicode-canonical-property-names-ecmascript": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/unicode-match-property-ecmascript": {"version": "2.0.0", "license": "MIT", "dependencies": {"unicode-canonical-property-names-ecmascript": "^2.0.0", "unicode-property-aliases-ecmascript": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/unicode-match-property-value-ecmascript": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/unicode-property-aliases-ecmascript": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/unique-filename": {"version": "1.1.1", "license": "ISC", "dependencies": {"unique-slug": "^2.0.0"}}, "node_modules/unique-slug": {"version": "2.0.2", "license": "ISC", "dependencies": {"imurmurhash": "^0.1.4"}}, "node_modules/universalify": {"version": "0.1.2", "devOptional": true, "license": "MIT", "engines": {"node": ">= 4.0.0"}}, "node_modules/unpipe": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/update-browserslist-db": {"version": "1.1.1", "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.0"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/uri-js": {"version": "4.4.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/uri-js/node_modules/punycode": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/util-deprecate": {"version": "1.0.2", "license": "MIT"}, "node_modules/utils-merge": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/uuid": {"version": "8.3.2", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/v8-compile-cache-lib": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/v8-compile-cache-lib/-/v8-compile-cache-lib-3.0.1.tgz", "integrity": "sha512-wa7YjyUGfNZngI/vtK0UHAN+lgDCxBPCylVXGp0zu59Fz5aiGtNXaq3DhIov063MorB+VfufLh3JlF2KdTK3xg==", "dev": true, "license": "MIT"}, "node_modules/validate-npm-package-name": {"version": "3.0.0", "dev": true, "license": "ISC", "dependencies": {"builtins": "^1.0.3"}}, "node_modules/vanilla-picker": {"version": "2.12.3", "license": "ISC", "peer": true, "dependencies": {"@sphinxxxx/color-conversion": "^2.2.2"}}, "node_modules/vary": {"version": "1.1.2", "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/void-elements": {"version": "2.0.1", "devOptional": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/watchpack": {"version": "2.4.2", "license": "MIT", "dependencies": {"glob-to-regexp": "^0.4.1", "graceful-fs": "^4.1.2"}, "engines": {"node": ">=10.13.0"}}, "node_modules/wbuf": {"version": "1.7.3", "license": "MIT", "dependencies": {"minimalistic-assert": "^1.0.0"}}, "node_modules/wcwidth": {"version": "1.0.1", "license": "MIT", "dependencies": {"defaults": "^1.0.3"}}, "node_modules/webpack": {"version": "5.96.1", "license": "MIT", "dependencies": {"@types/eslint-scope": "^3.7.7", "@types/estree": "^1.0.6", "@webassemblyjs/ast": "^1.12.1", "@webassemblyjs/wasm-edit": "^1.12.1", "@webassemblyjs/wasm-parser": "^1.12.1", "acorn": "^8.14.0", "browserslist": "^4.24.0", "chrome-trace-event": "^1.0.2", "enhanced-resolve": "^5.17.1", "es-module-lexer": "^1.2.1", "eslint-scope": "5.1.1", "events": "^3.2.0", "glob-to-regexp": "^0.4.1", "graceful-fs": "^4.2.11", "json-parse-even-better-errors": "^2.3.1", "loader-runner": "^4.2.0", "mime-types": "^2.1.27", "neo-async": "^2.6.2", "schema-utils": "^3.2.0", "tapable": "^2.1.1", "terser-webpack-plugin": "^5.3.10", "watchpack": "^2.4.1", "webpack-sources": "^3.2.3"}, "bin": {"webpack": "bin/webpack.js"}, "engines": {"node": ">=10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependenciesMeta": {"webpack-cli": {"optional": true}}}, "node_modules/webpack-dev-middleware": {"version": "5.3.0", "license": "MIT", "dependencies": {"colorette": "^2.0.10", "memfs": "^3.2.2", "mime-types": "^2.1.31", "range-parser": "^1.2.1", "schema-utils": "^4.0.0"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}}, "node_modules/webpack-dev-middleware/node_modules/schema-utils": {"version": "4.2.0", "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.9", "ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/webpack-dev-server": {"version": "4.7.3", "license": "MIT", "dependencies": {"@types/bonjour": "^3.5.9", "@types/connect-history-api-fallback": "^1.3.5", "@types/serve-index": "^1.9.1", "@types/sockjs": "^0.3.33", "@types/ws": "^8.2.2", "ansi-html-community": "^0.0.8", "bonjour": "^3.5.0", "chokidar": "^3.5.2", "colorette": "^2.0.10", "compression": "^1.7.4", "connect-history-api-fallback": "^1.6.0", "default-gateway": "^6.0.3", "del": "^6.0.0", "express": "^4.17.1", "graceful-fs": "^4.2.6", "html-entities": "^2.3.2", "http-proxy-middleware": "^2.0.0", "ipaddr.js": "^2.0.1", "open": "^8.0.9", "p-retry": "^4.5.0", "portfinder": "^1.0.28", "schema-utils": "^4.0.0", "selfsigned": "^2.0.0", "serve-index": "^1.9.1", "sockjs": "^0.3.21", "spdy": "^4.0.2", "strip-ansi": "^7.0.0", "webpack-dev-middleware": "^5.3.0", "ws": "^8.1.0"}, "bin": {"webpack-dev-server": "bin/webpack-dev-server.js"}, "engines": {"node": ">= 12.13.0"}, "peerDependencies": {"webpack": "^4.37.0 || ^5.0.0"}, "peerDependenciesMeta": {"webpack-cli": {"optional": true}}}, "node_modules/webpack-dev-server/node_modules/ansi-regex": {"version": "6.1.0", "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/webpack-dev-server/node_modules/schema-utils": {"version": "4.2.0", "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.9", "ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/webpack-dev-server/node_modules/strip-ansi": {"version": "7.1.0", "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/webpack-merge": {"version": "5.10.0", "dev": true, "license": "MIT", "dependencies": {"clone-deep": "^4.0.1", "flat": "^5.0.2", "wildcard": "^2.0.0"}, "engines": {"node": ">=10.0.0"}}, "node_modules/webpack-sources": {"version": "3.2.3", "license": "MIT", "engines": {"node": ">=10.13.0"}}, "node_modules/webpack-subresource-integrity": {"version": "5.1.0", "license": "MIT", "dependencies": {"typed-assert": "^1.0.8"}, "engines": {"node": ">= 12"}, "peerDependencies": {"html-webpack-plugin": ">= 5.0.0-beta.1 < 6", "webpack": "^5.12.0"}, "peerDependenciesMeta": {"html-webpack-plugin": {"optional": true}}}, "node_modules/webpack/node_modules/ajv": {"version": "6.12.6", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/webpack/node_modules/ajv-keywords": {"version": "3.5.2", "license": "MIT", "peerDependencies": {"ajv": "^6.9.1"}}, "node_modules/webpack/node_modules/ajv/node_modules/json-schema-traverse": {"version": "0.4.1", "license": "MIT"}, "node_modules/webpack/node_modules/schema-utils": {"version": "3.3.0", "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.8", "ajv": "^6.12.5", "ajv-keywords": "^3.5.2"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/websocket-driver": {"version": "0.7.4", "license": "Apache-2.0", "dependencies": {"http-parser-js": ">=0.5.1", "safe-buffer": ">=5.1.0", "websocket-extensions": ">=0.1.1"}, "engines": {"node": ">=0.8.0"}}, "node_modules/websocket-extensions": {"version": "0.1.4", "license": "Apache-2.0", "engines": {"node": ">=0.8.0"}}, "node_modules/which": {"version": "1.3.1", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/wide-align": {"version": "1.1.5", "dev": true, "license": "ISC", "dependencies": {"string-width": "^1.0.2 || 2 || 3 || 4"}}, "node_modules/wildcard": {"version": "2.0.1", "license": "MIT"}, "node_modules/wrap-ansi": {"version": "7.0.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrappy": {"version": "1.0.2", "license": "ISC"}, "node_modules/ws": {"version": "8.17.1", "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/y18n": {"version": "5.0.8", "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yallist": {"version": "3.1.1", "license": "ISC"}, "node_modules/yaml": {"version": "1.10.2", "license": "ISC", "engines": {"node": ">= 6"}}, "node_modules/yargs": {"version": "17.7.2", "license": "MIT", "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/yargs-parser": {"version": "21.1.1", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/yn": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/zone.js": {"version": "0.11.8", "license": "MIT", "dependencies": {"tslib": "^2.3.0"}}}}