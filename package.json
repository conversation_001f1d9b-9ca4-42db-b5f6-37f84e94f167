{"name": "accounting-angular", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "watch": "ng build --watch --configuration development", "test": "ng test", "local": "ng build visa-angular --named-chunks --configuration local", "build": "ng build visa-angular --named-chunks --configuration production", "serve:single-spa:accounting-angular": "ng s --project accounting-angular --disable-host-check --port 4200 --live-reload false"}, "private": true, "dependencies": {"@angular/animations": "~13.3.0", "@angular/common": "~13.3.0", "@angular/compiler": "~13.3.0", "@angular/core": "~13.3.0", "@angular/forms": "~13.3.0", "@angular/platform-browser": "~13.3.0", "@angular/platform-browser-dynamic": "~13.3.0", "@angular/router": "~13.3.0", "@maids/cc-erp-services": "^2.3.8-local.0", "@maids/cc-lib": "^2.4.35", "intl-tel-input": "^17.0.12", "ngx-dropzone-wrapper": "^10.0.1", "rxjs": "~7.5.0", "single-spa": ">=4.0.0", "single-spa-angular": "^6.3.1", "tslib": "^2.3.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-builders/custom-webpack": "13.1.0", "@angular-devkit/build-angular": "~13.3.11", "@angular/cli": "~13.3.11", "@angular/compiler-cli": "~13.3.0", "@types/intl-tel-input": "^17.0.1", "@types/jasmine": "~3.10.0", "@types/node": "^12.11.1", "jasmine-core": "~4.0.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.1.0", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "postcss": "^8.4.35", "postcss-loader": "^8.1.1", "postcss-prefix-selector": "^1.16.0", "replace-in-file-webpack-plugin": "^1.0.6", "style-loader": "^3.3.1", "typescript": "~4.6.2", "webpack": "^5.69.1"}}