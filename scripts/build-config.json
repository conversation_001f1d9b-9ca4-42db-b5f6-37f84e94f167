{"logging": {"enableVerbose": false, "showProgress": false, "showCompletion": true}, "routeExtraction": {"outputFileName": "routes-accounting", "entryPoint": "src/app/app-routing.module.ts", "routePrefix": "", "generateTypeScript": false, "generateSummary": false, "includeEmptyPaths": true, "includeParameterOnlyPaths": false, "generatePageCodeFromPath": false, "includeSourceFile": false, "createDevCopies": false}, "postBuild": {"fileMovements": [{"name": "security-visa.json", "source": "src/custom/security-accounting.json", "target": "dist/security-visa.json", "deleteSource": false, "required": true}]}}