const fs = require("fs");
const path = require("path");

class HierarchicalRouteExtractor {
  constructor(configPath = null) {
    this.routes = [];
    this.processedFilePaths = new Set(); // Track file + parent path combinations
    this.config = this.loadConfiguration(configPath);
    this.loggingConfig = this.loadLoggingConfiguration(configPath);
  }

  loadConfiguration(configPath) {
    const defaultConfig = {
      routeExtraction: {
        outputFileName: "app-routes",
        entryPoint: "src/app/app-routing.module.ts",
        routePrefix: "",
        generateTypeScript: true,
        generateSummary: true,
        includeEmptyPaths: false,
        includeParameterOnlyPaths: false,
        generatePageCodeFromPath: false,
        includeSourceFile: true,
        createDevCopies: true,
      },
    };

    if (configPath && fs.existsSync(configPath)) {
      try {
        const userConfig = JSON.parse(fs.readFileSync(configPath, "utf8"));
        const mergedConfig = {
          ...defaultConfig.routeExtraction,
          ...(userConfig.routeExtraction || userConfig),
        };
        return mergedConfig;
      } catch (error) {
        console.warn(
          `⚠️  Could not load config from ${configPath}, using defaults:`,
          error.message
        );
        return defaultConfig.routeExtraction;
      }
    }

    // Try to load from default location (renamed file)
    const defaultConfigPath = path.join(__dirname, "build-config.json");
    if (fs.existsSync(defaultConfigPath)) {
      try {
        const userConfig = JSON.parse(
          fs.readFileSync(defaultConfigPath, "utf8")
        );
        const mergedConfig = {
          ...defaultConfig.routeExtraction,
          ...(userConfig.routeExtraction || userConfig),
        };
        return mergedConfig;
      } catch (error) {
        console.warn(
          `⚠️  Could not load config from ${defaultConfigPath}, using defaults:`,
          error.message
        );
      }
    }

    return defaultConfig.routeExtraction;
  }

  loadLoggingConfiguration(configPath) {
    const defaultLogging = {
      enableVerbose: true,
      showProgress: true,
      showCompletion: true,
    };

    try {
      if (configPath && fs.existsSync(configPath)) {
        const userConfig = JSON.parse(fs.readFileSync(configPath, "utf8"));
        return { ...defaultLogging, ...(userConfig.logging || {}) };
      }

      // Try to load from default location
      const defaultConfigPath = path.join(__dirname, "build-config.json");
      if (fs.existsSync(defaultConfigPath)) {
        const userConfig = JSON.parse(
          fs.readFileSync(defaultConfigPath, "utf8")
        );
        return { ...defaultLogging, ...(userConfig.logging || {}) };
      }
    } catch (error) {
      // Silently fallback to defaults if config parsing fails
    }

    return defaultLogging;
  }

  // Logging methods
  logVerbose(...args) {
    if (this.loggingConfig.enableVerbose) {
      console.log(...args);
    }
  }

  logProgress(...args) {
    if (this.loggingConfig.showProgress) {
      console.log(...args);
    }
  }

  logCompletion(...args) {
    if (this.loggingConfig.showCompletion) {
      console.log(...args);
    }
  }

  logWarning(...args) {
    // Always show warnings
    console.warn(...args);
  }

  logError(...args) {
    // Always show errors
    console.error(...args);
  }

  // Main entry point
  extractRoutes() {
    this.logProgress("🔍 Starting hierarchical route extraction...");
    this.logVerbose(`📋 Configuration:`, this.config);

    // Start from the entry point (app-routing.module.ts)
    const entryPoint = path.resolve(this.config.entryPoint);
    if (!fs.existsSync(entryPoint)) {
      this.logError(`❌ Entry point not found: ${entryPoint}`);
      return;
    }

    // Process the entry point with empty parent path
    this.processRoutingFile(entryPoint, this.config.routePrefix);

    // Write the routes to files
    this.writeRoutesToFiles();

    this.logCompletion(
      `✅ Route extraction completed! Found ${this.routes.length} routes.`
    );
  }

  processRoutingFile(filePath, parentPath = "", parentPageCode = "", parentLabel = "") {
    // Use only the file path as the key to prevent processing the same file multiple times
    // regardless of parent path - each routing file should only be processed once
    const fileKey = filePath;

    if (this.processedFilePaths.has(fileKey)) {
      this.logVerbose(
        `🔄 Already processed: ${path.relative(
          process.cwd(),
          filePath
        )} - skipping to avoid duplicates`
      );
      return;
    }

    this.processedFilePaths.add(fileKey);
    this.logVerbose(
      `📝 Processing: ${path.relative(
        process.cwd(),
        filePath
      )} (parent: "${parentPath}")`
    );

    try {
      const content = fs.readFileSync(filePath, "utf8");
      this.parseRoutes(content, parentPath, filePath, parentPageCode, parentLabel);
    } catch (error) {
      this.logWarning(`⚠️  Could not process file: ${filePath}`, error.message);
    }
  }

  parseRoutes(content, parentPath, filePath, parentPageCode = "", parentLabel = "") {
    // Remove comments and clean up the content
    const cleanContent = content
      .replace(/\/\*[\s\S]*?\*\//g, "") // Remove block comments
      .replace(/\/\/.*$/gm, ""); // Remove line comments

    this.logVerbose(
      `  🔍 Parsing routes in: ${path.relative(process.cwd(), filePath)}`
    );

    // Look for CCRoutes specifically, but also fallback to Routes
    // Use a more sophisticated approach to capture the complete route array
    const routesPattern =
      /const\s+\w*[rR]outes\s*:\s*(CCRoutes|Routes)\s*=\s*\[/g;
    let match;
    let routesMatches = [];

    while ((match = routesPattern.exec(cleanContent)) !== null) {
      routesMatches.push(match);
      const startIndex = match.index + match[0].length - 1; // Position of the opening [
      const routesString = this.extractBalancedBrackets(
        cleanContent,
        startIndex
      );

      if (routesString) {
        this.logVerbose(
          `  📝 Processing route definition (${routesString.length} characters)`
        );
        this.logVerbose(
          `  📄 Route definition content: "${routesString.substring(
            0,
            200
          )}..."`
        );
        this.parseRouteArray(routesString, parentPath, filePath, parentPageCode, parentLabel);
      }
    }

    // If no const routes found, look for other route patterns
    if (routesMatches.length === 0) {
      this.logVerbose(
        `  🔍 No 'const routes' found, looking for other route patterns...`
      );

      // Look for direct route arrays in RouterModule.forChild() calls
      // Updated regex to handle TypeScript type casting like <CCRoutes>
      const forChildMatches = [
        ...cleanContent.matchAll(
          /RouterModule\.forChild\s*\(\s*(?:<[^>]*>)?\s*\[([\s\S]*?)\]\s*\)/g
        ),
      ];

      this.logVerbose(
        `  📋 Found ${forChildMatches.length} RouterModule.forChild definitions`
      );

      for (const forChildMatch of forChildMatches) {
        const routesString = forChildMatch[1];
        this.logVerbose(
          `  📝 Processing RouterModule.forChild (${routesString.length} characters)`
        );
        this.parseRouteArray(routesString, parentPath, filePath, parentPageCode, parentLabel);
      }
    }
  }

  parseRouteArray(routesString, parentPath, filePath, parentPageCode = "", parentLabel = "") {
    this.logVerbose(
      `  🔍 Parsing route array (${routesString.length} characters) with parent: "${parentPath}"`
    );

    const routeObjects = this.extractRouteObjects(routesString);
    this.logVerbose(`  📦 Extracted ${routeObjects.length} route objects`);

    routeObjects.forEach((routeObj, index) => {
      this.logVerbose(
        `  📝 Processing route object ${index + 1}: ${routeObj.substring(
          0,
          100
        )}...`
      );
      this.processRouteObject(routeObj, parentPath, filePath, parentPageCode, parentLabel);
    });
  }

  extractRouteObjects(routesString) {
    this.logVerbose(
      `    🔍 Extracting route objects from: "${routesString.substring(
        0,
        200
      )}..."`
    );

    const routes = [];
    let braceCount = 0;
    let bracketCount = 0;
    let currentRoute = "";
    let inString = false;
    let stringChar = "";
    let escapeNext = false;
    let debugCount = 0;

    for (let i = 0; i < routesString.length; i++) {
      const char = routesString[i];

      if (escapeNext) {
        escapeNext = false;
        currentRoute += char;
        continue;
      }

      if (char === "\\") {
        escapeNext = true;
        currentRoute += char;
        continue;
      }

      if ((char === '"' || char === "'" || char === "`") && !escapeNext) {
        if (!inString) {
          inString = true;
          stringChar = char;
        } else if (char === stringChar) {
          inString = false;
          stringChar = "";
        }
      }

      if (!inString) {
        if (char === "{") {
          braceCount++;
          if (debugCount < 10)
            this.logVerbose(
              `    📈 { found, braceCount: ${braceCount}, bracketCount: ${bracketCount}`
            );
        } else if (char === "}") {
          braceCount--;
          if (debugCount < 10)
            this.logVerbose(
              `    📉 } found, braceCount: ${braceCount}, bracketCount: ${bracketCount}`
            );
        } else if (char === "[") {
          bracketCount++;
          if (debugCount < 10)
            this.logVerbose(
              `    📈 [ found, braceCount: ${braceCount}, bracketCount: ${bracketCount}`
            );
        } else if (char === "]") {
          bracketCount--;
          if (debugCount < 10)
            this.logVerbose(
              `    📉 ] found, braceCount: ${braceCount}, bracketCount: ${bracketCount}`
            );
        }
      }

      currentRoute += char;
      debugCount++;

      // A route object is complete when we're back to 0 braces and brackets, and we just closed a brace
      if (
        braceCount === 0 &&
        bracketCount === 0 &&
        currentRoute.trim() &&
        char === "}"
      ) {
        const trimmedRoute = currentRoute.trim();
        this.logVerbose(
          `    🎯 Route object complete! Length: ${trimmedRoute.length}`
        );
        if (trimmedRoute.includes("path:") || trimmedRoute.includes("path ")) {
          this.logVerbose(
            `    ✅ Found route object: ${trimmedRoute.substring(0, 100)}...`
          );
          routes.push(trimmedRoute);
        } else {
          this.logVerbose(
            `    ❌ Skipped non-route object: ${trimmedRoute.substring(
              0,
              100
            )}...`
          );
        }
        currentRoute = "";
      }
    }

    this.logVerbose(`    📦 Total route objects found: ${routes.length}`);
    return routes;
  }

  processRouteObject(routeString, parentPath, filePath, parentPageCode = "", parentLabel = "") {
    // Extract path
    const pathMatches = [
      ...routeString.matchAll(/path\s*:\s*["'`]([^"'`]*)["'`]/g),
    ];

    if (pathMatches.length === 0) return;

    const routePath = pathMatches[0][1];

    // Skip paths based on configuration
    if (routePath === "" && !this.config.includeEmptyPaths) {
      return;
    }
    if (routePath.startsWith(":") && !this.config.includeParameterOnlyPaths) {
      return;
    }

    // Build full route
    const fullRoute = this.buildFullRoute(parentPath, routePath);

    // Extract data object using balanced braces approach
    let pageCode = "";
    let label = "";

    const dataMatch = routeString.match(/data\s*:\s*\{/);
    if (dataMatch) {
      const startIndex = dataMatch.index + dataMatch[0].length - 1; // Position of the opening {
      const dataContent = this.extractBalancedBraces(routeString, startIndex);

      if (dataContent) {
        this.logVerbose(`    📋 Found data object: ${dataContent.substring(0, 100)}...`);

        // Extract pageCode from data object
        const pageCodeMatches = [
          ...dataContent.matchAll(/pageCode\s*:\s*["'`]([^"'`]*)["'`]/g),
        ];
        pageCode = pageCodeMatches.length > 0 ? pageCodeMatches[0][1] : "";

        // Extract label from data object
        const labelMatches = [
          ...dataContent.matchAll(/label\s*:\s*["'`]([^"'`]*)["'`]/g),
        ];
        label = labelMatches.length > 0 ? labelMatches[0][1] : "";
      }
    } else {
      // Fallback: try to find pageCode and label as direct properties (for backwards compatibility)
      const pageCodeMatches = [
        ...routeString.matchAll(/pageCode\s*:\s*["'`]([^"'`]*)["'`]/g),
      ];
      pageCode = pageCodeMatches.length > 0 ? pageCodeMatches[0][1] : "";

      const labelMatches = [
        ...routeString.matchAll(/label\s*:\s*["'`]([^"'`]*)["'`]/g),
      ];
      label = labelMatches.length > 0 ? labelMatches[0][1] : "";
    }

    // If no pageCode found, inherit from the closest parent that has one
    if (!pageCode && parentPageCode) {
      pageCode = parentPageCode;
      this.logVerbose(`    📤 Inherited pageCode from closest parent: "${pageCode}"`);
    }

    // If no label found, inherit from the closest parent that has one
    if (!label && parentLabel) {
      label = parentLabel;
      this.logVerbose(`    📤 Inherited label from closest parent: "${label}"`);
    }

    this.logVerbose(`    🏷️  Final - Path: "${routePath}", PageCode: "${pageCode}", Label: "${label}"`);

    // Determine what to pass to children - use current values if they exist, otherwise pass parent values
    const inheritedPageCode = pageCode || parentPageCode;
    const inheritedLabel = label || parentLabel;

    // Check if route has children
    const hasChildren =
      routeString.includes("children:") || routeString.includes("children ");

    // Check if route has lazy loading
    const hasLazyLoading = routeString.includes("loadChildren");

    // If it's a concrete route (no children, no lazy loading), add it to the routes
    if (!hasChildren && !hasLazyLoading) {
      this.addRoute(fullRoute, pageCode, label, filePath);
    }

    // Process children if they exist
    if (hasChildren) {
      const childrenMatches = [
        ...routeString.matchAll(/children\s*:\s*\[([\s\S]*)\]/g),
      ];

      for (const childrenMatch of childrenMatches) {
        const childrenString = childrenMatch[1];
        const childRoutes = this.extractRouteObjects(childrenString);

        childRoutes.forEach((childRoute) => {
          // Pass the best available pageCode and label to children
          this.processRouteObject(childRoute, fullRoute, filePath, inheritedPageCode, inheritedLabel);
        });
      }
    }

    // Process lazy loading if it exists
    if (hasLazyLoading) {
      const lazyMatches = [
        ...routeString.matchAll(
          /loadChildren\s*:\s*[^{}]*?import\s*\(\s*["']([^"']+)["']\s*\)/g
        ),
      ];

      for (const lazyMatch of lazyMatches) {
        const importPath = lazyMatch[1];
        const resolvedModulePath = this.resolveModulePath(importPath, filePath);

        if (resolvedModulePath) {
          this.logVerbose(
            `🔗 Following lazy route: "${routePath}" -> ${path.relative(
              process.cwd(),
              resolvedModulePath
            )}`
          );
          // Pass the best available pageCode and label to lazy-loaded module
          this.processRoutingFile(resolvedModulePath, fullRoute, inheritedPageCode, inheritedLabel);
        }
      }
    }
  }

  resolveModulePath(importPath, currentFilePath) {
    // Handle relative imports like './modules/message-templates/message-templates.module'
    if (importPath.startsWith("./") || importPath.startsWith("../")) {
      const currentDir = path.dirname(currentFilePath);
      const modulePath = path.resolve(currentDir, importPath + ".ts");

      this.logVerbose(`🔍 Looking for routes for import: "${importPath}"`);
      this.logVerbose(`   Module path: ${modulePath}`);

      if (fs.existsSync(modulePath)) {
        const moduleDir = path.dirname(modulePath);
        this.logVerbose(`   Module directory: ${moduleDir}`);

        // First check if the module file itself contains routes
        const moduleContent = fs.readFileSync(modulePath, "utf8");
        if (this.containsRoutes(moduleContent)) {
          this.logVerbose(
            `   ✅ Found routes in module file: ${path.relative(
              process.cwd(),
              modulePath
            )}`
          );
          return modulePath;
        }

        // Recursively search the module directory for files containing routes
        const routingFile = this.findRoutingFileRecursively(moduleDir);
        if (routingFile) {
          this.logVerbose(
            `   ✅ Found routing file: ${path.relative(
              process.cwd(),
              routingFile
            )}`
          );
          return routingFile;
        }

        this.logWarning(`⚠️  Could not find routes for module: ${modulePath}`);
        this.logWarning(`   Searched directory: ${moduleDir}`);
      } else {
        this.logWarning(`⚠️  Module file does not exist: ${modulePath}`);
      }
    }

    return null;
  }

  containsRoutes(content) {
    // Check if the content contains route definitions
    const routeIndicators = [
      /const\s+\w*[rR]outes\s*:\s*(CCRoutes|Routes)\s*=/,
      /path\s*:\s*["']/,
      /loadChildren\s*:/,
      /children\s*:\s*\[/,
    ];

    return routeIndicators.some((pattern) => pattern.test(content));
  }

  findRoutingFileRecursively(dir, depth = 0) {
    if (depth > 10) return null; // Prevent infinite recursion, max 10 levels deep

    try {
      const items = fs.readdirSync(dir);

      // First pass: look for files in current directory
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);

        if (
          stat.isFile() &&
          item.endsWith(".ts") &&
          !item.endsWith(".spec.ts") &&
          !item.endsWith(".test.ts")
        ) {
          const content = fs.readFileSync(fullPath, "utf8");
          if (this.containsRoutes(content)) {
            this.logVerbose(
              `     - Found routes in: ${path.relative(
                process.cwd(),
                fullPath
              )}`
            );
            return fullPath;
          }
        }
      }

      // Second pass: recursively search subdirectories
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);

        if (
          stat.isDirectory() &&
          !item.startsWith(".") &&
          item !== "node_modules"
        ) {
          const result = this.findRoutingFileRecursively(fullPath, depth + 1);
          if (result) {
            return result;
          }
        }
      }
    } catch (error) {
      this.logWarning(`     - Could not read directory: ${dir}`);
    }

    return null;
  }

  buildFullRoute(parentPath, routePath) {
    if (routePath.startsWith("/")) {
      return routePath;
    }

    if (!parentPath || parentPath === "") {
      return this.config.routePrefix
        ? `${this.config.routePrefix}/${routePath}`
        : `/${routePath}`;
    }

    if (parentPath.endsWith("/")) {
      return parentPath + routePath;
    }

    return parentPath + "/" + routePath;
  }

  addRoute(route, pageCode, label, sourceFile) {
    // Avoid duplicates
    const existingRoute = this.routes.find((r) => r.route === route);
    if (!existingRoute) {
      const routeObject = {
        route: route,
        pageCode: pageCode,
        label: label,
      };

      // Conditionally include sourceFile based on configuration
      if (this.config.includeSourceFile) {
        routeObject.sourceFile = path.relative(process.cwd(), sourceFile);
      }

      this.routes.push(routeObject);
      this.logProgress(`  ✅ Added route: ${route} (pageCode: "${pageCode}")`);
    }
  }

  writeRoutesToFiles() {
    const outputDir = path.join(__dirname, "../dist");
    const outputPath = path.join(
      outputDir,
      `${this.config.outputFileName}.json`
    );

    // Ensure the output directory exists
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }

    // Remove duplicates and sort
    const uniqueRoutes = this.routes
      .filter(
        (route, index, self) =>
          index === self.findIndex((r) => r.route === route.route)
      )
      .sort((a, b) => a.route.localeCompare(b.route));

    // Write JSON file
    fs.writeFileSync(outputPath, JSON.stringify(uniqueRoutes, null, 2));

    // Write TypeScript definitions if enabled
    if (this.config.generateTypeScript) {
      const tsOutputPath = path.join(
        outputDir,
        `${this.config.outputFileName}.d.ts`
      );
      const tsContent = this.generateTypeScriptDefinitions(uniqueRoutes);
      fs.writeFileSync(tsOutputPath, tsContent);
      this.logProgress(`📄 TypeScript definitions written to: ${tsOutputPath}`);
    }

    // Also write development copies to the root (if enabled)
    if (this.config.createDevCopies) {
      const devOutputPath = path.join(
        __dirname,
        `../${this.config.outputFileName}.json`
      );
      fs.writeFileSync(devOutputPath, JSON.stringify(uniqueRoutes, null, 2));
      this.logProgress(`📄 Development copy written to: ${devOutputPath}`);

      if (this.config.generateTypeScript) {
        const devTsOutputPath = path.join(
          __dirname,
          `../${this.config.outputFileName}.d.ts`
        );
        const tsContent = this.generateTypeScriptDefinitions(uniqueRoutes);
        fs.writeFileSync(devTsOutputPath, tsContent);
        this.logProgress(
          `📄 Development TypeScript copy written to: ${devTsOutputPath}`
        );
      }
    }

    this.logCompletion(`📄 Routes written to: ${outputPath}`);
    if (this.config.createDevCopies) {
      this.logCompletion(`📄 Development copies written to root directory`);
    }
    this.logCompletion(`📊 Total routes extracted: ${uniqueRoutes.length}`);

    // Write a summary if enabled
    if (this.config.generateSummary) {
      this.writeSummary(uniqueRoutes);
    }
  }

  generateTypeScriptDefinitions(routes) {
    const routeUnion = routes.map((route) => `  | '${route.route}'`).join("\n");
    const pageCodesWithValues = routes.filter((route) => route.pageCode);
    const pageCodeUnion =
      pageCodesWithValues.length > 0
        ? pageCodesWithValues
            .map((route) => `  | '${route.pageCode}'`)
            .join("\n")
        : "  | string";

    const routeTypeName = this.pascalCase(
      this.config.outputFileName.replace(/-routes?$/, "")
    );

    // Build interface fields based on configuration
    let interfaceFields = `  route: string;
  pageCode: string;
  label: string;`;

    if (this.config.includeSourceFile) {
      interfaceFields += `
  sourceFile: string;`;
    }

    return `// Auto-generated file - Do not edit manually
// Generated on: ${new Date().toISOString()}
// Configuration: ${JSON.stringify(this.config, null, 2)}

export interface ${routeTypeName}Route {
${interfaceFields}
}

export type ${routeTypeName}Routes = ${routeTypeName}Route[];

export type ${routeTypeName}RoutePath =
${routeUnion};

export type ${routeTypeName}PageCode =
${pageCodeUnion};

export declare const ${this.config.outputFileName
      .toUpperCase()
      .replace(/-/g, "_")}: ${routeTypeName}Routes;
`;
  }

  pascalCase(str) {
    return str
      .split(/[-_]/)
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join("");
  }

  writeSummary(routes) {
    const summaryPath = path.join(__dirname, "../route-extraction-summary.md");

    const routesWithPageCode = routes.filter((route) => route.pageCode);
    const routesWithoutPageCode = routes.filter((route) => !route.pageCode);

    const summary = `# Route Extraction Summary

Generated on: ${new Date().toISOString()}
Configuration: ${this.config.outputFileName}
Total routes extracted: ${routes.length}
Routes with pageCode: ${routesWithPageCode.length}
Routes without pageCode: ${routesWithoutPageCode.length}

## Configuration Used

\`\`\`json
${JSON.stringify(this.config, null, 2)}
\`\`\`

## Routes by Category

${this.categorizeRoutes(routes)}

## Routes with PageCode

| Route | Page Code | Label | Source File |
|-------|-----------|-------|-------------|
${routesWithPageCode
  .map(
    (route) =>
      `| \`${route.route}\` | \`${route.pageCode}\` | ${route.label} | ${route.sourceFile} |`
  )
  .join("\n")}

## Routes without PageCode

| Route | Label | Source File |
|-------|-------|-------------|
${routesWithoutPageCode
  .map(
    (route) => `| \`${route.route}\` | ${route.label} | ${route.sourceFile} |`
  )
  .join("\n")}

## All Routes

| Route | Page Code | Label | Source File |
|-------|-----------|-------|-------------|
${routes
  .map(
    (route) =>
      `| \`${route.route}\` | \`${route.pageCode}\` | ${route.label} | ${route.sourceFile} |`
  )
  .join("\n")}
`;

    fs.writeFileSync(summaryPath, summary);
    this.logProgress(`📋 Summary written to: ${summaryPath}`);
  }

  categorizeRoutes(routes) {
    const categories = {};

    routes.forEach((route) => {
      const routeWithoutPrefix = route.route.replace(
        new RegExp(`^${this.config.routePrefix}`),
        ""
      );
      const parts = routeWithoutPrefix.split("/").filter((part) => part);
      const category = parts[0] || "root";

      if (!categories[category]) {
        categories[category] = [];
      }
      categories[category].push(route);
    });

    let output = "";
    Object.keys(categories)
      .sort()
      .forEach((category) => {
        output += `### ${
          category.charAt(0).toUpperCase() + category.slice(1)
        } (${categories[category].length} routes)\n\n`;
        categories[category].forEach((route) => {
          const pageCodeDisplay = route.pageCode
            ? ` - ${route.pageCode}`
            : " - (no pageCode)";
          output += `- \`${route.route}\`${pageCodeDisplay}\n`;
        });
        output += "\n";
      });

    return output;
  }

  extractBalancedBrackets(str, startIndex) {
    let bracketCount = 0;
    let content = "";
    let inString = false;
    let stringChar = "";
    let escapeNext = false;

    for (let i = startIndex; i < str.length; i++) {
      const char = str[i];

      if (escapeNext) {
        escapeNext = false;
        content += char;
        continue;
      }

      if (char === "\\") {
        escapeNext = true;
        content += char;
        continue;
      }

      if ((char === '"' || char === "'" || char === "`") && !escapeNext) {
        if (!inString) {
          inString = true;
          stringChar = char;
        } else if (char === stringChar) {
          inString = false;
          stringChar = "";
        }
      }

      if (!inString) {
        if (char === "[") {
          bracketCount++;
        } else if (char === "]") {
          bracketCount--;
        }
      }

      // Skip the opening bracket
      if (i === startIndex && char === "[") {
        continue;
      }

      // If we've closed all brackets, we're done
      if (bracketCount === 0 && char === "]") {
        return content;
      }

      content += char;
    }

    return content; // Return whatever we have if we reach the end
  }

  extractBalancedBraces(str, startIndex) {
    let braceCount = 0;
    let content = "";
    let inString = false;
    let stringChar = "";
    let escapeNext = false;

    for (let i = startIndex; i < str.length; i++) {
      const char = str[i];

      if (escapeNext) {
        escapeNext = false;
        content += char;
        continue;
      }

      if (char === "\\") {
        escapeNext = true;
        content += char;
        continue;
      }

      if ((char === '"' || char === "'" || char === "`") && !escapeNext) {
        if (!inString) {
          inString = true;
          stringChar = char;
        } else if (char === stringChar) {
          inString = false;
          stringChar = "";
        }
      }

      if (!inString) {
        if (char === "{") {
          braceCount++;
        } else if (char === "}") {
          braceCount--;
        }
      }

      // Skip the opening brace
      if (i === startIndex && char === "{") {
        continue;
      }

      // If we've closed all braces, we're done
      if (braceCount === 0 && char === "}") {
        return content;
      }

      content += char;
    }

    return content; // Return whatever we have if we reach the end
  }
}

// CLI interface
function main() {
  const args = process.argv.slice(2);
  const configPath = args
    .find((arg) => arg.startsWith("--config="))
    ?.split("=")[1];

  const extractor = new HierarchicalRouteExtractor(configPath);

  extractor.logProgress("🚀 Hierarchical Route Extractor");
  if (configPath) {
    extractor.logVerbose(`📋 Using config file: ${configPath}`);
  }

  extractor.extractRoutes();
}

// Run if this file is executed directly
if (require.main === module) {
  main();
}

module.exports = { HierarchicalRouteExtractor };
