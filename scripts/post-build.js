const fs = require('fs');
const path = require('path');

class PostBuildProcessor {
  constructor(configPath = null) {
    this.config = this.loadConfiguration(configPath);
    this.loggingConfig = this.loadLoggingConfiguration(configPath);
  }

  loadConfiguration(configPath) {
    const defaultConfig = {
      postBuild: {
        fileMovements: [
          {
            name: "security-admin.json",
            source: "dist/admin/security-admin.json",
            target: "dist/security-admin.json",
            deleteSource: true,
            required: true
          }
        ]
      }
    };

    if (configPath && fs.existsSync(configPath)) {
      try {
        const userConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        return userConfig.postBuild || defaultConfig.postBuild;
      } catch (error) {
        this.logWarning(`⚠️  Could not load config from ${configPath}, using defaults:`, error.message);
        return defaultConfig.postBuild;
      }
    }

    // Try to load from default location
    const defaultConfigPath = path.join(__dirname, 'build-config.json');
    if (fs.existsSync(defaultConfigPath)) {
      try {
        const userConfig = JSON.parse(fs.readFileSync(defaultConfigPath, 'utf8'));
        return userConfig.postBuild || defaultConfig.postBuild;
      } catch (error) {
        this.logWarning(`⚠️  Could not load config from ${defaultConfigPath}, using defaults:`, error.message);
      }
    }

    return defaultConfig.postBuild;
  }

  loadLoggingConfiguration(configPath) {
    const defaultLogging = {
      enableVerbose: true,
      showProgress: true,
      showCompletion: true
    };

    try {
      if (configPath && fs.existsSync(configPath)) {
        const userConfig = JSON.parse(fs.readFileSync(configPath, 'utf8'));
        return { ...defaultLogging, ...(userConfig.logging || {}) };
      }

      // Try to load from default location
      const defaultConfigPath = path.join(__dirname, 'build-config.json');
      if (fs.existsSync(defaultConfigPath)) {
        const userConfig = JSON.parse(fs.readFileSync(defaultConfigPath, 'utf8'));
        return { ...defaultLogging, ...(userConfig.logging || {}) };
      }
    } catch (error) {
      // Silently fallback to defaults if config parsing fails
    }

    return defaultLogging;
  }

  // Logging methods
  logVerbose(...args) {
    if (this.loggingConfig.enableVerbose) {
      console.log(...args);
    }
  }

  logProgress(...args) {
    if (this.loggingConfig.showProgress) {
      console.log(...args);
    }
  }

  logCompletion(...args) {
    if (this.loggingConfig.showCompletion) {
      console.log(...args);
    }
  }

  logWarning(...args) {
    // Always show warnings
    console.warn(...args);
  }

  logError(...args) {
    // Always show errors
    console.error(...args);
  }

  processFileMovements() {
    this.logProgress('🔄 Starting post-build file processing...');
    this.logVerbose(`📋 Configuration:`, this.config);

    let successCount = 0;
    let errorCount = 0;

    for (const movement of this.config.fileMovements) {
      try {
        this.processFileMovement(movement);
        successCount++;
      } catch (error) {
        this.logError(`❌ Error processing ${movement.name}:`, error.message);
        errorCount++;

        if (movement.required) {
          this.logError(`💥 Required file movement failed, stopping build process`);
          process.exit(1);
        }
      }
    }

    this.logCompletion(`✅ Post-build processing completed! ${successCount} successful, ${errorCount} failed.`);
  }

  processFileMovement(movement) {
    const sourceFile = path.resolve(movement.source);
    const targetFile = path.resolve(movement.target);

    this.logVerbose(`🔄 Processing ${movement.name}:`);
    this.logVerbose(`   Source: ${sourceFile}`);
    this.logVerbose(`   Target: ${targetFile}`);

    if (!fs.existsSync(sourceFile)) {
      if (movement.required) {
        throw new Error(`Required source file not found: ${sourceFile}`);
      } else {
        this.logWarning(`⚠️  Source file not found (optional): ${sourceFile}`);
        return;
      }
    }

    // Ensure target directory exists
    const targetDir = path.dirname(targetFile);
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
      this.logVerbose(`📁 Created target directory: ${targetDir}`);
    }

    // Copy the file to the target location
    fs.copyFileSync(sourceFile, targetFile);
    this.logVerbose(`📋 Copied ${movement.name} to target location`);

    // Remove the original file if requested
    if (movement.deleteSource) {
      fs.unlinkSync(sourceFile);
      this.logVerbose(`🗑️  Removed source file: ${sourceFile}`);
    }

    this.logProgress(`✅ Successfully processed ${movement.name}`);
  }
}

// CLI interface
function main() {
  const args = process.argv.slice(2);
  const configPath = args.find(arg => arg.startsWith('--config='))?.split('=')[1];

  const processor = new PostBuildProcessor(configPath);

  processor.logProgress('🚀 Post-Build File Processor');
  if (configPath) {
    processor.logVerbose(`📋 Using config file: ${configPath}`);
  }

  processor.processFileMovements();
}

// Run if this file is executed directly
if (require.main === module) {
  main();
}

module.exports = { PostBuildProcessor };
