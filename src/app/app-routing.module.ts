import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LoginComponent } from './modules/core/components/login/login.component';
import { EmptyRouteComponent } from './empty-route/empty-route.component';
import { environment } from 'src/environments/environment';

const routes: Routes = [
  {
    path: 'accounting/v2',
    children: [
      {
        path: 'upload-statements',
        loadChildren: () =>
          import('./modules/upload-statement/upload-statement.module').then(
            (m) => m.UploadStatementModule
          ),
        data: { label: 'Upload Statements', pageCode: 'accounting_UploadStatements' },
      },
      {
        path: 'deposits',
        loadChildren: () =>
          import('./modules/deposits/deposits.module').then(
            (m) => m.DepositsModule
          ),
        data: { label: 'Deposits', pageCode: 'accounting_deposits' },
      },
      {
        path: 'companies',
        loadChildren: () =>
          import('./modules/companies/companies.module').then(
            (m) => m.CompaniesModule
          ),
        data: { label: 'Companies', pageCode: 'companies' },
      },
      {
        path: 'failed-dd-generation',
        loadChildren: () =>
          import(
            './modules/failed-dd-generation/failed-dd-generation.module'
          ).then((m) => m.FailedDdGenerationModule),
        data: {
          label: 'Failed DD Generation',
          pageCode: 'ACCOUNTING__FailedDDsGeneration',
        },
      },
      {
        path: 'collection-events-config',
        loadChildren: () =>
          import(
            './modules/collection-events-config/collection-events-config.module'
          ).then((m) => m.CollectionEventsConfigModule),
        data: {
          label: 'Collection Events Config',
          pageCode: 'ACCOUNTING__CollectionEventsConfig',
        },
      },
      {
        path: 'threshold-table',
        loadChildren: () =>
          import('./modules/threshold-table/threshold-table.module').then(
            (m) => m.ThresholdTableModule
          ),
        data: { label: 'Purpose', pageCode: 'accounting_threshold_table' },
      },
      {
        path: 'send-manual-dd-to-bank',
        loadChildren: () =>
          import(
            './modules/send-manual-dd-to-bank/send-manual-dd-to-bank.module'
          ).then((m) => m.SendManualDdToBankModule),
        data: {
          label: 'Send Confirmed Manual DD to the bank',
          pageCode: 'send-manual-dd-to-bank',
        },
      },
      {
        path: 'multiple-dd-configuration',
        loadChildren: () =>
          import(
            './modules/multiple-dd-configuration/multiple-dd-configuration.module'
          ).then((m) => m.MultipleDdConfigurationModule),
        data: {
          label: 'Multiple DD Configuration',
          pageCode: 'multiple-dd-configuration',
        },
      },
      {
        path: 'risk-documents-mgmt',
        loadChildren: () =>
          import(
            './modules/risk-management-documents/risk-management-documents.module'
          ).then((m) => m.RiskManagementDocumentsModule),
        data: { label: 'Risk Documents Mgmt', pageCode: 'riskDocumentsMgmt' },
      },
      {
        path: 'insurance-agreements',
        loadChildren: () =>
          import(
            './modules/insurance-agreements/insurance-agreements.module'
          ).then((m) => m.InsuranceAgreementsModule),
        data: {
          label: 'Insurance Agreements',
          pageCode: 'accounting_insurance_agreements',
        },
      },
      {
        path: 'payments-automation/send-dd-to-bank',
        loadChildren: () =>
          import('./modules/send-dd-to-bank/send-dd-to-bank.module').then(
            (m) => m.SendDdToBankModule
          ),
        data: {
          label: 'Send DD to Bank',
          pageCode: 'accounting_send-dd-to-bank',
        },
      },
      {
        path: 'statement-of-account',
        loadChildren: () =>
          import(
            './modules/statement-of-account/statement-of-account.module'
          ).then((m) => m.StatementOfAccountModule),
        data: { pageCode: 'statementOfAccount' },
      },
      {
        path: 'contracts-info',
        loadChildren: () =>
          import(
            './modules/payment-plans-and-discounts/payment-plans-and-discounts.module'
          ).then((m) => m.PaymentPlansAndDiscountsModule),
        data: {
          label: 'Payment Plans and Discounts',
          pageCode: 'ACCOUNTING__PaymentPlansDataEntry',
        },
      },
      {
        path: 'dewa-report',
        loadChildren: () =>
          import('./modules/dewa-report/dewa-report.module').then(
            (m) => m.DewaReportModule
          ),
        data: { label: 'Dewa Report', pageCode: 'DewaReport' },
      },
      {
        path: 'client-refund-setup',
        loadChildren: () =>
          import(
            './modules/client-refund-setup/client-refund-setup.module'
          ).then((m) => m.ClientRefundSetupModule),
        data: {
          label: 'Family Refund Setup',
          pageCode: 'accounting_ClientRefundSetup',
        },
      },
      {
        path: 'purchase-order-history',
        loadChildren: () =>
          import(
            './modules/purchase-order-history/purchase-order-history.module'
          ).then((m) => m.PurchaseOrderHistoryModule),
        data: {
          label: 'Purchase Order History',
          pageCode: 'ACCOUNTING__PurchaseOrderHistory',
        },
      },
      {
        path: 'expense-items',
        loadChildren: () =>
          import('./modules/expense-items/expense-items.module').then(
            (m) => m.ExpenseItemsModule
          ),
        data: {
          label: 'Expense Management',
          pageCode: 'accounting_ExpenseItems',
        },
      },
      {
        path: 'expense-category',
        loadChildren: () =>
          import(
            '../app/modules/expense-category/expense-category.module'
          ).then((m) => m.ExpenseCategoryModule),
        data: {
          label: 'Expense Management',
          pageCode: 'accounting_ExpenseCategory',
        },
      },
      {
        path: 'recurring-cc-payments-issues-mgmt',
        loadChildren: () =>
          import(
            './modules/recurring-cc-payments-issues-mgmt/recurring-cc-payments-issues-mgmt.module'
          ).then((m) => m.RecurringCcPaymentsIssuesMgmtModule),
        data: {
          label: 'Reccurring CC Payments Issues Management Screen',
          pageCode: 'ACCOUNTING__recurringCCPaymentsIssuesMgmt',
        },
      },
      {
        path: 'pnl-vs-system-code',
        loadChildren: () =>
          import('./modules/pnl-vs-system-code/pnl-vs-system-code.module').then(
            (m) => m.PnlVsSystemCodeModule
          ),
        data: {
          label: 'P&L VS System Code',
          pageCode: 'accounting_pnl_vs_system_code',
        },
      },
      {
        path: 'credit-card-holder',
        loadChildren: () =>
          import('./modules/credit-card-holder/credit-card-holder.module').then(
            (m) => m.CreditCardHolderModule
          ),
        data: {
          label: 'Credit Card Holder',
          pageCode: 'accounting_CreditCardHolder',
        },
      },
      {
        path: 'cancel-dd-from-bank',
        loadChildren: () =>
          import(
            './modules/cancel-dd-from-bank/cancel-dd-from-bank.module'
          ).then((m) => m.CancelDdFromBankModule),
        data: {
          label: 'Cancel future DDs from bank',
          pageCode: 'accounting_cancelFutrueDdsFromBank',
        },
      },
      {
        path: 'payments-automation/contract-payments',
        loadChildren: () =>
          import('./modules/contract-payments/contract-payments.module').then(
            (m) => m.ContractPaymentsModule
          ),
        data: {
          label: 'Contract Payments Confirmation',
          pageCode: 'accounting_contract-payments',
        },
      },
      {
        path: 'expenses-refunds-history',
        loadChildren: () =>
          import(
            '../app/modules/expenses-refunds-history/expenses-refunds-history.module'
          ).then((m) => m.ExpensesRefundsHistoryModule),
        data: {
          label: 'Expenses refund history',
          pageCode: 'ExpenseRefundsHistory',
        },
      },
      {
        path: 'telecom-management',
        loadChildren: () =>
          import('./modules/telecom-management/telecom-management.module').then(
            (m) => m.TelecomManagementModule
          ),
        data: { label: 'Telecom Management', pageCode: 'TelecomManagement' },
      },
      {
        path: 'transactions-posting-engine',
        loadChildren: () =>
          import(
            './modules/transaction-posting-setup/transaction-posting-setup.module'
          ).then((m) => m.TransactionPostingSetupModule),
        data: {
          label: 'Transaction Posting Rules',
          pageCode: 'accounting_posting-engine',
        },
      },
      {
        path: 'maid-expenses-summary',
        loadChildren: () =>
          import(
            './modules/maid-expenses-summary/maid-expenses-summary.module'
          ).then((m) => m.MaidExpensesSummaryModule),
        data: {
          label: 'Maid Expenses Summary',
          pageCode: 'ACCOUNTING__MaidExpensesSummary',
        },
      },
      {
        path: 'purchase-requests',
        loadChildren: () =>
          import('./modules/purchase-requests/purchase-requests.module').then(
            (m) => m.PurchaseRequestsModule
          ),
        data: {
          label: 'Purchase Auditor',
          pageCode: 'ACCOUNTING__PurchaseRequests',
        },
      },
      {
        path: 'purchase-manager',
        loadChildren: () =>
          import('./modules/purchasing-manager/purchasing-manager.module').then(
            (m) => m.PurchasingManagerModule
          ),
        data: {
          label: 'Purchase Manager',
          pageCode: 'ACCOUNTING__PurchasingManager',
        },
      },
      {
        path: 'expenses',
        loadChildren: () =>
          import('./modules/expenses/expenses.module').then(
            (m) => m.ExpensesModule
          ),
        data: { label: 'Expenses', pageCode: 'Expenses' },
      },
      {
        path: 'audit-manager',
        loadChildren: () =>
          import('./modules/audit-manager/audit-manager.module').then(
            (m) => m.AuditManagerModule
          ),
        data: { label: 'Audit Manager' },
      },
      {
        path: 'insurance-invoicing',
        loadChildren: () =>
          import(
            './modules/insurance-invoicing/insurance-invoicing.module'
          ).then((m) => m.InsuranceInvoicingModule),
        data: {
          label: 'Insurance Invoicing',
          pageCode: 'accounting_insurance_invoicing',
        },
      },
      {
        path: 'payment-request-flow/add-maid-refund',
        loadChildren: () =>
          import('./modules/add-maid-refund/add-maid-refund.module').then(
            (m) => m.AddMaidRefundModule
          ),
        data: {
          label: 'Add Payment for Maid',
          pageCode: 'accounting_add_maid_refund',
        },
      },
      {
        path: 'accountant-todo',
        loadChildren: () =>
          import('./modules/accountant-todo/accountant-todo.module').then(
            (m) => m.AccountantTodoModule
          ),
        data: {
          label: 'Accountant Todo',
          pageCode: 'accounting_AccountantTodoList',
        },
      },
      {
        path: 'stock-keeper-todo',
        loadChildren: () =>
          import('./modules/stock-keeper-todo/stock-keeper-todo.module').then(
            (m) => m.StockKeeperTodoModule
          ),
        data: {
          label: 'Stock Keeper Todo',
          pageCode: 'accounting_StockKeeperTodo',
        },
      },
      {
        path: 'unknown-wire-transfer',
        loadChildren: () =>
          import(
            './modules/unknown-wire-transfer/unknown-wire-transfer.module'
          ).then((m) => m.UnknownWireTransferModule),
        data: {
          label: 'Unknown Wire Transfers',
          pageCode: 'UnknownWireTransfer',
        },
      },
      {
        path: 'client-refund-summary',
        loadChildren: () =>
          import(
            './modules/client-refund-summary/client-refund-summary.module'
          ).then((m) => m.ClientRefundSummaryModule),
        data: {
          label: 'Family Refund Summary',
          pageCode: 'accounting_client-refund-summary',
        },
      },
      {
        path: 'tenancy-contracts',
        loadChildren: () =>
          import('./modules/tenancy-contracts/tenancy-contracts.module').then(
            (m) => m.TenancyContractsModule
          ),
        data: {
          label: "Company's Contract & Agreements",
          pageCode: "Company'sContractAndAgreements",
        },
      },
      {
        path: 'request-expense',
        loadChildren: () =>
          import('./modules/request-expense/request-expense.module').then(
            (m) => m.RequestExpenseModule
          ),
        data: {
          label: 'Request Expense',
          pageCode: 'ACCOUNTING__RequestExpense',
        },
      },
      {
        path: 'cashier',
        loadChildren: () =>
          import('./modules/cashier/cashier.module').then(
            (m) => m.CashierModule
          ),
        data: { label: 'Cashier', pageCode: 'ACCOUNTING__CashierScreen' },
      },
      {
        path: 'suppliers-list',
        loadChildren: () =>
          import('./modules/suppliers/suppliers.module').then(
            (m) => m.SuppliersModule
          ),
        data: { label: 'Suppliers List', pageCode: 'accounting_SupplierList' },
      },
      {
        path: 'coo-questions/answer/:id',
        loadChildren: () =>
          import(
            './modules/answer-coo-question/answer-coo-question.module'
          ).then((m) => m.AnswerCooQuestionModule),
        data: {
          label: 'Question from COO',
          pageCode: 'ACCOUNTING__PurchaseOrderHistory',
        },
      },
      {
        path: 'payments-automation/import-new-file/ddFile',
        loadChildren: () =>
          import(
            './modules/bank-dd-activation-file/bank-dd-activation-file.module'
          ).then((m) => m.BankDdActivationFileModule),
        data: {
          label: 'Bank Direct Debit Activation File',
          pageCode: 'accounting_automation-importing-file-ddfile',
        },
      },
      {
        path: 'pdc-list',
        loadChildren: () =>
          import('./modules/pdc/pdc.module').then((m) => m.PdcModule),
        data: { label: 'PDC Management', pageCode: 'pdc-management' },
      },
      {
        path: 'payments-automation/dd-data-entry',
        loadChildren: () =>
          import('./modules/dd-data-entry/dd-data-entry.module').then(
            (m) => m.DdDataEntryModule
          ),
        data: { label: 'DD Data Entry', pageCode: 'accounting_dd-data-entry' },
      },
      {
        path: 'pay-invoices',
        loadChildren: () =>
          import('./modules/pay-invoices/pay-invoices.module').then(
            (m) => m.PayInvoicesModule
          ),
        data: { label: 'Pay Invoices', pageCode: 'accounting_PayInvoice' },
      },
      {
        path: 'manage-transactions',
        loadChildren: () =>
          import(
            './modules/manage-transactions/manage-transactions.module'
          ).then((m) => m.ManageTransactionsModule),
        data: { label: 'Manage Transactions', pageCode: 'ManageTransactions' },
      },
      {
        path: 'manage-buckets',
        loadChildren: () =>
          import('./modules/manage-buckets/manage-buckets.module').then(
            (m) => m.ManageBucketsModule
          ),
        data: { label: 'Manage Buckets', pageCode: 'ManageBuckets' },
      },
      {
        path: 'reconciliator',
        loadChildren: () =>
          import('./modules/reconciliator/reconciliator.module').then(
            (m) => m.ReconciliatorModule
          ),
        data: { label: 'Reconciliator', pageCode: 'ACCOUNTING__Reconciliator' },
      },
      {
        path: 'bank-dd-cancellation-file',
        loadChildren: () =>
          import(
            './modules/bank-dd-cancellation-file/bank-dd-cancellation-file.module'
          ).then((m) => m.BankDdCancellationFileModule),
        data: {
          label: 'Bank Direct Debit Cancellation File',
          pageCode: 'accounting_bank-direct-debit-cancellation-file',
        },
      },
      {
        path: 'visa-expenses',
        loadChildren: () =>
          import('./modules/visa-expenses/visa-expenses.module').then(
            (m) => m.VisaExpensesModule
          ),
        data: { label: 'Visa Expenses', pageCode: 'VisaExpenses' },
      },
      {
        path: 'payments-automation/direct-debit-applications',
        loadChildren: () =>
          import(
            '../app/modules/direct-debit-applications/direct-debit-applications.module'
          ).then((m) => m.DirectDebitApplicationsModule),
        data: {
          label: 'Direct Debit Applications',
          pageCode: 'accounting_direct-debit-applications',
        },
      },
      {
        path: 'dd-messaging-setup',
        loadChildren: () =>
          import('./modules/dd-messaging-setup/dd-messaging-setup.module').then(
            (m) => m.DdMessagingSetupModule
          ),
        data: {
          label: 'DD Messaging Setup',
          pageCode: 'accounting_ddMessagingSetup',
        },
      },
      {
        path: 'expenses-requests',
        loadChildren: () =>
          import('./modules/expense-requests/expense-requests.module').then(
            (m) => m.ExpenseRequestsModule
          ),
        data: {
          label: 'Expense Requests',
          pageCode: 'ACCOUNTING__ExpensesRequests',
        },
      },
      {
        path: 'contract-payments-files/:id',
        loadChildren: () =>
          import(
            '../app/modules/contract-payments-files/contract-payments-files.module'
          ).then((m) => m.ContractPaymentsFilesModule),
        data: {
          label: 'Contract Payments Files',
          pageCode: 'accountingPaymentsFiles',
        },
      },
    ],
    data: { label: 'Accounting', disabled: true },
  },
  { path: 'login', component: LoginComponent },
  { path: '**', component: EmptyRouteComponent },
];

@NgModule({
  imports: [
    RouterModule.forRoot(routes, {
      onSameUrlNavigation: 'reload',
      useHash: !environment.production && !environment.newErp,
    }),
  ],
  exports: [RouterModule],
})
export class AppRoutingModule {}
