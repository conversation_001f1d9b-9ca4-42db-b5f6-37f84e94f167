import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LoginComponent } from './modules/core/components/login/login.component';

const routes: Routes = [
  { path: 'login', component: LoginComponent },
  {
    path: 'upload-statements',
    loadChildren: () =>
      import('./modules/upload-statement/upload-statement.module').then(
        (m) => m.UploadStatementModule
      ),
    data: { label: 'Upload Statements' },
  },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule {}
