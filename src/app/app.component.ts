import { Component, Inject } from '@angular/core';
import {NavigationItem} from "@maids/cc-lib/layout";
import {CCThemeService, CC_AVAILABLE_THEMES} from "@maids/cc-lib/theme";
@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent {
  title = 'accounting';

  links:NavigationItem= {
    type: 'main',
    children: [
      {
        type: "menu",
        text:`${this.title}`,
        icon:'book',
        expanded:false,
        hidden:false,
        children:[
          {type: "link", text:"First Link", navLink:'/',hidden:false,active:true},
        ]
      },
    ],
  };
  constructor(
    @Inject(CC_AVAILABLE_THEMES) public readonly themes: CC_AVAILABLE_THEMES,
    private _themeService: CCThemeService,
  ) {
  }
}
