import { Component, Inject } from '@angular/core';
import { NavigationItem } from '@maids/cc-lib/layout';
import { CC_AVAILABLE_THEMES, CCThemeService } from '@maids/cc-lib/theme';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'microfrontend-accounting',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent {
  title = 'accounting';
  links: NavigationItem = {
    type: 'main',
    children: [],
  };
  localBuild = !environment.production && environment.newErp;
  env = {...environment, newErp: true}
  constructor(
    @Inject(CC_AVAILABLE_THEMES) public readonly themes: CC_AVAILABLE_THEMES,
    private _themeService: CCThemeService
  ) {}
}
