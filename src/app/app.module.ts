import { APP_INITIALIZER, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import {
  CCNotificationsModule,
  CCTranslateModule,
  CC_TRANSLATE_MODULE_LOADER,
} from '@maids/cc-lib/services';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import {
  HttpClientModule,
  HttpClient,
  HTTP_INTERCEPTORS,
} from '@angular/common/http';
import {
  CoreRouterSerializer,
  CCCoreModule,
  fromRouterState,
  CCPaginationModule,
} from '@maids/cc-lib/common';
import {
  CCAwaitThemeModule,
  CC_THEME_ASSETS_BASE_URL,
  provideThemeLoaders,
} from '@maids/cc-lib/theme';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { StoreRouterConnectingModule } from '@ngrx/router-store';
import { EffectsModule } from '@ngrx/effects';
import {CCBreadcrumbsModule, CCLayoutModule} from '@maids/cc-lib/layout';
import { StoreModule } from '@ngrx/store';
import { StoreDevtoolsModule } from '@ngrx/store-devtools';
import { environment } from '../environments/environment';
import {
  CCAuthInterceptor,
  CCAuthService,
  CCBackendEndpoint,
  CCErpAuthModule,
  CCErpIntegrationModule,
  CCLoadingIconModule,
  CCPicklistModule,
  LegacyNotificationService,
  MODULE_NAME,
} from '@maids/cc-erp-services';
import { AccountingCoreModule } from './modules/core/core.module';
import { NetworkErrorInterceptor } from './modules/core/interceptors/network-error.interceptor';
import { CCFileUploaderModule } from '@maids/cc-lib/file-uploader';
import { DropzoneConfigInterface, DROPZONE_CONFIG } from 'ngx-dropzone-wrapper';
import { API } from 'src/environments/api';

export function dropzoneConfigFactory(): DropzoneConfigInterface {
  return {
    url: API.upload, // mandatory, where to upload files
    filesizeBase: 1000, // in bytes
    maxFiles: 1, // default maximum allowed file upload for a single field
    autoProcessQueue: true,
  };
}
@NgModule({
  declarations: [AppComponent],
  imports: [
    BrowserModule,
    AppRoutingModule,
    HttpClientModule,
    AccountingCoreModule,
    CCTranslateModule.forRoot({
      loader: [
        {
          provide: CC_TRANSLATE_MODULE_LOADER,
          deps: [HttpClient],
          useFactory: function (http: HttpClient) {
            return new TranslateHttpLoader(http, 'assets/i18n/', '.json');
          },
        },
      ],
      defaultLanguage: 'en',
      useDefaultLang: true,
    }),
    CCCoreModule.forRoot({
      backendEndpoint: {useValue: environment.apiBase},
    }),
    BrowserAnimationsModule,
    StoreRouterConnectingModule.forRoot({
      stateKey: fromRouterState.RouterFeatureKey,
      serializer: CoreRouterSerializer,
    }),
    EffectsModule.forRoot([]),
    CCErpAuthModule.forRoot(),
    CCLayoutModule,
    CCAwaitThemeModule.forRoot({
      withAuth: CCAuthService,
      themeLoaders: provideThemeLoaders({
        'cc-default-light': () =>
          import('@maids/cc-lib/prebuilt-themes/default-light.scss'),
        'cc-default-dark': () =>
          import('@maids/cc-lib/prebuilt-themes/default-dark.scss'),
        'cc-amazon-light': () =>
          import('@maids/cc-lib/prebuilt-themes/amazon-light.scss'),
        'cc-amazon-dark': () =>
          import('@maids/cc-lib/prebuilt-themes/amazon-dark.scss'),
      }),
    }),
    CCPaginationModule.forRoot(),
    StoreModule.forRoot(
      {[fromRouterState.RouterFeatureKey]: fromRouterState.reducer},
      {}
    ),
    StoreDevtoolsModule.instrument({
      maxAge: 25,
      logOnly: environment.production,
    }),
    CCErpIntegrationModule,
    CCNotificationsModule.forRoot({
      legacyNotificationService: LegacyNotificationService,
    }),
    CCLoadingIconModule.forRoot(),
    CCFileUploaderModule.forRoot(),
    CCPicklistModule.forRoot({pageSize: 30}),
    CCBreadcrumbsModule,
    CCTranslateModule.forRoot({ loader: [{
        provide: CC_TRANSLATE_MODULE_LOADER,
        deps: [HttpClient],
        useFactory:function(http: HttpClient){
          return new TranslateHttpLoader(http, "assets/i18n/", ".json");
        }
      }],
      defaultLanguage: "en",
      useDefaultLang: true,}),
    CCCoreModule.forRoot({
        backendEndpoint: {useValue:environment.apiBase},
      }),
    CCAwaitThemeModule.forRoot({
        withAuth: CCAuthService,
        themeLoaders: provideThemeLoaders({
          "cc-default-light": () => import("@maids/cc-lib/prebuilt-themes/default-light.scss"),
          "cc-default-dark": () => import("@maids/cc-lib/prebuilt-themes/default-dark.scss"),
          "cc-amazon-light": () => import("@maids/cc-lib/prebuilt-themes/amazon-light.scss"),
          "cc-amazon-dark": () => import("@maids/cc-lib/prebuilt-themes/amazon-dark.scss"),
        })
      }),
    StoreRouterConnectingModule.forRoot({
        stateKey: fromRouterState.RouterFeatureKey,
        serializer: CoreRouterSerializer
      }),
    EffectsModule.forRoot([]),
    StoreModule.forRoot({[fromRouterState.RouterFeatureKey]: fromRouterState.reducer}, {}),
    StoreDevtoolsModule.instrument({ maxAge: 25, logOnly: environment.production }),
  ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      multi: true,
      useClass: NetworkErrorInterceptor,
    },
    { provide: HTTP_INTERCEPTORS, multi: true, useClass: CCAuthInterceptor },
    { provide: CC_THEME_ASSETS_BASE_URL, useValue: '.' },
    { provide: CCBackendEndpoint, useValue: environment.apiBase },
    { provide: MODULE_NAME, useValue: 'accounting' },
    { provide: DROPZONE_CONFIG, deps: [], useFactory: dropzoneConfigFactory },
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}
