import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import {
  CCTranslateModule,
  CC_TRANSLATE_MODULE_LOADER,
} from '@maids/cc-lib/services';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import {
  HttpClientModule,
  HttpClient,
  HTTP_INTERCEPTORS,
} from '@angular/common/http';
import {
  CCAuthInterceptor,
  CCAuthService,
  CCBackendEndpoint,
  CCErpAuthModule,
  CCLoadingIconModule,
  LegacyNotificationService,
  MODULE_NAME,
  CCErpIntegrationModule,
  DuplicateRequestInterceptor,
  CCPicklistModule,
  ErpRedirectionModule
} from '@maids/cc-erp-services';
import {
  CoreRouterSerializer,
  CCCoreModule,
  fromRouterState,
} from '@maids/cc-lib/common';
import {
  CCAwaitThemeModule,
  CC_THEME_ASSETS_BASE_URL,
  provideThemeLoaders,
} from '@maids/cc-lib/theme';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { StoreRouterConnectingModule } from '@ngrx/router-store';
import { EffectsModule } from '@ngrx/effects';
import { StoreModule } from '@ngrx/store';
import { StoreDevtoolsModule } from '@ngrx/store-devtools';
import { environment } from '../environments/environment';
import { assetUrl } from 'src/single-spa/asset-url';
import { CCNotificationsModule } from '@maids/cc-lib/services';
import { CCPaginationModule } from '@maids/cc-lib/common';
import { CCButtonModule } from '@maids/cc-lib/button';
import { CCIconModule } from '@maids/cc-lib/icon';
import { CommonModule, DatePipe } from '@angular/common';
import { CCBreadcrumbsModule, CCLayoutModule } from '@maids/cc-lib/layout';
import { AccountingCoreModule } from './modules/core/core.module';
import { DropzoneConfigInterface, DROPZONE_CONFIG } from 'ngx-dropzone-wrapper';
import { API } from 'src/environments/api';
import { CCFileUploaderModule } from '@maids/cc-lib/file-uploader';
import { NetworkErrorInterceptor } from '@maids/cc-lib/interceptors';
import { CcAppContainerModule } from '@maids/cc-lib/cc-app-container';
import { CCErpLayoutModule } from '@maids/cc-shared-components/erp-layout';
export function dropzoneConfigFactory(): DropzoneConfigInterface {
  return {
    url: API.upload, // mandatory, where to upload files
    filesizeBase: 1000, // in bytes
    maxFiles: 1, // default maximum allowed file upload for a single field
    autoProcessQueue: true,
  };
}

@NgModule({
  declarations: [AppComponent],
  imports: [
    CCLoadingIconModule.forRoot(),
    CCIconModule,
    ErpRedirectionModule.forRoot({
      replaceAuthGuard: environment.newErp,
    }),
    CCErpAuthModule.forRoot(),
    CCButtonModule,
    AccountingCoreModule,
    CommonModule,
    CCErpLayoutModule,
    CcAppContainerModule,
    BrowserModule,
    AppRoutingModule,
    HttpClientModule,
    CCBreadcrumbsModule,
    AccountingCoreModule,
    CCPaginationModule.forRoot(),
    CCNotificationsModule.forRoot({
      legacyNotificationService: LegacyNotificationService,
    }),
    CCTranslateModule.forRoot({
      loader: [
        {
          provide: CC_TRANSLATE_MODULE_LOADER,
          deps: [HttpClient],
          useFactory: function (http: HttpClient) {
            return new TranslateHttpLoader(http, assetUrl('i18n/'), '.json');
          },
        },
      ],
      defaultLanguage: 'en',
      useDefaultLang: true,
    }),
    CCErpIntegrationModule.forRoot({
      environment,
    }),
    CCCoreModule.forRoot({
      backendEndpoint: { useValue: environment.apiBase },
    }),
    CCLayoutModule,
    CCAwaitThemeModule.forRoot({
      withAuth: CCAuthService,
      themeLoaders: provideThemeLoaders({
        'cc-default-light': () =>
          import('@maids/cc-lib/prebuilt-themes/default-light.scss'),
        'cc-default-dark': () =>
          import('@maids/cc-lib/prebuilt-themes/default-dark.scss'),
        'cc-amazon-light': () =>
          import('@maids/cc-lib/prebuilt-themes/amazon-light.scss'),
        'cc-amazon-dark': () =>
          import('@maids/cc-lib/prebuilt-themes/amazon-dark.scss'),
        'cc-sunrise-light': () =>
          import('@maids/cc-lib/prebuilt-themes/cc-sunrise-light.scss'),
        'cc-sunrise-dark': () =>
          import('@maids/cc-lib/prebuilt-themes/cc-sunrise-dark.scss'),
      }),
    }),
    StoreRouterConnectingModule.forRoot({
      stateKey: fromRouterState.RouterFeatureKey,
      serializer: CoreRouterSerializer,
    }),
    CCPaginationModule.forRoot(),
    BrowserAnimationsModule,
    EffectsModule.forRoot([]),
    StoreModule.forRoot(
      { [fromRouterState.RouterFeatureKey]: fromRouterState.reducer },
      {}
    ),
    StoreDevtoolsModule.instrument({
      maxAge: 25,
      logOnly: environment.production,
    }),
    CCErpIntegrationModule,
    CCNotificationsModule.forRoot({
      legacyNotificationService: LegacyNotificationService,
    }),
    CCFileUploaderModule.forRoot(),
    CCPicklistModule.forRoot({ pageSize: 30 }),
    CCBreadcrumbsModule,
    CCCoreModule.forRoot({
      backendEndpoint: { useValue: environment.apiBase },
    }),
    CCLayoutModule,
    CCAwaitThemeModule.forRoot({
      withAuth: CCAuthService,
      themeLoaders: provideThemeLoaders({
        'cc-default-light': () =>
          import('@maids/cc-lib/prebuilt-themes/default-light.scss'),
        'cc-default-dark': () =>
          import('@maids/cc-lib/prebuilt-themes/default-dark.scss'),
        'cc-amazon-light': () =>
          import('@maids/cc-lib/prebuilt-themes/amazon-light.scss'),
        'cc-amazon-dark': () =>
          import('@maids/cc-lib/prebuilt-themes/amazon-dark.scss'),
        'cc-sunrise-light': () =>
          import('@maids/cc-lib/prebuilt-themes/cc-sunrise-light.scss'),
        'cc-sunrise-dark': () =>
          import('@maids/cc-lib/prebuilt-themes/cc-sunrise-dark.scss'),
      }),
    }),
  ],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      multi: true,
      useClass: NetworkErrorInterceptor,
    },
    {
      provide: HTTP_INTERCEPTORS,
      multi: true,
      useClass: DuplicateRequestInterceptor,
    },
    { provide: HTTP_INTERCEPTORS, multi: true, useClass: CCAuthInterceptor },
    {
      provide: CC_THEME_ASSETS_BASE_URL,
      useValue: assetUrl('').replace('assets/', ''),
    },
    { provide: CCBackendEndpoint, useValue: environment.apiBase },
    { provide: DROPZONE_CONFIG, deps: [], useFactory: dropzoneConfigFactory },
  ],
  bootstrap: [AppComponent],
})
export class AppModule {
}
