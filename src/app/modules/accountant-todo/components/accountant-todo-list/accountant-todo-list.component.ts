import { Component, OnInit } from '@angular/core';
import { AccountantTodoService } from '../../services/accountant-todo.service';
import { PageEvent } from '@angular/material/paginator';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { CCDialog } from '@maids/cc-lib/dialog';
import { TransferDetailsComponent } from '../transfer-details/transfer-details.component';
import { Router } from '@angular/router';
@Component({
  selector: 'app-accountant-todo-list',
  templateUrl: './accountant-todo-list.component.html',
  styleUrls: ['./accountant-todo-list.component.scss'],
})
export class AccountantTodoListComponent implements OnInit {
  records: any;
  currentPage = 0;
  constructor(
    private accountantTodoService: AccountantTodoService,
    public readonly notifications: CCNotificationService,
    private ccDialog: CCDialog,
    private router: Router,
    private mediaService: MediaService
  ) {}
  selectedTodos: any = {};
  isSelectAll = false;
  ngOnInit(): void {
    this.findOpenedTodos(0, 20);
  }
  findOpenedTodos(page: number, size: number = 20) {
    this.accountantTodoService.findOpenedTodos(page, size).subscribe((res) => {
      this.records = res;
      if (this.isSelectAll) {
        this.selectPage();
      } else {
        this.selectedTodos = {};
      }
    });
  }
  getNextPage(event: PageEvent) {
    this.currentPage = event.pageIndex;
    this.findOpenedTodos(event.pageIndex, event.pageSize);
  }
  gridCols: CCGridColumn[] = [
    { field: 'id', header: 'Select' },
    { field: 'taskName', header: 'Action' },
    {
      field: 'amount',
      header: 'Amount',
      formatter(rowData, colDef) {
        return rowData.amount
          ? rowData.taskName == 'BANK_TRANSFER' ||
            rowData.taskName == 'INTERNATIONAL_TRANSFER'
            ? ''
            : (rowData.amount !== '****'
                ? Number(rowData.amount).toLocaleString()
                : '****') +
              ' ' +
              (rowData.currency ? rowData.currency : ' AED')
          : '';
      },
    },
    { field: 'dueSince', header: 'Due Since' },
  ];
  cancelTodo(id: any, label: string) {
    this.ccDialog.confirm(
      '',
      `Are you sure you want to delete this record? This action is permanent and cannot be reversed.`,
      () => {
        this.accountantTodoService.cancelOpenedTodo(id).subscribe((res) => {
          this.notifications.notifySuccess(
            `${label} Todo cancelled successfully`
          );
          this.findOpenedTodos(0, 20);
        });
      }
    );
  }
  goto(row: any) {
    switch (row.taskName) {
      case 'BANK_TRANSFER':
        this.router.navigateByUrl('/payroll/bank-transfer-todo/' + row.id);
        break;
      case 'WPS':
        this.router.navigateByUrl('/payroll/wps-transfer/' + row.id);
        break;
      case 'INTERNATIONAL_TRANSFER':
        this.router.navigateByUrl(
          '/payroll/local-international-transfer/' + row.id
        );
        break;
      case 'LOCAL_TRANSFER':
        this.router.navigateByUrl(
          '/payroll/local-international-transfer/' + row.id
        );
        break;
      case 'SENDING_TRANSACTIONS':
        this.router.navigateByUrl(
          '/payroll/sending-transaction-number/' + row.id
        );
        break;
      case 'CASH':
        this.router.navigateByUrl('/payroll/pay-cash/' + row.id);
        break;
      case 'PENSION_AUTHORITY':
        this.router.navigateByUrl('/payroll/pension-authority/' + row.id);
        break;
      case 'SENDING_TRANSACTIONS':
        this.router.navigateByUrl(
          '/payroll/sending-transaction-number/' + row.id
        );
        break;

      case 'EXPENSE_BANK_TRANSFER':
        this.openTransferDetails(row);
        break;
      case 'EXPENSE_MONEY_TRANSFER':
        this.openTransferDetails(row);
        break;
      default:
        break;
    }
  }
  openTransferDetails(data: any) {
    this.ccDialog
      .originalOpen(TransferDetailsComponent, {
        data,
        width: '500px',
      })
      .afterClosed()
      .subscribe((res) => {
        if (res) {
          this.findOpenedTodos(this.currentPage, 20);
        }
      });
  }
  checkIfAllSelected() {
    if (this.checkIfSelectedPage() && this.isSelectAll) {
      this.isSelectAll = true;
    } else {
      this.isSelectAll = false;
    }
  }
  checkIfSelectedPage() {
    let selected = true;
    this.records?.content.forEach((element: any) => {
      if (!this.selectedTodos[element.id]) {
        if (this.isRowSelectable(element)) {
          selected = false;
        }
      }
    });
    return selected;
  }
  unSelectPage() {
    this.isSelectAll = false;
    this.records?.content.forEach((element: any) => {
      if (this.isRowSelectable(element)) {
        this.selectedTodos[element.id] = false;
      }
    });
  }
  selectPage(justPage?: boolean) {
    if (justPage) {
      this.isSelectAll = false;
    }
    this.records?.content.forEach((element: any) => {
      if (this.isRowSelectable(element)) {
        this.selectedTodos[element.id] = true;
      }
    });
  }
  selectAll() {
    if (this.isSelectAll) {
      this.unSelectAll();
      return;
    }
    this.isSelectAll = true;
    this.selectPage();
  }
  unSelectAll() {
    this.isSelectAll = false;
    this.selectedTodos = {};
  }
  getSelectableRowsCount() {
    return this.records?.content.filter((record: any) =>
      this.isRowSelectable(record)
    ).length;
  }
  closeSelectedTodos() {
    let selectedIds = this.getSelectedTodos();
    if (this.isSelectAll) {
      this.closeAllTodos();
      return;
    }
    this.ccDialog.confirm(
      '',
      'You are about to close ' +
        selectedIds.length +
        ' selected bank transfer to-dos, and this action is irreversible. Proceed?',
      () => {
        this.accountantTodoService
          .bulkAccountantActionByIds(selectedIds)
          .subscribe((res) => {
            this.notifications.notifySuccess(
              `Processing in progress!`
            );
            this.findOpenedTodos(this.currentPage, 20);
          });
      }
    );
  }
  closeAllTodos() {
    this.ccDialog.confirm(
      '',
      'You are about to close all bank transfer to-dos, and this action is irreversible. Proceed?',
      () => {
        this.accountantTodoService
          .bulkAccountantActionAllTodos()
          .subscribe((res) => {
            this.notifications.notifySuccess(
              'Processing in progress!'
            );
            this.findOpenedTodos(0, 20);
          });
      }
    );
  }
  getSelectedTodos() {
    let selectedIds: any[] = [];
    Object.keys(this.selectedTodos).forEach((key) => {
      if (this.selectedTodos[key]) {
        selectedIds.push(key);
      }
    });
    return selectedIds;
  }
  getSelectedRowsCount() {
    if (this.isSelectAll) {
      return this.totalTodos;
    }
    let count = 0;
    Object.keys(this.selectedTodos).forEach((key) => {
      if (this.selectedTodos[key]) {
        count++;
      }
    });
    return count;
  }
  get totalTodos() {
    return this.records?.secondaryTotal ?? 0;
  }
  get totalCount() {
    return this.records?.numberOfElements ?? 0;
  }
  exportCSV() {
    if (this.records.totalElements < 2000) {
      this.mediaService.downloadFile(
        'accounting/accountantTodo/findOpenedTodos/csv'
      );
    } else {
      this.accountantTodoService
        .exportCSVAccountantTodoMail()
        .subscribe((res) => {
          this.notifications.notifySuccess(res);
        });
    }
  }
  isRowSelectable(record: any): boolean {
    return (
      record &&
      !!record.beneficiaryType &&
      ['BUCKET_REPLENISHMENT', 'REFUNDS', 'EXPENSES_SUPPLIER'].includes(
        record.beneficiaryType.beneficiaryType
      ) &&
      record.taskName === 'EXPENSE_BANK_TRANSFER'
    );
  }
}
