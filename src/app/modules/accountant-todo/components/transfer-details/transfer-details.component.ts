import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { CC_DIALOG_DATA, CCDialogRef } from '@maids/cc-lib/dialog';
import { AccountantTodoService } from '../../services/accountant-todo.service';
import { CCNotificationService } from '@maids/cc-lib/services';
import { CCFileUploaderConfig } from '@maids/cc-lib/file-uploader';
@Component({
  selector: 'app-transfer-details',
  templateUrl: './transfer-details.component.html',
  styleUrls: ['./transfer-details.component.scss'],
})
export class TransferDetailsComponent implements OnInit {
  form = this.fb.group({
    transferSlip: [''],
    total: [''],
    charges: [''],
  });
  transferDetails = this.fb.group({
    label: [''],
    amount: [''],
    currency: [''],
    accountName: [''],
    accountNumber: [''],
    iban: [''],
    taskName: [''],
    bankName: [''],
    bankCity: [''],
    bankCountry: [''],
    swift: [''],
    address: [''],
    sendRequestForApprovalAction: [''],
    description: [''],
    contractProspectType: [''],
  });
  constructor(
    @Inject(CC_DIALOG_DATA) public data: any,
    private fb: FormBuilder,
    private accountantTodoService: AccountantTodoService,
    private notifications: CCNotificationService,
    private ccDialogRef: CCDialogRef<TransferDetailsComponent>
  ) {}
  save() {
    let payload: any = {
      id: this.data.id,
      amount: this.transferDetails.value.amount,
      attachments: this.form.value.transferSlip
        ? [{ id: this.form.value.transferSlip[0].id }]
        : null,
    };
    if (this.data.taskName == 'EXPENSE_MONEY_TRANSFER') {
      payload.total = this.form.value.total;
      payload.charges = this.form.value.charges;
    }
    this.accountantTodoService.accountantAction(payload).subscribe({
      next: (res) => {
        this.notifications.notifySuccess('Done Successfully');
        this.ccDialogRef.close(true);
      },
      error: (err) => {
        this.notifications.notifyError(err.error.message);
      },
    });
  }

  ngOnInit(): void {
    this.transferDetails.patchValue({
      label: this.data.label,
      currency: this.data.currency,
      amount: this.data.amount,
      accountName: this.data.accountName,
      accountNumber: this.data.accountNumber,
      iban: this.data.iban,
      taskName: this.data.taskName,
      swift: this.data.swift,
      bankName: this.data.bankName,
      bankCity: this.data.bankCity,
      bankCountry: this.data.bankCountry,
      address: this.data.address,
      sendRequestForApprovalAction: this.data.sendRequestForApprovalAction,
      description: this.data.description,
      contractProspectType: this.data.contractProspectType.code,
    });
    this.transferDetails.disable();
  }
  copy() {
    if (this.transferDetails.value.description) {
      navigator.clipboard
        .writeText(this.transferDetails.value.description)
        .then(() => {
          this.notifications.notifySuccess(
            'Transfer details copied to clipboard'
          );
        });
    } else {
      this.notifications.notifyError('There is no text to copy!');
    }
  }
  config: CCFileUploaderConfig = {
    maxFilesize: 10,
    maxFiles: 1,
  };
}
