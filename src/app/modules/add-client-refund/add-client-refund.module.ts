import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { AddClientRefundRoutingModule } from './add-client-refund-routing.module';
import { AddClientRefundComponent } from './components/add-client-refund/add-client-refund.component';
import { CCButtonModule } from '@maids/cc-lib/button';
import { CCInputModule } from '@maids/cc-lib/input';
import { CCSelectInputModule } from '@maids/cc-lib/select-input';
import { CCRadioButtonModule } from '@maids/cc-lib/radio-button';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CCCheckboxModule } from '@maids/cc-lib/checkbox';
import { CCValidateByModule } from '@maids/cc-lib/validation';
import { CCTextareaModule } from '@maids/cc-lib/textarea';
import { CCAmountInputModule } from '@maids/cc-lib/masked-input';
import { CCDatagridModule } from '@maids/cc-lib/datagrid';
import { CCFileUploaderModule } from '@maids/cc-lib/file-uploader';

@NgModule({
  declarations: [AddClientRefundComponent],
  imports: [
    CommonModule,
    AddClientRefundRoutingModule,
    CCButtonModule,
    CCInputModule,
    CCTextareaModule,
    CCSelectInputModule,
    CCValidateByModule,
    CCFileUploaderModule.forChild({}),
    CCAmountInputModule,
    CCDatagridModule,
    CCRadioButtonModule,
    CCCheckboxModule,
    CCAmountInputModule,
    ReactiveFormsModule,
    FormsModule,
  ],
})
export class AddClientRefundModule {}
