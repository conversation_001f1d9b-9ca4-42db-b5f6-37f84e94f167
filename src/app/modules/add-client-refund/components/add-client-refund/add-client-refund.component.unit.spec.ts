import { AddClientRefundComponent } from './add-client-refund.component';
import { FormBuilder } from '@angular/forms';
import { of, throwError } from 'rxjs';

describe('AddClientRefundComponent - Unit Tests (Business Logic)', () => {
  let component: AddClientRefundComponent;
  let mockService: any;
  let mockNotifications: any;
  let mockRouter: any;
  let mockActivatedRoute: any;
  let formBuilder: FormBuilder;

  beforeEach(() => {
    // Create comprehensive mocks without TestBed
    mockService = {
      getAllPurposesWithSetup: jasmine.createSpy().and.returnValue(of([])),
      getPaymentMethodPicklist: jasmine.createSpy().and.returnValue(of([])),
      ACRgetParameter: jasmine.createSpy().and.returnValue(of([{ value: 'test' }])),
      createClientRefund: jasmine.createSpy().and.returnValue(of({})),
      gethowMuchRefundOptions: jasmine.createSpy().and.returnValue(of([])),
      getClientInfo: jasmine.createSpy().and.returnValue(of({})),
      getClientContractsWithStatus: jasmine.createSpy().and.returnValue(of([])),
      ACRgetClientContracts: jasmine.createSpy().and.returnValue(of([])),
      getLastPaymentPaidByPurpose: jasmine.createSpy().and.returnValue(of({})),
      getPendingPayrollMonths: jasmine.createSpy().and.returnValue(of([])),
      getComplaintsData: jasmine.createSpy().and.returnValue(of([])),
      getUnusedDaysForCompensatingContract: jasmine.createSpy().and.returnValue(of({})),
      getAmountAtTime: jasmine.createSpy().and.returnValue(of({})),
      ACRcheckiban: jasmine.createSpy().and.returnValue(of({})),
      searchPaymentByContractId: jasmine.createSpy().and.returnValue(of([])),
      ACRclientAccountInfo: jasmine.createSpy().and.returnValue(of({})),
      getClientsPage: jasmine.createSpy().and.returnValue(of([]))
    };

    mockNotifications = {
      notifySuccess: jasmine.createSpy(),
      notifyError: jasmine.createSpy()
    };

    mockRouter = {
      navigateByUrl: jasmine.createSpy()
    };

    mockActivatedRoute = {
      queryParams: of({}),
      snapshot: { queryParams: {} }
    };

    formBuilder = new FormBuilder();
    
    // Create component instance directly
    component = new AddClientRefundComponent(
      mockActivatedRoute,
      mockService,
      formBuilder,
      mockNotifications,
      mockRouter
    );

    // Initialize form manually without calling ngOnInit to avoid subscriptions
    // component.ngOnInit(); // Skip this to avoid service subscription issues
  });

  describe('Amount Calculation Logic', () => {
    beforeEach(() => {
      // Setup calculation variables based on AngularJS implementation
      component.PartialRefundPurposeId = 1;
      component.ministryRateForPartialRefund = 50;
      component.mildEscalationId = 4;
      component.severeEscalationId = 5;
      component.standardWeeklyRefundOfUnusedWeeksId = 3;
      component.xMonthlyPayment = 2000;
      component.yMonthlyPayment = 1800;
      component.receivedPaymentAmount = 1500;
    });

    it('should calculate daily partial refund with mild escalation correctly', () => {
      // Setup non-weekly plan
      component.formGroup.patchValue({
        purpose: { id: 1 },
        isWeeklyPlan: false,
        selectedPartialRefundPaymentMethod: 4, // mild escalation
        numberOfUsedDays: 10
      });

      component.calcAmount();

      // Expected: ceil(2000 - (10 * 50)) = ceil(1500) = 1500
      expect(component.formGroup.get('amount')?.value).toBe(1500);
    });

    it('should calculate daily partial refund with severe escalation correctly', () => {
      component.formGroup.patchValue({
        purpose: { id: 1 },
        isWeeklyPlan: false,
        selectedPartialRefundPaymentMethod: 5, // severe escalation
        numberOfUnusedDays: 15
      });

      component.calcAmount();

      // Expected: ceil(2000 / 30.4 * 15) = ceil(986.84) = 987
      expect(component.formGroup.get('amount')?.value).toBe(987);
    });

    it('should calculate weekly partial refund with standard weekly refund correctly', () => {
      component.formGroup.patchValue({
        purpose: { id: 1 },
        isWeeklyPlan: true,
        selectedPartialRefundPaymentMethod: 3, // standard weekly
        numberOfUsedWeeks: 2
      });

      component.calcAmount();

      // Expected: ceil(2000 - (1800 / 4 * 2)) = ceil(2000 - 900) = 1100
      expect(component.formGroup.get('amount')?.value).toBe(1100);
    });

    it('should calculate weekly partial refund with mild escalation correctly', () => {
      component.formGroup.patchValue({
        purpose: { id: 1 },
        isWeeklyPlan: true,
        selectedPartialRefundPaymentMethod: 4, // mild escalation
        numberOfFullUsedWeeks: 2,
        numberOfUsedDays: 15
      });

      component.calcAmount();

      const remainingUsedDays = 15 % 7; // = 1
      // Expected: max(ceil(2000 - (1800 / 4 * 2) - (50 * 1)), 0) = max(ceil(1050), 0) = 1050
      expect(component.formGroup.get('amount')?.value).toBe(1050);
    });

    it('should calculate compensating for unused days correctly', () => {
      component.formGroup.patchValue({
        purpose: { id: 2 }, // different purpose
        isWeeklyPlan: false,
        selectedPartialRefundPaymentMethod: null,
        numberOfUnusedDays: 20
      });

      component.calcAmount();

      // Expected: ceil(1500 / 30.4 * 20) = ceil(986.84) = 987
      expect(component.formGroup.get('amount')?.value).toBe(987);
    });

    it('should return 0 for negative calculations', () => {
      component.formGroup.patchValue({
        purpose: { id: 1 },
        isWeeklyPlan: true,
        selectedPartialRefundPaymentMethod: 4, // mild escalation
        numberOfFullUsedWeeks: 10, // high usage
        numberOfUsedDays: 70
      });

      component.calcAmount();

      // Should result in negative value but returns 0
      expect(component.formGroup.get('amount')?.value).toBe(0);
    });
  });

  describe('Validation Logic', () => {
    beforeEach(() => {
      component.defaultNumberOfUnusedDays = 20;
      component.numberOfUnusedValidation = 5;
    });

    it('should validate minimum unused days correctly', () => {
      const control = { value: 10 } as any;
      const result = component.MinMaxUnusedDaysValidation(control);
      
      expect(result).toEqual({ 
        error: 'Number should be between 15 and 25' 
      });
    });

    it('should validate maximum unused days correctly', () => {
      const control = { value: 30 } as any;
      const result = component.MinMaxUnusedDaysValidation(control);
      
      expect(result).toEqual({ 
        error: 'Number should be between 15 and 25' 
      });
    });

    it('should pass validation for valid unused days', () => {
      const control = { value: 20 } as any;
      const result = component.MinMaxUnusedDaysValidation(control);
      
      expect(result).toBeNull();
    });

    it('should validate amount is not negative', () => {
      const control = { value: -100 } as any;
      const result = component.amountValidation(control);
      
      expect(result).toEqual({ 
        error: "Amount shouldn't be negative or zero" 
      });
    });

    it('should validate amount is not zero', () => {
      const control = { value: 0 } as any;
      const result = component.amountValidation(control);
      
      expect(result).toEqual({ 
        error: "Amount shouldn't be negative or zero" 
      });
    });

    it('should pass validation for positive amount', () => {
      const control = { value: 1000 } as any;
      const result = component.amountValidation(control);
      
      expect(result).toBeNull();
    });

    it('should validate number of used days is greater than zero', () => {
      const control = { value: 0 } as any;
      const result = component.numberOfUsedDaysValidation(control);
      
      expect(result).toEqual({ 
        error: 'Number should be more than zero' 
      });
    });

    it('should pass validation for positive used days', () => {
      const control = { value: 10 } as any;
      const result = component.numberOfUsedDaysValidation(control);
      
      expect(result).toBeNull();
    });
  });

  describe('Helper Functions', () => {
    it('should convert string to number correctly', () => {
      expect(component.coerceNumberProperty('123')).toBe(123);
      expect(component.coerceNumberProperty('123.45')).toBe(123.45);
      expect(component.coerceNumberProperty(456)).toBe(456);
    });

    it('should handle invalid number strings', () => {
      expect(component.coerceNumberProperty('abc')).toBe(0);
      expect(component.coerceNumberProperty('')).toBe(0);
      expect(component.coerceNumberProperty(null)).toBeNaN();
      expect(component.coerceNumberProperty(undefined)).toBeNaN();
    });

    it('should calculate min unused days correctly', () => {
      component.defaultNumberOfUnusedDays = 20;
      component.numberOfUnusedValidation = 5;
      
      expect(component.getMinUnusedDays(5)).toBe(15);
    });

    it('should calculate max unused days correctly', () => {
      component.defaultNumberOfUnusedDays = 20;
      component.numberOfUnusedValidation = 5;
      
      expect(component.getMaxUnusedDays(5)).toBe(25);
    });
  });

  describe('Business Logic Methods', () => {
    it('should handle saved partial refund values correctly', () => {
      // Setup initial values
      component.formGroup.patchValue({
        numberOfUnusedDays: 20,
        numberOfUsedDays: 10,
        amount: 1500
      });

      // Enable unique type (should save values)
      component.formGroup.patchValue({ isUniqueTypeOfPartialRefund: true });
      
      expect(component.savedPartialRefundValues).toEqual({
        numberOfUnusedDays: 20,
        numberOfUsedDays: 10,
        amount: 1500
      });

      // Change values
      component.formGroup.patchValue({
        numberOfUnusedDays: 25,
        numberOfUsedDays: 5,
        amount: 2000
      });

      // Disable unique type (should restore values)
      component.formGroup.patchValue({ isUniqueTypeOfPartialRefund: false });

      expect(component.formGroup.get('numberOfUnusedDays')?.value).toBe(20);
      expect(component.formGroup.get('numberOfUsedDays')?.value).toBe(10);
      expect(component.formGroup.get('amount')?.value).toBe(1500);
    });

    it('should handle contract type filtering correctly', () => {
      // Test MaidVisa contract type restrictions
      const contract = {
        id: 123,
        contractProspectType: 'maidvisa.ae_prospect',
        isScheduledForTermination: false
      };

      component.PartialRefundPurposeId = 1;
      component.formGroup.patchValue({ contractID: contract });

      // Should set partialOptionRemoved to true for non-terminated MaidVisa contracts
      expect(component.partialOptionRemoved).toBeTruthy();
    });

    it('should handle payment method calculations for different scenarios', () => {
      // Setup for weekly plan with severe escalation
      component.formGroup.patchValue({
        purpose: { id: 1 },
        isWeeklyPlan: true,
        selectedPartialRefundPaymentMethod: 5,
        numberOfFullUsedWeeks: 1,
        numberOfUsedDays: 10
      });

      component.xMonthlyPayment = 2000;
      component.yMonthlyPayment = 1800;
      component.severeEscalationId = 5;

      component.calcAmount();

      const remainingUsedDays = 10 % 7; // = 3
      // Expected: max(ceil(2000 - (1800/4 * 1) - (1800/28 * 3)), 0)
      // = max(ceil(2000 - 450 - 192.86), 0) = max(ceil(1357.14), 0) = 1358
      expect(component.formGroup.get('amount')?.value).toBe(1358);
    });
  });

  describe('Data Transformation for API', () => {
    it('should prepare API data correctly for partial refund', () => {
      // Setup form data
      component.formGroup.patchValue({
        selectedClient: { id: 1, text: 'Test Client' },
        contractID: { id: 123, text: 'Contr-123' },
        purpose: { id: 1, text: 'Partial refunds' },
        amount: 1000,
        notes: 'Test notes',
        paymentMethod: 'BANK_TRANSFER',
        iban: 'AE123456789',
        accountName: 'Test Account',
        bankName: 'Test Bank',
        selectedPartialRefundPaymentMethod: 2,
        numberOfUnusedDays: 15
      });

      component.ibanValid = true;
      component.PartialRefundPurposeId = 1;

      // Mock the service call
      component.addClientRefund();

      expect(mockService.createClientRefund).toHaveBeenCalledWith(jasmine.objectContaining({
        contract: { id: 123 },
        client: { id: 1 },
        purpose: { id: 1 },
        amount: 1000,
        notes: 'Test notes',
        methodOfPayment: 'BANK_TRANSFER',
        iban: 'AE123456789',
        accountName: 'Test Account',
        bankName: 'Test Bank',
        partialRefundForCancellationPaymentMethod: { id: 2 },
        numberOfUnusedDays: 15
      }));
    });

    it('should handle conditional refund data correctly', () => {
      component.formGroup.patchValue({
        selectedClient: { id: 1 },
        contractID: { id: 123 },
        purpose: { id: 2 },
        amount: 1000,
        paymentMethod: 'BANK_TRANSFER',
        conditionalRefund: true
      });

      component.requiredPayments = { '1': true, '3': true };
      component.ibanValid = true;

      component.addClientRefund();

      expect(mockService.createClientRefund).toHaveBeenCalledWith(jasmine.objectContaining({
        conditionalRefund: true,
        requiredPayments: [{ id: 1 }, { id: 3 }]
      }));
    });
  });

  describe('Error Handling', () => {
    it('should handle service errors gracefully', () => {
      mockService.createClientRefund.and.returnValue(throwError('Service error'));
      
      component.formGroup.patchValue({
        selectedClient: { id: 1 },
        contractID: { id: 123 },
        purpose: { id: 1 },
        amount: 1000,
        paymentMethod: 'BANK_TRANSFER'
      });

      component.ibanValid = true;
      component.addClientRefund();

      expect(mockNotifications.notifyError).toHaveBeenCalled();
    });

    it('should validate required fields before submission', () => {
      // Missing required fields
      component.formGroup.patchValue({
        selectedClient: null,
        contractID: null,
        purpose: null
      });

      component.addClientRefund();

      expect(mockService.createClientRefund).not.toHaveBeenCalled();
    });

    it('should validate IBAN before submission', () => {
      component.formGroup.patchValue({
        selectedClient: { id: 1 },
        contractID: { id: 123 },
        purpose: { id: 1 },
        amount: 1000,
        paymentMethod: 'BANK_TRANSFER',
        iban: 'invalid-iban'
      });

      component.ibanValid = false;

      component.addClientRefund();

      expect(mockNotifications.notifyError).toHaveBeenCalledWith('Please add a valid IBAN');
      expect(mockService.createClientRefund).not.toHaveBeenCalled();
    });
  });
}); 