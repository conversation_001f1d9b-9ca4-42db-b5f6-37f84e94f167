import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';
import { CCNotificationService } from '@maids/cc-lib/services';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

import { AddClientRefundComponent } from './add-client-refund.component';
import { AddClientRefundService } from '../../services/add-client-refund.service';

describe('AddClientRefundComponent - Integration Tests', () => {
  let component: AddClientRefundComponent;
  let fixture: ComponentFixture<AddClientRefundComponent>;
  let mockService: jasmine.SpyObj<AddClientRefundService>;
  let mockNotifications: jasmine.SpyObj<CCNotificationService>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockActivatedRoute: any;

  beforeEach(async () => {
    const serviceSpy = jasmine.createSpyObj('AddClientRefundService', [
      'getClientsPage', 'getClientContractsWithStatus', 'ACRgetClientContracts',
      'ACRclientAccountInfo', 'getAllPurposesWithSetup', 'getPaymentMethodPicklist',
      'ACRgetParameter', 'gethowMuchRefundOptions', 'getPendingPayrollMonths',
      'getComplaintsData', 'getLastPaymentPaidByPurpose', 'getClientInfo',
      'getUnusedDaysForCompensatingContract', 'getAmountAtTime', 'ACRcheckiban',
      'searchPaymentByContractId', 'createClientRefund'
    ]);

    const notificationsSpy = jasmine.createSpyObj('CCNotificationService', [
      'notifySuccess', 'notifyError'
    ]);

    const routerSpy = jasmine.createSpyObj('Router', ['navigateByUrl']);

    mockActivatedRoute = {
      queryParams: of({}),
      snapshot: { queryParams: {} }
    };

    await TestBed.configureTestingModule({
      declarations: [AddClientRefundComponent],
      imports: [ReactiveFormsModule],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      providers: [
        { provide: AddClientRefundService, useValue: serviceSpy },
        { provide: CCNotificationService, useValue: notificationsSpy },
        { provide: Router, useValue: routerSpy },
        { provide: ActivatedRoute, useValue: mockActivatedRoute }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(AddClientRefundComponent);
    component = fixture.componentInstance;
    mockService = TestBed.inject(AddClientRefundService) as jasmine.SpyObj<AddClientRefundService>;
    mockNotifications = TestBed.inject(CCNotificationService) as jasmine.SpyObj<CCNotificationService>;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;

    // Setup default responses
    setupDefaultServiceResponses();
    fixture.detectChanges();
  });

  function setupDefaultServiceResponses() {
    mockService.getAllPurposesWithSetup.and.returnValue(of([
      { id: 1, name: 'Partial refunds - for cancellation', purposeSetup: { allowMonthlyRefunds: false, linkComplaint: false, requireAttachment: false, complaintTypes: [] } },
      { id: 2, name: 'Compensating for unused days', purposeSetup: { allowMonthlyRefunds: false, linkComplaint: false, requireAttachment: false, complaintTypes: [] } },
      { id: 3, name: "Maid's salary due to absconder maid outside the country", purposeSetup: { allowMonthlyRefunds: false, linkComplaint: false, requireAttachment: false, complaintTypes: [] } },
      { id: 4, name: 'Other', purposeSetup: { allowMonthlyRefunds: true, linkComplaint: true, requireAttachment: true, complaintTypes: [{ id: 1 }] } }
    ]));

    mockService.getPaymentMethodPicklist.and.returnValue(of([
      { id: 1, code: 'client_will_pay_according_to_the_monthly_payment', name: 'Client will pay according to the monthly payment' },
      { id: 2, code: 'client_will_pay_according_to_the_ministry_rate', name: 'Client will pay according to the ministry rate' },
      { id: 3, code: 'standard_weekly_refund_of_unused_weeks', name: 'Standard weekly refund of unused weeks' },
      { id: 4, code: 'mild_escalation', name: 'Mild escalation' },
      { id: 5, code: 'severe_escalation', name: 'Severe escalation' }
    ]));

    mockService.ACRgetParameter.and.returnValue(of([{ value: 'test-value' }]));
    mockService.gethowMuchRefundOptions.and.returnValue(of([
      { id: 1, code: "refund_only_maid's_pending_salary", name: "Refund only maid's pending salary" },
      { id: 2, code: 'refund_full_amount', name: 'Refund full amount' }
    ]));

    mockService.getClientContractsWithStatus.and.returnValue(of([
      { id: 123, text: 'Contr-123', isScheduledForTermination: false, contractProspectType: { code: 'maids.cc_prospect' }, housemaid: { id: 456 } }
    ]));

    mockService.ACRclientAccountInfo.and.returnValue(of({
      iban: 'AE123456789', accountName: 'Test Account', bankName: 'Test Bank'
    }));

    mockService.createClientRefund.and.returnValue(of({ success: true }));
  }

  describe('Complete Workflow Tests', () => {
    it('should handle complete partial refund workflow', async () => {
      // Step 1: Select client
      const client = { id: 1, text: 'Test Client' };
      component.formGroup.patchValue({ selectedClient: client });
      
      // Step 2: Select contract
      const contract = { id: 123, text: 'Contr-123', isScheduledForTermination: true, contractProspectType: 'maids.cc_prospect', housemaidId: 456 };
      component.formGroup.patchValue({ contractID: contract });

      // Setup partial refund purpose
      component.PartialRefundPurposeId = 1;
      component.partialRefundMonthlyPaymentId = 1;
      component.xMonthlyPayment = 2000;
      component.yMonthlyPayment = 1800;
      component.isScheduledForTermination = true;

      // Step 3: Select partial refund purpose
      const partialRefundPurpose = { id: 1, text: 'Partial refunds - for cancellation', allowMonthlyRefunds: false, linkComplaint: false, requireAttachment: false };
      component.formGroup.patchValue({ purpose: partialRefundPurpose });

      // Step 4: Select payment method
      component.formGroup.patchValue({ selectedPartialRefundPaymentMethod: 1 });

      // Step 5: Set usage days
      component.formGroup.patchValue({ numberOfUnusedDays: 15 });

      // Step 6: Calculate amount (should be automatic)
      component.calcAmount();

      // Step 7: Set payment details
      component.formGroup.patchValue({
        paymentMethod: 'BANK_TRANSFER',
        iban: 'AE123456789',
        accountName: 'Test Account',
        bankName: 'Test Bank'
      });

      component.ibanValid = true;

      // Step 8: Submit
      component.addClientRefund();

      // Verify the complete data structure matches AngularJS version
      expect(mockService.createClientRefund).toHaveBeenCalledWith(jasmine.objectContaining({
        contract: { id: 123 },
        client: { id: 1 },
        purpose: { id: 1 },
        amount: Math.ceil(15 * (1800 / 30.4)),
        methodOfPayment: 'BANK_TRANSFER',
        conditionalRefund: false,
        partialRefundForCancellationPaymentMethod: { id: 1 },
        numberOfUnusedDays: 15,
        iban: 'AE123456789',
        accountName: 'Test Account',
        bankName: 'Test Bank'
      }));
    });

    it('should handle complete maid salary refund workflow', async () => {
      // Setup
      const client = { id: 1, text: 'Test Client' };
      const contract = { id: 123, text: 'Contr-123', contractProspectType: 'maidvisa.ae_prospect', housemaidId: 456 };
      const maidSalaryPurpose = { id: 3, text: "Maid's salary due to absconder maid outside the country" };

      mockService.getPendingPayrollMonths.and.returnValue(of([
        { payrollLog: { id: 1, payrollMonth: '2023-01' }, notes: null, totalSalary: 1000, payment: { id: 101, amountOfPayment: 1200 } }
      ]));

      // Workflow
      component.formGroup.patchValue({ selectedClient: client });
      component.formGroup.patchValue({ contractID: contract });
      component.formGroup.patchValue({ purpose: maidSalaryPurpose });

      // Wait for pending salary records to load
      expect(mockService.getPendingPayrollMonths).toHaveBeenCalledWith(456, 123);

      const pendingSalaryRecord = { id: 1, text: '2023-01', payrollMonth: '2023-01-01', totalSalary: 1000, payment: { id: 101, amountOfPayment: 1200 } };
      component.formGroup.patchValue({ pendingSalaryRecord: pendingSalaryRecord });

      const refundOption = { id: 1, code: "refund_only_maid's_pending_salary", name: "Refund only maid's pending salary" };
      component.formGroup.patchValue({ howMuchWeRefundOption: refundOption });

      component.formGroup.patchValue({
        paymentMethod: 'CREDIT_CARD',
        transferReference: 'PAY-123456'
      });

      component.addClientRefund();

      expect(mockService.createClientRefund).toHaveBeenCalledWith(jasmine.objectContaining({
        purpose: { id: 3 },
        howMuchWeRefundOption: { id: 1 },
        pendingSalaryRecord: '2023-01-01',
        housemaidPayrollLogId: 1,
        transferReference: 'PAY-123456'
      }));
    });

    it('should handle conditional refund with multiple payments workflow', async () => {
      // Setup
      const client = { id: 1, text: 'Test Client' };
      const contract = { id: 123, text: 'Contr-123' };

      mockService.searchPaymentByContractId.and.returnValue(of([
        { id: 1, status: { label: 'PDC' }, methodOfPayment: { label: 'Bank Transfer' }, dateOfPayment: '2023-01-01', amountOfPayment: 500 },
        { id: 2, status: { label: 'PRE_PDP' }, methodOfPayment: { label: 'Bank Transfer' }, dateOfPayment: '2023-01-15', amountOfPayment: 600 }
      ]));

      // Workflow
      component.formGroup.patchValue({ selectedClient: client });
      component.formGroup.patchValue({ contractID: contract });
      component.formGroup.patchValue({ purpose: { id: 2, text: 'Compensating for unused days' } });
      component.formGroup.patchValue({ amount: 1000 });
      component.formGroup.patchValue({ paymentMethod: 'BANK_TRANSFER' });
      component.formGroup.patchValue({ conditionalRefund: true });

      // Select required payments
      component.requiredPayments = { '1': true, '2': false };

      component.formGroup.patchValue({
        iban: 'AE123456789',
        accountName: 'Test Account'
      });

      component.ibanValid = true;
      component.addClientRefund();

      expect(mockService.createClientRefund).toHaveBeenCalledWith(jasmine.objectContaining({
        conditionalRefund: true,
        requiredPayments: [{ id: 1 }]
      }));
    });
  });

  describe('Edge Cases and Error Scenarios', () => {
    it('should handle MaidVisa contract type restrictions', () => {
      const contract = { 
        id: 123, 
        contractProspectType: 'maidvisa.ae_prospect',
        isScheduledForTermination: false 
      };

      mockService.getAmountAtTime.and.returnValue(of({
        unusedDays: 15,
        usedDays: 10,
        isWeeklyPlan: false,
        isTerminated: false,
        paymentOfTerminationMonth: 2000,
        cptAmount: 1800,
        receivedPaymentAmount: 1500
      }));

      component.PartialRefundPurposeId = 1;
      component.formGroup.patchValue({ contractID: contract });
      component.getAmountAtTime(123, null);

      // Should filter out partial refund option for non-terminated MaidVisa contracts
      expect(component.partialOptionRemoved).toBeTruthy();
    });

    it('should handle validation errors correctly', () => {
      component.defaultNumberOfUnusedDays = 20;
      component.numberOfUnusedValidation = 5;

      // Test min/max validation (max should be 25, min should be 15)
      const invalidControl = { value: 30 } as any;
      const result = component.MinMaxUnusedDaysValidation(invalidControl);
      expect(result).toEqual({ error: 'Number should be between 15 and 25' });

      // Test amount validation
      const negativeAmountControl = { value: -100 } as any;
      const amountResult = component.amountValidation(negativeAmountControl);
      expect(amountResult).toEqual({ error: "Amount shouldn't be negative or zero" });
    });

    it('should handle saved partial refund values toggle', () => {
      component.formGroup.patchValue({
        numberOfUnusedDays: 20,
        numberOfUsedDays: 10,
        amount: 1500
      });

      // Enable unique type
      component.formGroup.patchValue({ isUniqueTypeOfPartialRefund: true });
      
      expect(component.savedPartialRefundValues).toEqual({
        numberOfUnusedDays: 20,
        numberOfUsedDays: 10,
        amount: 1500
      });

      // Change values
      component.formGroup.patchValue({
        numberOfUnusedDays: 25,
        numberOfUsedDays: 5,
        amount: 2000
      });

      // Disable unique type (should restore values)
      component.formGroup.patchValue({ isUniqueTypeOfPartialRefund: false });

      expect(component.formGroup.get('numberOfUnusedDays')?.value).toBe(20);
      expect(component.formGroup.get('numberOfUsedDays')?.value).toBe(10);
      expect(component.formGroup.get('amount')?.value).toBe(1500);
    });

    it('should handle contract switching correctly', () => {
      const client = { id: 1, text: 'Test Client' };
      const contract1 = { id: 123, text: 'Contr-123' };
      const contract2 = { id: 456, text: 'Contr-456' };

      component.formGroup.patchValue({ selectedClient: client });
      component.formGroup.patchValue({ contractID: contract1 });

      // Fill some form data
      component.formGroup.patchValue({
        iban: 'AE123456789',
        accountName: 'Test Account',
        numberOfUnusedDays: 15
      });

      // Switch contract (should clear certain fields)
      component.formGroup.patchValue({ contractID: contract2 });

      // Some fields should be reset
      expect(component.complaintOptions).toEqual([]);
      expect(component.defaultNumberOfUnusedDays).toBe(0);
    });

    it('should handle parameter not found errors', () => {
      mockService.ACRgetParameter.and.returnValue(of([])); // Empty response

      component.getParameter('TEST_PARAMETER').subscribe();

      expect(mockNotifications.notifyError).toHaveBeenCalledWith('Parameter TEST_PARAMETER Not Found');
    });

    it('should handle weekly vs daily calculations correctly', () => {
      component.PartialRefundPurposeId = 1;
      component.mildEscalationId = 4;
      component.severeEscalationId = 5;
      component.standardWeeklyRefundOfUnusedWeeksId = 3;
      component.ministryRateForPartialRefund = 50;
      component.xMonthlyPayment = 2000;
      component.yMonthlyPayment = 1800;

      // Test weekly plan with mild escalation
      component.formGroup.patchValue({
        purpose: { id: 1 },
        isWeeklyPlan: true,
        selectedPartialRefundPaymentMethod: 4,
        numberOfUsedDays: 15,
        numberOfFullUsedWeeks: 2
      });

      component.calcAmount();

      const remainingUsedDays = 15 % 7;
      const expectedAmount = Math.max(
        Math.ceil(2000 - (1800 / 4 * 2) - (50 * remainingUsedDays)),
        0
      );

      expect(component.formGroup.get('amount')?.value).toBe(expectedAmount);
    });
  });

  describe('URL Parameter Integration', () => {
    it('should pre-fill form from URL parameters exactly like AngularJS', () => {
      mockActivatedRoute.snapshot.queryParams = {
        clientID: '1',
        contractID: '123',
        amount: '1500',
        numberOfUnusedDays: '20',
        numberOfUsedDays: '8',
        isUniqueTypeOfPartialRefund: 'true',
        selectedPartialRefundPaymentMethod: '2'
      };

      mockService.ACRgetParameter.and.returnValue(of([{ value: 'Partial refunds - for cancellation' }]));

      component.PartialRefundPurposeId = 1;
      component.checkAmount();

      expect(component.formGroup.get('purpose')?.value).toBe(1);
      expect(component.formGroup.get('isUniqueTypeOfPartialRefund')?.value).toBe(true);
      expect(component.formGroup.get('selectedPartialRefundPaymentMethod')?.value).toBe(2);
    });

    it('should handle iframe detection and disable form', () => {
      mockActivatedRoute.queryParams = of({ clientID: '1', contractID: '123' });
      
      // Mock window.location and window.parent.location to simulate iframe
      const originalLocation = window.location;
      const originalParentLocation = window.parent.location;
      
      Object.defineProperty(window, 'location', {
        value: { href: 'http://child.com' },
        writable: true
      });
      
      Object.defineProperty(window.parent, 'location', {
        value: { href: 'http://parent.com' },
        writable: true
      });

      component.ngOnInit();

      expect(component.disableAll).toBeTruthy();

      // Restore original values
      Object.defineProperty(window, 'location', {
        value: originalLocation,
        writable: true
      });
      Object.defineProperty(window.parent, 'location', {
        value: originalParentLocation,
        writable: true
      });
    });
  });

  describe('Data Transformation Tests', () => {
    it('should transform form data to API format exactly like AngularJS', () => {
      // Setup form with complex data
      const formData = {
        selectedClient: { id: 1, text: 'Test Client' },
        contractID: { id: 123, text: 'Contr-123' },
        purpose: { 
          id: 4, 
          text: 'Other',
          allowMonthlyRefunds: true,
          linkComplaint: true,
          requireAttachment: true
        },
        amount: 1000,
        notes: 'Test notes',
        paymentMethod: 'BANK_TRANSFER',
        iban: 'AE123456789',
        accountName: 'Test Account',
        bankName: 'Test Bank',
        conditionalRefund: true,
        refundMonthsCnt: 6,
        selectedComplaint: { id: 5, text: 'Complaint 1' },
        proofAttachment: [{ id: 123 }]
      };

      component.formGroup.patchValue(formData);
      component.requiredPayments = { '1': true, '3': true };
      component.ibanValid = true;
      component.otherPurposeId = 4;

      component.addClientRefund();

      expect(mockService.createClientRefund).toHaveBeenCalledWith(jasmine.objectContaining({
        contract: { id: 123 },
        client: { id: 1 },
        purpose: { id: 4 },
        amount: 1000,
        notes: 'Test notes',
        methodOfPayment: 'BANK_TRANSFER',
        conditionalRefund: true,
        iban: 'AE123456789',
        accountName: 'Test Account',
        bankName: 'Test Bank',
        numberOfMonthlyPayments: 6,
        complaint: { id: 5 },
        attachments: [{ id: 123 }],
        requiredPayments: [{ id: 1 }, { id: 3 }]
      }));
    });

    it('should handle credit card conditional refund correctly', () => {
      component.formGroup.patchValue({
        selectedClient: { id: 1 },
        contractID: { id: 123 },
        purpose: { id: 2 },
        amount: 1000,
        paymentMethod: 'CREDIT_CARD',
        conditionalRefund: true,
        requiredCCPayment: '5',
        transferReference: 'PAY-123456'
      });

      component.addClientRefund();

      expect(mockService.createClientRefund).toHaveBeenCalledWith(jasmine.objectContaining({
        conditionalRefund: true,
        requiredCCPayment: [{ id: 5 }],
        transferReference: 'PAY-123456'
      }));
    });
  });
}); 