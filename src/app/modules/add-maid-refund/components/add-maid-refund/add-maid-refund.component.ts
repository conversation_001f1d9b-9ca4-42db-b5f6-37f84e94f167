import { Component, OnInit } from '@angular/core';
import { AddMaidRefundService } from '../../services/add-maid-refund.service';
import { FormBuilder, Validators } from '@angular/forms';
import { CCDialog } from '@maids/cc-lib/dialog';
import { PaginationRequest } from '@maids/cc-lib/common';
import { Observable, tap } from 'rxjs';
import { SelectOption } from '@maids/cc-lib/select-input';
import { Router } from '@angular/router';
import { CCNotificationService } from '@maids/cc-lib/services';
import * as moment from 'moment';
import { CCValidatorFn } from '@maids/cc-lib/validation';
import { MaidPaymentValidationComponent } from '../maid-payment-validation/maid-payment-validation.component';
import { EditAmountComponent } from '../edit-amount/edit-amount.component';
import { CCFileUploaderConfig } from '@maids/cc-lib/file-uploader';

@Component({
  selector: 'app-add-maid-refund',
  templateUrl: './add-maid-refund.component.html',
  styleUrls: ['./add-maid-refund.component.scss'],
})
export class AddMaidRefundComponent implements OnInit {
  formGroup = this.fb.group({
    selectedHousemaid: [''],
    expense: [''],
    purposeAdditionalDescription: [''],
    vacationDays: [''],
    selectedReferredHousemaid: [''],
    amountAlreadyPaid: [false],
    amount: [''],
    loanAmount: [''],
    paymentMethod: [''],
    selectedBucket: [''],
    notes: [''],
    invoice: [''],
    cashier: [''],
  });
  fileConfig: CCFileUploaderConfig = {
    maxFilesize: 5,
  };
  vacationDays: any = {
    totalSalary: 0.0,
    balanceOfVacationDays: 0.0,
    requestedDays: 0.0,
    edited: false,
  };
  expense: any;
  salaryDisputeCode: any;
  vacationDaysCode: any;
  bonusesForHousemaidsCode: any;
  otherExpenseCode: any;
  maidPaymentValidation: any;
  paymentValidationTitle: string = '';
  checkSpentThirtyDays: any;
  disableAmount: boolean = false;
  paymentMethodOptions: any[] = [];
  bucketOptions: any[] = [];
  activeMaids: any[] = [];
  nationalityText: string = '';
  referralBonusAmount: number = 0;
  editAmountModalText: string = '';
  shownEditAmount: boolean = false;
  shown: boolean = false;
  showBucket: boolean = false;
  readonly cashierOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.addMaidRefundService.getCashiersOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  constructor(
    private addMaidRefundService: AddMaidRefundService,
    private fb: FormBuilder,
    private ccDialog: CCDialog,
    private router: Router,
    private notifications: CCNotificationService
  ) {}
  readonly housemaidOptions = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.addMaidRefundService.AMRgetHouseMaidOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly referredMaidOptions = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.addMaidRefundService
      .AMRgetReferredMaidOptions(
        pageReq.page,
        pageReq.size,
        pageReq.searchString
      )
      .pipe(tap((res: any) => (this.activeMaids = res.content)));
  };
  expenseOptions: any[] = [];
  purposeOptions: any[] = [];
  bonusPurposeOptions: any[] = [];
  otherExpensePurposeOptions: any[] = [];
  ngOnInit(): void {
    this.getPurposeOptions();
    this.getExpenseForPage();
    this.getSalaryDisputeCode();
    this.getVacationDays();
    this.getBonusesForHousemaidsCode();
    this.getOtherExpenseCode();
    this.handleFormValueChange();
  }
  getPurposeOptions() {
    this.getPurposesPicklist(
      'HousemaidPurposesAdditionalDescription',
      this.purposeOptions
    );
    this.getPurposesPicklist(
      'HousemaidPurposesForBonusAdditionalDescription',
      this.bonusPurposeOptions
    );
    this.getPurposesPicklist(
      'HousemaidPurposesForMaidsAtOtherExpensesAdditionalDescription',
      this.otherExpensePurposeOptions
    );
  }
  getExpenseForPage() {
    return this.addMaidRefundService.AMRgetExpenseForPage().subscribe((res) => {
      this.expenseOptions = res;
    });
  }
  getSalaryDisputeCode() {
    return this.addMaidRefundService
      .AMRgetPublicParameterByCode('EXPENSE_SALARY_DISPUTE_CODE')
      .subscribe((res) => {
        this.salaryDisputeCode = res[0].value;
      });
  }
  getVacationDays() {
    return this.addMaidRefundService
      .AMRgetPublicParameterByCode('EXPENSE_VACATION_DAYS_CODE')
      .subscribe((res) => {
        this.expense = res[0];
      });
  }
  getBonusesForHousemaidsCode() {
    return this.addMaidRefundService
      .AMRgetPublicParameterByCode('EXPENSE_BONUS_CODE')
      .subscribe((res) => {
        this.bonusesForHousemaidsCode = res[0].value;
      });
  }
  getOtherExpenseCode() {
    return this.addMaidRefundService
      .AMRgetPublicParameterByCode('EXPENSE_MAIDS_AT_OTHER_EXPENSES_CODE')
      .subscribe((res) => {
        this.otherExpenseCode = res[0].value;
      });
  }
  handleFormValueChange() {
    this.formGroup.controls['selectedHousemaid'].valueChanges.subscribe(
      (val) => {
        if (val) {
          this.getMaidPaymentValidation(val.id);
        }
        if (
          val &&
          this.expense &&
          this.expense.code == this.formGroup.controls['vacationDays'].value &&
          this.formGroup.controls['selectedHousemaid'].value
        ) {
          this.checkToCreateVacationRequest();
          this.disableAmount = true;
        } else {
          this.formGroup.controls['vacationDays'].setValue(0);
          this.disableAmount = false;
          this.formGroup.controls['amount'].setValue('');
        }
      }
    );
    this.formGroup.controls['selectedReferredHousemaid'].valueChanges.subscribe(
      (val) => {
        if (val) {
          this.getCheckSpentThirtyDays(val.id);
        }
      }
    );
    this.formGroup.controls['vacationDays'].valueChanges.subscribe((val) => {
      if (val) {
        this.calculate();
      }
    });
    this.formGroup.controls['loanAmount'].valueChanges.subscribe((val) => {
      if (val && this.formGroup.controls['amountAlreadyPaid'].value) {
        this.formGroup.controls['amount'].setValue(val);
      }
    });
    this.formGroup.controls['amount'].valueChanges.subscribe((val) => {
      if (
        this.expense &&
        this.expense.code === this.bonusesForHousemaidsCode &&
        this.formGroup.controls['purposeAdditionalDescription'].value.code ==
          'referral_bonus' &&
        !!val &&
        val != this.checkSpentThirtyDays.referralBonusAmount
      ) {
        let selected = this.activeMaids.filter(
          (e: any) =>
            e.id ==
            this.formGroup.controls['selectedReferredHousemaid'].value.id
        )[0];
        this.nationalityText = selected.nationality.name;
        this.referralBonusAmount =
          this.checkSpentThirtyDays.referralBonusAmount;
      }
    });
    this.formGroup.controls['amountAlreadyPaid'].valueChanges.subscribe(
      (val) => {
        if (val == true) {
          this.formGroup.controls['amount'].setValue('');
          if (this.paymentMethodOptions.find((e) => e.id == 'SALARY')) {
            this.formGroup.controls['paymentMethod'].setValue('SALARY');
          } else {
            this.notifications.notifyError(
              'You should have salary in payment methods.'
            );
          }
        }
      }
    );
    this.formGroup.controls['expense'].valueChanges.subscribe((val) => {
      if (val) {
        this.formGroup.controls['purposeAdditionalDescription'].setValue('');
        if(val.code == this.salaryDisputeCode || val.code == this.bonusesForHousemaidsCode || val.code == this.otherExpenseCode){
          this.formGroup.controls['purposeAdditionalDescription'].addValidators([Validators.required]);
          this.formGroup.controls['purposeAdditionalDescription'].updateValueAndValidity();
        }else{
          this.formGroup.controls['purposeAdditionalDescription'].removeValidators([Validators.required]);
          this.formGroup.controls['purposeAdditionalDescription'].updateValueAndValidity();
        }
        if(val.code == this.vacationDaysCode){
          this.formGroup.controls['vacationDays'].addValidators([Validators.required]);
          this.formGroup.controls['vacationDays'].updateValueAndValidity();
        }else{
          this.formGroup.controls['vacationDays'].removeValidators([Validators.required]);
          this.formGroup.controls['vacationDays'].updateValueAndValidity();
        }
        if(val.code == this.bonusesForHousemaidsCode && this.formGroup.controls['purposeAdditionalDescription'].value.code == 'referral_bonus'){
          this.formGroup.controls['selectedReferredHousemaid'].addValidators([Validators.required]);
          this.formGroup.controls['selectedReferredHousemaid'].updateValueAndValidity();
        }else{
          this.formGroup.controls['selectedReferredHousemaid'].removeValidators([Validators.required]);
          this.formGroup.controls['selectedReferredHousemaid'].updateValueAndValidity();
        }
        this.addMaidRefundService
          .AMRgetPaymentsMethodsByExpense(val.id)
          .subscribe((res: any) => {
            this.paymentMethodOptions = [];
            this.getPurposeOptions();
            this.expense = res;
            if (this.expense.paymentMethods?.length) {
              this.paymentMethodOptions = this.expense.paymentMethods.map(
                (item: any) => ({ id: item.value, text: item.label })
              );
              if (this.expense.paymentMethods?.length == 1) {
                this.formGroup.controls['paymentMethod'].setValue(
                  this.paymentMethodOptions[0].id
                );
              } else {
                this.formGroup.controls['paymentMethod'].setValue('');
              }
            }
            this.expense.showAmountAsLoan =
              this.expense.relatedTos.find(
                (item: any) => item.relatedToType.value === 'MAID'
              ) &&
              this.expense.allowToAddLoan &&
              this.expense.loanType;
            this.formGroup.controls['amount'].setValue(
              this.expense.defaultAmount
            );
            if (
              this.expense &&
              this.expense.code === this.vacationDaysCode &&
              this.formGroup.controls['selectedHousemaid'].value
            ) {
              this.checkToCreateVacationRequest();
              this.disableAmount = true;
            } else {
              this.formGroup.controls['vacationDays'].setValue(0);
              this.disableAmount = false;
              this.formGroup.controls['amount'].setValue(0);
            }

            if (this.expense && this.expense.code === this.salaryDisputeCode) {
              this.purposeOptions.forEach((item: any) => {
                if (item.text.toLowerCase() === 'no show') {
                  this.formGroup.controls[
                    'purposeAdditionalDescription'
                  ].setValue(item);
                  this.formGroup.controls[
                    'purposeAdditionalDescription'
                  ].disable();
                } else {
                  this.formGroup.controls[
                    'purposeAdditionalDescription'
                  ].enable();
                }
              });
            }
          });
      } else {
        this.expense = {};
      }
    });
    this.formGroup.controls['paymentMethod'].valueChanges.subscribe((val) => {
      this.showBucket = false;
      this.formGroup.controls['selectedBucket'].setValue('');
      this.formGroup.controls['selectedBucket'].clearValidators();
      this.formGroup.controls['selectedBucket'].updateValueAndValidity();

      if (
        (val == 'CASH' && this.expense?.fromCashBuckets?.length > 1) ||
        (val == 'CREDIT_CARD' &&
          this.expense?.fromCreditCardBuckets?.length > 1)
      ) {
        this.showBucket = true;
        this.formGroup.controls['selectedBucket'].setValidators([
          Validators.required,
        ]);
        this.formGroup.controls['selectedBucket'].updateValueAndValidity();
        if (val == 'CASH') {
          this.bucketOptions = this.expense?.fromCashBuckets?.map(
            (item: any) => {
              return { id: item.id, text: item.label };
            }
          );
        } else {
          this.bucketOptions = this.expense.fromCreditCardBuckets.map(
            (item: any) => {
              return { id: item.id, text: item.label };
            }
          );
        }
        this.formGroup.controls['selectedBucket'].setValue(
          this.formGroup.controls['paymentMethod'].value == 'CASH'
            ? this.expense?.fromCashBuckets?.id
            : this.expense.fromCreditCardBuckets.id
        );
      }
      if (val == 'CASH') {
        this.formGroup.controls['cashier'].addValidators(Validators.required);
        this.formGroup.controls['cashier'].updateValueAndValidity();
      } else {
        this.formGroup.controls['cashier'].removeValidators(Validators.required);
        this.formGroup.controls['cashier'].updateValueAndValidity();
      }
    });
  }
  checkToCreateVacationRequest() {
    this.addMaidRefundService
      .checkToCreateVacationRequest(
        this.formGroup.controls['selectedHousemaid'].value.id
      )
      .subscribe((res) => {
        if (res && res.isRenewedMaid && res.isVacationRequestDateValid) {
          this.vacationDays.totalSalary = res.totalSalary;
          this.addMaidRefundService
            .gethousemaidvacationsinfo(
              this.formGroup.controls['selectedHousemaid'].value.id
            )
            .subscribe((res) => {
              if (res.vacationBalance > 0) {
                this.vacationDays.balanceOfVacationDays = res.vacationBalance;
                this.formGroup.controls['vacationDays'].setValue(0);
              } else {
                this.showNotEligibleMsg();
              }
            });
        } else {
          this.showNotEligibleMsg();
        }
      });
  }
  getCheckSpentThirtyDays(id: any) {
    this.addMaidRefundService
      .getCheckSpentThirtyDays(id)
      .subscribe((res: any) => {
        this.checkSpentThirtyDays = res;
        if (!res.passedTheRequiredDaysWithClient) {
          this.formGroup.controls['amount'].setValue(res.referralBonusAmount);
          let calcDays = !isNaN(res.sumOfThePassedDays)
            ? 30 - parseInt(res.sumOfThePassedDays)
            : 30;
          this.notifications.notifyError(
            `${this.formGroup.controls['selectedReferredHousemaid'].value.text} hasn't spent 30 days with a client. Please try again after ${calcDays} `
          );
        } else {
          this.formGroup.controls['amount'].setValue(res.referralBonusAmount);
        }
      });
  }
  calculate() {
    if (
      this.formGroup.controls['vacationDays'].value &&
      this.formGroup.controls['vacationDays'].value <=
        this.vacationDays.balanceOfVacationDays
    ) {
      this.vacationDays.edited = true;
      this.formGroup.controls['amount'].setValue(
        Math.round(
          (this.formGroup.controls['vacationDays'].value *
            this.vacationDays.totalSalary) /
            30.4
        )
      );
    } else {
      this.notifications.notifyError(
        `The number of requested days ${this.formGroup.controls['vacationDays'].value} exceeds the vacation balance ${this.vacationDays.balanceOfVacationDays}`
      );
      this.formGroup.controls['vacationDays'].setValue(0);
    }
  }
  getPurposesPicklist(apiName: any, picklistObj: any) {
    return this.addMaidRefundService
      .getPurposesPicklist(apiName)
      .subscribe((res) => {
        if (apiName == 'HousemaidPurposesAdditionalDescription') {
          this.purposeOptions = res;
        }
        if (apiName == 'HousemaidPurposesForBonusAdditionalDescription') {
          this.bonusPurposeOptions = res;
        }
        if (
          apiName ==
          'HousemaidPurposesForMaidsAtOtherExpensesAdditionalDescription'
        ) {
          this.otherExpensePurposeOptions = res;
        }
      });
  }
  showNotEligibleMsg() {
    this.notifications.notifyError(
      this.formGroup.controls['selectedHousemaid'].value.text +
        ' is not eligible for getting paid her vacation days.'
    );
    this.formGroup.controls['amount'].setValue('');
    this.formGroup.controls['selectedHousemaid'].setValue('');
  }
  getMaidPaymentValidation(id: any) {
    let currentDate = moment(new Date()).format('YYYY-MM-DD');
    this.addMaidRefundService
      .getMaidPaymentValidation(id, currentDate)
      .subscribe((res: any) => {
        this.maidPaymentValidation = res;
        this.paymentValidationTitle = `${this.formGroup.controls['selectedHousemaid'].value.text} has ${res?.length} other additions added this month . Are you sure you want to proceed`;
      });
  }
  addMaidRefund() {
    if (this.formGroup.valid) {
      if (
        !this.shown &&
        !this.shownEditAmount &&
        this.maidPaymentValidation?.length > 0
      ) {
        this.shownEditAmount = false;
        this.shown = true;
        this.ccDialog
          .originalOpen(MaidPaymentValidationComponent, {
            data: {
              paymentValidationTitle: this.paymentValidationTitle,
              maidPaymentValidation: this.maidPaymentValidation,
            },
            minWidth: '70vw',
          })
          .afterClosed()
          .subscribe((res: any) => {
            if (res) {
              this.addMaidRefund();
            }
          });
        return;
      }
      if (
        !!this.formGroup.controls['selectedReferredHousemaid'].value &&
        this.expense.code == this.bonusesForHousemaidsCode &&
        this.formGroup.controls['purposeAdditionalDescription'].value.code ==
          'referral_bouns' &&
        !this.shownEditAmount
      ) {
        if (
          this.checkSpentThirtyDays.referralBonusAmount > 0 &&
          this.checkSpentThirtyDays.referralBonusAmount !=
            this.formGroup.controls['amount'].value
        ) {
          this.shownEditAmount = true;
          this.shown = false;
          this.ccDialog
            .originalOpen(EditAmountComponent, {
              data: {
                nationalityText: this.nationalityText,
                referralBonusAmount: this.referralBonusAmount,
                selectedHousemaid:
                  this.formGroup.controls['selectedReferredHousemaid'].value
                    .text,
                amount: this.formGroup.controls['amount'].value,
              },
            })
            .afterClosed()
            .subscribe((res: any) => {
              if (res) {
                this.addMaidRefund();
              }
            });
          return;
        }
      }
      if (this.formGroup.controls['amountAlreadyPaid'].value) {
        this.formGroup.controls['amount'].setValue(
          this.formGroup.controls['loanAmount'].value
        );
      }
      let data: any = {};
      data = {
        housemaid: this.formGroup.controls['selectedHousemaid'].value
          ? { id: this.formGroup.controls['selectedHousemaid'].value.id }
          : '',
        relatedToId: this.formGroup.controls['selectedHousemaid'].value.id,
        beneficiaryId: this.formGroup.controls['selectedHousemaid'].value.id,
        expense: { id: this.formGroup.controls['expense'].value.id },
        amount: this.formGroup.controls['amount'].value,
        paymentMethod: this.formGroup.controls['paymentMethod'].value,
        loanAmount: this.formGroup.controls['loanAmount'].value,
        amountAlreadyPaid: this.formGroup.controls['amountAlreadyPaid'].value,
        expenseRequestType: 'MAID_PAYMENT',
        notes: this.formGroup.controls['notes'].value,
      };
      if (this.showBucket) {
        data.bucket = {
          id: this.formGroup.controls['selectedBucket'].value,
        };
      }
      if (
        (this.formGroup.controls['paymentMethod'].value == 'CASH' &&
          this.expense?.fromCashBuckets?.length === 1) ||
        (this.formGroup.controls['paymentMethod'].value == 'CREDIT_CARD' &&
          this.expense?.fromCreditCardBuckets?.length === 1)
      ) {
        data.bucket = {
          id:
            this.formGroup.controls['paymentMethod'].value == 'CASH'
              ? this.expense?.fromCashBuckets[0].id
              : this.expense?.fromCreditCardBuckets[0].id,
        };
      }
      if (
        this.expense &&
        (this.expense.code == this.salaryDisputeCode ||
          this.expense.code == this.bonusesForHousemaidsCode ||
          (this.expense && this.expense.code == this.otherExpenseCode))
      ) {
        data.purposeAdditionalDescription = {
          id: this.formGroup.controls['purposeAdditionalDescription'].value.id,
        };
      }
      if (
        this.expense.code == this.bonusesForHousemaidsCode &&
        this.formGroup.controls['purposeAdditionalDescription'].value.code ==
          'referral_bouns'
      ) {
        data.referredMaid =
          this.formGroup.controls['selectedReferredHousemaid'].value.id;
      }
      data.attachments = [];
      if (
        this.formGroup.controls['invoice'].value &&
        this.formGroup.controls['invoice'].value[0].id
      ) {
        data.attachments.push(this.formGroup.controls['invoice'].value[0]);
      }
      if (this.formGroup.controls['paymentMethod'].value === 'CASH') {
        data.cashier = { id: this.formGroup.controls['cashier'].value };
      }
      this.addMaidRefundService.addMaidRefund(data).subscribe({
        next: (res: any) => {
          this.notifications.notifySuccess(
            'Payment request added successfully'
          );
          this.back();
        },
        error: (err: any) => {
          this.notifications.notifyError(err.error.message);
        },
      });
    }
  }

  back() {
    this.router.navigateByUrl('/accounting/v2/maid-expenses-summary');
  }
  readonly amountValidation: CCValidatorFn = (control) => {
    if (
      (control.value === 0 || control.value < 0) &&
      (this.formGroup.controls['amountAlreadyPaid'].value == false ||
        !this.disableAmount)
    ) {
      return { error: 'Amount should be greater than zero' };
    }
    return null;
  };
}
