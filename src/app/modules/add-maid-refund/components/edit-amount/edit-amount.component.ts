import { Component, Inject, OnInit } from '@angular/core';
import { CCDialogRef } from '@maids/cc-lib/dialog';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
@Component({
  selector: 'app-edit-amount',
  templateUrl: './edit-amount.component.html',
  styleUrls: ['./edit-amount.component.scss']
})
export class EditAmountComponent implements OnInit {

  constructor(  @Inject(MAT_DIALOG_DATA) public data: any,
  private ccDialogRef: CCDialogRef<EditAmountComponent>) { }

  ngOnInit(): void {
  }

  addMaidRefund() {
    this.ccDialogRef.close(true);
  }
}
