import { HttpClient, HttpContext } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import {
  CCBackendEndpoint,
  REQ_SHOW_LOADING_ICON,
} from '@maids/cc-erp-services';
import { map, Observable } from 'rxjs';
import { API } from 'src/environments/api';

@Injectable({
  providedIn: 'root',
})
export class AddMaidRefundService {
  constructor(
    @Inject(CCBackendEndpoint) private _api: string,
    private _http: HttpClient
  ) {}
  AMRgetHouseMaidOptions(
    page: number,
    size: number = 20,
    search: string = ''
  ): Observable<any> {
    const context = new HttpContext().set(REQ_SHOW_LOADING_ICON, false);
    return this._http
      .get(`${this._api}/${API.AMRgetHouseMaidOptions}`, {
        params: { search, page, size },
        context,
      })
      .pipe(
        map((res: any) => {
          return res.content.map((item: any) => {
            return {
              text: item.label,
              id: item.id,
            };
          });
        })
      );
  }
  AMRgetReferredMaidOptions(
    page: number,
    size: number = 20,
    search: string = ''
  ): Observable<any> {
    const context = new HttpContext().set(REQ_SHOW_LOADING_ICON, false);
    return this._http
      .get(`${this._api}/${API.AMRgetReferredMaidOptions}`, {
        params: { search, page, size },
        context,
      })
      .pipe(
        map((res: any) => {
          return res.content.map((item: any) => {
            return {
              text: item.name,
              id: item.id,
            };
          });
        })
      );
  }
  getCheckSpentThirtyDays(id: number): Observable<any> {
    return this._http.get(`${this._api}/${API.getCheckSpentThirtyDays}`, {
      params: { housemaid: id },
    });
  }
  getMaidPaymentValidation(id: number, currentDate: any): Observable<any> {
    return this._http.get(`${this._api}/${API.getMaidPaymentValidation}`, {
      params: { id: id, payrollMonthDate: currentDate },
    });
  }
  getPurposesPicklist(apiName: any): Observable<any> {
    return this._http
      .get(`${this._api}/${API.getPurposesPicklist}/${apiName}`)
      .pipe(
        map((res: any) => {
          return res.map((item: any) => {
            return {
              text: item.name,
              id: item.id,
              code: item?.code,
            };
          });
        })
      );
  }
  addMaidRefund(payload: any): Observable<any> {
    return this._http.post(`${this._api}/${API.addMaidRefund}`, payload);
  }
  AMRgetExpenseForPage(): Observable<any> {
    return this._http
      .get(`${this._api}/${API.AMRgetExpenseForPage}`, {
        params: { page: 'MAID_PAYMENT' },
      })
      .pipe(
        map((res: any) =>
          res.map((item: any) => ({ text: item.label, id: item.id }))
        )
      );
  }
  AMRgetPaymentsMethodsByExpense(val: any): Observable<any> {
    return this._http.get(
      `${this._api}/${API.AMRgetPaymentsMethodsByExpense}/${val}`
    );
  }
  AMRgetPublicParameterByCode(code: string): Observable<any> {
    return this._http.get(`${this._api}/${API.AMRgetPublicParameterByCode}`, {
      params: { code: code },
    });
  }
  checkToCreateVacationRequest(id: number): Observable<any> {
    return this._http.get(`${this._api}/${API.checkToCreateVacationRequest}`, {
      params: { maid_id: id },
    });
  }
  gethousemaidvacationsinfo(id: number): Observable<any> {
    return this._http.get(
      `${this._api}/${API.gethousemaidvacationsinfo}/${id}`
    );
  }
  getCashiersOptions(
    page: number,
    size: number = 50,
    search: string = ''
  ): Observable<any> {
    const context = new HttpContext().set(REQ_SHOW_LOADING_ICON, false);
    return this._http
      .get(`${this._api}/${API.getCashiersOptions}`, {
        params: { page, size, search },
        context,
      })
      .pipe(
        map((res: any) => {
          return res.content.map((item: any) => ({
            id: item.id,
            text: item.label,
          }));
        })
      );
  }
}
