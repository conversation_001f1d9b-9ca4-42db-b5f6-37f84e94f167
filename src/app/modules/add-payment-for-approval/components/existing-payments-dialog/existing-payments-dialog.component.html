<div cc-std-dialog>
  <cc-dialog-header>
    <h1 cc-dialog-title class="text-left text-danger">Error</h1>
    <a role="button" type="button" cc-icon-button cc-dialog-close-button cc-dialog-close icon="close"></a>
  </cc-dialog-header>
  <cc-dialog-content class="px-3">
    <div class="row">
      <div class="col-md-12 text-left">
        <p>You can not add this monthly DD. This contract already has an adjustment payment that covers the same month
          as the one you want to add</p>
      </div>
    </div>
    <cc-datagrid
      class="w-100"
      [data]="records ?? []"
      [columns]="gridCols"
      [pageOnFront]="false"
      [stickyHeader]="true"
      [showColumnMenuButton]="true"
      [showColumnMenuHeader]="false"
      [columnMenuButtonIcon]="'settings'"
      [columnMovable]="true"
    ></cc-datagrid>
  </cc-dialog-content>
  <cc-dialog-actions>
    <button cc-raised-button (click)="close()">Close</button>
  </cc-dialog-actions>
</div>
