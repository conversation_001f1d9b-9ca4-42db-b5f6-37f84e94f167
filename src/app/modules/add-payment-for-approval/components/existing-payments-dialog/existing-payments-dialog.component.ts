import {ChangeDetectorRef, Component, Inject, OnInit} from '@angular/core';
import {CC_DIALOG_DATA, CCDialogRef} from "@maids/cc-lib/dialog";
import {CCGridColumn} from "@maids/cc-lib/datagrid";


@Component({
  selector: 'app-existing-payments-dialog',
  templateUrl: './existing-payments-dialog.component.html',
  styleUrls: ['./existing-payments-dialog.component.scss']
})
export class ExistingPaymentsDialogComponent implements OnInit {

  records: any;
  constructor(@Inject(CC_DIALOG_DATA) public data: any,
              private cdr: ChangeDetectorRef,
              private dialogRef: CCDialogRef<ExistingPaymentsDialogComponent>) {
  }

  ngOnInit(): void {
    this.records = this.data?.records;
  }

  gridCols: CCGridColumn[] = [
    { field: 'paymentId', header: 'DD Reference Number (Payment ID)' },
    { field: 'typeOfPayment', header: 'Type of Payment' },
    { field: 'amount', header: 'Amount' },
    { field: 'status', header: 'Status' },
    { field: 'paymentDate', header: 'Payment Date' },
  ];

  close() {
    this.dialogRef.close();
  }

}
