import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuditTodoListComponent } from './components/audit-todo-list/audit-todo-list.component';
import { ReplenishmentRequestsHistoryComponent } from './components/replenishment-requests-history/replenishment-requests-history.component';
import { ExpenseMissingTaxInvoicesComponent } from './components/expense-missing-tax-invoices/expense-missing-tax-invoices.component';
import { ExpenseMissingInvoicesDesmissedComponent } from './components/expense-missing-invoices-desmissed/expense-missing-invoices-desmissed.component';
import { EditsDoneByReconciliatorComponent } from './components/edits-done-by-reconciliator/edits-done-by-reconciliator.component';

const routes: Routes = [
  {
    path: '',
    component: AuditTodoListComponent,
    data: { label: '' },
  },
  {
    path: 'history',
    component: ReplenishmentRequestsHistoryComponent,
    data: { label: 'Replenishment Requests History' },
  },
  {
    path: 'expenses-missing-tax-invoices/:reportDate',
    component: ExpenseMissingTaxInvoicesComponent,
    data: { label: 'Expense Missing Tax Invoices' },
  },
  {
    path: 'expenses-missing-invoices-dismissed-by-reconciliator/:reportDate',
    component: ExpenseMissingInvoicesDesmissedComponent,
    data: { label: 'Expense Missing Invoices Dismissed By Reconciliator' },
  },
  {
    path: 'edits-done-by-reconciliator/:reportDate',
    component: EditsDoneByReconciliatorComponent,
    data: { label: 'Edits Done By Reconciliator' },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AuditManagerRoutingModule {}
