import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { AuditManagerRoutingModule } from './audit-manager-routing.module';
import { AuditTodoListComponent } from './components/audit-todo-list/audit-todo-list.component';
import { ReplenishmentRequestsHistoryComponent } from './components/replenishment-requests-history/replenishment-requests-history.component';
import { AddReplenishmentRequestComponent } from './components/add-replenishment-request/add-replenishment-request.component';
import {
  CCDatepickerModule,
  CCDaterangePickerModule,
} from '@maids/cc-lib/date';
import { CCDatagridModule } from '@maids/cc-lib/datagrid';
import { CCInputModule } from '@maids/cc-lib/input';
import { CCSelectInputModule } from '@maids/cc-lib/select-input';
import { CCButtonModule } from '@maids/cc-lib/button';
import { CCDividerModule } from '@maids/cc-lib/divider';
import { CCDialogModule } from '@maids/cc-lib/dialog';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ExpenseMissingTaxInvoicesComponent } from './components/expense-missing-tax-invoices/expense-missing-tax-invoices.component';
import { ExpenseMissingInvoicesDesmissedComponent } from './components/expense-missing-invoices-desmissed/expense-missing-invoices-desmissed.component';
import { EditsDoneByReconciliatorComponent } from './components/edits-done-by-reconciliator/edits-done-by-reconciliator.component';
import { CCIconModule } from '@maids/cc-lib/icon';

@NgModule({
  declarations: [
    AuditTodoListComponent,
    ReplenishmentRequestsHistoryComponent,
    AddReplenishmentRequestComponent,
    ExpenseMissingTaxInvoicesComponent,
    ExpenseMissingInvoicesDesmissedComponent,
    EditsDoneByReconciliatorComponent,
  ],
  imports: [
    CommonModule,
    AuditManagerRoutingModule,
    CCDaterangePickerModule,
    CCDatagridModule,
    CCInputModule,
    CCSelectInputModule,
    CCButtonModule,
    CCDatepickerModule,
    ReactiveFormsModule,
    CCDividerModule,
    CCDialogModule,
    CCIconModule,
    FormsModule,
  ],
})
export class AuditManagerModule {}
