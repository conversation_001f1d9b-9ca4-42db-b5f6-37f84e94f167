import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AddReplenishmentRequestComponent } from './add-replenishment-request.component';

describe('AddReplenishmentRequestComponent', () => {
  let component: AddReplenishmentRequestComponent;
  let fixture: ComponentFixture<AddReplenishmentRequestComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ AddReplenishmentRequestComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AddReplenishmentRequestComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
