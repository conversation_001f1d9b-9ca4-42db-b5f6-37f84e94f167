import { Component, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { AuditManagerService } from '../../services/audit-manager.service';
import { SelectOption } from '@maids/cc-lib/select-input';
import { PaginationRequest } from '@maids/cc-lib/common';
import { Observable, map } from 'rxjs';
import { CCNotificationService } from '@maids/cc-lib/services';
import { CCDialogRef } from '@maids/cc-lib/dialog';

@Component({
  selector: 'app-add-replenishment-request',
  templateUrl: './add-replenishment-request.component.html',
  styleUrls: ['./add-replenishment-request.component.scss'],
})
export class AddReplenishmentRequestComponent implements OnInit {
  replenishmentForm = this.formBuilder.group({
    bucketId: ['', [Validators.required]],
    amount: ['', [Validators.required]],
    cashier: [null],
    fromBucketId: [null],
  });
  readonly buckets = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.auditManagerService
      .searchBuckets(pageReq.page, pageReq.size, pageReq.searchString)
      .pipe(
        map((res: any) => {
          return res.content.map((bucket: any) => ({
            id: bucket.id,
            text: bucket.name,
            autoReplenishment: bucket.autoReplenishment,
            bucketType: bucket.bucketType.value,
          }));
        })
      );
  };
  readonly cashPayerOptions = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.auditManagerService.getCashiersOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  constructor(
    private formBuilder: FormBuilder,
    private auditManagerService: AuditManagerService,
    public readonly notification: CCNotificationService,
    private ccDialogRef: CCDialogRef<AddReplenishmentRequestComponent>
  ) {}

  ngOnInit(): void {
    this.replenishmentForm.controls['bucketId'].valueChanges.subscribe(
      (val: any) => {
        if (!val?.autoReplenishment) {
          this.replenishmentForm.controls['fromBucketId'].addValidators([
            Validators.required,
          ]);
          this.replenishmentForm.controls[
            'fromBucketId'
          ].updateValueAndValidity();
        } else {
          this.replenishmentForm.controls['fromBucketId'].clearValidators();
          this.replenishmentForm.controls[
            'fromBucketId'
          ].updateValueAndValidity();
        }
        if (val?.bucketType == 'CASH_BOX') {
          this.replenishmentForm.controls['cashier'].addValidators([
            Validators.required,
          ]);
          this.replenishmentForm.controls['cashier'].updateValueAndValidity();
        } else {
          this.replenishmentForm.controls['cashier'].clearValidators();
          this.replenishmentForm.controls['cashier'].updateValueAndValidity();
        }
      }
    );
  }
  sendRequest() {
    let payload;
    payload = {
      body: {
        amount: this.replenishmentForm.value.amount,
        fromBucketId: this.replenishmentForm.value.fromBucketId
          ? this.replenishmentForm.value.fromBucketId
          : null,
        cashierId: this.replenishmentForm.value.cashier
          ? this.replenishmentForm.value.cashier
          : null,
      },
    };
    this.auditManagerService
      .addApprovedRequest(this.replenishmentForm.value.bucketId.id, payload)
      .subscribe({
        next: (res: any) => {
          this.ccDialogRef.close(true);
          this.notification.notifySuccess('Sent Successfully', 2000);
        },
      });
  }
}
