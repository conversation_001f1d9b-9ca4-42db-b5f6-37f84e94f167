import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Observable } from 'rxjs';
import { AuditManagerService } from '../../services/audit-manager.service';
import { ExpenseModel } from '../../model/expense-model';
import { MediaService } from '@maids/cc-lib/services';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { Attachment } from '@maids/cc-lib/common';

@Component({
  selector: 'app-expense-missing-invoices-desmissed',
  templateUrl: './expense-missing-invoices-desmissed.component.html',
  styleUrls: ['./expense-missing-invoices-desmissed.component.scss'],
})
export class ExpenseMissingInvoicesDesmissedComponent implements OnInit {
  reportDate = this.route.snapshot.params['reportDate'];
  report$!: Observable<ExpenseModel[]>;
  constructor(
    private route: ActivatedRoute,
    private auditManagerService: AuditManagerService,
    private mediaService: MediaService
  ) {}
  gridCols: CCGridColumn[] = [
    { field: 'transactionId', header: 'Transaction ID' },
    { field: 'expenseToPost.label', header: 'Expense' },
    { field: 'fromBucket.label', header: 'Bucket' },
    { field: 'beneficiaryName', header: 'Beneficiary' },
    { field: 'paymentMethod', header: 'Payment Method' },
    { field: 'amount', header: 'Amount' },
    { field: 'description', header: 'Description' },
  ];
  ngOnInit(): void {
    if (this.reportDate) {
      this.report$ = this.auditManagerService.missingInvoiceDismissed(
        this.reportDate
      );
    }
  }
}
