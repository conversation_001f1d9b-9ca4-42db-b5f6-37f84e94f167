import { Component, OnInit } from '@angular/core';
import { map, Observable, tap } from 'rxjs';
import { AuditManagerService } from '../../services/audit-manager.service';
import { FormBuilder } from '@angular/forms';
import { PaginationRequest } from '@maids/cc-lib/common';
import { SelectOption } from '@maids/cc-lib/select-input';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { PageEvent } from '@angular/material/paginator';

@Component({
  selector: 'app-replenishment-requests-history',
  templateUrl: './replenishment-requests-history.component.html',
  styleUrls: ['./replenishment-requests-history.component.scss'],
})
export class ReplenishmentRequestsHistoryComponent implements OnInit {
  replenishmentRequests: any;
  filterForm = this.formBuilder.group({
    bucketId: [''],
    fromDate: [''],
    toDate: [''],
  });
  readonly buckets = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.auditManagerService
      .searchBuckets(pageReq.page, pageReq.size, pageReq.searchString)
      .pipe(
        map((res: any) => {
          return res.content.map(
            (bucket: any) =>
              ({
                id: bucket.id,
                text: bucket.name,
              } as SelectOption)
          );
        })
      );
  };
  gridCols: CCGridColumn[] = [
    { field: 'bucket.name', header: 'Bucket' },
    { field: 'fillFrom.label', header: 'Filled From' },
    { field: 'amount', header: 'Amount' },
    { field: 'requestDate', header: 'Date' },
    { field: 'status.label', header: 'Status' },
  ];
  constructor(
    private auditManagerService: AuditManagerService,
    private formBuilder: FormBuilder
  ) {}

  ngOnInit(): void {
    this.getReplenishmentRequests();
  }
  getReplenishmentRequests(page: number = 0, size: number = 20) {
    const filters: any = {
      page: page,
      size: size,
    };
    const bucketId = this.filterForm.controls['bucketId'].value;
    const fromDate = this.filterForm.controls['fromDate'].value;
    const toDate = this.filterForm.controls['toDate'].value;
    if (bucketId) {
      filters.bucketId = bucketId;
    }
    if (fromDate) {
      filters.fromDate = fromDate;
    }
    if (toDate) {
      filters.toDate = toDate;
    }
    this.auditManagerService
      .getReplenishmentRequests(filters)
      .subscribe((res: any) => {
        this.replenishmentRequests = res;
      });
  }
  getNextPage(event: PageEvent) {
    this.getReplenishmentRequests(event.pageIndex, event.pageSize);
  }
}
