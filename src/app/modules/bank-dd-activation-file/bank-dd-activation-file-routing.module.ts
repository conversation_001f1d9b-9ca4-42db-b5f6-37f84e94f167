import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ImportNewFileComponent } from './components/import-new-file/import-new-file.component';
import { DdFileComponent } from './components/dd-file/dd-file.component';
const routes: Routes = [
  {
    path: '',
    component: ImportNewFileComponent,
    data: { label: '' },
  },
  {
    path: ':id',
    component: DdFileComponent,
    data: {
      label: 'Importing Bank Direct Debit Activation File',
      pageCode: 'accounting_automation-importing-file-paymentFile',
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class BankDdActivationFileRoutingModule {}
