import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { BankDdActivationFileService } from '../../services/bank-dd-activation-file.service';
import { CCDialogRef } from '@maids/cc-lib/dialog';
import * as moment from 'moment';
import { CCNotificationService } from '@maids/cc-lib/services';
import { CCFileUploaderConfig } from '@maids/cc-lib/file-uploader';
@Component({
  selector: 'app-import-file-dialog',
  templateUrl: './import-file-dialog.component.html',
  styleUrls: ['./import-file-dialog.component.scss'],
})
export class ImportFileDialogComponent implements OnInit {
  formGroup = this.formBuilder.group({
    date: ['', Validators.required],
    time: [moment(new Date().getTime()).format('HH:mm'), Validators.required],
    attachments: ['', Validators.required],
  });
  fileUploaderconfig: CCFileUploaderConfig = {
    maxFilesize: 10,
    acceptedFiles: '.xlsx,.xls,.csv',
  };
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private formBuilder: FormBuilder,
    private bankDdActivationFileService: BankDdActivationFileService,
    private ccDialogRef: CCDialogRef<ImportFileDialogComponent>,
    public readonly notifications: CCNotificationService
  ) {}

  ngOnInit(): void {}
  import() {
    let payload: any;
    payload = {
      date: this.formGroup.value.date + ' ' + this.formGroup.value.time + ':00',
      attachments: [{ id: this.formGroup.value.attachments[0].id }],
    };
    this.bankDdActivationFileService.createDDFile(payload).subscribe({
      next: (res: any) => {
        this.ccDialogRef.close(true);
        this.notifications.notifySuccess('Success');
      },
      error: (err: any) => {
        this.notifications.notifyError(err.error.message);
      },
    });
  }
}
