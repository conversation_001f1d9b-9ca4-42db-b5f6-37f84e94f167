<div class="acc-8362 px-2">
  <div class="row my-2 justify-content-end">
    <button cc-raised-button color="primary" (click)="importNew()">
      Import New DD Activation File
    </button>
  </div>
  <div class="row">
    <cc-datagrid
      class="my-2 w-100"
      [data]="records?.content ?? []"
      [columns]="gridCols"
      [length]="records?.totalElements ?? 0"
      [pageOnFront]="false"
      [pageIndex]="records?.number ?? 0"
      [pageSize]="records?.size ?? 0"
      [pageSizeOptions]="[20]"
      (page)="getNextPage($event)"
      [stickyHeader]="true"
      [showColumnMenuButton]="true"
      [showColumnMenuHeader]="false"
      [columnMenuButtonIcon]="'settings'"
      [columnMovable]="true"
      [cellTemplate]="{ file: file, fileParsed: fileParsed }"
    >
      <cc-grid-actions-list
        *ccActionData="let ctx of records?.content; row as row"
        [renderedActionsCount]="2"
        style="width: fit-content; gap: 8px"
      >
        <button
          *cc-action
          [disabled]="row.fileParsed == false && row.fileParsed.length != 0"
          cc-raised-button
          (click)="details(row.id)"
          color="primary"
        >
          Details
        </button>
        <button
          *cc-action
          cc-raised-button
          (click)="delete(row.id)"
        >
          Delete
        </button>
      </cc-grid-actions-list>
    </cc-datagrid>
    <ng-template #file let-row let-index="index" let-col="colDef">
      <a class="cc-secondary" (click)="downloadFile(row.uuid)"> {{ row.fileName }}</a>
    </ng-template>
    <ng-template #fileParsed let-row let-index="index" let-col="colDef">
      <ng-container *ngIf="row.fileParsed === true; else processing">
      </ng-container>
      <ng-template #processing>
        <div class="row align-items-center justify-content-center">
          <div class="col-md-auto">Processing</div>
          <div class="col-md-auto">
            <mat-spinner color="accent" [diameter]="30"></mat-spinner>
          </div>
        </div>
      </ng-template>
    </ng-template>
  </div>
</div>
