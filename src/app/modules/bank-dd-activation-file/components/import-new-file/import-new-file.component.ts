import { Component, OnInit, OnDestroy } from '@angular/core';
import { BankDdActivationFileService } from '../../services/bank-dd-activation-file.service';
import { ActivatedRoute, Router } from '@angular/router';
import { PageEvent } from '@angular/material/paginator';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { CCDialog } from '@maids/cc-lib/dialog';
import { ImportFileDialogComponent } from '../import-file-dialog/import-file-dialog.component';
@Component({
  selector: 'app-import-new-file',
  templateUrl: './import-new-file.component.html',
  styleUrls: ['./import-new-file.component.scss'],
})
export class ImportNewFileComponent implements OnInit, OnDestroy {
  records: any;
  page: number = 0;
  size: number = 20;
  private intervalId: any;
  constructor(
    private bankDdActivationFileService: BankDdActivationFileService,
    private route: ActivatedRoute,
    private mediaService: MediaService,
    private router: Router,
    public readonly notifications: CCNotificationService,
    private ccDialog: CCDialog
  ) {}

  ngOnInit(): void {
    this.getProjectedList(0, 20, true);
    this.intervalId = setInterval(() => {
      this.getProjectedList(this.page, this.size, false);
    }, 15000);
  }

  ngOnDestroy(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  }
  getProjectedList(
    page: number = 0,
    size: number = 20,
    withloading: boolean = true
  ) {
    {
      this.bankDdActivationFileService
        .getDDProjectedList({ page, size }, withloading)
        .subscribe((res) => {
          this.records = res;
        });
    }
  }
  getNextPage(event: PageEvent) {
    this.getProjectedList(event.pageIndex, event.pageSize, true);
    this.page = event.pageIndex;
    this.size = event.pageSize;
  }
  gridCols: CCGridColumn[] = [
    { field: 'date', header: 'Date Time' },
    {
      field: 'status',
      header: 'Status',
      formatter: (row: any) => {
        if (row.status === 'DONE') {
          return '<span class="cc-secondary">Done</span>';
        }
        return 'Under Processing';
      },
    },
    { field: 'fileParsed', header: '' },
    {
      field: 'file',
      header: 'File',
    },
  ];
  downloadFile(uuid: string) {
    this.mediaService.downloadFile(`public/download/${uuid}`);
  }
  importNew() {
    this.ccDialog
      .originalOpen(ImportFileDialogComponent, {})
      .afterClosed()
      .subscribe((res) => {
        if (res) {
          this.getProjectedList(0, 20, true);
        }
      });
  }
  details(id: number) {
    this.router.navigateByUrl(
      `/accounting/v2/payments-automation/import-new-file/ddFile/${id}`
    );
  }
  delete(id: number) {
    let payload: any;
    payload = {
      id: id,
      hidden: true,
    };
    this.ccDialog.confirm(
      '',
      'Are you sure you want to hide this file?',
      () => {
        this.bankDdActivationFileService
          .hideDDFile(payload)
          .subscribe((res) => {
            this.getProjectedList(0, 20, true);
            this.notifications.notifySuccess('Success');
          });
      }
    );
  }
}
