import { ComponentFixture, TestBed } from '@angular/core/testing';

import { MatchToCurrentDdComponent } from './match-to-current-dd.component';

describe('MatchToCurrentDdComponent', () => {
  let component: MatchToCurrentDdComponent;
  let fixture: ComponentFixture<MatchToCurrentDdComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ MatchToCurrentDdComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(MatchToCurrentDdComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
