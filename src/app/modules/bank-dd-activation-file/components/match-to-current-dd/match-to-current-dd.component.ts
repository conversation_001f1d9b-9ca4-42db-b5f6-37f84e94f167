import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { BankDdActivationFileService } from '../../services/bank-dd-activation-file.service';
import { PageEvent } from '@angular/material/paginator';
import { Sort } from '@angular/material/sort';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { CCDialogRef } from '@maids/cc-lib/dialog';
@Component({
  selector: 'app-match-to-current-dd',
  templateUrl: './match-to-current-dd.component.html',
  styleUrls: ['./match-to-current-dd.component.scss'],
})
export class MatchToCurrentDdComponent implements OnInit {
  records: any;
  selectedMatchCurrentDD: any[] = [];
  selectedRowId: any;

  currentPageMTC: number = 0;
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private bankDdActivationFileService: BankDdActivationFileService,
    private ccDialogRef: CCDialogRef<MatchToCurrentDdComponent>
  ) {}

  ngOnInit(): void {
    this.GetMatchToCurrentDD(this.data);
    console.log(this.data);
  }
  GetMatchToCurrentDD(element: any) {
    let searchFilter: any = {};
    let contractParts = element.contract.split('-');
    if (
      element.amount &&
      element.contract.length > 0 &&
      contractParts.length > 1
    ) {
      let ddMethodFilter =
        element.contract[0] == 'M'
          ? {
              field: 'ddMethod',
              operation: '=',
              fieldType: 'com.magnamedia.module.type.DirectDebitMethod',
              value: '{"id":"MANUAL","label":"Manual"}',
              valueField: '',
            }
          : {
              field: 'ddMethod',
              operation: '=',
              fieldType: 'com.magnamedia.module.type.DirectDebitMethod',
              value: '{"id":"AUTOMATIC","label":"Automatic"}',
              valueField: '',
            };
      searchFilter = {
        and: true,
        right: {
          and: true,
          right: {
            field: 'directDebit.contractPaymentTerm.contract.id',
            operation: 'Equals',
            fieldType: 'long',
            value: parseInt(contractParts[contractParts.length - 1]),
            valueField: null,
          },
          left: {
            field: 'amount',
            operation: 'Equals',
            fieldType: 'double',
            value: element.amount,
            valueField: null,
          },
        },
        left: {
          and: true,
          right: ddMethodFilter,
          left: {
            field: 'status',
            operation: '=',
            fieldType: 'com.magnamedia.module.type.DirectDebitFileStatus',
            value: '{"id":"SENT","label":"Sent"}',
            valueField: '',
          },
        },
      };
    }
    this.bankDdActivationFileService
      .GetMatchToCurrentDD(searchFilter)
      .subscribe((res: any) => {
        this.records = res;
      });
  }
  getNextPage(event: PageEvent) {
    this.bankDdActivationFileService.searchSubjectMTC.next({
      params: {
        page: event.pageIndex,
        size: event.pageSize,
        sort: this.bankDdActivationFileService.searchSubjectMTC.getValue()
          .params.sort,
      },
    });
    this.currentPageMTC = event.pageIndex;
    this.GetMatchToCurrentDD(this.data);
  }
  onSortChange(event: Sort) {
    this.bankDdActivationFileService.searchSubjectMTC.next({
      params: {
        page: this.bankDdActivationFileService.searchSubjectMTC.getValue()
          .params.page,
        size: this.bankDdActivationFileService.searchSubjectMTC.getValue()
          .params.size,
        sort: event.active + ',' + event.direction,
      },
    });
    this.GetMatchToCurrentDD(this.data);
  }
  MTCGridCols: CCGridColumn[] = [
    { field: 'select', header: 'Select' },
    { field: 'rowNum', header: 'Row Number' },
    {
      field: 'accountName',
      header: 'Account Name',
      formatter(rowData, colDef) {
        return rowData.accountName;
      },
      sortable: true,
      sortProp: {
        arrowPosition: 'before',
        id: 'accountName',
        start: 'desc',
      },
    },
    {
      field: 'bankName',
      header: 'Bank',
      formatter(rowData, colDef) {
        return rowData.directDebit && rowData.directDebit.contractPaymentTerm
          ? rowData.directDebit.contractPaymentTerm.bankName
          : '';
      },
      sortable: true,
      sortProp: {
        arrowPosition: 'before',
        id: 'directDebit.contractPaymentTerm.bankName',
        start: 'desc',
      },
    },
    {
      field: 'applicationId',
      header: 'DD ID',
      formatter(rowData, colDef) {
        return rowData.applicationId;
      },
      sortable: true,
      sortProp: {
        arrowPosition: 'before',
        id: 'applicationId',
        start: 'desc',
      },
    },
    {
      field: 'amount',
      header: 'Amount',
      formatter(rowData, colDef) {
        return rowData.amount;
      },
      sortable: true,
      sortProp: {
        arrowPosition: 'before',
        id: 'amount',
        start: 'desc',
      },
    },
    {
      field: 'paymentType',
      header: 'DD Type',
      formatter(rowData, colDef) {
        return rowData.directDebit && rowData.directDebit.paymentType
          ? rowData.directDebit.paymentType.name
          : '';
      },
    },
    {
      field: 'startDate',
      header: 'DD Start Date',
      formatter(rowData, colDef) {
        return rowData.startDate;
      },
      sortable: true,
      sortProp: {
        arrowPosition: 'before',
        id: 'startDate',
        start: 'desc',
      },
    },
    {
      field: 'expiryDate',
      header: 'DD Expiry Date',
      formatter(rowData, colDef) {
        return rowData.expiryDate;
      },
      sortable: true,
      sortProp: {
        arrowPosition: 'before',
        id: 'expiryDate',
        start: 'desc',
      },
    },
  ];
  SaveMatchToCurrentDD() {
    this.bankDdActivationFileService
      .SaveMatchToCurrentDD(this.data.id,this.selectedRowId)
      .subscribe((res: any) => {
        this.ccDialogRef.close(true);
      });
  }
  onCheckboxChange(id: any, isChecked: boolean): void {
    if (isChecked) {
      // Unselect all other rows
      this.selectedRowId = id;
      this.records?.content.forEach((row: any) => {
        this.selectedMatchCurrentDD[row.id] = row.id === id;
      });
    } else {
      // Deselect the current row
      this.selectedRowId = null;
      this.selectedMatchCurrentDD[id] = false;
    }
  }
}
