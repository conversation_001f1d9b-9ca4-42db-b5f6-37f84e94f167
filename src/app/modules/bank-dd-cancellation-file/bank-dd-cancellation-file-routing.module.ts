import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { BankDdCancellationFileListComponent } from './components/bank-dd-cancellation-file-list/bank-dd-cancellation-file-list.component';
import { ImportingBankDdCancellationFileComponent } from './components/importing-bank-dd-cancellation-file/importing-bank-dd-cancellation-file.component';

const routes: Routes = [
  {
    path: '',
    component: BankDdCancellationFileListComponent,
    data: { pageCode: 'accounting_bank-direct-debit-cancellation-file' },
  },
  {
    path: 'importing-bank-dd-cancellation-file/:id',
    component: ImportingBankDdCancellationFileComponent,
    data: {
      label: 'Importing Bank Direct Debit Cancellation File',
      pageCode: 'accounting_importing-bank-direct-debit-cancellation-file',
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class BankDdCancellationFileRoutingModule {}
