import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { BankDdCancellationFileRoutingModule } from './bank-dd-cancellation-file-routing.module';
import { BankDdCancellationFileListComponent } from './components/bank-dd-cancellation-file-list/bank-dd-cancellation-file-list.component';
import { ImportingBankDdCancellationFileComponent } from './components/importing-bank-dd-cancellation-file/importing-bank-dd-cancellation-file.component';
import { MatchedFilesComponent } from './components/matched-files/matched-files.component';
import { UnmatchedFilesComponent } from './components/unmatched-files/unmatched-files.component';
import { PreviouslyMatchedFilesComponent } from './components/previously-matched-files/previously-matched-files.component';
import { CCDatagridModule } from '@maids/cc-lib/datagrid';
import { CCDialogModule } from '@maids/cc-lib/dialog';
import { CCIconModule } from '@maids/cc-lib/icon';
import { CCButtonModule } from '@maids/cc-lib/button';
import { CCFileUploaderModule } from '@maids/cc-lib/file-uploader';
import { CCCheckboxModule } from '@maids/cc-lib/checkbox';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ImportNewComponent } from './components/import-new/import-new.component';
import { MatchedRejectedFilesComponent } from './components/matched-rejected-files/matched-rejected-files.component';
@NgModule({
  declarations: [
    BankDdCancellationFileListComponent,
    ImportingBankDdCancellationFileComponent,
    MatchedFilesComponent,
    UnmatchedFilesComponent,
    PreviouslyMatchedFilesComponent,
    ImportNewComponent,
    MatchedRejectedFilesComponent,
  ],
  imports: [
    CommonModule,
    BankDdCancellationFileRoutingModule,
    CCDatagridModule,
    CCDialogModule,
    CCIconModule,
    CCButtonModule,
    CCFileUploaderModule.forChild({}),
    CCCheckboxModule,
    MatProgressSpinnerModule,
    FormsModule,
    ReactiveFormsModule,
    MatProgressSpinnerModule,
  ],
})
export class BankDdCancellationFileModule {}
