<div class="d-flex my-2">
  <button cc-raised-button color="primary" (click)="import()">
    Import New DD Cancellation File
  </button>
</div>
<cc-datagrid
  class="my-2"
  [data]="files?.content"
  [columns]="gridCols"
  [length]="files?.totalElements"
  [pageOnFront]="false"
  [pageIndex]="files?.number"
  [pageSize]="files?.size"
  [pageSizeOptions]="[20]"
  (page)="getNextPage($event)"
  [stickyHeader]="true"
  [columnMovable]="true"
  [columnHideable]="true"
  [showColumnMenuButton]="true"
  [showColumnMenuHeader]="false"
  [columnMenuButtonIcon]="'settings'"
  [cellTemplate]="{ file: file, status: status }"
>
  <cc-grid-actions-list
    *ccActionData="let ctx of files?.content; row as row"
    style="width: max-content"
  >
    <button *cc-action cc-raised-button color="primary" (click)="details(row)">
      Details
    </button>
    <button *cc-action cc-raised-button color="accent" (click)="delete(row)">
      Delete
    </button>
  </cc-grid-actions-list>
</cc-datagrid>
<ng-template #file let-row let-index="index" let-col="colDef">
  <div style="max-width: 600px">
    <a
      class="cc-secondary desc-row"
      (click)="downloadFile(row.uuid, row.fileName)"
      *ngIf="row.fileName"
      >{{ row.fileName }}</a
    >
  </div>
</ng-template>
<ng-template #status let-row let-index="index" let-col="colDef">
  <div style="max-width: 600px">
    <span *ngIf="row.status == 'DONE'; else elseBlock">Done</span>
  </div>
</ng-template>
<ng-template #elseBlock class="row align-items-center justify-content-center">
  <div class="col-12 d-flex justify-content-center">
    <mat-spinner diameter="20"></mat-spinner>
  </div>
  <div class="col-12 d-flex justify-content-center">
    <span class="ml-2">Under Processing</span>
  </div>
</ng-template>
