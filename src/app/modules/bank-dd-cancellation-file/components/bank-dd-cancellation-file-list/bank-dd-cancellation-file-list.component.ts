import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Observable } from 'rxjs';
import { BandDdCacellationFileService } from '../../services/band-dd-cacellation-file.service';
import { CCDialog } from '@maids/cc-lib/dialog';
import { PageEvent } from '@angular/material/paginator';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { HttpClient } from '@angular/common/http';
import { API } from 'src/environments/api';
import { Router } from '@angular/router';
import { ImportNewComponent } from '../import-new/import-new.component';

@Component({
  selector: 'app-bank-dd-cancellation-file-list',
  templateUrl: './bank-dd-cancellation-file-list.component.html',
  styleUrls: ['./bank-dd-cancellation-file-list.component.scss'],
})
export class BankDdCancellationFileListComponent implements OnInit, OnDestroy {
  files: any | null = null;
  private refreshIntervalId: any;
  constructor(
    private bandDdCacellationFileService: BandDdCacellationFileService,
    private ccDialog: CCDialog,
    private mediaService: MediaService,
    private http: HttpClient,
    public readonly notification: CCNotificationService,
    private router: Router
  ) {}
  gridCols: CCGridColumn[] = [
    { field: 'date', header: 'Date Time' },
    { field: 'status', header: 'Status' },
    {
      field: 'oldestPendingForCancellationDD',
      header: 'Oldest Pending For Cancellation DD',
    },
    { field: 'file', header: 'File' },
  ];
  ngOnInit(): void {
    this.getBDDcancelationfiles();
    if (this.refreshIntervalId) {
      clearInterval(this.refreshIntervalId);
    }
    this.refreshIntervalId = setInterval(() => {
      this.getBDDcancelationfiles();
    }, 60000);
  }
  ngOnDestroy(): void {
    if (this.refreshIntervalId) {
      clearInterval(this.refreshIntervalId);
    }
  }
  getBDDcancelationfiles(page: number = 0, size: number = 20) {
    this.bandDdCacellationFileService
      .getBDDcancelationfiles({ page, size })
      .subscribe((res: any) => {
        this.files = res;
      });
  }
  getNextPage(e: PageEvent) {
    this.getBDDcancelationfiles(e.pageIndex, e.pageSize);
  }
  import() {
    this.ccDialog
      .originalOpen(ImportNewComponent, { data: null })
      .afterClosed()
      .subscribe((res: boolean) => {
        if (res) {
          this.getBDDcancelationfiles();
        }
      });
  }
  downloadFile(uuid: string, fileName: string) {
    let path = `public/download/${uuid}`;
    this.http
      .get(`${API.base}/${path}`, { responseType: 'blob' })
      .subscribe((res: any) => {
        const blob = new Blob([res], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        document.body.appendChild(a);
        a.setAttribute('style', 'display: none');
        a.href = url;
        a.download = `${fileName}.csv`;
        a.click();
        window.URL.revokeObjectURL(url);
        a.remove();
      });
  }
  delete(element: any) {
    let payload: any;
    payload = {
      id: element.id,
      hidden: true,
    };
    this.ccDialog.confirm(
      '',
      'Are you sure you want to delete this Record?',
      () => {
        this.bandDdCacellationFileService
          .deleteBDDcancelationfiles(payload)
          .subscribe({
            next: (res: any) => {
              this.getBDDcancelationfiles();
              this.notification.notifySuccess('Success');
            },
            error: (err: any) => {
              this.notification.notifySuccess(err.error.message);
            },
          });
      }
    );
  }
  details(element: any) {
    this.router.navigateByUrl(
      `accounting/v2/bank-dd-cancellation-file/importing-bank-dd-cancellation-file/${element.id}`
    );
  }
}
