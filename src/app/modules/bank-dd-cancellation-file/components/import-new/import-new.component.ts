import { Component, OnInit } from '@angular/core';
import { CCDialogRef } from '@maids/cc-lib/dialog';
import { BandDdCacellationFileService } from '../../services/band-dd-cacellation-file.service';
import { FormBuilder, Validators } from '@angular/forms';
import { CCNotificationService } from '@maids/cc-lib/services';
import { CCFileUploaderConfig } from '@maids/cc-lib/file-uploader';

@Component({
  selector: 'app-import-new',
  templateUrl: './import-new.component.html',
  styleUrls: ['./import-new.component.scss'],
})
export class ImportNewComponent implements OnInit {
  formGroup = this.formBuilder.group({
    excel_file: [null, [Validators.required]],
  });
  constructor(
    private ccDialogRef: CCDialogRef<ImportNewComponent>,
    private bandDdCacellationFileService: BandDdCacellationFileService,
    private formBuilder: FormBuilder,
    public readonly notifications: CCNotificationService
  ) {}
  config: CCFileUploaderConfig = {
    maxFilesize: 25,
  };
  ngOnInit(): void {}
  import() {
    let payload: any;
    payload = {
      attachments: [{ id: this.formGroup.controls['excel_file'].value[0].id }],
      date: this.formGroup.controls['excel_file'].value[0].creationDate,
    };
    this.bandDdCacellationFileService
      .createBDDcancelationfiles(payload)
      .subscribe({
        next: (res: any) => {
          this.notifications.notifySuccess(res);
          this.ccDialogRef.close(true);
        },
        error: (err: any) => {
          this.notifications.notifyError(err.error.message);
        },
      });
  }
}
