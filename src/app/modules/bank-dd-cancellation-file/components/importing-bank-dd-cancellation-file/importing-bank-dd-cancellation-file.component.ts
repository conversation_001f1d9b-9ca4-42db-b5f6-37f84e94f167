import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { MediaService } from '@maids/cc-lib/services';

@Component({
  selector: 'app-importing-bank-dd-cancellation-file',
  templateUrl: './importing-bank-dd-cancellation-file.component.html',
  styleUrls: ['./importing-bank-dd-cancellation-file.component.scss'],
})
export class ImportingBankDdCancellationFileComponent implements OnInit {
  id: number = 0;
  constructor(
    private route: ActivatedRoute,
    private mediaService: MediaService
  ) {}

  ngOnInit(): void {
    this.id = this.route.snapshot.params['id'];
  }
  exportAllToExcel() {
    this.exportToExcel('MATCHED');
    this.exportToExcel('NOT_MATCHED');
    this.exportToExcel('PREV_MATCHED');
  }
  exportToExcel(matched: string) {
    var url = matched
      ? 'bddcancelationfiles/getrecords/csv/' +
        this.route.snapshot.params['id'] +
        '?matched=' +
        matched
      : 'bddcancelationfiles/getrecords/csv/' +
        this.route.snapshot.params['id'];
    this.mediaService.downloadFile('accounting/' + url);
  }
}
