<div class="acc-7878">
  <div class="d-flex justify-content-end my-2">
    <button
      class="col-md-auto"
      cc-raised-button
      [disabled]="total == 0"
      (click)="
        checkIfSelectedPageMatched()
          ? unSelectPageMatched()
          : selectPageMatched(true)
      "
      color="accent"
    >
      {{ checkIfSelectedPageMatched() ? "Unselect" : "Select" }} Page
    </button>
    <button
      cc-raised-button
      color="accent"
      class="col-md-auto ml-1"
      [disabled]="total == 0"
      (click)="selectAllMatched()"
    >
      {{ isSelectAllMatched ? "Unselect" : "Select" }} All
    </button>

    <button
      cc-raised-button
      [disabled]="selectedRowsCount <= 0"
      class="red col-md-auto mx-1"
      (click)="DDconfirm()"
      color="primary"
    >
      Confirm Changes
    </button>

    <button
      cc-raised-button
      color="accent"
      (click)="exportToExcel()"
      class="col-md-auto"
    >
      Export To Excel
    </button>
  </div>
  <div class="d-flex my-1">
    <div class="d-flex">
      <span style="font-weight: bold" class="mr-1">Matched and confirmed: </span>
      <span class="cc-secondary">{{ total }}</span>
      <span class="mx-2">,</span>
      <span style="font-weight: bold" class="mr-1">Selected Records: </span>
      <span class="cc-secondary"> {{ selectedRowsCount }}</span>
    </div>
  </div>
  <cc-datagrid
    class="my-2"
    [data]="records?.content ?? []"
    [columns]="gridCols"
    [length]="records?.totalElements ?? 0"
    [pageOnFront]="false"
    [pageIndex]="records?.number ?? 0"
    [pageSize]="records?.size ?? 0"
    [pageSizeOptions]="[20]"
    (page)="getNextPage($event)"
    [stickyHeader]="true"
    [columnMovable]="true"
    [columnHideable]="true"
    [showColumnMenuButton]="true"
    [showColumnMenuHeader]="false"
    [columnMenuButtonIcon]="'settings'"
    [cellTemplate]="{ select: select }"
  ></cc-datagrid>
  <ng-template #select let-row let-index="index" let-col="colDef">
    <cc-checkbox
      *ngIf="!row.processing"
      [(ngModel)]="selectedMatched[row.id]"
      (ngModelChange)="updateSelectedRowsCount()"
    ></cc-checkbox>
    <mat-spinner
      *ngIf="row.processing"
      [diameter]="30"
      color="accent"
    ></mat-spinner>
  </ng-template>
</div>
