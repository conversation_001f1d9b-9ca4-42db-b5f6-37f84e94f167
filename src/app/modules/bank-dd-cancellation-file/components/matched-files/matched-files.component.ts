import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import { BandDdCacellationFileService } from '../../services/band-dd-cacellation-file.service';
import { PageEvent } from '@angular/material/paginator';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { CCNotificationService } from '@maids/cc-lib/services';
import { PageableResponseModel } from '@maids/cc-lib/common';
import { BehaviorSubject, Observable } from 'rxjs';

@Component({
  selector: 'app-matched-files',
  templateUrl: './matched-files.component.html',
  styleUrls: ['./matched-files.component.scss'],
})
export class MatchedFilesComponent implements OnInit, OnDestroy {
  @Input() id: number = 0;
  records: any;
  total: number = 0;
  isSelectAllMatched = false;
  selectedMatched: boolean[] = [];
  selectedRowsCount = 0;
  private refreshIntervalId: any;
  currentPage = 0;

  constructor(
    private bandDdCacellationFileService: BandDdCacellationFileService,
    public readonly notifictaions: CCNotificationService
  ) {}
  ngOnInit(): void {
    this.getMatched();
    if (this.refreshIntervalId) {
      clearInterval(this.refreshIntervalId);
    }
    this.refreshIntervalId = setInterval(() => {
      this.getMatched(this.currentPage, 20);
    }, 15000);
  }
  ngOnDestroy(): void {
    if (this.refreshIntervalId) {
      clearInterval(this.refreshIntervalId);
    }
  }
  gridCols: CCGridColumn[] = [
    { field: 'select', header: 'Select' },
    {
      field: 'clientName',
      header: 'Family Name',
      formatter(rowData, colDef) {
        return rowData.directDebitFile.contractPaymentTerm.contract.client.name;
      },
    },
    {
      field: 'accountName',
      header: 'Account Name',
      formatter(rowData, colDef) {
        return rowData.directDebitFile.accountName;
      },
    },
    {
      field: 'applicationId',
      header: 'Contract',
      formatter(rowData, colDef) {
        return rowData.directDebitFile.applicationId;
      },
    },
    {
      field: 'amount',
      header: 'DD Amount',
      formatter(rowData, colDef) {
        return rowData.directDebitFile.amount;
      },
    },
    {
      field: 'presentmentDate',
      header: 'Presentation Date',
      formatter(rowData, colDef) {
        return rowData.directDebitFile.presentmentDate;
      },
    },
    {
      field: 'startDate',
      header: 'DD Start Date',
      formatter(rowData, colDef) {
        return rowData.directDebitFile.startDate;
      },
    },
    {
      field: 'expiryDate',
      header: 'DD Expiry Date',
      formatter(rowData, colDef) {
        return rowData.directDebitFile.expiryDate;
      },
    },
    {
      field: 'status',
      header: 'Status',
      formatter(rowData, colDef) {
        return rowData.directDebitFile.status.label;
      },
    },
  ];
  getMatched(page: number = 0, size: number = 20) {
    this.bandDdCacellationFileService
      .getRecordsBDDcancelationfiles(this.id, {
        matched: 'MATCHED',
        page,
        size,
      })
      .subscribe((res: any) => {
        this.total = res.totalElements;
        this.records = res;
        if (this.isSelectAllMatched) {
          this.selectPageMatched();
        }
      });
  }
  getNextPage(event: PageEvent) {
    this.currentPage = event.pageIndex;
    this.getMatched(event.pageIndex, event.pageSize);
  }
  selectAllMatched() {
    if (this.isSelectAllMatched) {
      this.unSelectAllMatched();
      return;
    }
    this.isSelectAllMatched = true;
    this.selectPageMatched();
  }
  unSelectAllMatched() {
    this.isSelectAllMatched = false;
    this.selectedMatched = [];
    this.selectedRowsCount=0
  }

  checkIfSelectedPageMatched() {
    let selected = true;
    this.records?.content.forEach((element: any) => {
      if (!this.selectedMatched[element.id]) {
        selected = false;
      }
    });
    return this.records?.content.length > 0 ? selected : false;
  }
  unSelectPageMatched() {
    this.isSelectAllMatched = false;
    this.selectedRowsCount=0
    this.records?.content.forEach((element: any) => {
      this.selectedMatched[element.id] = false;
    });
  }
  selectPageMatched(justPage?: any) {
    if (justPage) {
      this.isSelectAllMatched = false;
      this.selectedRowsCount=this.records?.content.length
    }else{
      this.selectedRowsCount=this.records.totalElements
      this.isSelectAllMatched=true
    }
    this.records?.content.forEach((element: any) => {
      this.selectedMatched[element.id] = true;
    });
  }
  checkIfAllMatched() {
    Object.keys(this.selectedMatched).forEach((element: any) => {
      if (!this.selectedMatched[element]) {
        this.isSelectAllMatched = false;
      }
    });
  }
  DDconfirm() {
    if (this.isSelectAllMatched) {
      this.bandDdCacellationFileService
        .confirmAllddcancelationfile({ id: this.id })
        .subscribe({
          next: (res: any) => {
            this.selectedMatched = [];
            this.getMatched();
            this.updateSelectedRowsCount();
            this.notifictaions.notifySuccess('Confirmed Successfully');
          },
          error: (err: any) => {
            this.notifictaions.notifyError(err.error.message);
          },
        });
    } else {
      this.bandDdCacellationFileService
        .confirmddcancelationfile(this.getSelectedMatched())
        .subscribe({
          next: (res: any) => {
            this.selectedMatched = [];
            this.updateSelectedRowsCount();
            this.getMatched();
            this.notifictaions.notifySuccess('Confirmed Successfully');
          },
          error: (err: any) => {
            this.notifictaions.notifyError(err.error.message);
          },
        });
    }
  }
  getSelectedMatched() {
    let selected: any[] = [];
    let keys = Object.keys(this.selectedMatched);
    keys.forEach((element: any) => {
      if (this.selectedMatched[element]) {
        selected.push(element);
      }
    });
    return selected;
  }
  exportToExcel() {
    this.bandDdCacellationFileService
      .exportDDCancelationFile(this.id, 'MATCHED')
      .subscribe({
        next: (res: any) => {
          this.bandDdCacellationFileService.downloadFile(
            res,
            'MATCHEDDD_Records.csv'
          );
        },
        error: (err: any) => {
          this.notifictaions.notifyError(err.error.message);
        },
      });
  }
  updateSelectedRowsCount() {
    this.selectedRowsCount = Object.values(this.selectedMatched || {}).filter(
      Boolean
    ).length;
  }
}
