import { Component, Input, OnInit, OnD<PERSON>roy } from '@angular/core';
import { BandDdCacellationFileService } from '../../services/band-dd-cacellation-file.service';
import { PageEvent } from '@angular/material/paginator';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { CCNotificationService } from '@maids/cc-lib/services';

@Component({
  selector: 'app-previously-matched-files',
  templateUrl: './previously-matched-files.component.html',
  styleUrls: ['./previously-matched-files.component.scss'],
})
export class PreviouslyMatchedFilesComponent implements OnInit, OnDestroy {
  @Input() id: number = 0;
  records: any | null = null;
  total: number = 0;
  private refreshIntervalId: any;
  currentPage = 0;
  constructor(
    private bandDdCacellationFileService: BandDdCacellationFileService,
    public readonly notifictaions: CCNotificationService
  ) {}
  gridCols: CCGridColumn[] = [
    {
      field: 'clientName',
      header: 'Family Name',
      formatter(rowData, colDef) {
        return rowData.directDebitFile.contractPaymentTerm.contract.client.name;
      },
    },
    {
      field: 'accountName',
      header: 'Account Name',
      formatter(rowData, colDef) {
        return rowData.directDebitFile.accountName;
      },
    },
    {
      field: 'applicationId',
      header: 'Contract',
      formatter(rowData, colDef) {
        return rowData.directDebitFile.applicationId;
      },
    },
    {
      field: 'amount',
      header: 'DD Amount',
      formatter(rowData, colDef) {
        return rowData.directDebitFile.amount;
      },
    },
    {
      field: 'presentmentDate',
      header: 'Presentation Date',
      formatter(rowData, colDef) {
        return rowData.directDebitFile.presentmentDate;
      },
    },
    {
      field: 'startDate',
      header: 'DD Start Date',
      formatter(rowData, colDef) {
        return rowData.directDebitFile.startDate;
      },
    },
    {
      field: 'expiryDate',
      header: 'DD Expiry Date',
      formatter(rowData, colDef) {
        return rowData.directDebitFile.expiryDate;
      },
    },
    {
      field: 'status',
      header: 'Status',
      formatter(rowData, colDef) {
        return rowData.directDebitFile.status.label;
      },
    },
  ];
  ngOnInit(): void {
    this.getPrevMatched();
    if (this.refreshIntervalId) {
      clearInterval(this.refreshIntervalId);
    }
    this.refreshIntervalId = setInterval(() => {
      this.getPrevMatched(this.currentPage, 20);
    }, 15000);
  }
  ngOnDestroy(): void {
    if (this.refreshIntervalId) {
      clearInterval(this.refreshIntervalId);
    }
  }
  getPrevMatched(page: number = 0, size: number = 20) {
    this.bandDdCacellationFileService
      .getRecordsBDDcancelationfiles(this.id, {
        matched: 'PREV_MATCHED',
        page,
        size,
      })
      .subscribe((res: any) => {
        this.total = res.totalElements;
        this.records = res;
      });
  }
  getNextPage(event: PageEvent) {
    this.currentPage = event.pageIndex;
    this.getPrevMatched(event.pageIndex, event.pageSize);
  }
  exportToExcel() {
    this.bandDdCacellationFileService
      .exportDDCancelationFile(this.id, 'PREV_MATCHED')
      .subscribe({
        next: (res: any) => {
          this.bandDdCacellationFileService.downloadFile(
            res,
            'PreviouslyMatched_Records.csv'
          );
        },
        error: (err: any) => {
          this.notifictaions.notifyError(err.error.message);
        },
      });
  }
}
