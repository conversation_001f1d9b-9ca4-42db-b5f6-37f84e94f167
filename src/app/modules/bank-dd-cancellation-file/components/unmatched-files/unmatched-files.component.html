<div class="acc-7878">
  <div class="d-flex justify-content-end my-2">
    
      <button
        cc-raised-button
        [disabled]="total == 0"
        (click)="
          checkIfSelectedPageUnMatched()
            ? unSelectPageUnMatched()
            : selectPageUnMatched(true)
        "
        class="col-md-auto"
        color="accent"
      >
        {{ checkIfSelectedPageUnMatched() ? "UnSelect" : "Select" }} Page
      </button>
    
      <button
        cc-raised-button
        [disabled]="total == 0"
        class="col-md-auto ml-1"
        (click)="selectAllUnMatched()"
        color="accent"
      >
        {{ isSelectAllUnMatched ? "Unselect All" : "Select All" }} ({{ total }})
      </button>
      <button
        cc-raised-button
        [disabled]="selectedRowsCount <= 0"
        class="red col-md-auto mx-1"
        (click)="dismiss()"
        color="primary"
      >
        Dismiss
      </button>
      <button class="col-md-auto" cc-raised-button color="accent" (click)="exportToExcel()">
        Export To Excel
      </button>
  </div>
  <div class="d-flex my-1">
      <span style="font-weight: bold" class="mr-1">UnMatched: </span>
    <span class="cc-secondary"> {{ total }}</span>
    <span class="mx-2">,</span>
    <span style="font-weight: bold" class="mr-1">Selected: </span>
    <span class="cc-secondary"> {{ selectedRowsCount }}</span>
  </div>
  <cc-datagrid
    class="my-2"
    [data]="records?.content ?? []"
    [columns]="gridCols"
    [length]="records?.totalElements ?? 0"
    [pageOnFront]="false"
    [pageIndex]="records?.number ?? 0"
    [pageSize]="records?.size ?? 0"
    [pageSizeOptions]="[20]"
    (page)="getNextPage($event)"
    [stickyHeader]="true"
    [columnMovable]="true"
    [columnHideable]="true"
    [showColumnMenuButton]="true"
    [showColumnMenuHeader]="false"
    [columnMenuButtonIcon]="'settings'"
    [cellTemplate]="{ select: select }"
  ></cc-datagrid>
  <ng-template #select let-row let-index="index" let-col="colDef">
    <cc-checkbox
      [(ngModel)]="selectedUnMatched[row.id]"
      (ngModelChange)="updateSelectedRowsCount()"
    ></cc-checkbox>
  </ng-template>
</div>
