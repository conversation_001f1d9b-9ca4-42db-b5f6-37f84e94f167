import { Component, Input, OnInit } from '@angular/core';
import { BandDdCacellationFileService } from '../../services/band-dd-cacellation-file.service';
import { PageEvent } from '@angular/material/paginator';
import { CCNotificationService } from '@maids/cc-lib/services';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { BehaviorSubject, Observable } from 'rxjs';
import { PageableResponseModel } from '@maids/cc-lib/common';

@Component({
  selector: 'app-unmatched-files',
  templateUrl: './unmatched-files.component.html',
  styleUrls: ['./unmatched-files.component.scss'],
})
export class UnmatchedFilesComponent implements OnInit {
  @Input() id: number = 0;

  records: any;
  total: number = 0;
  isSelectAllUnMatched: boolean = false;
  currentPageMatched: number = 0;
  unmatched: number = 0;
  selectedUnMatched: boolean[] = [];
  selectedRowsCount=0
  currentPage = 0;
  constructor(
    private bandDdCacellationFileService: BandDdCacellationFileService,
    public readonly notifictaions: CCNotificationService
  ) {}

  ngOnInit(): void {
    this.getNotMatched(this.currentPage, 20);
  }
  gridCols: CCGridColumn[] = [
    { field: 'select', header: 'Select' },
    {
      field: 'ddaRefNo',
      header: 'DD Reference Number',
    },
    { field: 'cancelReason', header: 'Cancellation Reason' },
    { field: 'directDebitFile.status.label', header: 'Status' },
  ];
  getNotMatched(page: number = 0, size: number = 20) {
    this.bandDdCacellationFileService
      .getRecordsBDDcancelationfiles(this.id, {
        matched: 'NOT_MATCHED',
        page,
        size,
      })
      .subscribe((res: any) => {
        this.records = res;
        this.total = res.totalElements;
        if (this.isSelectAllUnMatched) {
          this.selectPageUnMatched();
        }
      });
  }
  getNextPage(event: PageEvent) {
    this.currentPage = event.pageIndex;
    this.getNotMatched(event.pageIndex, event.pageSize);
  }
  selectAllUnMatched() {
    if (this.isSelectAllUnMatched) {
      this.unSelectAllUnMatched();
      return;
    }
    this.isSelectAllUnMatched = true;
    this.selectPageUnMatched();
  }
  unSelectAllUnMatched() {
    this.isSelectAllUnMatched = false;
    this.selectedUnMatched = [];
    this.selectedRowsCount=0
  }

  checkIfSelectedPageUnMatched() {
    let selected = true;
    this.records?.content.forEach((element: any) => {
      if (!this.selectedUnMatched[element.id]) {
        selected = false;
      }
    });
    return this.records?.content.length > 0 ? selected : false;
  }
  unSelectPageUnMatched() {
    this.isSelectAllUnMatched = false;
    this.selectedRowsCount=0
    this.records?.content.forEach((element: any) => {
      this.selectedUnMatched[element.id] = false;
    });
  }
  selectPageUnMatched(justPage?: any) {
    if (justPage) {
      this.isSelectAllUnMatched = false;
      this.selectedRowsCount=this.records?.content.length
    }else{
      this.selectedRowsCount=this.records.totalElements
      this.isSelectAllUnMatched=true
    }
    this.records?.content.forEach((element: any) => {
      this.selectedUnMatched[element.id] = true;
    });
  }
  checkIfAllUnMatched() {
    var keys = Object.keys(this.selectedUnMatched);
    keys.forEach((element: any) => {
      if (!this.selectedUnMatched[element]) {
        this.isSelectAllUnMatched = false;
      }
    });
  }
  exportToExcel() {
    this.bandDdCacellationFileService
      .exportDDCancelationFile(this.id, 'NOT_MATCHED')
      .subscribe({
        next: (res: any) => {
          this.bandDdCacellationFileService.downloadFile(
            res,
            'UNMATCHEDDD_Records.csv'
          );
        },
        error: (err: any) => {
          this.notifictaions.notifyError(err.error.message);
        },
      });
  }
  dismiss() {
    if (this.isSelectAllUnMatched) {
      this.bandDdCacellationFileService
        .dismissAllddBDcancelationfile({ id: this.id })
        .subscribe({
          next: (res: any) => {
            this.notifictaions.notifySuccess('Dismissed Successfully');
            this.selectedUnMatched = [];
            this.updateSelectedRowsCount()
            this.getNotMatched();
          },
          error: (err: any) => {
            this.notifictaions.notifyError(err.error.message);
          },
        });
    } else {
      this.bandDdCacellationFileService
        .bulkdeleteBDDcancelationfiles(this.getSelectedUnMatched())
        .subscribe({
          next: (res: any) => {
            this.notifictaions.notifySuccess('Dismissed Successfully');
            this.selectedUnMatched = [];
            this.updateSelectedRowsCount()
            this.getNotMatched();
          },
          error: (err: any) => {
            this.notifictaions.notifyError(err.error.message);
          },
        });
    }
  }
  getSelectedUnMatched() {
    let selected: any[] = [];
    let keys = Object.keys(this.selectedUnMatched);
    keys.forEach((element: any) => {
      if (this.selectedUnMatched[element]) {
        selected.push(element);
      }
    });
    return selected;
  }
  updateSelectedRowsCount(){
    this.selectedRowsCount = Object.values(this.selectedUnMatched || {}).filter(
      Boolean
    ).length;
  }
}
