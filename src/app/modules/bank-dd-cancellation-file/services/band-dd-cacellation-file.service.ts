import { HttpClient, HttpContext } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import {
  CCBackendEndpoint,
  REQ_SHOW_LOADING_ICON,
} from '@maids/cc-erp-services';
import { Observable } from 'rxjs';
import { API } from 'src/environments/api';

@Injectable({
  providedIn: 'root',
})
export class BandDdCacellationFileService {
  constructor(
    private _http: HttpClient,
    @Inject(CCBackendEndpoint) private _api: CCBackendEndpoint
  ) {}
  getBDDcancelationfiles(params: any): Observable<any> {
    return this._http.get(`${this._api}/${API.getBDDcancelationfiles}`, {params});
  }
  deleteBDDcancelationfiles(payload: any): Observable<any> {
    return this._http.post(
      `${this._api}/${API.deleteBDDcancelationfiles}`,
      payload
    );
  }
  getRecordsBDDcancelationfiles(id: number, params: any): Observable<any> {
    const context = new HttpContext().set(REQ_SHOW_LOADING_ICON, false);
    return this._http.get(
      `${this._api}/${API.getRecordsBDDcancelationfiles}/${id}`,
      { params, context }
    );
  }
  createBDDcancelationfiles(payload: any): Observable<any> {
    return this._http.post(
      `${this._api}/${API.createBDDcancelationfiles}`,
      payload
    );
  }
  confirmddcancelationfile(payload: any): Observable<any> {
    return this._http.post(`${this._api}/${API.confirmdd}`, payload);
  }
  confirmAllddcancelationfile(payload: any): Observable<any> {
    return this._http.post(`${this._api}/${API.confirmAlldd}`, payload);
  }
  exportDDCancelationFile(id: any, matched: string): Observable<any> {
    return this._http.get(
      `${this._api}/${API.exportDDCancelationFile}/${id}?matched=${matched}`,
      { responseType: 'blob' }
    );
  }
  bulkdeleteBDDcancelationfiles(payload: any): Observable<any> {
    return this._http.post(`${this._api}/${API.bulkdelete}`, payload);
  }
  dismissAllddBDcancelationfile(payload: any): Observable<any> {
    return this._http.post(`${this._api}/${API.dismissAlldd}`, payload);
  }
  downloadFile(data: Blob, fileName: string) {
    const url = window.URL.createObjectURL(data);
    const a = document.createElement('a');
    document.body.appendChild(a);
    a.setAttribute('style', 'display: none');
    a.href = url;
    a.download = fileName;
    a.click();
    window.URL.revokeObjectURL(url);
    a.remove();
  }
  approveRejectionByBankForDDs(payload: any): Observable<any> {
    return this._http.post(`${this._api}/${API.approveRejectionByBankForDDs}`, payload);
  }
  approveAllRejectionByBankForDDs(payload: any): Observable<any> {
    return this._http.post(`${this._api}/${API.approveAllRejectionByBankForDDs}`, payload);
  }
}
