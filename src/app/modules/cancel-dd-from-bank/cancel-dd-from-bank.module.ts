import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { CancelDdFromBankRoutingModule } from './cancel-dd-from-bank-routing.module';
import { CancelDdFromBankListComponent } from './components/cancel-dd-from-bank-list/cancel-dd-from-bank-list.component';
import { CancelDdFromSentToBankComponent } from './components/cancel-dd-from-sent-to-bank/cancel-dd-from-sent-to-bank.component';
import { CancelDdFormDownloadComponent } from './components/cancel-dd-form-download/cancel-dd-form-download.component';
import { CCAdvancedSearchModule } from '@maids/cc-lib/advanced-search';
import { CCDatagridModule } from '@maids/cc-lib/datagrid';
import { CCButtonModule } from '@maids/cc-lib/button';
import { CCDialogModule } from '@maids/cc-lib/dialog';
import { CCIconModule } from '@maids/cc-lib/icon';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CCCheckboxModule } from '@maids/cc-lib/checkbox';
import { CCInputModule } from '@maids/cc-lib/input';
import { CCSelectInputModule } from '@maids/cc-lib/select-input';
import { CCValidateByModule } from '@maids/cc-lib/validation';
import { MatDialogModule } from '@angular/material/dialog';
@NgModule({
  declarations: [
    CancelDdFromBankListComponent,
    CancelDdFromSentToBankComponent,
    CancelDdFormDownloadComponent,
  ],
  imports: [
    CommonModule,
    CancelDdFromBankRoutingModule,
    CCAdvancedSearchModule,
    CCDatagridModule,
    CCButtonModule,
    CCDialogModule,
    CCIconModule,
    CCCheckboxModule,
    CCInputModule,
    CCSelectInputModule,
    CCValidateByModule,
    MatDialogModule,
    ReactiveFormsModule,
    FormsModule,
  ],
})
export class CancelDdFromBankModule {}
