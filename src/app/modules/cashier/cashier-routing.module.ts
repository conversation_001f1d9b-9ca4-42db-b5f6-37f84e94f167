import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CashierComponent } from './components/cashier/cashier.component';
import { PayComponent } from './components/pay/pay.component';
import { CollectInvoiceComponent } from './components/collect-invoice/collect-invoice.component';

const routes: Routes = [
  { path: '', component: CashierComponent, data: { label: '' } },
  {
    path: 'pay/:id',
    component: PayComponent,
    data: { label: 'Cashier Pay', pageCode: 'ACCOUNTING__CashierPay' },
  },
  {
    path: 'collect-invoice/:id',
    component: CollectInvoiceComponent,
    data: {
      label: 'Cashier Collect Invoice',
      pageCode: 'ACCOUNTING__CashierCollectInvoice',
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class CashierRoutingModule {}
