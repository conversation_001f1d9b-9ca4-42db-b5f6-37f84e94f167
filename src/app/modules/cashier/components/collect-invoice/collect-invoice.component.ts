import { Component, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MediaService, CCNotificationService } from '@maids/cc-lib/services';
import { map, Observable } from 'rxjs';
import { CashierService } from '../../services/cashier.service';
import { CCPreviewAttachmentComponent } from '@maids/cc-lib/preview-attachment';
import { MatDialog } from '@angular/material/dialog';
import { CCValidatorFn } from '@maids/cc-lib/validation';
import { CCFileUploaderConfig } from '@maids/cc-lib/file-uploader';

@Component({
  selector: 'app-collect-invoice',
  templateUrl: './collect-invoice.component.html',
  styleUrls: ['./collect-invoice.component.scss'],
})
export class CollectInvoiceComponent implements OnInit {
  formGroup = this.formBuilder.group({
    invoice: [''],
    taxable: [''],
    vatAmount: [''],
    attachedValidVatInvoice: [''],
    vatInvoice: [''],
    invoiceAttached: [false],
  });
  hideQuestion: boolean = false;
  details: any | null = null;
  config: CCFileUploaderConfig = {
    maxFiles: 1,
    maxFilesize: 10,
  };
  constructor(
    private route: ActivatedRoute,
    private cashierService: CashierService,
    private mediaService: MediaService,
    private formBuilder: FormBuilder,
    private router: Router,
    private dialog: MatDialog,
    public readonly notification: CCNotificationService
  ) {}

  ngOnInit(): void {
    this.cashierService
      .paymentDetails(this.route.snapshot.params['id'])
      .subscribe((res: any) => {
        this.details = res;
        this.details.invoiceAttachment = res.attachments.find((_: any) => {
          return _.tag.includes('EXPENSE_REQUEST_INVOICE');
        });
        this.details.vatInvoiceAttachment = res.attachments.find((_: any) => {
          return _.tag.includes('EXPENSE_PAYMENT_VAT_INVOICE');
        });
        this.details.filteredAttachments = this.details.attachments.filter(
          (_: any) => {
            return !_.tag.includes('SIGNATURE');
          }
        );
        if (this.details.invoiceAttachment) {
          this.formGroup.controls['invoice'].setValue([
            this.details.invoiceAttachment,
          ]);
        }
        if (this.details.vatInvoiceAttachment) {
          this.formGroup.controls['vatInvoice'].setValue([
            this.details.vatInvoiceAttachment,
          ]);
        }
        setTimeout(() => {
          this.formGroup.controls['vatAmount'].setValue(this.details.vatAmount);
          this.formGroup.controls['invoiceAttached'].setValue(
            this.details.invoiceAttached !== ''
              ? this.details.invoiceAttached
              : ''
          );
          this.formGroup.controls['taxable'].setValue(
            this.details.taxable !== '' ? this.details.taxable : ''
          );
          this.formGroup.controls['attachedValidVatInvoice'].setValue(
            this.details.attachedValidVatInvoice !== ''
              ? this.details.attachedValidVatInvoice
              : ''
          );
        });
        if (
          !res.attachments.find((_: any) => {
            return _.tag.includes('EXPENSE_REQUEST_INVOICE');
          })
        ) {
          this.formGroup.controls['invoiceAttached'].setValue('');
          this.hideQuestion = true;
        }
      });
    this.handleConditionalValidators();
  }
  handleConditionalValidators() {
    this.formGroup.controls['taxable'].valueChanges.subscribe((taxable) => {
      const vatAmountControl = this.formGroup.controls['vatAmount'];
      const attachedValidVatInvoiceControl =
        this.formGroup.controls['attachedValidVatInvoice'];
      if (taxable && this.details.requiresInvoice) {
        vatAmountControl.setValidators(Validators.required);
        attachedValidVatInvoiceControl.setValidators(Validators.required);
      } else {
        vatAmountControl.clearValidators();
        vatAmountControl.reset();
        attachedValidVatInvoiceControl.clearValidators();
        attachedValidVatInvoiceControl.reset();
      }

      vatAmountControl.updateValueAndValidity();
      attachedValidVatInvoiceControl.updateValueAndValidity();
    });

    this.formGroup.controls['attachedValidVatInvoice'].valueChanges.subscribe(
      (validInvoice) => {
        const vatInvoiceControl = this.formGroup.controls['vatInvoice'];
        if (this.formGroup.controls['taxable'].value && !validInvoice) {
          vatInvoiceControl.setValidators(Validators.required);
        } else {
          vatInvoiceControl.clearValidators();
          vatInvoiceControl.reset();
        }

        vatInvoiceControl.updateValueAndValidity();
      }
    );
  }
  preview(row: any) {
    return this.cashierService.getFile(row.id).subscribe((res: any) => {
      this.previewFile(res, row.name);
    });
  }
  previewFile(data: any, fileName: string) {
    let blob = new Blob([data], { type: data.type });
    const blobUrl = URL.createObjectURL(blob);
    let file_type = data.type.split('/')[1];
    this.dialog.open(CCPreviewAttachmentComponent, {
      width: '100%',
      data: {
        url: blobUrl,
        blob: blob,
        type: file_type,
        filename: fileName,
      },
    });
  }
  save() {
    if (this.formGroup.valid) {
      let data: any = { ...this.details, ...this.formGroup.value };
      if (this.details.requiresInvoice) {
        data.invoiceAttached =
          data.invoiceAttached !== '' ? data.invoiceAttached : '';
        data.taxable = data.taxable !== '' ? data.taxable : '';
        data.attachedValidVatInvoice =
          data.attachedValidVatInvoice !== ''
            ? data.attachedValidVatInvoice
            : '';
      }
      if (!data.invoiceAttached && data.invoice && data.invoice[0].id) {
        data.attachments.push(data.invoice[0]);
      } else {
        delete data.invoice;
      }
      if (
        !data.attachedValidVatInvoice &&
        data.vatInvoice &&
        data.vatInvoice[0].id
      ) {
        data.attachments.push(data.vatInvoice[0]);
      } else {
        delete data.vatInvoice;
      }
      data.attachments.forEach((attachment: any) => ({
        id: attachment.id,
        tag: attachment.tag,
      }));
      let payload: any;
      payload = {
        ...this.formGroup.value,
        attachments: data.attachments,
      };
      this.cashierService
        .cashierCollectInvoice(this.route.snapshot.params['id'], payload)
        .subscribe({
          next: (res: any) => {
            this.notification.notifySuccess(
              'Cashier collect invoice successfully saved!',
              2000
            );
            this.router.navigateByUrl('/accounting/v2/cashier');
          },
          error: (err: any) => {
            this.notification.notifyError(err.error.message, 2000);
          },
        });
    }
  }
  test() {
    Object.keys(this.formGroup.controls).forEach((key: any) => {
      if (this.formGroup.controls[key].invalid) {
        console.log(key);
      }
    });
  }
}
