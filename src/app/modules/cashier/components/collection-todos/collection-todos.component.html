<div class="acc-7779">
  <div class="row mx-1 mt-2">
    <div class="col-md-auto">
      <div class="row">
        <span class="bold-font">Collection to-dos</span>
        <span class="cc-secondary ml-1">
          ({{ todos?.totalElements ?? 0 }})</span
        >
      </div>
    </div>
  </div>
  <cc-datagrid
    class="my-2"
    [data]="todos?.content ?? 0"
    [columns]="gridCols"
    [length]="todos?.totalElements ?? 0"
    [pageOnFront]="false"
    [pageIndex]="todos?.number ?? 0"
    [pageSize]="todos?.size ?? 0"
    [pageSizeOptions]="[20]"
    (page)="getNextPage($event)"
    [stickyHeader]="true"
    [showColumnMenuButton]="true"
    [showColumnMenuHeader]="false"
    [columnMenuButtonIcon]="'settings'"
    [columnMovable]="true"
    [noResultTemplate]="noResult"
  >
    <cc-grid-actions-list
      *ccActionData="let ctx of todos?.content; row as row"
      [renderedActionsCount]="2"
      style="width: fit-content; gap: 2px"
    >
      <ng-container *ngIf="row.status.value === 'PENDING'">
        <button
          *cc-action
          cc-raised-button
          [color]="'primary'"
          (click)="pay(row)"
        >
          Collect
        </button>
      </ng-container>
      <ng-container
        *ngIf="
          row.reason === 'Absconding removal' && row.status.value === 'PENDING'
        "
      >
        <button
          *cc-action
          cc-raised-button
          [color]="'accent'"
          (click)="reject(row)"
        >
          Reject
        </button>
      </ng-container>
    </cc-grid-actions-list>
  </cc-datagrid>
  <ng-template #noResult> </ng-template>
</div>
