import { Component, OnDestroy, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { map, Observable } from 'rxjs';
import { CashierService } from '../../services/cashier.service';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { CCPreviewAttachmentComponent } from '@maids/cc-lib/preview-attachment';
import { MatDialog } from '@angular/material/dialog';
import { CCFileUploaderConfig } from '@maids/cc-lib/file-uploader';

@Component({
  selector: 'app-pay',
  templateUrl: './pay.component.html',
  styleUrls: ['./pay.component.scss'],
})
export class PayComponent implements OnInit, OnDestroy {
  formGroup = this.formBuilder.group({
    invoice: [''],
    taxable: [''],
    vatAmount: [''],
    attachedValidVatInvoice: [''],
    vatInvoice: [''],
    invoiceAttached: [''],
  });
  signature: any;
  details: any;
  type: number = 0;
  payNow: boolean = false;
  hideQuestion: boolean = false;
  hideButton: boolean = false;
  config: CCFileUploaderConfig = {
    maxFiles: 1,
    maxFilesize: 10,
  };
  constructor(
    private route: ActivatedRoute,
    private cashierService: CashierService,
    private mediaService: MediaService,
    private dialog: MatDialog,
    private formBuilder: FormBuilder,
    private router: Router,
    public readonly notification: CCNotificationService
  ) {}

  ngOnInit(): void {
    this.type = this.route.snapshot.queryParams['type'];
    this.cashierService
      .paymentDetails(this.route.snapshot.params['id'])
      .subscribe((res: any) => {
        this.details = res;
        this.details.date = res.creationDate.split(' ')[0];
        this.details.invoiceAttachment = res.attachments.find((_: any) => {
          return _.tag.includes('EXPENSE_REQUEST_INVOICE');
        });
        this.details.vatInvoiceAttachment = res.attachments.find((_: any) => {
          return _.tag.includes('EXPENSE_PAYMENT_VAT_INVOICE');
        });
        this.details.filteredAttachments = this.details.attachments.filter(
          (_: any) => {
            return !_.tag.includes('SIGNATURE');
          }
        );
        this.formGroup.controls['vatAmount'].setValue(res.vatAmount);

        this.formGroup.controls['attachedValidVatInvoice'].setValue(
          res.attachedValidVatInvoice !== ''
            ? this.formGroup.controls['attachedValidVatInvoice'].value
            : ''
        );
        this.formGroup.controls['invoiceAttached'].setValue(
          res.invoiceAttached !== ''
            ? this.formGroup.controls['invoiceAttached'].value
            : ''
        );
        this.formGroup.controls['taxable'].setValue(
          res.taxable !== '' ? this.formGroup.controls['taxable'].value : ''
        );
        if (res.status !== 'PENDING') {
          this.notification.notifyError('Payment already paid');
          this.hideButton = true;
        }
        if (
          !res.attachments.find((_: any) =>
            _.tag.includes('EXPENSE_REQUEST_INVOICE')
          )
        ) {
          this.formGroup.controls['invoiceAttached'].setValue(false);
          this.hideQuestion = true;
        }
      });
  }
  pay() {
    this.payNow = true;
  }
  preview(uuid: any, name: string) {
    return this.mediaService
      .getFile('public/download/' + uuid)
      .subscribe((res: any) => {
        this.previewFile(res, name);
      });
  }
  previewFile(data: any, fileName: string) {
    let blob = new Blob([data], { type: data.type });
    const blobUrl = URL.createObjectURL(blob);
    let file_type = data.type.split('/')[1];
    this.dialog.open(CCPreviewAttachmentComponent, {
      width: '100%',
      data: {
        url: blobUrl,
        blob: blob,
        type: file_type,
        filename: fileName,
      },
    });
  }
  collect() {
    let params;
    params = {
      keepOriginalSize: true,
      tag: 'EXPENSE_PAYMENT_SIGNATURE',
    };
    const base64String = this.signature;
    const mimeType = base64String.match(
      /data:([a-zA-Z0-9]+\/[a-zA-Z0-9-.+]+).*,.*/
    )?.[1];
    const file = this.cashierService.base64ToBlob(base64String, mimeType);
    const fileName = `signature.${mimeType.split('/')[1]}`;
    const formData = new FormData();
    formData.append('file', file, fileName);
    this.cashierService.uploadFile(params, formData).subscribe({
      next: (res: any) => {
        this.details.attachments.push(res);
        this.details.attachments.forEach((element: any) => {
          ({ id: element.id, tag: element.tag });
        });
        this.cashierService
          .cashierCollectMoney(res.id, this.details.id, this.details)
          .subscribe((res: any) => {
            this.preview(res.uuid, fileName);
            this.notification.notifySuccess('Payment Successful', 2000);
            this.router.navigateByUrl('/accounting/v2/cashier');
          });
      },
    });
  }
  done() {
    let params;
    params = {
      keepOriginalSize: true,
      tag: 'EXPENSE_PAYMENT_SIGNATURE',
    };
    const base64String = this.signature;
    const mimeType = base64String.match(
      /data:([a-zA-Z0-9]+\/[a-zA-Z0-9-.+]+).*,.*/
    )?.[1];
    const file = this.cashierService.base64ToBlob(base64String, mimeType);
    const fileName = `signature.${mimeType.split('/')[1]}`;
    const formData = new FormData();
    formData.append('file', file, fileName);
    this.cashierService.uploadFile(params, formData).subscribe({
      next: (res: any) => {
        let data: any = { ...this.details, ...this.formGroup.value };
        if (this.details?.requiresInvoice) {
          data.invoiceAttached =
            data.invoiceAttached !== '' ? data.invoiceAttached : '';
          data.taxable = data.taxable !== '' ? data.taxable : '';
          data.attachedValidVatInvoice =
            data.attachedValidVatInvoice !== ''
              ? data.attachedValidVatInvoice
              : '';
        }
        if (!data.invoiceAttached && data.invoice && data.invoice[0].id) {
          data.attachments.push(data.invoice[0]);
        } else {
          delete data.invoice;
        }
        if (
          !data.attachedValidVatInvoice &&
          data.vatInvoice &&
          data.vatInvoice[0].id
        ) {
          data.attachments.push(data.vatInvoice[0]);
        } else {
          delete data.vatInvoice;
        }
        delete data.method;
        delete data.currentTasks;
        delete data.beneficiaryType;
        delete data.expenseRequestTodos;
        data.attachments.push(res);
        data.attachments.forEach((attachment: any) => ({
          id: attachment.id,
          tag: attachment.tag,
        }));
        this.cashierService
          .cashierPayMoney(this.details.id, res.id, data)
          .subscribe({
            next: (response: any) => {
              if (response) {
                this.notification.notifySuccess(
                  'Cashier collect signature successfully saved!'
                );
                this.mediaService
                  .getFile('public/download/' + res.id)
                  .subscribe((res: any) => {
                    this.previewFile(res, fileName);
                  });
                setTimeout(() => {
                  this.router.navigateByUrl('/accounting/v2/cashier');
                }, 1000);
              }
            },
            error: (err: any) => {
              this.notification.notifyError(err.error.message, 2000);
            },
          });
      },
    });
  }
  ngOnDestroy(): void {}
}
