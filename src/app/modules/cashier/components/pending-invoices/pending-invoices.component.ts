import { Component, OnInit } from '@angular/core';
import { Observable } from 'rxjs';
import { CashierService } from '../../services/cashier.service';
import { PageEvent } from '@angular/material/paginator';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { Router } from '@angular/router';

@Component({
  selector: 'app-pending-invoices',
  templateUrl: './pending-invoices.component.html',
  styleUrls: ['./pending-invoices.component.scss'],
})
export class PendingInvoicesComponent implements OnInit {
  pendingInvoices: any;
  gridCols: CCGridColumn[] = [
    { field: 'action', header: '' },
    { field: 'beneficiary', header: 'Beneficiary' },
    { field: 'description', header: 'Description' },
    {
      field: 'amount',
      header: 'Amount (AED)',
      formatter: (rowData: any) => {
        return rowData.amount
          ? rowData.amount.toLocaleString()
          : '';
      },
    },
  ];
  constructor(private cashierService: CashierService, private router: Router) {}

  ngOnInit(): void {
    this.getPendingInvoices();
  }
  getPendingInvoices(page: number = 0, size: number = 20) {
    this.cashierService
      .getPedingInvoicePayment({
        page,
        size,
      })
      .subscribe((res: any) => {
        this.pendingInvoices = res;
      });
  }
  getNextPage(event: PageEvent) {
    this.getPendingInvoices(event.pageIndex, event.pageSize);
  }
  pay(id: number) {
    this.router.navigateByUrl(`/accounting/v2/cashier/collect-invoice/${id}`);
  }
}
