<div class="acc-7779">
  <div class="d-flex justify-content-between my-2">
    <div class="col-md-auto">
      <div class="row">
        <span>Balance: &nbsp;</span>
        <span class="cc-secondary">
          AED {{ balance ? balance.toLocaleString() : "" }}
        </span>
      </div>
      <div class="row">
        <span class="bold-font">Pending Payments</span>
        <span class="cc-secondary ml-1">
          ({{ pendingPayments?.totalElements ?? 0 }})</span
        >
      </div>
    </div>
    <div class="col-md-auto px-0 row">
      <div class="col-md-auto">
        <button cc-raised-button (click)="closeCashBox()">
          Close Cash Box
        </button>
      </div>
      <div class="col-md-auto">
        <button cc-raised-button color="primary" (click)="addCashPayment()">
          Add Cash Payment
        </button>
      </div>
    </div>
  </div>
  <cc-datagrid
    class="my-2"
    [data]="pendingPayments?.content ?? []"
    [columns]="gridCols"
    [length]="pendingPayments?.totalElements ?? 0"
    [pageOnFront]="false"
    [pageIndex]="pendingPayments?.number ?? 0"
    [pageSize]="pendingPayments?.size ?? 0"
    [pageSizeOptions]="[20]"
    (page)="getNextPage($event)"
    [stickyHeader]="true"
    [showColumnMenuButton]="true"
    [showColumnMenuHeader]="false"
    [columnMenuButtonIcon]="'settings'"
    [columnMovable]="true"
    [noResultTemplate]="noResult"
    [cellTemplate]="{ description: description }"
  >
    <cc-grid-actions-list
      *ccActionData="let ctx of pendingPayments?.content; row as row"
      [renderedActionsCount]="3"
      style="width: fit-content; gap: 2px"
    >
      <button
        *cc-action
        cc-raised-button
        [color]="row.navigation === 'PAY-MONEY-3' ? 'accent' : 'primary'"
        (click)="pay(row.id, row.navigation)"
      >
        {{ row.action }}
      </button>
      <ng-container *ngIf="row.action !== 'Collect'">
        <button
          cc-raised-button
          *cc-action
          color="accent"
          (click)="reassign(row.id)"
        >
          Re-assign
        </button>
      </ng-container>
      <ng-container *ngIf="row.replenishmentTodo ||  (row.expenseToPost && row.expenseToPost.code !== 'SC 11')">
        <button cc-raised-button *cc-action (click)="dismiss(row.id)">
          Dismiss
        </button>
      </ng-container>
    </cc-grid-actions-list>
  </cc-datagrid>
  <ng-template #description let-row let-index="index" let-col="colDef">
    <span class="desc-row">{{ row.description }}</span>
  </ng-template>
  <ng-template #noResult> </ng-template>
</div>
