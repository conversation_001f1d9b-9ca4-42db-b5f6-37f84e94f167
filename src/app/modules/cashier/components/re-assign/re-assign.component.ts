import { Component, Inject, OnInit } from '@angular/core';
import { PaginationRequest } from '@maids/cc-lib/common';
import { SelectOption } from '@maids/cc-lib/select-input';
import { Observable } from 'rxjs';
import { CashierService } from '../../services/cashier.service';
import { FormBuilder, Validators } from '@angular/forms';
import { CC_DIALOG_DATA, CCDialogRef } from '@maids/cc-lib/dialog';

@Component({
  selector: 'app-re-assign',
  templateUrl: './re-assign.component.html',
  styleUrls: ['./re-assign.component.scss'],
})
export class ReAssignComponent implements OnInit {
  form = this.fb.group({
    cashier: [null, [Validators.required]],
    reassignNote: [null, [Validators.required]],
  });
  readonly cashPayerOptions = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.cashierService.getCashiersOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  constructor(
    private cashierService: CashierService,
    private fb: FormBuilder,
    @Inject(CC_DIALOG_DATA) public data: any,
    private dialogRef: CCDialogRef<ReAssignComponent>
  ) {}

  ngOnInit(): void {}
  submitReassign() {
    this.cashierService
      .reassignCashier(this.data.id, {
        newCashierId: this.form.value.cashier,
        note: this.form.value.reassignNote,
      })
      .subscribe({
        next: () => {
          this.dialogRef.close(true);
        },
      });
  }
}
