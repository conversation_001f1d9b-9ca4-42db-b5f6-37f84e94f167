import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ClientRefundListComponent } from './components/client-refund-list/client-refund-list.component';
import { ClientRefundFormComponent } from './components/client-refund-form/client-refund-form.component';

const routes: Routes = [
  { path: '', component: ClientRefundListComponent, data: { label: '' } },
  {
    path: 'form',
    component: ClientRefundFormComponent,
    data: {
      label: 'Family Refund Setup Form',
      pageCode: 'accounting_ClientRefundSetup',
    },
  },
  {
    path: 'form/:id',
    component: ClientRefundFormComponent,
    data: {
      label: 'Family Refund Setup Form',
      pageCode: 'accounting_ClientRefundSetup',
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ClientRefundSetupRoutingModule {}
