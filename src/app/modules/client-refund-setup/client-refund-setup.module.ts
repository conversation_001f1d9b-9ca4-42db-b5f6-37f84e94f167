import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ClientRefundSetupRoutingModule } from './client-refund-setup-routing.module';
import { ClientRefundListComponent } from './components/client-refund-list/client-refund-list.component';
import { ClientRefundFormComponent } from './components/client-refund-form/client-refund-form.component';
import { CCDatagridModule } from '@maids/cc-lib/datagrid';
import { CCInputModule } from '@maids/cc-lib/input';
import { CCCheckboxModule } from '@maids/cc-lib/checkbox';
import { CCRadioButtonModule } from '@maids/cc-lib/radio-button';
import { CCSelectInputModule } from '@maids/cc-lib/select-input';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CCButtonModule } from '@maids/cc-lib/button';
import { CCIconModule } from '@maids/cc-lib/icon';
import { CCAmountInputModule } from '@maids/cc-lib/masked-input';
import { CCValidateByModule } from '@maids/cc-lib/validation';

@NgModule({
  declarations: [ClientRefundListComponent, ClientRefundFormComponent],
  imports: [
    CommonModule,
    ClientRefundSetupRoutingModule,
    CCDatagridModule,
    CCInputModule,
    CCCheckboxModule,
    CCSelectInputModule,
    CCButtonModule,
    CCIconModule,
    CCRadioButtonModule,CCValidateByModule,
    CCAmountInputModule,
    ReactiveFormsModule,
    FormsModule,
  ],
})
export class ClientRefundSetupModule {}
