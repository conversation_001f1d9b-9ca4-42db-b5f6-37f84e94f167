<div class="container w-75 p-4 acc-8011">
  <form [formGroup]="setupForm">
    <cc-select
      label="Monthly Report Categories"
      formControlName="selectedCategory"
      [data]="categoryOptions"
    ></cc-select>
    <p
      class="text-danger"
      *ngIf="selectedPurposeObj && selectedPurposeObj.categorySamePurpose"
    >
      Category is same as purpose
    </p>
    <cc-select
      label="Family Refund Purpose"
      [data]="clientRefundPurposeOptions"
      formControlName="selectedPurpose"
      [(ngModel)]="selectedPurposeObj"
      [required]="true"
      [emitFullSelectOption]="true"
    ></cc-select>
    <div
      class="row align-items-center"
      [ngClass]="{
        'd-none':
          !setupForm.controls['selectedPurpose'].value ||
          setupForm.controls['selectedPurpose'].value != PartialRefundPurposeId
      }"
    >
      <label class="col-3">How the family will pay? </label>
      <cc-radio-group
        formControlName="selectedPartialRefundPaymentMethod"
        class="col-6"
      >
        <cc-radio-button
          *ngFor="let item of partialRefundPaymentMethods"
          [value]="item?.id"
          class="row col-md-auto"
          >{{ item?.text }}</cc-radio-button
        >
      </cc-radio-group>
    </div>
    <div class="row align-items-center">
      <div class="col-3">
        <label>Link Complaint </label>
      </div>
      <cc-radio-group
        class="col-6 row justify-content-between"
        formControlName="linkComplaint"
      >
        <div class="col-md-auto">
          <cc-radio-button value="no">No</cc-radio-button>
        </div>
        <div class="col-md-auto">
          <cc-radio-button value="yes">Yes</cc-radio-button>
        </div>
      </cc-radio-group>
    </div>
    <div class="row justify-content-end align-items-center">
      <div class="col-6">
        <cc-checkbox
          formControlName="linkWithAllComplaints"
          [ngClass]="{
            'd-none': setupForm.controls['linkComplaint'].value === 'no'
          }"
          >Link with all complaints</cc-checkbox
        >
      </div>
    </div>
    <div
      [ngClass]="{
        'd-none':
          setupForm.controls['linkComplaint'].value === 'no' ||
          setupForm.controls['linkWithAllComplaints'].value === true
      }"
      class="row align-items-center"
    >
      <label class="col-6 required-label">Select Complaint Types to link</label>
      <div
        class="col-6 border border-secondary"
        style="max-height: 300px; overflow-y: auto"
      >
        <cc-checkbox
          class="row col-md-auto"
          *ngFor="let item of compliantTypesList"
          [(ngModel)]="selectedTypes[item?.id]"
          [ngModelOptions]="{ standalone: true }"
          >{{ item?.text }}</cc-checkbox
        >
      </div>
    </div>
    <div class="row align-items-center my-2">
      <div class="col-3">
        <label>Validate family's bank </label>
      </div>
      <cc-radio-group
        class="col-6 row justify-content-between"
        formControlName="validateClientBank"
      >
        <div class="col-md-auto">
          <cc-radio-button value="no">No</cc-radio-button>
        </div>
        <div class="col-md-auto">
          <cc-radio-button value="yes">Yes</cc-radio-button>
        </div>
      </cc-radio-group>
    </div>
    <div
      class="row align-items-center"
      [ngClass]="{
        'd-none': setupForm.controls['validateClientBank'].value === 'no'
      }"
    >
      <div class="col-6 required-label">
        <label>Limit purpose to these banks </label>
      </div>
      <div
        class="col-6 border border-secondary"
        style="max-height: 300px; overflow-y: auto"
      >
        <cc-checkbox
          class="row col-md-auto"
          *ngFor="let item of banksList"
          [(ngModel)]="selectedBanks[item?.id]"
          [ngModelOptions]="{ standalone: true }"
          >{{ item?.text }}</cc-checkbox
        >
      </div>
    </div>
    <cc-select
      label="Limit Requests to"
      [lazyPageFetcher]="multipleUserOptions"
      [(ngModel)]="selectedUsers"
      [ngModelOptions]="{ standalone: true }"
      [multiple]="true"
      [emitFullSelectOption]="true"
    ></cc-select>
    <div class="row">
      <div class="col-6">
        <cc-checkbox formControlName="autoApproved">Auto-approved </cc-checkbox>
      </div>
    </div>
    <cc-select
      [ngClass]="{
        'd-none': setupForm.controls['autoApproved'].value === true
      }"
      label="Approved By"
      [lazyPageFetcher]="userOptions"
      [(ngModel)]="userApprover"
      formControlName="approvedBy"
      [emitFullSelectOption]="true"
      [required]="!setupForm.controls['autoApproved'].value"
    ></cc-select>
    <div class="row align-items-center">
      <div class="col-8">
        <cc-input
          label="Limit for COO Approval"
          formControlName="limitCEOApproval"
          [ccValidateBy]="[limitCEOApprovalValidation]"
        ></cc-input>
      </div>
      <div class="col-4">
        <cc-checkbox formControlName="checkCeoLimit"
          >Check COO Limit</cc-checkbox
        >
      </div>
    </div>
    <div class="row align-items-center">
      <div class="col-3">
        <label>Allow Monthly Refunds</label>
      </div>
      <cc-radio-group
        formControlName="allowMonthlyRefunds"
        class="col-6 row justify-content-between"
      >
        <div class="col-md-auto">
          <cc-radio-button value="no">No</cc-radio-button>
        </div>
        <div class="col-md-auto">
          <cc-radio-button value="yes">Yes</cc-radio-button>
        </div>
      </cc-radio-group>
    </div>
    <div class="row align-items-center">
      <div class="col-3">
        <label>Requires Attachment</label>
      </div>
      <cc-radio-group
        formControlName="requiresAttachments"
        class="col-6 row justify-content-between"
      >
        <div class="col-md-auto">
          <cc-radio-button value="no">No</cc-radio-button>
        </div>
        <div class="col-md-auto">
          <cc-radio-button value="yes">Yes</cc-radio-button>
        </div>
      </cc-radio-group>
    </div>
    <div class="row">
      <div class="col-6">
        <cc-checkbox formControlName="hidden"
          >Hide purpose from Refund Screen</cc-checkbox
        >
      </div>
    </div>
  </form>
  <hr />
  <div class="row justify-content-end">
    <div class="col-md-auto">
      <button cc-raised-button (click)="cancel()">Cancel</button>
    </div>
    <div class="col-md-auto">
      <button
        cc-raised-button
        color="accent"
        (click)="save()"
        [disabled]="!setupForm.valid"
      >
        {{ formType == "create" ? "Save" : "Update" }}
      </button>
    </div>
  </div>
</div>
