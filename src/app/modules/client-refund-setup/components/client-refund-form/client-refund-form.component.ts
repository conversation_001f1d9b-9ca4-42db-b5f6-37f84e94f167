import { Component, OnInit } from '@angular/core';
import { FormB<PERSON>er, Validators } from '@angular/forms';
import { PaginationRequest } from '@maids/cc-lib/common';
import { forkJoin, Observable, tap } from 'rxjs';
import { ClientRefundService } from '../../services/client-refund.service';
import { CCNotificationService } from '@maids/cc-lib/services';
import { ActivatedRoute, Router } from '@angular/router';
import { CCValidatorFn } from '@maids/cc-lib/validation';

@Component({
  selector: 'app-client-refund-form',
  templateUrl: './client-refund-form.component.html',
  styleUrls: ['./client-refund-form.component.scss'],
})
export class ClientRefundFormComponent implements OnInit {
  setupForm = this.formBuilder.group({
    linkComplaint: ['no'],
    validateClientBank: ['no'],
    autoApproved: [false],
    checkCeoLimit: [false],
    linkWithAllComplaints: [false],
    hidden: [false],
    selectedPurpose: [''],
    selectedCategory: [''],
    approvedBy: [''],
    limitCEOApproval: [''],
    allowMonthlyRefunds: ['no'],
    requiresAttachments: ['no'],
    complaintTypesFake: [''],
    selectedPartialRefundPaymentMethod: [null],
  });
  selectedPurposeObj: any;
  PartialRefundPurposeId: any;
  userApprover: any;
  categoryOptions: any[] = [];
  clientRefundPurposeOptions: any[] = [];
  compliantTypesList: any[] = [];
  banksList: any[] = [];
  selectedTypes: boolean[] = [];
  selectedBanks: boolean[] = [];
  selectedUsers: any = null;
  partialRefundPaymentMethods: any[] = [];
  formType: string = '';
  dataLoaded: boolean = false;
  readonly multipleUserOptions = (
    pageReq: PaginationRequest
  ): Observable<any> => {
    return this.clientRefundService.getCliendRefundUsers(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly userOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.clientRefundService.getCliendRefundUsers(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  constructor(
    private formBuilder: FormBuilder,
    private clientRefundService: ClientRefundService,
    public readonly notifications: CCNotificationService,
    private router: Router,
    private route: ActivatedRoute
  ) {}

  ngOnInit(): void {
    if (this.route.snapshot.params['id']) {
      this.formType = 'edit';
    } else {
      this.formType = 'create';
      this.dataLoaded = true;
    }
    this.loadAllData();
    this.setupForm.controls['autoApproved'].valueChanges.subscribe((val) => {
      if (val == true) {
        this.setupForm.controls['approvedBy'].clearValidators();
        this.setupForm.controls['approvedBy'].updateValueAndValidity();
      } else {
        this.setupForm.controls['approvedBy'].addValidators(
          Validators.required
        );
        this.setupForm.controls['approvedBy'].updateValueAndValidity();
      }
    });
    this.setupForm.controls['selectedCategory'].valueChanges.subscribe(
      (val) => {
        if (val && this.dataLoaded) {
          this.setupForm.controls['selectedPurpose'].setValue('');
          this.selectedPurposeObj = null;
          this.getallclientspurpose(val);
        }
      }
    );
    this.setupForm.controls['checkCeoLimit'].valueChanges.subscribe((val) => {
      if (val == true) {
        this.setupForm.controls['limitCEOApproval'].addValidators(
          Validators.required
        );
        this.setupForm.controls['limitCEOApproval'].updateValueAndValidity();
      } else {
        this.setupForm.controls['limitCEOApproval'].clearValidators();
        this.setupForm.controls['limitCEOApproval'].updateValueAndValidity();
      }
    });
  }

  getClientRefundParameter(code: string) {
    this.clientRefundService
      .getClientRefundParameter(code)
      .subscribe((res: any) => {
        if (res.length == 0) {
          this.notifications.notifyError(`Parameter ${code} Not Found`);
          return;
        }
      });
  }
  getallclientspurpose(selectedCategoryId: any) {
    this.clientRefundService
      .getallclientspurpose(selectedCategoryId)
      .subscribe((res: any) => {
        if (res.length > 0) {
          this.clientRefundPurposeOptions = res;
        }
      });
  }
  loadAllData() {
    forkJoin({
      clientRefundPurpose: this.clientRefundService.getallclientspurpose(''),
      paymentMethods:
        this.clientRefundService.getPaymentMethodPicklistClientRefund(),
      checkParameter: this.clientRefundService.getClientRefundParameter(
        'payment_request_purpose_partial_refund'
      ),
      refundPurposeCategory:
        this.clientRefundService.getClientRefundPurposeCategory(),
      compliantTypes: this.clientRefundService.getComplaintsClientRefundTypes(),
      banksList: this.clientRefundService.getClientRefundBanksList(),
    }).subscribe(
      ({
        clientRefundPurpose,
        paymentMethods,
        checkParameter,
        refundPurposeCategory,
        compliantTypes,
        banksList,
      }) => {
        this.clientRefundPurposeOptions = clientRefundPurpose;
        this.partialRefundPaymentMethods = paymentMethods;
        this.categoryOptions = refundPurposeCategory;
        this.compliantTypesList = compliantTypes;
        this.banksList = banksList;
        if (checkParameter.length === 0) {
          this.notifications.notifyError(
            'Payment Request Purpose Partial Refund Parameter Not Found'
          );
        }
        const partialRefundPurpose = checkParameter[0].value;
        this.PartialRefundPurposeId = this.clientRefundPurposeOptions.find(
          (x: any) => x.text.indexOf(partialRefundPurpose) > -1
        )?.id;
        if (this.route.snapshot.params['id']) {
          this.getFormData(this.route.snapshot.params['id']);
        }
      }
    );
  }
  getFormData(id: number) {
    this.clientRefundService
      .getClientRefundSetupData(id)
      .subscribe((res: any) => {
        this.setupForm.controls['linkComplaint'].setValue(
          res.linkComplaint ? 'yes' : 'no'
        );
        this.setupForm.controls['validateClientBank'].setValue(
          res.validateClientBank ? 'yes' : 'no'
        );
        this.setupForm.controls['allowMonthlyRefunds'].setValue(
          res.allowMonthlyRefunds ? 'yes' : 'no'
        );
        this.setupForm.controls['requiresAttachments'].setValue(
          res.requireAttachment ? 'yes' : 'no'
        );
        this.setupForm.controls['autoApproved'].setValue(res.autoApproved);
        this.setupForm.controls['linkWithAllComplaints'].setValue(
          res.linkWithAllComplaints
        );
        this.setupForm.controls['hidden'].setValue(res.hidden);
        this.setupForm.controls['checkCeoLimit'].setValue(res.checkCeoLimit);
        this.setupForm.controls['selectedPurpose'].setValue(
          res.paymentRequestPurpose ? res.paymentRequestPurpose.id : ''
        );
        this.selectedPurposeObj = {
          id: res.paymentRequestPurpose.id,
          text: res.paymentRequestPurpose.label,
          categorySamePurpose: res.paymentRequestPurpose.categorySamePurpose,
        };
        this.setupForm.controls['selectedCategory'].setValue(
          res.paymentRequestPurpose && res.paymentRequestPurpose?.refundCategory
            ? res.paymentRequestPurpose.refundCategory?.id
            : ''
        );
        this.setupForm.controls['selectedPartialRefundPaymentMethod'].setValue(
          res.partialRefundForCancellationPaymentMethod?.id
        );
        if (
          res.paymentRequestPurpose &&
          res.paymentRequestPurpose?.refundCategory
        ) {
          this.getallclientspurpose(
            res.paymentRequestPurpose.refundCategory?.id
          );
        }

        if (res.requestedBy) {
          this.selectedUsers = res.requestedBy.map((user: any) => {
            return {
              id: user.id.toString(),
              text: user.label,
            };
          });
        }
        if (res.approvedBy) {
          this.userApprover = {
            id: res.approvedBy.id,
            text: res.approvedBy.label,
          };
        }
        this.setupForm.controls['limitCEOApproval'].setValue(
          res.limitForCeoApproval
        );
        if (res.complaintTypes) {
          res.complaintTypes.forEach((item: any) => {
            this.selectedTypes[item.id] = true;
          });
        }
        if (res.banks) {
          res.banks.forEach((item: any) => {
            this.selectedBanks[item.id] = true;
          });
        }
        this.dataLoaded = true;
      });
    this.getallclientspurpose(id);
  }
  getSelectedComplaintTypes() {
    if (this.setupForm.controls['linkComplaint'].value === 'yes') {
      return Object.keys(this.selectedTypes)
        .filter((item: any) => this.selectedTypes[item])
        .map((item: any) => {
          return { id: item };
        });
    }

    return null;
  }
  getSelectedBanks() {
    if (this.setupForm.controls['validateClientBank'].value === 'yes') {
      return Object.keys(this.selectedBanks)
        .filter((item: any) => this.selectedBanks[item])
        .map((item: any) => {
          return { id: item };
        });
    }

    return null;
  }
  save() {
    let payload: any;
    payload = {
      linkComplaint:
        this.setupForm.controls['linkComplaint'].value == 'yes' ? true : false,
      complaintTypes: this.getSelectedComplaintTypes(),
      validateClientBank:
        this.setupForm.controls['validateClientBank'].value == 'yes'
          ? true
          : false,
      autoApproved: this.setupForm.controls['autoApproved'].value,
      hidden: this.setupForm.controls['hidden'].value,
      linkWithAllComplaints:
        this.setupForm.controls['linkWithAllComplaints'].value,
      banks: this.getSelectedBanks(),
      allowMonthlyRefunds:
        this.setupForm.controls['allowMonthlyRefunds'].value == 'yes'
          ? true
          : false,
      requireAttachment:
        this.setupForm.controls['requiresAttachments'].value == 'yes'
          ? true
          : false,
      limitForCeoApproval:
        this.setupForm.controls['limitCEOApproval'].value > 0
          ? this.setupForm.controls['limitCEOApproval'].value
          : '',
      requestedBy: this.selectedUsers
        ? this.selectedUsers.map((item: any) => {
            return { id: item.id };
          })
        : null,
      paymentRequestPurpose: {
        id: this.setupForm.controls['selectedPurpose'].value.id,
      },
      checkCeoLimit: this.setupForm.controls['checkCeoLimit'].value,
    };
    if (this.setupForm.controls['autoApproved'].value == false) {
      payload.approvedBy = {
        id: this.setupForm.controls['approvedBy'].value.id,
      };
    }
    if (this.route.snapshot.params['id']) {
      payload.id = this.route.snapshot.params['id'];
    }
    if (
      this.setupForm.controls['selectedPurpose'].value.id ==
      this.PartialRefundPurposeId
    ) {
      payload.partialRefundForCancellationPaymentMethod = {
        id: this.setupForm.controls['selectedPartialRefundPaymentMethod'].value,
      };
    }
    if (this.route.snapshot.params['id']) {
      this.clientRefundService.updateClientRefundSetup(payload).subscribe({
        next: (res: any) => {
          this.notifications.notifySuccess('Updated Successfully');
          this.router.navigateByUrl('/accounting/v2/client-refund-setup');
        },
        error: (err: any) => {
          this.notifications.notifyError(err.error.message);
        },
      });
    } else {
      this.clientRefundService.createClientRefundSetup(payload).subscribe({
        next: (res: any) => {
          this.notifications.notifySuccess('Added Successfully');
          this.router.navigateByUrl('/accounting/v2/client-refund-setup');
        },
        error: (err: any) => {
          this.notifications.notifyError(err.error.message);
        },
      });
    }
  }
  UnselectCompliants() {
    this.selectedTypes = [];
  }
  cancel() {
    this.router.navigateByUrl(`/accounting/v2/client-refund-setup`);
  }
  limitCEOApprovalValidation: CCValidatorFn = (control) => {
    if (this.setupForm.controls['checkCeoLimit'].value === true) {
      if (!control.value || control.value <= 0) {
        return { error: 'Limit should be greater than 0' };
      }
      return null;
    }
    return null;
  };
}
