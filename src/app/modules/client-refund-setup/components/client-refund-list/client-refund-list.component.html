<div class="acc-8011">
  <div class="d-flex justify-content-end my-2">
    <button
      cc-button
      color="accent"
      style="padding-left: 30px"
      (click)="goToAddRule()"
    >
      <cc-icon class="icon">add</cc-icon> Add Rule
    </button>
  </div>
  <cc-datagrid
    class="my-2"
    [data]="records"
    [columns]="gridCols"
    [pageOnFront]="false"
    [showPaginator]="false"
    [stickyHeader]="true"
    [showColumnMenuButton]="true"
    [showColumnMenuHeader]="false"
    [columnMenuButtonIcon]="'settings'"
    [columnMovable]="true"
    [cellTemplate]="{ paymentRequestPurpose: paymentRequestPurpose }"
    [noResultTemplate]="noResult"
  ></cc-datagrid>
  <ng-template
    #paymentRequestPurpose
    let-row
    let-index="index"
    let-col="colDef"
  >
    <a
      target="_blank"
      style="color: #92000a; font-weight: bold;cursor: pointer;"
      (click)="goToRule(row)"
      >{{ row.paymentRequestPurpose.label }}</a
    >
  </ng-template>
  <ng-template #noResult> </ng-template>
</div>
