import { Component, OnInit } from '@angular/core';
import { ClientRefundService } from '../../services/client-refund.service';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { Router } from '@angular/router';

@Component({
  selector: 'app-client-refund-list',
  templateUrl: './client-refund-list.component.html',
  styleUrls: ['./client-refund-list.component.scss'],
})
export class ClientRefundListComponent implements OnInit {
  records: any | null = null;
  gridCols: CCGridColumn[] = [
    {
      field: 'paymentRequestPurpose',
      header: 'Refund Purpose',
    },
    {
      field: 'linkComplaint',
      header: 'Link Complaint',
      formatter(rowData, colDef) {
        return rowData.linkComplaint ? 'Yes' : 'No';
      },
    },
    {
      field: 'requestedBy',
      header: 'Requested By',
      formatter(rowData, colDef) {
        return rowData.requestedBy
          ? rowData.requestedBy.map((x: any) => x.fullName).join(', ')
          : '';
      },
    },
    {
      field: 'approvedBy',
      header: 'Approved By',
      formatter(rowData, colDef) {
        return rowData.approvedBy ? rowData.approvedBy.label : 'All';
      },
    },
    { field: 'limitForCeoApproval', header: 'Limit for COO Approval' },
    {
      field: 'requireAttachment',
      header: 'Requires Attachment',
      formatter(rowData, colDef) {
        return rowData.requireAttachment ? 'Yes' : 'No';
      },
    },
  ];
  constructor(
    private clientRefundService: ClientRefundService,
    private router: Router
  ) {}
  ngOnInit(): void {
    this.getClientRefundSetup();
  }
  getClientRefundSetup() {
    this.clientRefundService.getClientRefundSetup().subscribe((res: any) => {
      this.records = res;
    });
  }
  goToAddRule() {
    this.router.navigateByUrl('/accounting/v2/client-refund-setup/form');
  }
  goToRule(row:any){
    this.router.navigateByUrl(`/accounting/v2/client-refund-setup/form/${row.id}`);
  }
}
