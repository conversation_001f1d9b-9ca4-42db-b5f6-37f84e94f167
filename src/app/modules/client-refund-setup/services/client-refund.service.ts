import { HttpClient, HttpContext } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { CCBackendEndpoint, REQ_SHOW_LOADING_ICON } from '@maids/cc-erp-services';
import { map, Observable } from 'rxjs';
import { API } from 'src/environments/api';

@Injectable({
  providedIn: 'root',
})
export class ClientRefundService {
  constructor(
    private _http: HttpClient,
    @Inject(CCBackendEndpoint) private _api: CCBackendEndpoint
  ) {}
  getClientRefundSetup(): Observable<any> {
    return this._http.get(`${this._api}/${API.getClientRefundSetup}`);
  }
  getClientRefundSetupData(id: number): Observable<any> {
    return this._http.get(`${this._api}/${API.getClientRefundSetupData}/${id}`);
  }
  getCliendRefundUsers(
    page: number,
    size: number = 50,
    search: string = ''
  ): Observable<any> {
    const context = new HttpContext().set(REQ_SHOW_LOADING_ICON, false);

    return this._http
      .get(`${this._api}/${API.getCliendRefundUsers}`, {
        params: { page, size, search },context
      })
      .pipe(
        map((val: any) => {
          return val.content.map((user: any) => {
            return { text: user.label, id: user.id };
          });
        })
      );
  }
  getallclientspurpose(selectedCategory?: any): Observable<any> {
    return this._http
      .get(`${this._api}/${API.getallclientspurpose}`, {
        params: { category: selectedCategory },
      })
      .pipe(
        map((val: any) => {
          return val.map((purpose: any) => {
            return {
              text: purpose.name,
              id: purpose.id,
              categorySamePurpose: purpose.categorySamePurpose,
            };
          });
        })
      );
  }
  getClientRefundPurposeCategory() {
    return this._http
      .get(`${this._api}/${API.getClientRefundPurposeCategory}`)
      .pipe(
        map((val: any) => {
          return val.map((purpose: any) => {
            return {
              text: purpose.name,
              id: purpose.id,
              code: purpose.code,
            };
          });
        })
      );
  }
  getPaymentMethodPicklistClientRefund(): Observable<any> {
    return this._http
      .get(`${this._api}/${API.getPaymentMethodPicklistClientRefund}`)
      .pipe(
        map((val: any) => {
          return val.map((val: any) => {
            return { text: val.name, id: val.id };
          });
        })
      );
  }
  getClientRefundParameter(code: string): Observable<any> {
    return this._http.get(`${this._api}/${API.getClientRefundParameter}`, {
      params: { code: code },
    });
  }
  getComplaintsClientRefundTypes(): Observable<any> {
    return this._http
      .get(`${this._api}/${API.getComplaintsClientRefundTypes}`)
      .pipe(
        map((val: any) => {
          let children: any[] = [];
          val.map((val: any) => {
            val.types.forEach((item: any) => {
              children.push({ text: item.label, id: item.id });
            });
          });

          return children;
        })
      );
  }
  getClientRefundBanksList(): Observable<any> {
    return this._http.get(`${this._api}/${API.getClientRefundBanksList}`).pipe(
      map((val: any) => {
        return val.map((val: any) => {
          return { text: val.name, id: val.id };
        });
      })
    );
  }
  createClientRefundSetup(payload: any): Observable<any> {
    return this._http.post(
      `${this._api}/${API.createClientRefundSetup}`,
      payload
    );
  }
  updateClientRefundSetup(payload: any): Observable<any> {
    return this._http.post(
      `${this._api}/${API.updateClientRefundSetup}`,
      payload
    );
  }
}
