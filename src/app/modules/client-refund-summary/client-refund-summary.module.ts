import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ClientRefundSummaryComponent } from './components/client-refund-summary/client-refund-summary.component';
import { ClientRefundSummaryFiltersComponent } from './components/client-refund-summary-filters/client-refund-summary-filters.component';
import { AttachmentsDialogComponent } from './components/attachments-dialog/attachments-dialog.component';
import { CCRoutes } from '@maids/cc-lib/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CCButtonModule } from '@maids/cc-lib/button';
import { CCRadioButtonModule } from '@maids/cc-lib/radio-button';
import { CCInputModule } from '@maids/cc-lib/input';
import { CCSelectInputModule } from '@maids/cc-lib/select-input';
import { CCDatepickerModule } from '@maids/cc-lib/date';
import { CCCheckboxModule } from '@maids/cc-lib/checkbox';
import { CCIconModule } from '@maids/cc-lib/icon';
import { CCDatagridModule } from '@maids/cc-lib/datagrid';
import { CCAccordionModule } from '@maids/cc-lib/accordion';
import { CCDialogModule } from '@maids/cc-lib/dialog';

const routes: CCRoutes = [
  {
    path: '',
    component: ClientRefundSummaryComponent,
  },
];

@NgModule({
  declarations: [
    ClientRefundSummaryComponent,
    ClientRefundSummaryFiltersComponent,
    AttachmentsDialogComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    FormsModule,
    ReactiveFormsModule,
    CCButtonModule,
    CCRadioButtonModule,
    CCInputModule,
    CCSelectInputModule,
    CCDatepickerModule,
    CCCheckboxModule,
    CCIconModule,
    CCDatagridModule,
    CCAccordionModule,
    CCDialogModule,
  ],
})
export class ClientRefundSummaryModule {}
