<div cc-dialog-header>
  <div class="d-flex justify-content-between w-100">
    <div>Attachments</div>
    <div>
      <cc-icon (click)="closeDialog()" style="cursor: pointer">close</cc-icon>
    </div>
  </div>
</div>
<div cc-dialog-content>
  <div class="d-flex justify-content-between">
    <div class="custom-border p-2 w-100 text-center">
      <p class="m-0">File</p>
    </div>
    <div class="custom-border p-2 w-100 text-center">
      <p class="m-0">Date</p>
    </div>
    <div class="custom-border p-2 w-100 text-center">
      <p class="m-0">Actions</p>
    </div>
  </div>
  <div class="d-flex justify-content-between" style="background: #f0f0f0" *ngFor="let attachment of data.attachments">
    <div class="custom-border p-2 w-100 text-center">{{ attachment.name }}</div>
    <div class="custom-border p-2 w-100 text-center">{{ attachment.creationDate }}</div>
    <div
      class="custom-border p-2 w-100 text-center d-flex justify-content-center align-items-center"
      (click)="downloadAttachment(attachment)"
      style="cursor: pointer">
      <div>
        <cc-icon>folder</cc-icon>
      </div>
      <div>Open</div>
    </div>
  </div>
</div>
<!-- <div cc-dialog-content>
  <table class="attachments-table">
    <thead>
      <tr>
        <th>File</th>
        <th>Date</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let attachment of data.attachments">
        <td>{{ attachment.name }}</td>
        <td>{{ attachment.creationDate }}</td>
        <td>
          <div class="d-flex" style="cursor: pointer" (click)="downloadAttachment(attachment)">
            <div>
              <cc-icon>folder</cc-icon>
            </div>
            <div>Open</div>
          </div>
        </td>
      </tr>
    </tbody>
  </table>
</div> -->
