import { Component, Inject, OnInit } from '@angular/core';
import { CC_DIALOG_DATA, CCDialog, CCDialogRef } from '@maids/cc-lib/dialog';
import { ClientRefundSummaryService } from '../../services/client-refund-summary.service';
import { MediaService } from '@maids/cc-lib/services';
import { CCPreviewAttachmentComponent } from '@maids/cc-lib/preview-attachment';

@Component({
  selector: 'app-attachments-dialog',
  templateUrl: './attachments-dialog.component.html',
  styleUrls: ['./attachments-dialog.component.scss'],
})
export class AttachmentsDialogComponent implements OnInit {
  constructor(
    private _dialogRef: CCDialogRef<AttachmentsDialogComponent>,
    @Inject(CC_DIALOG_DATA) public data: { attachments: any[] },
    private _clientRefundSummaryService: ClientRefundSummaryService,
    private _mediaService: MediaService,
    private _ccDialog: CCDialog
  ) {}

  ngOnInit(): void {}

  downloadAttachment(attachment: any) {
    // this._clientRefundSummaryService
    //   .downloadAttachment(attachment)
    //   .subscribe((res: any) => {});
    return this._mediaService.getFile('public/download/' + attachment.uuid).subscribe((data: any) => {
      this.previewFile(data);
    });
  }
  preview(uuid?: any) {
    return this._mediaService.getFile('public/download/' + uuid).subscribe((data: any) => {
      this.previewFile(data);
    });
  }
  previewFile(data: any) {
    let blob = new Blob([data], { type: data.type });
    const blobUrl = URL.createObjectURL(blob);
    let file_type = data.type.split('/')[1];
    this._ccDialog.originalOpen(CCPreviewAttachmentComponent, {
      width: '100%',
      data: {
        url: blobUrl,
        blob: blob,
        type: file_type,
      },
    });
  }
  closeDialog() {
    this._dialogRef.close();
  }
}
