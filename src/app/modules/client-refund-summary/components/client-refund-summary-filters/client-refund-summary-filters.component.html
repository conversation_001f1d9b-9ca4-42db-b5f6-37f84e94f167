<div class="p-3">
  <form [formGroup]="filtersForm">
    <div class="row pb-3 align-items-end">
      <div class="col-md-4 d-flex gap-2 align-items-baseline">
        <p class="pr-3">Status</p>
        <button
          cc-button
          class="btn-sm"
          [color]="isSelected(item.id) ? 'primary' : 'accent'"
          *ngFor="let item of repeatOnDaysOptions"
          (click)="setRepeatOnDays(item.id)"
          type="button">
          {{ item.text }}
        </button>
      </div>

      <div class="col-md-4" style="margin-bottom: .4rem;">
        <cc-checkbox formControlName="lenientFilter">Is Lenient</cc-checkbox>
      </div>
      <div class="col"></div>
    </div>
    <div class="row">
      <div class="col">
        <cc-input label="Name" placeholder="Name" formControlName="nameFilter"></cc-input>
      </div>
      <div class="col">
        <cc-select label="Purpose" formControlName="purposeFilter" placeholder="Purpose" [data]="purposes"> </cc-select>
      </div>
      <div class="col">
        <cc-select label="Category" placeholder="Category" formControlName="categoryFilter" [data]="categories"> </cc-select>
      </div>
    </div>
    <div class="row">
      <div class="col">
        <cc-input label="Requested By" placeholder="Requested By" formControlName="creatorFilter"></cc-input>
      </div>
      <div class="col">
        <cc-select label="Complaint" placeholder="Complaint" formControlName="complaintTypeFilter" [data]="complaintTypes"> </cc-select>
      </div>
      <div class="col">
        <cc-select
          formControlName="paymentMethodFilter"
          label="Refund Method"
          placeholder="Refund Method"
          [data]="paymentMethods"></cc-select>
      </div>
    </div>
    <div class="row">
      <div class="col">
        <cc-datepicker label="From Date" placeholder="From Date" formControlName="fromDateFilter"></cc-datepicker>
      </div>
      <div class="col">
        <cc-datepicker label="To Date" placeholder="To Date" formControlName="toDateFilter"></cc-datepicker>
      </div>
      <div class="col"></div>
    </div>
    <div class="row">
      <div class="col"></div>
      <div class="col d-flex justify-content-center">
        <button cc-raised-button color="primary" (click)="filter()">Filter <cc-icon class="filter-icon">filter_alt</cc-icon></button>
      </div>
      <div class="col"></div>
    </div>
  </form>
</div>
