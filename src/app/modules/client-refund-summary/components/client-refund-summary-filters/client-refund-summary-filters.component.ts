import {
  Component,
  EventEmitter,
  OnD<PERSON>roy,
  OnInit,
  Output,
} from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ClientRefundSummaryService } from '../../services/client-refund-summary.service';
import { map, Subject, takeUntil } from 'rxjs';
import { SelectOption } from '@maids/cc-lib/select-input';
import {
  ClientRefundSummaryFilters,
  ClientRefundSummaryFiltersProperties,
} from '../../models/client-refund-summary-filters.model';

@Component({
  selector: 'app-client-refund-summary-filters',
  templateUrl: './client-refund-summary-filters.component.html',
  styleUrls: ['./client-refund-summary-filters.component.scss'],
})
export class ClientRefundSummaryFiltersComponent implements OnInit, OnDestroy {
  @Output() filtersChange = new EventEmitter<ClientRefundSummaryFilters[]>();
  repeatOnDaysOptions: SelectOption[] = [
    { id: 'PENDING', text: 'Pending' },
    { id: 'PAID', text: 'Paid' },
    { id: 'REJECTED', text: 'Rejected' },
  ];
  constructor(
    private _fb: FormBuilder,
    private _clientRefundSummaryService: ClientRefundSummaryService
  ) {}

  purposes: SelectOption[] = [];
  categories: SelectOption[] = [];
  complaintTypes: SelectOption[] = [];
  paymentMethods: SelectOption[] = [];
  destroy$ = new Subject<void>();

  filtersForm: FormGroup = this._fb.group({
    statusFilter: [],
    lenientFilter: false,
    nameFilter: '',
    creatorFilter: '',
    statusChangeDate: '',
    purposeFilter: '',
    categoryFilter: '',
    complaintTypeFilter: '',
    paymentMethodFilter: '',
    fromDateFilter: '',
    toDateFilter: '',
  });
  ngOnInit(): void {
    this.getPurposes();
    this.getCategories();
    this.getComplaintTypes();
    this.getPaymentMethods();

    this.filtersForm
      .get('categoryFilter')
      ?.valueChanges.pipe(takeUntil(this.destroy$))
      .subscribe((res) => {
        this.getPurposes(res as string);
      });

    this.filter();
  }

  getPurposes(category?: string) {
    this._clientRefundSummaryService
      .getPurposes(category)
      .pipe(
        map((res: { id: string; name: string }[]) =>
          res.map((item) => <SelectOption>{ text: item.name, id: item.id })
        )
      )
      .subscribe((res) => {
        this.purposes = res;
      });
  }

  getCategories() {
    this._clientRefundSummaryService
      .getCategories()
      .pipe(
        map((res: { id: string; name: string; code: string }[]) =>
          res.map(
            (item) =>
              <SelectOption>{ text: item.name, id: item.id, code: item.code }
          )
        )
      )
      .subscribe((res) => {
        this.categories = res;
      });
  }

  getComplaintTypes() {
    this._clientRefundSummaryService
      .getComplaintTypes()
      .pipe(
        map((res: { types: { label: string; id: string }[] }[]) =>
          res.flatMap((category) =>
            category.types.map(
              (item) => <SelectOption>{ text: item.label, id: item.id }
            )
          )
        )
      )
      .subscribe((res) => {
        this.complaintTypes = res;
      });
  }

  getPaymentMethods() {
    this._clientRefundSummaryService
      .getPaymentMethods()
      .pipe(
        map((res: { label: string; id: string }[]) =>
          res.map((item) => <SelectOption>{ text: item.label, id: item.id })
        )
      )
      .subscribe((res) => {
        this.paymentMethods = res;
      });
  }

  private _formatFilters(): ClientRefundSummaryFilters[] {
    const filters: ClientRefundSummaryFilters[] = [];
    if (this.filtersForm.get('statusFilter')?.value) {
      filters.push({
        property: 'status',
        operation: '=',
        value: this.filtersForm.get('statusFilter')?.value,
      });
    }
    filters.push({
      property: 'lenient',
      operation: '=',
      value: this.filtersForm.get('lenientFilter')?.value,
    });
    if (this.filtersForm.get('nameFilter')?.value) {
      filters.push({
        property: 'client.name',
        operation: 'like',
        value: this.filtersForm.get('nameFilter')?.value,
      });
    }
    if (this.filtersForm.get('purposeFilter')?.value) {
      filters.push({
        property: 'purpose.id',
        operation: '=',
        value: this.filtersForm.get('purposeFilter')?.value,
      });
    }
    if (this.filtersForm.get('categoryFilter')?.value) {
      filters.push({
        property: 'purpose.refundCategory.id',
        operation: '=',
        value: this.filtersForm.get('categoryFilter')?.value,
      });
    }
    if (this.filtersForm.get('complaintTypeFilter')?.value) {
      filters.push({
        property: 'complaint.primaryType.id',
        operation: '=',
        value: this.filtersForm.get('complaintTypeFilter')?.value,
      });
    }
    if (this.filtersForm.get('paymentMethodFilter')?.value) {
      filters.push({
        property: 'methodOfPayment',
        operation: '=',
        value: this.filtersForm.get('paymentMethodFilter')?.value,
      });
    }
    if (this.filtersForm.get('creatorFilter')?.value) {
      filters.push({
        property: 'creator.loginName',
        operation: 'like',
        value: '%' + this.filtersForm.get('creatorFilter')?.value + '%',
      });
    }
    if (this.filtersForm.get('toDateFilter')?.value) {
      filters.push({
        property: 'statusChangeDate',
        operation: '<=',
        value: this.filtersForm.get('toDateFilter')?.value,
      });
    }
    if (this.filtersForm.get('fromDateFilter')?.value) {
      filters.push({
        property: 'statusChangeDate',
        operation: '>=',
        value: this.filtersForm.get('fromDateFilter')?.value,
      });
    }
    return filters;
  }

  filter() {
    this.filtersChange.emit(this._formatFilters());
  }

  isSelected(day: string): boolean {
    const statusFilter = this.filtersForm.get('statusFilter')?.value;
    return statusFilter === day;
  }
  setRepeatOnDays(value: string) {
    const currentValue = this.filtersForm.get('statusFilter')?.value;
    // Toggle: if the same value is clicked, unselect it (set to null/empty)
    const newValue = currentValue === value ? null : value;
    this.filtersForm.get('statusFilter')?.setValue(newValue);
    this.filter();
  }
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
