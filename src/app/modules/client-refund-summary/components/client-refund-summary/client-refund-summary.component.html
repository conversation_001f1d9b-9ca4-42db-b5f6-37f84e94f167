<app-client-refund-summary-filters (filtersChange)="onFiltersChange($event)"></app-client-refund-summary-filters>
<div class="d-flex justify-content-end p-3">
  <button cc-raised-button (click)="exportData()">
    <cc-icon [inline]="true">description</cc-icon>
    Export to Excel
  </button>
</div>
<div class="p-3" *ngIf="tableData$ | async as tableData">
  <cc-datagrid
    [columns]="columns"
    [data]="tableData?.content ?? []"
    [length]="tableData?.totalElements"
    [pageIndex]="tableData?.number"
    [pageSize]="tableData?.size"
    [pageOnFront]="false"
    [sortDirection]="sortDirection.value"
    [sortActive]="sortActive.value"
    (sortChange)="onSortChange($event)"
    [noResultTemplate]="noResultTpl"
    [stickyHeader]="true"
    (page)="nextPage($event)"
    [cellTemplate]="{ attachments: attachmentsTemplate }">
    <ng-template #attachmentsTemplate let-row>
      <div *ngIf="(row.attachments && row.attachments.length == 0) || !row.attachments; else attachments">No Attachments</div>
      <ng-template #attachments>
        <div>Attachments</div>
        <div>
          <button cc-icon-button (click)="openAttachmentsDialog(row)">
            <cc-icon>folder_open</cc-icon>
          </button>
        </div>
      </ng-template>
    </ng-template>
    <ng-template #noResultTpl>
      <div class="text-center p-4">
        <cc-icon class="text-muted">search_off</cc-icon>
        <p class="mt-2">No results found</p>
      </div>
    </ng-template>
  </cc-datagrid>
</div>
