import { AfterViewInit, ChangeDetectorRef, Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { BehaviorSubject, Observable, of, Subject, takeUntil } from 'rxjs';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { ClientRefundSummaryService } from '../../services/client-refund-summary.service';
import { PageEvent } from '@angular/material/paginator';
import { PaginatedEntity, PaginationRequest } from '@maids/cc-lib/common';
import { CCDialog } from '@maids/cc-lib/dialog';
import { AttachmentsDialogComponent } from '../attachments-dialog/attachments-dialog.component';
import { ClientRefundSummaryFilters } from '../../models/client-refund-summary-filters.model';
import { MediaService } from '@maids/cc-lib/services';
@Component({
  selector: 'app-client-refund-summary',
  templateUrl: './client-refund-summary.component.html',
  styleUrls: ['./client-refund-summary.component.scss'],
})
export class ClientRefundSummaryComponent implements OnInit, OnDestroy {
  destroy$ = new Subject<void>();
  sortActive = new BehaviorSubject<any>('');
  sortDirection = new BehaviorSubject<any>('');
  columns: CCGridColumn[] = [
    {
      header: 'Name',
      field: 'client.name',
      sortable: true,
      width: '10rem',
      formatter: (row: any) => {
        return row.client.label;
      },
    },
    {
      header: 'Category',
      field: 'purpose.refundCategory.name',
      sortable: true,
      width: '10rem',
      formatter: (row: any) => {
        return row.purpose.category;
      },
    },
    {
      header: 'Purpose',
      field: 'purpose.name',
      sortable: true,
      width: '20rem',
      formatter: (row: any) => {
        return row.purpose.label;
      },
    },
    {
      header: 'Amount',
      field: 'amount',
      width: '5rem',
      sortable: true,
    },
    {
      header: 'Master Amount',
      field: 'parent.amount',
      width: '12rem',
      sortable: true,
      formatter: (row: any) => {
        return row.parent.amount || ' ';
      },
    },
    {
      header: 'Method of Payment',
      field: 'methodOfPayment',
      width: '10rem',
      formatter: (row: any) => {
        return row.methodOfPayment.label;
      },
    },
    {
      header: 'Status',
      field: 'status',
      width: '5rem',
      sortable: true,
      formatter: (row: any) => {
        return row.status.label;
      },
    },
    {
      header: 'Is Lenient',
      field: 'lenient',
      width: '5rem',
      formatter: (row: any) => {
        return row.lenient ? 'Yes' : 'No';
      },
    },
    {
      header: 'Leniency type',
      field: 'leniencyType',
      width: '10rem',
      formatter: (row: any) => {
        return row.leniencyType || ' ';
      },
    },
    {
      header: 'Contract Type',
      field: 'contractType',
      width: '10rem',
    },
    {
      header: 'Last Action Date',
      field: 'lastModificationDate',
      width: '10rem',
      sortable: true,
    },
    {
      header: 'Notes',
      field: 'notes',
      width: '10rem',
      sortable: true,
      formatter: (row: any) => {
        return row.notes || ' ';
      },
    },
    {
      header: 'Request ID',
      field: 'id',
      sortable: true,
      formatter: (row: any) => {
        return row.displayId || ' ';
      },
    },
    {
      header: 'Transaction',
      field: 'transaction.id',
      width: '10rem',
      sortable: true,
      formatter: (row: any) => {
        return row.transaction ? row.transaction.id || row.transaction : ' ';
      },
    },
    {
      header: 'Requested by',
      field: 'creator.fullName',
      width: '10rem',
      sortable: true,
      formatter: (row: any) => {
        return row.requesterUserName || ' ';
      },
    },
    {
      header: 'Complaint',
      field: 'complaint.primaryType.name',
      width: '10rem',
      sortable: true,
      formatter: (row: any) => {
        return row.complaint.label || ' ';
      },
    },
    {
      header: 'Attachments',
      field: 'attachments',
      width: '10rem',
      formatter: (row: any) => {
        if (row.attachments.length == 0) return 'No Attachments';
        return row.attachments;
      },
    },
  ];

  filters: ClientRefundSummaryFilters[] = [];

  data?: any;
  tableData$!: Observable<any>;

  constructor(
    private _clientRefundSummaryService: ClientRefundSummaryService,
    private _dialog: CCDialog,
    private _mediaService: MediaService
  ) {}

  ngOnInit(): void {}

  getDGData(page: number, size: number, searchData: ClientRefundSummaryFilters[], sort?: string) {
    this.tableData$ = this._clientRefundSummaryService
      .searchClientRefunds(page ?? 0, size ?? 20, searchData, sort)
      .pipe(takeUntil(this.destroy$));
  }
  exportData() {
    this._mediaService.downloadFile(['accounting', 'clientRefundTodo', 'downloadSearchAttachment', 'csv'].join('/'), '', {
      method: 'POST',
      body: this.filters,
    });
  }
  nextPage(event: PageEvent) {
    const page = event.pageIndex;
    const size = event.pageSize;
    this.getDGData(page, size, this.filters, this.sortActive.value + ',' + this.sortDirection.value);
  }

  openAttachmentsDialog(row: any) {
    const dialogRef = this._dialog.originalOpen(AttachmentsDialogComponent, {
      width: '550px',
      data: { attachments: row.attachments },
    });
  }

  onFiltersChange(filters: ClientRefundSummaryFilters[]) {
    this.filters = filters;
    this.getDGData(0, 20, this.filters, this.sortActive.value + ',' + this.sortDirection.value);
  }

  onSortChange(event: any) {
    this.sortActive.next(event.active);
    this.sortDirection.next(event.direction);
    this.getDGData(0, 20, this.filters, event.active + ',' + event.direction);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
