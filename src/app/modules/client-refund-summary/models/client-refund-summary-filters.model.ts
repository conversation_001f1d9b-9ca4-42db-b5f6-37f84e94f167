export interface ClientRefundSummaryFiltersProperties {
  status?: string;
  lenient?: boolean;
  purpose?: { id: number | string; refundCategory: { id: number | string } };
  client?: { name: string };
  methodOfPayment?: string;
  complaint?: { primaryType: { id: number | string } };
  creator?: { loginName: string };
  statusChangeDate?: string;
}

export type ClientRefundSummaryFiltersOperations = '=' | 'like' | '<=' | '>=';

export interface ClientRefundSummaryFilters {
  operation: ClientRefundSummaryFiltersOperations;
  property:
    | keyof ClientRefundSummaryFiltersProperties
    | 'client.name'
    | 'purpose.refundCategory.id'
    | 'purpose.id'
    | 'complaint.primaryType.id'
    | 'creator.loginName';
  value: any;
}
