import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { CCBackendEndpoint } from '@maids/cc-erp-services';
import { Attachment, PaginatedEntity } from '@maids/cc-lib/common';
import { Observable } from 'rxjs';
import { API } from 'src/environments/api';
import { ClientRefundSummaryFilters } from '../models/client-refund-summary-filters.model';

@Injectable({
  providedIn: 'root',
})
export class ClientRefundSummaryService {
  constructor(private _http: HttpClient, private _api: CCBackendEndpoint) {}

  getPurposes(category?: string): Observable<any[]> {
    const params = {
      category: category ?? '',
    };
    return this._http.get<any[]>([this._api, API.getPurposes].join('/'), {
      params,
    });
  }

  getCategories(): Observable<any[]> {
    return this._http.get<any[]>([this._api, API.getCategoriesPicklist].join('/'));
  }

  getComplaintTypes(): Observable<any[]> {
    return this._http.get<any[]>([this._api, API.getComplaintTypes].join('/'));
  }

  getPaymentMethods(): Observable<any[]> {
    return this._http.get<any[]>([this._api, API.getPaymentMethods].join('/'));
  }

  searchClientRefunds(
    page: number = 0,
    size: number = 20,
    searchData: ClientRefundSummaryFilters[],
    sort?: string
  ): Observable<PaginatedEntity<any>> {
    const params:any = {
      page: page,
      size: size,
    };
    if (sort) {
      params.sort = sort;
    }
    const body = [...searchData]
    return this._http.post<any>(
      [this._api, API.searchClientRefunds].join('/'),
      body,
      { params }
    );
  }

  downloadAttachment(uuid: string) {
    return this._http.get([API.download, uuid].join('/'), {
      responseType: 'blob',
    });
  }

  exportClientRefunds(filters: ClientRefundSummaryFilters[]): Observable<any> {
    return this._http.post<any>([this._api, API.exportClientRefunds].join('/'), filters);
  }
}
