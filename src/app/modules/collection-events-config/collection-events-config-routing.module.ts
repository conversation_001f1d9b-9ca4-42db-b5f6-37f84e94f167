import { RouterModule, Routes } from '@angular/router';
import { CollectionEventsConfigListComponent } from './components/collection-events-config-list/collection-events-config-list.component';
import { SubEventsComponent } from './components/sub-events/sub-events.component';
import { NgModule } from '@angular/core';

const routes: Routes = [
  {
    path: '',
    component: CollectionEventsConfigListComponent,
    data: { label: '' },
  },
  {
    path: 'sub-events/:id',
    component: SubEventsComponent,
    data: {
      label: 'Sub Events',
      pageCode: 'ACCOUNTING__CollectionEventsConfig',
    },
  },
];
@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class CollectionEventsConfigRoutingModule {}
