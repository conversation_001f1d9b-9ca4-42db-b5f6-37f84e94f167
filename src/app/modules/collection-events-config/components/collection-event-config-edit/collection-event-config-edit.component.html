<cc-dialog-header>
  <h1 cc-dialog-title>Edit Event Config</h1>
  <a
    role="button"
    type="button"
    cc-icon-button
    cc-dialog-close-button
    cc-dialog-close
  ></a>
</cc-dialog-header>
<cc-dialog-content>
  <form [formGroup]="formGroup">
    <div class="form-group row align-items-center px-2">
      <cc-input
        label="Title"
        class="w-100"
        [required]="true"
        type="text"
        formControlName="name"
        [ccValidateBy]="[validateNotEmpty]"
      ></cc-input>
    </div>
    <div class="form-group row align-items-center px-2">
      <cc-input
        label="Max flows runs"
        class="w-100"
        type="text"
        formControlName="maxFlowRuns"
        [ccValidateBy]="[validateNotEmpty]"
      ></cc-input>
    </div>
    <div>
      <cc-checkbox
        color="accent"
        formControlName="stopMessagesOnContractCancellation"
      >
        Stop messages for cancelled contract?
      </cc-checkbox>
    </div>
    <div>
      <cc-checkbox
        color="accent"
        formControlName="stopTodosOnContractCancellation"
      >
        Stop todos for cancelled contract?
      </cc-checkbox>
    </div>
    <div>
      <cc-checkbox color="accent" formControlName="closeToDosUponCompletion">
        Close ToDos Upon Completion?
      </cc-checkbox>
    </div>
    <div class="form-group row align-items-center px-2">
      <cc-select
        class="w-100"
        label="Scheduled termination reason"
        formControlName="cancellationReason"
        [lazyPageFetcher]="cancellationReasons"
        [ccValidateBy]="[validateNotEmpty]"
        [emitFullSelectOption]="true"
        [search]="true"
      ></cc-select>
    </div>
  </form>
  <div class="d-flex float-right">
    <button cc-button cc-dialog-close>Cancel</button>
    <button cc-button color="accent" (click)="save()">Save</button>
  </div>
</cc-dialog-content>
