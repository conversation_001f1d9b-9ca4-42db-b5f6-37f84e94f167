<cc-dialog-header>
  <h1 cc-dialog-title>Edit Sub-Event</h1>
  <a role="button" type="button" (click)="cancel()"><cc-icon>close</cc-icon></a>
</cc-dialog-header>
<cc-dialog-content>
  <form [formGroup]="formGroup">
    <div class="form-group row align-items-center px-2">
      <cc-input
        label="Title"
        class="w-100"
        type="text"
        formControlName="name"
        [ccValidateBy]="[validateNotEmpty]"
      ></cc-input>
    </div>
    <div class="form-group row align-items-center px-2">
      <cc-input
        label="Max Trials"
        class="w-100"
        type="text"
        formControlName="maxTrials"
        [ccValidateBy]="[validateNotEmpty]"
      ></cc-input>
    </div>
    <div class="form-group row align-items-center px-2">
      <cc-input
        label="Trials max reminders"
        class="w-100"
        type="text"
        formControlName="maxReminders"
        [ccValidateBy]="[validateNotEmpty]"
      ></cc-input>
    </div>
    <div>
      <cc-checkbox
        color="accent"
        formControlName="terminateContractOnMaxTrials"
      >
        Schedule contract for termination on max trial?
      </cc-checkbox>
    </div>
    <div>
      <cc-checkbox
        color="accent"
        formControlName="terminateContractOnMaxReminders"
      >
        Schedule contract for termination on max reminder?
      </cc-checkbox>
    </div>
    <div class="form-group row align-items-center px-2">
      <cc-select
        label="Required Action"
        class="w-100"
        formControlName="requiredAction"
        [data]="requiredActions$ | async"
        [ccValidateBy]="[validateNotEmpty]"
        [search]="true"
        [emitFullSelectOption]="true"
      ></cc-select>
    </div>
    <div class="form-group row align-items-center px-2">
      <cc-select
        label="Required Document"
        class="w-100"
        formControlName="requiredDocument"
        [data]="requiredDocuments$ | async"
        [ccValidateBy]="[validateNotEmpty]"
        [search]="true"
        [emitFullSelectOption]="true"
      ></cc-select>
    </div>
  </form>
  <div class="d-flex float-right">
    <button cc-button cc-dialog-close>Cancel</button>
    <button cc-button color="accent" (click)="save()">Save</button>
  </div>
</cc-dialog-content>
