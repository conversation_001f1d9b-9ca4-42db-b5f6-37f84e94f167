import { Component, Inject, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { CCDialog } from '@maids/cc-lib/dialog';
import { CCNotificationService } from '@maids/cc-lib/services';
import { EventConfigStoreService } from '../../../services/event-config-store.service';
import { EventConfigService } from '../../../services/event-config.service';
import { PaginatedEntity, PaginationRequest } from '@maids/cc-lib/common';
import { map, Observable, tap } from 'rxjs';
import { CCValidatorFn } from '@maids/cc-lib/validation';
import { ManageTagsComponent } from '../manage-tags.component';
import { SelectOption } from '@maids/cc-lib/select-input';

@Component({
  selector: 'app-key-value-tag',
  templateUrl: './key-value-tag.component.html',
  styleUrls: ['./key-value-tag.component.scss'],
})
export class KeyValueTagComponent implements OnInit {
  fetchTags = (pageReq: PaginationRequest): Observable<SelectOption[]> => {
    return this.eventConfigService
      .fetchTags(
        pageReq.page,
        pageReq.size,
        pageReq.searchString,
        'query',
        pageReq.searchString
      )
      .pipe(
        map((data) =>
          data.content.map(
            (opt: any) => ({ id: opt.id, text: opt.name } as SelectOption)
          )
        )
      );
  };
  formGroup!: FormGroup;
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    public dialogRef: MatDialogRef<KeyValueTagComponent>,
    private store: EventConfigStoreService,
    private eventConfigService: EventConfigService,
    private formBilder: FormBuilder,
    public readonly notifications: CCNotificationService,
    private ccDialog: CCDialog
  ) {}

  ngOnInit(): void {
    this.formGroup = this.formBilder.group({
      key: ['', Validators.required],
      value: ['', Validators.required],
    });
  }
  save() {
    if (this.formGroup.valid) {
      this.eventConfigService
        .createTag(
          this.formGroup.controls['key'].value.text,
          this.formGroup.controls['value'].value
        )
        .subscribe({
          next: (res: any) => {
            let payload;
            payload = {
              id: this.data.id,
              tags: [
                ...this.data.tags,
                {
                  id: res.id,
                },
              ],
            };
            this.eventConfigService.update(payload).subscribe({
              next: (tags: any) => {
                this.notifications.notifySuccess('Updated Successfully', 2000);
                this.dialogRef.close(true);
                this.ccDialog.originalOpen(ManageTagsComponent, {
                  data: {
                    id: this.data.id,
                  },
                });
              },
              error: (err) => {
                this.notifications.notifyError(err.error.message, 2000);
              },
            });
          },
          error: (err) => {
            this.notifications.notifyError(err.error.message, 2000);
          },
        });
    }
  }
  cancel() {
    this.dialogRef.close();
    this.ccDialog.originalOpen(ManageTagsComponent, {
      data: {
        id: this.data.id,
      },
    });
  }
}
