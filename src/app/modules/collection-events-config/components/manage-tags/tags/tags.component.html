<cc-dialog-header>
  <h1 cc-dialog-title>Add Tags</h1>
  <a
    role="button"
    type="button"
    cc-icon-button
    cc-dialog-close-button
    cc-dialog-close
    (click)="cancel()"
  ></a
></cc-dialog-header>
<cc-dialog-content>
  <cc-select
    [(ngModel)]="selectedTags"
    name="name"
    [lazyPageFetcher]="fetchTags"
    search="true"
    multiple="true"
    [emitFullSelectOption]="true"
  ></cc-select>
</cc-dialog-content>
<cc-dialog-actions>
  <button cc-button (click)="cancel()">Cancel</button>
  <button cc-button color="accent" (click)="save()">Save</button>
</cc-dialog-actions>
