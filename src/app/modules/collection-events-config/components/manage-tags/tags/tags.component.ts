import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { CCDialog } from '@maids/cc-lib/dialog';
import { CCNotificationService } from '@maids/cc-lib/services';
import { EventConfigStoreService } from '../../../services/event-config-store.service';
import { EventConfigService } from '../../../services/event-config.service';
import { PaginatedEntity, PaginationRequest } from '@maids/cc-lib/common';
import { map, Observable, of, tap } from 'rxjs';
import { ManageTagsComponent } from '../manage-tags.component';
import { SelectOption } from '@maids/cc-lib/select-input';
@Component({
  selector: 'app-tags',
  templateUrl: './tags.component.html',
  styleUrls: ['./tags.component.scss'],
})
export class TagsComponent implements OnInit {
  formGroup!: FormGroup;
  dataExtended: any[] = [];
  fetchTags = (pageReq: PaginationRequest): Observable<SelectOption[]> => {
    return this.eventConfigService
      .fetchTags(
        pageReq.page,
        pageReq.size,
        pageReq.searchString,
        'query',
        pageReq.searchString
      )
      .pipe(
        map((data) =>
          data.content.map(
            (opt: any) => ({ id: opt.id, text: opt.name } as SelectOption)
          )
        )
      );
  };
  selectedTags: any[] = [];
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    public dialogRef: MatDialogRef<TagsComponent>,
    private store: EventConfigStoreService,
    private eventConfigService: EventConfigService,
    private formBilder: FormBuilder,
    public readonly notifications: CCNotificationService,
    private ccDialog: CCDialog
  ) {}
  ngOnInit(): void {
    this.selectedTags = this.data.tags;
  }
  save() {
    let payload;
    let tags = this.selectedTags.map((tag: any) => {
      return {
        id: tag.id,
      };
    });
    payload = {
      id: this.data.id,
      tags: tags,
    };
    console.log(payload);
    
    this.eventConfigService.update(payload).subscribe({
      next: (tags: any) => {
        this.notifications.notifySuccess('Updated Successfully', 2000);
        this.dialogRef.close(true);
        this.ccDialog.originalOpen(ManageTagsComponent, {
          data: {
            id: this.data.id,
          },
        });
      },
      error: (err) => {
        this.notifications.notifyError(err.error.message, 2000);
      },
    });
  }
  cancel() {
    this.dialogRef.close();
    this.ccDialog.originalOpen(ManageTagsComponent, {
      data: {
        id: this.data.id,
      },
    });
  }
}
