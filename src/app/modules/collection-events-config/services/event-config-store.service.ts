import { Injectable } from '@angular/core';
import { BaseStoreService, SearchModel } from '@maids/cc-lib/common';
import { Store } from '@ngrx/store';
import { EventConfigService } from './event-config.service';
import * as EventConfigActions from '../store/events-config-action';
import * as SubEventActions from '../store/sub-event-action';
import * as SubEventSelector from '../store/sub-event.selector';
import * as EventConfigSelector from '../store/events-config-selectors';
import { initialState } from '../store/events-config-reducer';
import { BehaviorSubject, map, Observable } from 'rxjs';
import { SortDirection } from '@angular/material/sort';
import { SubEventModel } from '../models/sub-event.model';

@Injectable({
  providedIn: 'root',
})
export class EventConfigStoreService extends BaseStoreService {
  searchSubject = new BehaviorSubject<SearchModel<any>>(initialState.search);

  readonly getEventsConfigList$ = this.store.select(
    EventConfigSelector.selectEventsConfigList
  );
  readonly getSubEventsConfigList$ = this.store.select(
    SubEventSelector.selectSubEventsConfig
  );
  constructor(store: Store, _eventConfig: EventConfigService) {
    super(store);
  }
  readonly updateSearchQueryParams = (event: {
    pageIndex: number;
    pageSize: number;
  }) => {
    this.store.dispatch(
      EventConfigActions.updateEventsConfigListParams({ event })
    );
    this.searchSubject.next({
      search: this.searchSubject.getValue().search,
      params: {
        page: event.pageIndex,
        size: event.pageSize,
      },
    });
    this.loadData({
      search: this.searchSubject.getValue(),
      params: { page: event.pageIndex, size: event.pageSize },
    });
  };
  readonly updateSearch = (event: any) => {
    this.store.dispatch(EventConfigActions.updateSearch({ event }));
  };
  readonly loadData = (search: SearchModel<any>) => {
    this.store.dispatch(
      EventConfigActions.fetchEventsConfigList({
        search,
      })
    );
  };
  readonly updateSearchSortQueryParams = (event: {
    active: string;
    direction: SortDirection;
  }) => {
    this.store.dispatch(
      EventConfigActions.updateSearchSortQueryParams({ event })
    );
    this.searchSubject.next({
      search: this.searchSubject.getValue().search,
      params: {
        sort: event.active + ',' + event.direction,
      },
    });
    this.loadData({
      search: this.searchSubject.getValue(),
      params: { sort: event.active + ',' + event.direction },
    });
  };
  readonly loadSubEventData = (id: number) => {
    this.store.dispatch(
      SubEventActions.getSubEvent({
        id,
        search: { params: { page: 0, size: 20 } },
      })
    );
  };
  pickListsCodes(): string[] {
    return [];
  }
  resetState(): void {
    this.store.dispatch(EventConfigActions.resetEventsConfigList());
  }
  readonly getEventConfig$ = (id: number) =>
    this.store.select(EventConfigSelector.selectEventsConfigList).pipe(
      map((event) => {
        return event.content.find((item) => item.id === id);
      })
    );
  readonly getEventTags$ = (id: number) =>
    this.store.select(EventConfigSelector.selectEventsConfigList).pipe(
      map((event) => {
        return event.content.find((item) => item.id === id)?.tags;
      })
    );
  readonly removeTag = (id: number, tagId: number) => {
    this.store.dispatch(EventConfigActions.removeTag({ id, tagId }));
  };
  readonly deleteSubEvent = (id: number) => {
    this.store.dispatch(SubEventActions.deleteSubEvent({ id }));
  };
  readonly getSubEventConfig$ = (
    id: number
  ): Observable<SubEventModel | undefined> =>
    this.store.select(SubEventSelector.selectSubEventsConfig).pipe(
      map((event) => {
        return event.find((item) => item.id === id);
      })
    );
  addNewPeriod(id: number) {
    this.store.dispatch(SubEventActions.addNewPeriod({ id }));
  }
}
