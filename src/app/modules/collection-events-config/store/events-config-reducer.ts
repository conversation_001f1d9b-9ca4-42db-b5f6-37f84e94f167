import { CollectionEventsConfigStateModel } from './events-config-state.model';
import { createReducer, on } from '@ngrx/store';
import * as EventConfigActions from './events-config-action';
export const EventConfigFeatureKey: 'collection_events_config' =
  'collection_events_config';
export const initialState: CollectionEventsConfigStateModel = {
  eventsConfigList: {
    content: [],
    number: 0,
    size: 0,
    totalPages: 0,
    totalElements: 0,
  },
  search: {
    search: {},
    params: {
      page: 0,
      size: 20,
      sort: '',
    },
  },
};
export const reducer = createReducer(
  initialState,
  on(EventConfigActions.fetchEventsConfigList, (state, { search }) => {
    return {
      ...state,
      defaultParams: {
        search: search.search,
        prams: search.params,
      },
    };
  }),
  on(EventConfigActions.fetchEventsConfigListSuccess, (state, { data }) => {
    return {
      ...state,
      eventsConfigList: data,
    };
  }),
  on(EventConfigActions.resetEventsConfigList, (state) => {
    return {
      ...state,
      eventsConfigList: initialState.eventsConfigList,
    };
  }),
  on(EventConfigActions.removeTag, (state, { id, tagId }) => {

    return {
      ...state,
      eventsConfigList: {
        ...state.eventsConfigList,
        content: state.eventsConfigList.content.map((event) => {
          if (event.id === id) {
            return {
              ...event,
              tags: event.tags.filter((tag) => tag.id !== tagId),
            };
          }
          return event;
        }),
      },
    };
  })
);
