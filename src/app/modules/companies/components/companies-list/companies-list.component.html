<div class="acc-7904">
  <div class="d-flex justify-content-end my-2">
    <button
      cc-stroked-button
      color="accent"
      style="padding-right: 30px"
      (click)="addCompany()"
    >
      Add New Company <cc-icon class="icon">add</cc-icon>
    </button>
  </div>
  <cc-datagrid
    class="my-2"
    [data]="companies?.content"
    [columns]="gridCols"
    [length]="companies?.totalElements"
    [pageOnFront]="false"
    [pageIndex]="companies?.number"
    [pageSize]="companies?.size"
    [pageSizeOptions]="[20]"
    (page)="getNextPage($event)"
    [stickyHeader]="true"
    [columnMovable]="true"
    [columnHideable]="true"
    [showColumnMenuButton]="true"
    [showColumnMenuHeader]="false"
    [columnMenuButtonIcon]="'settings'"
    [cellTemplate]="{ isActive: isActive }"
  >
    <cc-grid-actions-list
      *ccActionData="let ctx of companies?.content; row as row"
      [renderedActionsCount]="3"
      style="width: fit-content; gap: 8px"
    >
      <button
        *cc-action
        cc-stroked-button
        color="basic"
        (click)="editCompany(row)"
      >
        Edit
      </button>
      <ng-container>
        <button *cc-action cc-raised-button color="warn" (click)="delete(row)">
          Delete
        </button>
      </ng-container>
      <ng-container *ngIf="row.isActive">
        <button
          *cc-action
          cc-stroked-button
          color="basic"
          (click)="disable(row)"
        >
          Disable
        </button>
      </ng-container>
      <ng-container *ngIf="!row.isActive">
        <button
          *cc-action
          cc-stroked-button
          class="bg-success text-white"
          (click)="enable(row)"
        >
          Enable
        </button>
      </ng-container>
    </cc-grid-actions-list>
  </cc-datagrid>
  <ng-template #isActive let-row let-index="index" let-col="colDef">
    <span
      [ngClass]="{ 'text-success': row.isActive, 'text-danger': !row.isActive }"
      >{{ row.isActive ? "Enabled" : "Disabled" }}</span
    >
  </ng-template>
</div>
