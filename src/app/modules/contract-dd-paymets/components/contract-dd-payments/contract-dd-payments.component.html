<!-- <cc-breadcrumbs></cc-breadcrumbs> -->
<div class="container-fluid pt-3 pl-5 pr-5 pb-5">
  <div class="d-flex flex-column gap-3">
    <div>
      <strong class="cc-secondary">1.Family Details </strong>
      <span class="pl-2">
        <span class="cc-secondary">(Type:</span>
        {{ paymentInfo?.contract?.contractProspectType ? paymentInfo?.contract?.contractProspectType?.label : ''
        }}<span class="cc-secondary">)</span>
      </span>
    </div>
    <div class="row border-bottom border-bottom">
      <div class="col-md-6 d-flex gap-2 align-items-baseline">
        <h6 class="m-0">Family Name</h6>
        <p class="m-0">{{ paymentInfo?.client?.name }}</p>
      </div>
      <div class="col-md-6 d-flex gap-2 align-items-baseline">
        <h6 class="m-0">Mobile Number</h6>
        <p class="m-0">{{ paymentInfo?.client?.mobileNumber }}</p>
      </div>
    </div>
    <div class="row border-bottom">
      <div class="col-md-6 d-flex gap-2 align-items-baseline">
        <h6 class="m-0">E-mail</h6>
        <p class="m-0">{{ paymentInfo?.client?.email }}</p>
      </div>
    </div>
    <div><strong class="cc-secondary">2. Contract Details</strong></div>
    <div class="row border-bottom">
      <div class="col-md-6 d-flex gap-2 align-items-baseline">
        <h6 class="m-0">Start Date</h6>
        <p class="m-0">{{ paymentInfo?.contract?.startOfContract?.split(' ')[0] }}</p>
      </div>
      <div class="col-md-6 d-flex gap-2 align-items-baseline">
        <h6 class="m-0">Contact ID</h6>
        <p class="m-0">{{ paymentInfo?.contract?.id }}</p>
      </div>
    </div>
    <div class="row border-bottom">
      <div class="col-md-6 d-flex gap-2 align-items-baseline">
        <h6 class="m-0">End Date</h6>
        <p class="m-0">{{ paymentInfo?.contract?.endOfContract?.split(' ')[0] }}</p>
      </div>
      <div class="col-md-6 d-flex gap-2 align-items-baseline" *ngIf="mode != 'maidVisa'">
        <h6 class="m-0">Contract Type</h6>
        <p class="m-0">{{ getValue(paymentInfo?.contract?.contractType) }}</p>
      </div>
      <div class="col-md-6 d-flex gap-2 align-items-baseline" *ngIf="mode == 'maidVisa'">
        <h6 class="m-0">Worker Name</h6>
        <p class="m-0">{{ paymentInfo?.contract?.workerName }}</p>
      </div>
    </div>
    <div class="row border-bottom">
      <div class="col-md-6 d-flex gap-2 align-items-baseline">
        <h6 class="m-0">Contract Duration</h6>
        <p class="m-0">{{ contractDuration }}</p>
      </div>
      <div class="col-md-6 d-flex gap-2 align-items-baseline" *ngIf="mode == 'maidVisa'">
        <h6 class="m-0">Worker Nationality</h6>
        <p class="m-0">{{ paymentInfo?.contract?.workerNationality ? paymentInfo?.contract?.workerNationality?.label : '' }}</p>
      </div>
    </div>
    <div class="row border-bottom">
      <div class="col-md-6 d-flex gap-2 align-items-baseline">
        <h6 class="m-0">Initial Payments Duration</h6>
        <p class="m-0">{{ paymentsDuration }}</p>
      </div>
      <div class="col-md-6 d-flex gap-2 align-items-baseline" *ngIf="mode == 'maidVisa'">
        <h6 class="m-0">Worker Salary</h6>
        <p class="m-0">{{ paymentInfo?.contract?.workerSalary + ' ' }}AED</p>
      </div>
    </div>
    <div class="row border-bottom" *ngIf="mode == 'maidVisa'">
      <div class="col-md-6 d-flex gap-2 align-items-baseline">
        <h6 class="m-0">Worker Current Situation</h6>
        <p class="m-0">{{ paymentInfo?.contract?.workerCurrentSituation ? paymentInfo?.contract?.workerCurrentSituation?.label : '' }}</p>
      </div>
      <div class="col-md-6 d-flex gap-2 align-items-baseline">
        <h6 class="m-0">Worker Type</h6>
        <p class="m-0">{{ paymentInfo?.contract?.workerType ? paymentInfo?.contract?.workerType?.label : '' }}</p>
      </div>
    </div>
  </div>
  <div class="d-flex justify-content-center m-4" *ngIf="directDebitCount == 0">
    <h6 class="cc-secondary">There is no DD payment to generate, please press "Save" to generate the receipt</h6>
  </div>
  <div class="custom-div" [formGroup]="form" *ngIf="directDebitCount > 0">
    <div class="row">
      <cc-input-mask
        [showMaskTyped]="true"
        [ccValidateBy]="[maskValidator]"
        [mask]="maskEid"
        [prefix]="'784'"
        [patterns]="patterns"
        [required]="true"
        class="col-md-8"
        label="EID Of Bank Holder"
        formControlName="EID"></cc-input-mask>
    </div>
    <div class="row align-items-center">
      <cc-input class="col-md-7" label="IBAN" formControlName="IBAN" (ngModelChange)="ibanInfo = {}"></cc-input>
      <div class="pb-3">
        <button [disabled]="!form.get('IBAN')?.value || ibanInfo?.success" cc-raised-button class="w-100" (click)="validateIBAN()">
          Vlidate
        </button>
      </div>
    </div>
    <div *ngIf="ibanInfo && ibanInfo.success" class="pt-1 pb-4 pl-1">
      <h6 class="text-success m-0">
        BIC:
        {{
          ibanInfo && ibanInfo?.bank_data
            ? ibanInfo?.bank_data?.bic + ' ' + ibanInfo?.bank_data?.city + ', ' + ibanInfo?.bank_data?.country
            : ''
        }}
      </h6>
    </div>
    <div *ngIf="ibanInfo && !ibanInfo.success" class="pt-1 pb-4 pl-1">
      <h6 class="cc-warn">{{ ibanInfo ? ibanInfo?.error : '' }}</h6>
    </div>
    <div class="row">
      <div class="col-md-5">
        <cc-input label="Bank Name" [disabled]="true"  formControlName="bankName"></cc-input>
        <div *ngIf="form.get('bankName')?.invalid" class="text-danger errorBank">This field is required</div>
      </div>

      <div class="col-md-3">
        <cc-select
          label="Bank Name"
          
          formControlName="bankNameSelect"
          [lazyPageFetcher]="fetchBankName"
          [data]="bankNameOptions"></cc-select>
      </div>
      <div class="col-md-8">
        <cc-checkbox formControlName="spouseWillSignDD">The spouse will sign the Direct Debit</cc-checkbox>
      </div>
      <div class="col-md-8">
        <cc-input label="Account Name" [required]="form.get('IBAN')?.value != ''" formControlName="accountName"></cc-input>
      </div>
      <div class="col-md-8">
        <cc-file-uploader  [dropzoneConfig]="dropzoneconfig" class="w-100" label="Upload Screenshot of Bank Details" tag="iban" formControlName="IBANFile"></cc-file-uploader>
      </div>
    </div>
  </div>
  <h6 class="mt-5">Direct Debits</h6>
  <div>
    <div class="d-flex justify-content-between border">
      <p class="m-0 p-2 text-center border-right" style="width: 20%">Type</p>
      <p class="m-0 p-2 text-center border-right" style="width: 20%">Amount (AED)</p>
      <p class="m-0 p-2 text-center border-right" style="width: 20%">From Date</p>
      <p class="m-0 p-2 text-center border-right" style="width: 20%">To Date</p>
      <p class="m-0 p-2 text-center border-right" style="width: 20%">Status</p>
    </div>
    <div
      class="d-flex border"
      *ngFor="let payment of directDebitArr; let i = index"
      [ngStyle]="{ 'background-color': i % 2 != 0 ? '#f0f0f0' : 'white' }">
      <p class="m-0 p-2 text-center border-right" style="width: 20%">{{ payment.type ? payment.type.label : ' ' }}</p>
      <p class="m-0 p-2 text-center border-right" style="width: 20%">{{ payment.amount }}</p>
      <p class="m-0 p-2 text-center border-right" style="width: 20%">{{ payment.startDate.split(' ')[0] | date : 'dd MMM yyyy' }}</p>
      <p class="m-0 p-2 text-center border-right" style="width: 20%">{{ payment.expiryDate.split(' ')[0] | date : 'dd MMM yyyy' }}</p>
      <p class="m-0 p-2 text-center border-right" style="width: 20%">
        {{ payment?.status?.label }}
        <span class="cc-warn"
          >{{ payment.status && payment.status.value == 'PENDING' && payment.nonCompletedInfo ? '(Uncompleted)' : ' ' }}
        </span>
      </p>
    </div>
  </div>
  <div class="paymentDiv mt-4" *ngIf="!useSignatureHide" style="background: #f0f0f0" [formGroup]="form">
    <div class="customeDiv">
      <div class="row p-3" *ngIf="usePreviousApprovedSignatureShow || useNonRejectedSignatureShow">
        <div class="col-md-9">
          <cc-checkbox *ngIf="usePreviousApprovedSignatureShow" formControlName="useApprovedSignature"
            >Use a previous approved signature</cc-checkbox
          >
          <cc-checkbox *ngIf="!usePreviousApprovedSignatureShow" formControlName="useApprovedSignature"
            >Use existed signature (Not approved)</cc-checkbox
          >
        </div>
      </div>
    </div>
    <div class="customeDiv" *ngIf="!form.get('useApprovedSignature')?.value">
      <div class="row justify-content-center">
        <div class="col-md-3" *ngFor="let signatureControl of signatureFilesArray.controls; let i = index">
          <cc-signature
            (ngModelChange)="signatureChange($event, i)"
            [formControl]="$any(signatureControl)"
            [required]=" !form.get('useApprovedSignature')?.value"></cc-signature>
          <span class="cc-warn w-100 d-flex justify-content-center"> Signature {{ i + 1 }} </span>
        </div>
      </div>
    </div>
  </div>
  <div class="d-flex justify-content-end gap-2 pt-3">
    <button cc-raised-button (click)="saveChangesNoDD()">Family will not sign DD now</button>
    <button cc-raised-button color="primary" (click)="confirmSave()" [disabled]="form.invalid">Save {{ directDebitCount > 0 ? '& Generate DD' : '' }}</button>
  </div>
</div>
