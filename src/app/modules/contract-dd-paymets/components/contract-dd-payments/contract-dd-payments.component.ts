import { ChangeDetectionStrategy, ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ContractDdPaymentsService } from '../../services/contract-dd-payments.service';
import { FormArray, FormBuilder, Validators } from '@angular/forms';
import { PaginationRequest } from '@maids/cc-lib/common';
import { BehaviorSubject, debounceTime, distinctUntilChanged, map, Observable, startWith } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import * as moment from 'moment';
import { CCValidatorFn } from '@maids/cc-lib/validation';
import { SelectOption } from '@maids/cc-lib/select-input';
import { CCDialog } from '@maids/cc-lib/dialog';
import { HasFrontEidDocumentDialogComponent } from '../has-front-eid-document-dialog/has-front-eid-document-dialog.component';
import { CCNotificationService } from '@maids/cc-lib/services';
import { CCFileUploaderConfig } from '@maids/cc-lib/file-uploader';
@Component({
  selector: 'app-contract-dd-payments',
  templateUrl: './contract-dd-payments.component.html',
  styleUrls: ['./contract-dd-payments.component.scss'],
})
export class ContractDdPaymentsComponent implements OnInit {
  usePreviousApprovedSignatureShow: boolean = false;
  useNonRejectedSignatureShow: boolean = false;
  skipFrontEIDDocument: boolean = false;
  currentAction = new BehaviorSubject<string>('');
  clientHasFrontEIDDocument: boolean = false;
  useSignatureHide: boolean = true;
  signtureArray: { file: any; index: number }[] = [];
  maskEid = '-efgh-0000000-0';
  bankNameOptions: SelectOption[] = [];
  clientDocumentsTypes: any;
  patterns = {
    e: { pattern: /([1]|[2])/ },
    f: { pattern: /(9|0)/ },
    g: { pattern: /[0-9]/ },
    h: { pattern: /[0-9]/ },
    '0000000': { pattern: /[0-9]/ },
    '0': { pattern: /[0-9]/ },
  };
  ibanInfo: any = {};
  form = this.fb.group({
    EID: ['784', Validators.required],
    IBAN: ['AE'],
    bankName: ['', Validators.required],
    bankNameSelect: ['', Validators.required],
    spouseWillSignDD: [false],
    accountName: ['', Validators.required],
    IBANFile: [''],
    useApprovedSignature: [false],
    signatureFiles: this.fb.array([]),
    idFront: [''],
  });
  dropzoneconfig: CCFileUploaderConfig = {
    maxFiles: 1,
    maxFilesize: 25,
  };
  readonly fetchBankName = (pageReq: PaginationRequest) => {
    const params: any = {
      search: pageReq.searchString || '',
      page: 0,
      size: 100,
    };
    return this._contractDdPaymentsService
      .bankNameSelectOptions(params)
      .pipe(map((val: any) => val.map((option: any) => ({ id: option.id, text: option.label } as SelectOption))));
  };
  directDebitCount: number = 0;
  directDebitArr: any[] = [];
  id = this.route.snapshot.params['id'];
  contractDuration: string = '';
  paymentsDuration: string = '';
  mode: string = '';
  paymentInfo: any;
  constructor(
    private _contractDdPaymentsService: ContractDdPaymentsService,
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private cdr: ChangeDetectorRef,
    private _dialog: CCDialog,
    private _notifications: CCNotificationService
  ) {}

  ngOnInit(): void {
    this.getClientDocumentType();
    this.getFileSignature();
    if (this.id) {
      this._contractDdPaymentsService.getcontractpaymenttermbycontractwithdirectdebitpayments(this.id).subscribe((res) => {
        this.paymentInfo = res;
        if (res && res.directDebits.length > 0) {
          this.directDebitCount = res.directDebits.length;
        }
        const start = moment(res?.contract?.startOfContract, 'YYYY-MM-DD');
        const end = moment(res?.contract?.endOfContract, 'YYYY-MM-DD').add(1, 'day');

        const years = end.diff(start, 'years');
        start.add(years, 'years');

        const months = end.diff(start, 'months');
        start.add(months, 'months');

        const days = end.diff(start, 'days');

        this.contractDuration = [years > 0 ? `${years} years` : '', months > 0 ? `${months} months` : '', days > 0 ? `${days} days` : '']
          .filter(Boolean)
          .join(' ');

        const paymentsDurationEnd = moment().add(res.contract.paymentsDuration, 'months');
        const paymentsDurationStart = moment();
        const paymentsYears = paymentsDurationEnd.diff(paymentsDurationStart, 'years');
        paymentsDurationStart.add(paymentsYears, 'years');
        const paymentsMonths = paymentsDurationEnd.diff(paymentsDurationStart, 'months');
        paymentsDurationStart.add(paymentsMonths, 'months');
        const paymentsDays = paymentsDurationEnd.diff(paymentsDurationStart, 'days');

        this.paymentsDuration = [
          paymentsYears > 0 ? `${paymentsYears} years` : '',
          paymentsMonths > 0 ? `${paymentsMonths} months` : '',
          paymentsDays > 0 ? `${paymentsDays} days` : '',
        ]
          .filter(Boolean)
          .join(' ');
        this.form.controls['EID'].setValue(res.client.eid);
        if (res.contract.contractProspectType?.code === 'maidvisa.ae_prospect') {
          this.mode = 'maidVisa';
        }

        res.directDebits?.forEach((item: any) => {
          if (!item.id || item.nonCompletedInfo) {
            this.useSignatureHide = false;
          }
        });

        // Populate form with contract payment term data
        if (res && res.contractPaymentTerm) {
          if (res.contractPaymentTerm.ibanNumber) {
            this.form.controls['IBAN'].setValue(res.contractPaymentTerm.ibanNumber);
          }

          // Use setTimeout to simulate $timeout behavior
          setTimeout(() => {
            this.form.controls['bankName'].setValue(res.contractPaymentTerm.bankName);
            if (res.contractPaymentTerm.bank) {
              this.form.controls['bankNameSelect'].setValue(res.contractPaymentTerm.bank.id);
              this.bankNameOptions = [
                {
                  id: res.contractPaymentTerm.bank.id,
                  text: res.contractPaymentTerm.bank.label,
                },
              ];
            }
          });

          if (res.contractPaymentTerm.accountName) {
            this.form.controls['accountName'].setValue(res.contractPaymentTerm.accountName);
          }

          if (res.contractPaymentTerm.spouseWillSignDD !== undefined) {
            this.form.controls['spouseWillSignDD'].setValue(res.contractPaymentTerm.spouseWillSignDD);
          }

          if (res.contractPaymentTerm.eid) {
            this.form.controls['EID'].setValue(res.contractPaymentTerm.eid);
          }

          if (res.contractPaymentTerm.attachments && res.contractPaymentTerm.attachments.length > 0) {
            const file = res.contractPaymentTerm.attachments.find((obj: any) => obj.tag === 'iban');
            if (file) {
              this.form.controls['IBANFile'].setValue([file]);
            }
          }
        }
        this.cdr.detectChanges();

        // Set direct debit array

        this.directDebitArr = res.directDebits.sort((a: any, b: any) => {
          const dateA = moment(a.startDate, 'YYYY-MM-DD');
          const dateB = moment(b.startDate, 'YYYY-MM-DD');
          return dateA.isBefore(dateB) ? -1 : dateA.isAfter(dateB) ? 1 : 0;
        });
      });
    }
    this.form.controls['EID'].valueChanges.pipe(debounceTime(300), distinctUntilChanged()).subscribe((value) => {
      if (value) {
        this._contractDdPaymentsService.hasapprovedddfile(this.id, value).subscribe((res) => {
          if (res && res?.useApprovedSignature) {
            this.usePreviousApprovedSignatureShow = true;
          }
          if (res && res?.useNonRejectedSignature) {
            this.useNonRejectedSignatureShow = true;
          }
        });
      }
    });
    this.form.get('IBAN')?.valueChanges.subscribe((value) => {
      this.form.get('bankName')?.setValue('');
      this.form.get('bankNameSelect')?.setValue('');
    });
    this.cdr.detectChanges();
  }
  getValue(value: string) {
    if (!value) {
      return '';
    }
    return value
      .split('_')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }
  maskValidator: CCValidatorFn = (control) => {
    if (!control.value || control.value === '784') {
      return null;
    }
    var mask = '784' + this.maskEid.replace(/-/g, '');

    var value = control.value.replace(/-/g, '');
    if (!value.startsWith('784')) {
      value = '784' + value;
    }

    if (value.length !== mask.length) {
      this.form.get('EID')?.markAsTouched();
      return { invalidLength: 'Please enter valid EID' };
    }
    return null;
  };
  getClientDocumentType() {
    this._contractDdPaymentsService.ClientDocumentType().subscribe((res: any) => {
      this.clientDocumentsTypes = res;
    });
  }
  get signatureFilesArray(): FormArray {
    return this.form.get('signatureFiles') as FormArray;
  }

  // Method to add a signature form control
  addSignatureControl(): void {
    this.signatureFilesArray.push(this.fb.control(null, Validators.required));
  }

  // Method to remove a signature form control
  removeSignatureControl(index: number): void {
    this.signatureFilesArray.removeAt(index);
  }

  // Method to get signature values
  getSignatureValues(): any[] {
    return this.signatureFilesArray.controls.map((control) => control.value);
  }

  // Method to set signature values
  setSignatureValues(values: any[]): void {
    // Clear existing controls
    while (this.signatureFilesArray.length !== 0) {
      this.signatureFilesArray.removeAt(0);
    }

    // Add controls with values
    values.forEach((value) => {
      this.signatureFilesArray.push(this.fb.control(value));
    });
  }
  getFileSignature() {
    let params: any = {
      code: 'NUMBER_OF_DIRECT_DEBIT_SIGNATURES',
    };
    this._contractDdPaymentsService.CDPgetParameter(params).subscribe((res: any) => {
      const signatureCount = res[0].value;

      // Clear existing signature controls
      while (this.signatureFilesArray.length !== 0) {
        this.signatureFilesArray.removeAt(0);
      }

      // Add new signature controls
      for (let i = 0; i < signatureCount; i++) {
        this.addSignatureControl();
      }
    });
  }
  signatureChange(event: any, index: number) {
    let data = event;
    var imageConfig = {
      mimeType: 'image/png',
      extntion: '.png',
    };
    let blob = new Blob([data]);
    let file = new File([blob], 'signature-' + new Date().toISOString().replace(/:|\./g, '-') + imageConfig.extntion, {
      type: imageConfig.mimeType,
    });
    var formData = new FormData();
    formData.append('file', file);
    formData.append('tag', 'dd_signature');
    let params: any = {
      keepOriginalSize: true,
      tag: 'dd_signature',
    };
    this._contractDdPaymentsService.uploadFile(formData, params).subscribe((res: any) => {
      // Check if index already exists in the array
      const existingIndex = this.signtureArray.findIndex((item) => item.index === index);

      if (existingIndex !== -1) {
        // Replace the file for existing index
        this.signtureArray[existingIndex].file = res;
      } else {
        // Add new item if index doesn't exist
        this.signtureArray.push({
          file: res,
          index: index,
        });
      }
    });
  }
  validateIBAN() {
    this._contractDdPaymentsService.CDPcheckiban(this.form.get('IBAN')?.value).subscribe((res: any) => {
      this.ibanInfo = res;

      // Set bank name from IBAN info
      this.form.controls['bankName'].setValue(this.ibanInfo && this.ibanInfo.bank_data ? this.ibanInfo.bank_data.bank : '');

      // Handle picklist item info for bank selection
      if (this.ibanInfo && this.ibanInfo.picklistItemInfo) {
        this.form.controls['bankNameSelect'].setValue(this.ibanInfo.picklistItemInfo.id);
        this.bankNameOptions = [
          {
            id: this.ibanInfo.picklistItemInfo.id,
            text: this.ibanInfo.picklistItemInfo.name,
          },
        ];
      } else {
        this.form.controls['bankNameSelect'].setValue('');
      }

      // Handle IBAN validation
      if (this.ibanInfo && this.ibanInfo.validations && this.ibanInfo.validations.iban.code !== '001') {
        this.ibanInfo.error = this.ibanInfo.validations.iban.message;
        this.ibanInfo.success = false;
      } else {
        this.ibanInfo.success = true;
      }
    });
  }
  saveChangesNoDD() {
    // if (this.form.get('EID')?.invalid) {
    //   this.form.markAllAsTouched();
    //   return;
    // }
    if (!this.checkClientHasFrontEIDDocument('saveChangesNoDD')) {
      return;
    }
    const data = this.getPayload();
    this._contractDdPaymentsService.updatecontractpaymenttermwithpayments(data).subscribe((res: any) => {
      this.router.navigate(['/sales/sales-lady-workorder']);
      this._notifications.notifySuccess('The Direct Debit Payments Updated successfully');
    });
  }
  getPayload(action?: string) {
    const payload: any = {
      contractPaymentTerm: {
        id: this.paymentInfo?.contractPaymentTerm?.id,
        version: this.paymentInfo?.contractPaymentTerm?.version,
        eid: this.form.get('EID')?.value,
        ibanNumber: this.form.get('IBAN')?.value == 'AE' ? null : this.form.get('IBAN')?.value,
        bankName: this.form.get('bankName')?.value ?? null,
        bank: this.form.get('bankNameSelect')?.value ? { id: this.form.get('bankNameSelect')?.value } : null,
        accountName: this.form.get('accountName')?.value,
        spouseWillSignDD: this.form.get('spouseWillSignDD')?.value,
      },
    };
    // Add IBAN file attachment if exists
    if (this.form.get('IBANFile')?.value) {
      payload.contractPaymentTerm.attachments = [{ id: this.form.get('IBANFile')?.value[0]?.id }];
    }
    var formData = new FormData();

    formData.append('contractPaymentTerm', new Blob([JSON.stringify(payload.contractPaymentTerm)], { type: 'application/json' }));
    formData.append('generateDD', new Blob([JSON.stringify(false)], { type: 'application/json' }));
    formData.append('ignoreRejectionDDs', new Blob([JSON.stringify(true)], { type: 'application/json' }));
    if (action == 'saveChanges') {
      const data = {
        payments: this.paymentInfo.payments.map((item: any) => ({
          id: item.id,
          paymentType: { id: item.paymentType.id },
          paymentMethod: item.paymentMethod.value ?? null,
          amount: item.amount,
          discountAmount: item.discountAmount ?? null,
          additionalDiscountAmount: item.additionalDiscountAmount ?? null,
          date: item.date.split(' ')[0] + ' 00:00:00',
          description: item.description ?? null,
          confirmed: item.confirmed ?? null,
          isCalculated: item.isCalculated ?? null,
          isProRated: item.isProRated ?? null,
          oneTime: item.oneTime ?? null,
        })),
      };
      formData.append(
        'useApprovedSignature',
        new Blob([JSON.stringify(this.form.get('useApprovedSignature')?.value)], { type: 'application/json' })
      );
      formData.append('payments', new Blob([JSON.stringify(data.payments)], { type: 'application/json' }));
      if (this.signatureFilesArray.length > 0) {
        console.log(this.signtureArray);
        
        for (let i = 0; i < this.signtureArray.length; i++) {
          if (this.signtureArray[i].file && this.signtureArray[i].file.data) {
            formData.append('signatures', this.dataURItoBlob(this.signtureArray[i].file.data));
          }
        }
      }
    }

    return formData;
  }
  confirmSave() {
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      this.cdr.detectChanges();
      return;
    }
    this._dialog.confirm('', 'Are you sure want to generate Direct Debits for all new payments?', () => {
      this.saveChanges();
    });
  }
  checkClientHasFrontEIDDocument(action: string) {
    if (!this.paymentInfo.clientHasFrontEIDDocument && !this.skipFrontEIDDocument && this.directDebitCount > 0) {
      setTimeout(() => {
        this.currentAction.next(action);
        this.form.get('idFront')?.setValue(null);
      });
      const dialog = this._dialog.originalOpen(HasFrontEidDocumentDialogComponent, {
        data: {
          idFront: this.form.get('idFront')?.value,
          currentAction: this.currentAction.value,
          clientDocumentsTypes: this.clientDocumentsTypes,
          clientId: this.paymentInfo?.client?.id,
        },
      });
      dialog.afterClosed().subscribe((res: any) => {
        if (!res.isSave) {
          this.skipFrontEIDDocument = res.skipFrontEIDDocument;
          this.saveChangesNoDD();
        }
        if (res.isSave) {
          this.clientHasFrontEIDDocument = res.clientHasFrontEIDDocument;
          if (this.currentAction.value == 'saveChangesNoDD') {
            this.saveChangesNoDD();
          } else {
            this.saveChanges();
          }
        }
      });
      // Note: Dialog box implementation would need to be handled via your preferred dialog service
      return false;
    }
    return true;
  }
  saveChanges() {
    this.currentAction.next('saveChanges');
    if (this.form.invalid) {
      this.form.markAllAsTouched();
      this.cdr.detectChanges();
      return;
    }

    if (!this.checkClientHasFrontEIDDocument('saveChanges')) {
      return;
    }
    const data = this.getPayload('saveChanges');
    this._contractDdPaymentsService.updatecontractpaymenttermwithpayments(data).subscribe((res: any) => {
      this.router.navigateByUrl('/sales/sales-lady-workorder');
      this._notifications.notifySuccess('The Direct Debit Payments Updated successfully');
    });
  }

  // Add this method to convert data URI to Blob
  dataURItoBlob(dataURI: string): Blob {
    const byteString = atob(dataURI.split(',')[1]);
    const mimeString = dataURI.split(',')[0].split(':')[1].split(';')[0];
    const ab = new ArrayBuffer(byteString.length);
    const ia = new Uint8Array(ab);

    for (let i = 0; i < byteString.length; i++) {
      ia[i] = byteString.charCodeAt(i);
    }

    return new Blob([ab], { type: mimeString });
  }
}
