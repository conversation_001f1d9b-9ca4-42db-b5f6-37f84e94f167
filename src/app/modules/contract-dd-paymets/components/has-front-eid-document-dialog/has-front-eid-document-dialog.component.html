<div cc-std-dialog>
  <cc-dialog-header>
    <h1 cc-dialog-title class="text-center">Insert Emirates ID front side</h1>
    <a role="button" type="button" cc-icon-button cc-dialog-close-button cc-dialog-close icon="close"></a>
  </cc-dialog-header>
  <cc-dialog-content class="p-2">
  <div [formGroup]="form">
    <cc-file-uploader formControlName="idFront" label="Emirates ID front side" [required]="true" tag="EMIRATES_ID_FRONT_SIDE"id="id1" [dropzoneConfig]="config"></cc-file-uploader>
  </div>

  </cc-dialog-content>
  <cc-dialog-actions>
    <!-- <button cc-raised-button cc-dialog-close  *ngIf="!data.isSave">Ok</button> -->
    <button cc-raised-button *ngIf="data.currentAction=='saveChangesNoDD'"cc-dialog-close (click)="saveFrontEIDDocument(false)">Save </button>
    <button cc-raised-button (click)="saveFrontEIDDocument(true)">Skip</button>
  </cc-dialog-actions>
</div>