import { ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, FormControl } from '@angular/forms';
import { CC_DIALOG_DATA, CCDialogRef } from '@maids/cc-lib/dialog';
import { CCNotificationService } from '@maids/cc-lib/services';
import { ContractDdPaymentsService } from '../../services/contract-dd-payments.service';
import { CCFileUploaderConfig } from '@maids/cc-lib/file-uploader';

@Component({
  selector: 'app-has-front-eid-document-dialog',
  templateUrl: './has-front-eid-document-dialog.component.html',
  styleUrls: ['./has-front-eid-document-dialog.component.scss']
})
export class HasFrontEidDocumentDialogComponent implements OnInit {
  form!: FormGroup;
  config: CCFileUploaderConfig = {
    maxFilesize: 25,
  };
  constructor(
    @Inject(CC_DIALOG_DATA) public data: any,
    private cdr: ChangeDetectorRef,
    private dialogRef: CCDialogRef<HasFrontEidDocumentDialogComponent>,
    private fb: FormBuilder,
    private service: ContractDdPaymentsService,
    private notification: CCNotificationService
  ) {}
  ngOnInit(): void {
    this.form = this.fb.group({
      idFront: new FormControl(null),
    });
  }
  saveFrontEIDDocument(isSave: boolean) {
    if (!isSave) {
      this.dialogRef.close({ isSend: false, skipFrontEIDDocument: true,isSave:false });
    }
    let type = this.data.clientDocumentsTypes.find((item: any) => item.code == 'emirates_id_front_side');
    var data={
      attachments:[{id:this.form.get('idFront')?.value.id}],
      client:{id:this.data.clientId},
      type:type.id,
    }
    this.service.createClientdocuments(data).subscribe((res: any) => {
      this.notification.notifySuccess('The file saved successfully');
      this.dialogRef.close({ isSend: true, clientHasFrontEIDDocument: true,isSave:true });
    });
  }

}
