import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ContractDdPaymetsRoutingModule } from './contract-dd-paymets-routing.module';
import { ContractDdPaymentsComponent } from './components/contract-dd-payments/contract-dd-payments.component';
import { CCInputModule } from '@maids/cc-lib/input';
import { CCSignatureModule } from '@maids/cc-lib/signature';
import { CCFileUploaderModule } from '@maids/cc-lib/file-uploader';
import { CCInputMaskModule } from '@maids/cc-lib/masked-input';
import { CCSelectInputModule } from '@maids/cc-lib/select-input';
import { CCBreadcrumbsModule } from '@maids/cc-lib/layout';
import { CCCheckboxModule } from '@maids/cc-lib/checkbox';
import { ReactiveFormsModule } from '@angular/forms';
import { CCButtonModule } from '@maids/cc-lib/button';
import { CCValidateByModule } from '@maids/cc-lib/validation';
import { HasFrontEidDocumentDialogComponent } from './components/has-front-eid-document-dialog/has-front-eid-document-dialog.component';
import { CCDialogModule } from '@maids/cc-lib/dialog';

@NgModule({
  declarations: [ContractDdPaymentsComponent, HasFrontEidDocumentDialogComponent],
  imports: [
    CommonModule,
    ContractDdPaymetsRoutingModule,
    CCInputModule,
    CCSignatureModule,
    CCFileUploaderModule,
    CCInputMaskModule,
    CCSelectInputModule,
    CCBreadcrumbsModule,
    ReactiveFormsModule,
    CCCheckboxModule,
    CCFileUploaderModule,
    CCButtonModule,
    CCValidateByModule,
    CCDialogModule
  ],
})
export class ContractDdPaymetsModule {}
