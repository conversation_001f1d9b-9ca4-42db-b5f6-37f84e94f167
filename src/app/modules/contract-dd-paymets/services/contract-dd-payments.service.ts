import { HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { CCBackendEndpoint } from '@maids/cc-erp-services';
import { Observable } from 'rxjs';
import { API } from 'src/environments/api';

@Injectable({
  providedIn: 'root',
})
export class ContractDdPaymentsService {
  constructor(
    private _http: HttpClient,
    @Inject(CCBackendEndpoint) private _api: CCBackendEndpoint
  ) {}
  bankNameSelectOptions(page:number=0,size:number=100 ,search:string=''): Observable<any> {
    return this._http.get(`${this._api}/${API.bankNameSelectOptions}`,{
      params:{
        page,
        size,
        search
      }
    })
  }
  getcontractpaymenttermbycontractwithdirectdebitpayments(id:any): Observable<any> {
    return this._http.get(`${this._api}/${API.getcontractpaymenttermbycontractwithdirectdebitpayments(id)}`)
  }
  updatecontractpaymenttermwithpayments(payload:any): Observable<any> {
    return this._http.post(`${this._api}/${API.updatecontractpaymenttermwithpayments}`,payload)
  }
  CDPcheckiban(iban:any): Observable<any> {
    return this._http.get(`${this._api}/${API.CDPcheckiban}/${iban}`)
  }
  CDPgetParameter(params:any): Observable<any> {
    return this._http.get(`${this._api}/${API.CDPgetParameter}`,{params:params})
  }
  ClientDocumentType(): Observable<any> {
    return this._http.get(`${this._api}/${API.ClientDocumentType}`)
  }
  createClientdocuments(payload:any): Observable<any> {
    return this._http.post(`${this._api}/${API.createClientdocuments}`,payload)
  }
  hasapprovedddfile(id:any,eid:any): Observable<any> {
    return this._http.get(`${this._api}/${API.hasapprovedddfile}/${id}?eid=${eid}`)
  }
  uploadFile(data:any ,params:any): Observable<any> {
    return this._http.post(`${API.upload}`,data,{params:params});
  }
}
