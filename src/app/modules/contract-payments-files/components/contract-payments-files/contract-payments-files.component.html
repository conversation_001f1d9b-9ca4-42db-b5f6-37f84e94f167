<div class="container" *ngIf="contractPaymentsFiles as vm">
  <div>
    <div>
      <h6>Direct Debit Activation Files</h6>
    </div>
    <div class="body" *ngFor="let payment of vm.directDebits; trackBy: trackByPayment">
      <div class="head">
        <span style="font-weight: bold">DD Info:</span>(
        <span><span style="font-weight: bold">Start:</span> {{ payment.startDate.split(' ')[0] }}</span>
        <span><span style="font-weight: bold">, End:</span> {{ payment.expiryDate.split(' ')[0] }}</span>
        <span><span style="font-weight: bold">, Amount:</span> {{ formatAmount(payment.amount) }}</span>
        <span><span style="font-weight: bold">, Status:</span> {{ payment.status ? payment.status.label : '' }}</span>
        <span><span style="font-weight: bold">, Type:</span> {{ payment.category ? payment.category.label : '' }}</span>
        )
      </div>
      <div class="p-3">
        <table class="table table-bordered table table-striped text-center">
          <tr style="background-color: #f5f5f5">
            <td>File</td>
            <td>Status</td>
            <td>Type</td>
            <td>Update Date</td>
          </tr>
          <tr *ngFor="let file of payment.directDebitFiles; trackBy: trackByFile; let i = index">
            <td [ngStyle]="{ 'background-color': i % 2 != 0 ? '#f5f5f5' : '' }">
              <a
                class="cc-secondary"
                *ngIf="filterArrValue(file.attachments).length > 0"
                (click)="preview(filterArrValue(file.attachments)[0].uuid)">
                {{ filterArrValue(file.attachments).length > 0 ? filterArrValue(file.attachments)[0].name : '' }} </a
              >&nbsp;
              <button
                cc-raised-button
                *ngIf="filterArrValue(file.attachments).length > 0"
                (click)="showDownloadModal(filterArrValue(file.attachments)[0].id)">
                PDF of images
              </button>
            </td>
            <td [ngStyle]="{ 'background-color': i % 2 != 0 ? '#f5f5f5' : '' }">{{ file.status ? file.status.label : '' }}</td>
            <td [ngStyle]="{ 'background-color': i % 2 != 0 ? '#f5f5f5' : '' }">{{ file.type ? file.type : '' }}</td>
            <td [ngStyle]="{ 'background-color': i % 2 != 0 ? '#f5f5f5' : '' }">{{ file.lastModificationDate.substring(0, 10) }}</td>
            <td [ngStyle]="{ 'background-color': i % 2 != 0 ? '#f5f5f5' : '' }">
              <div
                *ngIf="hasPermissionFileAction && payment.status && payment.status.value === 'PENDING'"
                class="btn-group w3-margin-0 w3-padding-0"
                style="display: flex; justify-content: center">
                <button
                  *ngIf="file.status && file.status.value === 'NOT_SENT'"
                  (click)="openDirectDebitModal(vm.directDebits.indexOf(payment), payment.directDebitFiles.indexOf(file))"
                  class="btn btn-success btn-sm btn-raised">
                  <cc-icon [inline]="true">edit</cc-icon>
                </button>
                <button
                  *ngIf="file.status && file.status.value === 'NOT_SENT'"
                 cc-raised-button
                  (click)="DirectDebitFileChangeStatus(vm.directDebits.indexOf(payment), payment.directDebitFiles.indexOf(file), 'SENT')">
                  Send
                </button>
                <button
                  *ngIf="file.status && file.status.value === 'SENT'"
                  cc-raised-button
                  (click)="
                    DirectDebitFileChangeStatus(vm.directDebits.indexOf(payment), payment.directDebitFiles.indexOf(file), 'APPROVED')
                  ">
                  Approve
                </button>
                <button
                  *ngIf="file.status && file.status.value === 'SENT'"
                  cc-raised-button
                  (click)="
                    DirectDebitFileChangeStatus(vm.directDebits.indexOf(payment), payment.directDebitFiles.indexOf(file), 'REJECTED')
                  ">
                  Reject
                </button>
                <button
                  *ngIf="file.status && (file.status.value === 'APPROVED' || file.status.value === 'REJECTED')"
                  cc-raised-button
                  (click)="
                    DirectDebitFileChangeStatus(vm.directDebits.indexOf(payment), payment.directDebitFiles.indexOf(file), 'NOT_SENT')
                  ">
                  Cancel Status
                </button>
              </div>
            </td>
          </tr>
        </table>
      </div>
      <div class="d-flex justify-content-end p-4">
        <button cc-raised-button (click)="openDirectDebitModal(vm.directDebits.indexOf(payment), -1)">Add Files</button>
      </div>
    </div>

    <div *ngIf="vm.paymentsReceiptAttachment">
      <div class="d-flex align-items-center gap-2 p-3">
        Family Receipt
        <button cc-raised-button (click)="openUpdateFilesModal()"><cc-icon [inline]="true">edit_square</cc-icon> Update File</button>
      </div>
      <div class="body">
        <div class="head text-center">File</div>
        <div class="p-3">
          <a class="cc-secondary" (click)="preview(vm.paymentsReceiptAttachment.uuid)">{{ vm.paymentsReceiptAttachment.name }}</a>
        </div>
      </div>
    </div>
  </div>

  <div class="d-flex justify-content-between pt-4">
      <div class="d-flex gap-3">
        <button cc-raised-button (click)="printAll(true)">Print All (Pending DD)</button>
        <button cc-raised-button (click)="printAll(false)">Print All</button>
      </div>
      <div class="d-flex gap-3">
        <button cc-raised-button color="primary" (click)="sendFilesToClient()">
          Send Files to Family <cc-icon [inline]="true">send</cc-icon>
        </button>
        <button cc-raised-button color="primary" (click)="doneAction()">Done</button>
      </div>
  </div>
</div>
