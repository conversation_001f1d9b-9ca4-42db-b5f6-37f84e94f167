import { Component, Inject, OnInit } from '@angular/core';
import { CCDialogRef, CC_DIALOG_DATA } from '@maids/cc-lib/dialog';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { ContractPaymentsFilesService } from '../../../services/contract-payments-files.service';

@Component({
  selector: 'app-download-option-dialog',
  templateUrl: './download-option-dialog.component.html',
  styleUrls: ['./download-option-dialog.component.scss'],
})
export class DownloadOptionDialogComponent implements OnInit {
  constructor(
    private dialogRef: CCDialogRef<DownloadOptionDialogComponent>,
    private notification: CCNotificationService,
    private mediaService: MediaService,
    @Inject(CC_DIALOG_DATA) public data: any
  ) {}

  ngOnInit(): void {}
  downloadPdfOFImages(id: any, type: 'firstimage' | 'all'): void {
    // this.service.getPdfOfImages(id, type).subscribe((res) => console.log(res));
    this.mediaService.downloadFile(`accounting/contractpaymentterm/getpdfofimages/${id}/${type}`);
  }
}
