import { Component, OnInit } from '@angular/core';
import { ContractPaymentsService } from '../../services/contract-payments.service';
import { PageEvent } from '@angular/material/paginator';
import { Sort } from '@angular/material/sort';
import { FormBuilder } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { CCDialog } from '@maids/cc-lib/dialog';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { API } from 'src/environments/api';
import { AttachmentsComponent } from '../attachments/attachments.component';
@Component({
  selector: 'app-contract-payments',
  templateUrl: './contract-payments.component.html',
  styleUrls: ['./contract-payments.component.scss'],
})
export class ContractPaymentsComponent implements OnInit {
  formGroup = this.formBuilder.group({
    clientNameOp: [''],
    clientNameFilter: [''],
    contractNumberFilter: [''],
    authorizationCodeFilter: [''],
    transferRefOp: [''],
    transferReferenceFilter: [''],
    paymentDateOp: [''],
    paymentDateFilter: [''],
    groupByClient: [''],
  });
  errorDigit: any;
  constructor(
    private contractPaymentsService: ContractPaymentsService,
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private ccDialog: CCDialog,
    public readonly notifications: CCNotificationService,
    private router: Router,
    private mediaService: MediaService
  ) {}
  records: any;
  provider: string = 'CHECKOUT';
  selectedPayments: { [key: string]: boolean } = {};
  current_row: any;
  filterData: any[] = [
    { property: 'confirmed', operation: '=', value: false },
    { property: 'showOnERP', operation: '=', value: true },
    { property: 'paymentMethod', operation: '<>', value: 'DIRECT_DEBIT' },
  ];
  operatorOptions: any[] = [
    { id: 'equals', text: 'Equals' },
    { id: 'contains', text: 'Contains' },
  ];
  dateOperatorOptions: any[] = [
    { id: '<', text: '<' },
    { id: '<=', text: '<=' },
    { id: '>', text: '>' },
    { id: '>=', text: '>=' },
    { id: '=', text: '=' },
    { id: '>=', text: 'From' },
    { id: '<=', text: 'To' },
  ];
  gridCols: CCGridColumn[] = [
    {
      field: 'actions',
      header: '',
      type: 'button',
      class: 'max',
      buttonConfig: {
        mode: 'multiple',
        disabled: false,
        buttons: [
          {
            type: 'raised',
            color: 'primary',
            text: 'Confirm Payment',
            disabled: false,
            hidden: (row: any) => {
              let visible = true;
              if (row.hasOwnProperty('replaceOf')) {
                if (row.replaceOf) {
                  visible = false;
                }
              }
              if (row.paymentMethod.value == 'WIRE_TRANSFER') {
                visible = false;
              }
              return !visible;
            },
            callback: (row: any) => {
              this.confirmPayment(row);
            },
          },
          {
            type: 'raised',
            color: 'accent',
            text: 'Match',
            disabled: false,
            hidden: (row: any) => {
              return row.paymentMethod.value != 'WIRE_TRANSFER';
            },
            callback: (row: any) => {
              this.confirmPayment(row);
            },
          },
          {
            type: 'raised',
            color: 'primary',
            text: 'Confirm And Replace',
            disabled: false,
            hidden: (row: any) => {
              let visible = false;
              if (row.hasOwnProperty('replaceOf')) {
                if (row.replaceOf) {
                  visible = true;
                }
              }
              return !visible;
            },
            callback: (row: any) => {
              this.confirmPayment(row);
            },
          },
          {
            type: 'raised',
            color: 'accent',
            text: 'Cancel',
            disabled: false,
            hidden: (row: any) => {
              return false;
            },
            callback: (row: any) => {
              this.ccDialog.confirm(
                '',
                'Are you sure you want to cancel this payment?',
                () => {
                  this.cancelOperation(row, 'notDD');
                }
              );
            },
          },
        ],
      },
    },
    { field: 'select', header: 'Select' },
    {
      field: 'clientName',
      header: 'Family',
      formatter(rowData, colDef) {
        if (rowData.paymentMethod != 'DIRECT_DEBIT') {
          return `<a class="cc-secondary" href="#!/client/details/${rowData.clientId}">${rowData.clientName}</a>`;
        } else {
          return '-';
        }
      },
      sortable: true,
      sortProp: {
        id: 'contractPaymentTerm.contract.client.name',
        arrowPosition: 'before',
        start: 'asc',
      },
    },
    {
      field: 'housemaidName',
      header: 'Housemaid',
      formatter(rowData, colDef) {
        return rowData.housemaidName;
      },
      sortable: true,
      sortProp: {
        id: 'contractPaymentTerm.contract.housemaid.name',
        arrowPosition: 'before',
        start: 'asc',
      },
      hide: true,
    },
    {
      field: 'contractId',
      header: 'Contract',
      formatter(rowData, colDef) {
        return rowData.contractId;
      },
      sortable: true,
      sortProp: {
        id: 'contractPaymentTerm.contract.id',
        arrowPosition: 'before',
        start: 'asc',
      },
    },
    {
      field: 'contractType',
      header: 'Type',
      formatter(rowData, colDef) {
        return rowData.contractType;
      },
      sortable: true,
      sortProp: {
        id: 'contractPaymentTerm.contract.contractType',
        arrowPosition: 'before',
        start: 'asc',
      },
      hide: true,
    },
    {
      field: 'nationalityName',
      header: 'Nationality',
      formatter(rowData, colDef) {
        return rowData.nationalityName;
      },
      sortable: true,
      sortProp: {
        id: 'contractPaymentTerm.housemaid.nationality.name',
        arrowPosition: 'before',
        start: 'asc',
      },
    },
    {
      field: 'creationDate',
      header: 'Date Changed to Received',
      formatter(rowData, colDef) {
        return rowData.creationDate;
      },
      width: '200px',
      sortable: true,
      sortProp: {
        id: 'creationDate',
        arrowPosition: 'before',
        start: 'asc',
      },
    },
    {
      field: 'totalAmount',
      header: 'Amount',
      formatter(rowData, colDef) {
        if (!rowData.active) {
          return `<span style="color:#808080"> ${rowData.totalAmount} Inactive </span>`;
        } else {
          return rowData.totalAmount;
        }
      },
    },
    {
      field: 'transferReference',
      header: 'Checkout Payment ID',
      formatter(rowData, colDef) {
        return rowData.transferReference;
      },
      sortable: true,
      sortProp: {
        id: 'transferReference',
        arrowPosition: 'before',
        start: 'asc',
      },
    },
    {
      field: 'expectedDate',
      header: 'Expected Date',
      formatter(rowData, colDef) {
        return rowData.expectedDate;
      },
      sortable: true,
      sortProp: {
        id: 'expectedDate',
        arrowPosition: 'before',
        start: 'asc',
      },
    },
    {
      field: 'typesOfPaymentsLabels',
      header: 'Payment Type',
      formatter(rowData, colDef) {
        return rowData.typesOfPaymentsLabels;
      },
      sortable: true,
      sortProp: {
        id: 'paymentType.name',
        arrowPosition: 'before',
        start: 'asc',
      },
    },
    {
      field: 'paymentMethod',
      header: 'Payment Method',
      formatter(rowData, colDef) {
        return rowData.paymentMethod.label;
      },
      sortable: true,
      sortProp: {
        id: 'paymentMethod',
        arrowPosition: 'before',
        start: 'asc',
      },
    },
    {
      field: 'source',
      header: 'Source',
      formatter(rowData, colDef) {
        return rowData.source.label;
      },
      sortable: true,
      sortProp: { id: 'source', arrowPosition: 'before', start: 'asc' },
    },
    {
      field: 'description',
      header: 'Description',
      formatter(rowData, colDef) {
        return rowData.description ? rowData.description : '';
      },
      sortable: true,
      sortProp: { id: 'description', arrowPosition: 'before', start: 'asc' },
    },
    {
      field: 'authorizationCode',
      header: 'Authorization Code',
      formatter(rowData, colDef) {
        return rowData.authorizationCode;
      },
    },
    {
      field: 'attachment',
      header: 'View',
    },
  ];
  PaytabsCols: CCGridColumn[] = [
    {
      field: 'actions',
      header: '',
      type: 'button',
      class: 'max',
      buttonConfig: {
        mode: 'multiple',
        disabled: false,
        buttons: [
          {
            type: 'raised',
            color: 'primary',
            text: 'Confirm Payment',
            disabled: false,
            hidden: (row: any) => {
              let visible = true;
              if (row.hasOwnProperty('replaceOf')) {
                if (row.replaceOf) {
                  visible = false;
                }
              }
              if (row.paymentMethod.value == 'WIRE_TRANSFER') {
                visible = false;
              }
              return !visible;
            },
            callback: (row: any) => {
              this.confirmPayment(row);
            },
          },
          {
            type: 'raised',
            color: 'accent',
            text: 'Match',
            disabled: false,
            hidden: (row: any) => {
              return row.paymentMethod.value != 'WIRE_TRANSFER';
            },
            callback: (row: any) => {
              this.confirmPayment(row);
            },
          },
          {
            type: 'raised',
            color: 'accent',
            text: 'Confirm And Replace',
            disabled: false,
            hidden: (row: any) => {
              let visible = false;
              if (row.hasOwnProperty('replaceOf')) {
                if (row.replaceOf) {
                  visible = true;
                }
              }
              return !visible;
            },
            callback: (row: any) => {
              this.confirmPayment(row);
            },
          },
          {
            type: 'raised',
            color: 'accent',
            text: 'Cancel',
            disabled: false,
            hidden: (row: any) => {
              return false;
            },
            callback: (row: any) => {
              this.ccDialog.confirm(
                '',
                'Are you sure you want to cancel this payment?',
                () => {
                  this.cancelOperation(row, 'notDD');
                }
              );
            },
          },
        ],
      },
    },
    { field: 'select', header: 'Select' },
    {
      field: 'clientName',
      header: 'Family',
      formatter(rowData, colDef) {
        if (rowData.paymentMethod.value != 'DIRECT_DEBIT') {
          return `<a class="cc-secondary" href="#!/client/details/${rowData.clientId}">${rowData.clientName}</a>`;
        } else {
          return '-';
        }
      },
      sortable: true,
      sortProp: {
        id: 'contractPaymentTerm.contract.client.name',
        arrowPosition: 'before',
        start: 'asc',
      },
    },
    {
      field: 'housemaidName',
      header: 'Housemaid',
      formatter(rowData, colDef) {
        return rowData.housemaidName;
      },
      sortable: true,
      sortProp: {
        id: 'contractPaymentTerm.contract.housemaid.name',
        arrowPosition: 'before',
        start: 'asc',
      },
      hide: true,
    },
    {
      field: 'contractId',
      header: 'Contract',
      formatter(rowData, colDef) {
        return rowData.contractId;
      },
      sortable: true,
      sortProp: {
        id: 'contractPaymentTerm.contract.id',
        arrowPosition: 'before',
        start: 'asc',
      },
    },
    {
      field: 'contractType',
      header: 'Type',
      formatter(rowData, colDef) {
        return rowData.contractType;
      },
      sortable: true,
      sortProp: {
        id: 'contractPaymentTerm.contract.contractType',
        arrowPosition: 'before',
        start: 'asc',
      },
      hide: true,
    },
    {
      field: 'nationalityName',
      header: 'Nationality',
      formatter(rowData, colDef) {
        return rowData.nationalityName;
      },
      sortable: true,
      sortProp: {
        id: 'contractPaymentTerm.housemaid.nationality.name',
        arrowPosition: 'before',
        start: 'asc',
      },
    },
    {
      field: 'creationDate',
      header: 'Date Changed to Received',
      formatter(rowData, colDef) {
        return rowData.creationDate;
      },
      width: '200px',
      sortable: true,
      sortProp: { id: 'creationDate', arrowPosition: 'before', start: 'asc' },
    },
    {
      field: 'amount',
      header: 'Amount',
      formatter(rowData, colDef) {
        if (!rowData.active) {
          return `<span style="color:#808080"> ${rowData.totalAmount} Inactive </span>`;
        } else {
          return rowData.totalAmount;
        }
      },
      sortable: true,
      sortProp: { id: 'amount', arrowPosition: 'before', start: 'asc' },
    },
    {
      field: 'transferReference',
      header: 'Tran Ref Number',
      formatter(rowData, colDef) {
        return rowData.transferReference;
      },
      sortable: true,
      sortProp: {
        id: 'transferReference',
        arrowPosition: 'before',
        start: 'asc',
      },
    },
    {
      field: 'expectedDate',
      header: 'Expected Date',
      formatter(rowData, colDef) {
        return rowData.expectedDate;
      },
      sortable: true,
      sortProp: { id: 'expectedDate', arrowPosition: 'before', start: 'asc' },
    },
    {
      field: 'typesOfPaymentsLabels',
      header: 'Payment Type',
      formatter(rowData, colDef) {
        return rowData.typesOfPaymentsLabels;
      },
      sortable: true,
      sortProp: {
        id: 'paymentType.name',
        arrowPosition: 'before',
        start: 'asc',
      },
    },
    {
      field: 'paymentMethod',
      header: 'Payment Method',
      formatter(rowData, colDef) {
        return rowData.paymentMethod.label;
      },
      sortable: true,
      sortProp: {
        id: 'paymentMethod',
        arrowPosition: 'before',
        start: 'asc',
      },
    },
    {
      field: 'source',
      header: 'Source',
      formatter(rowData, colDef) {
        return rowData.source.label;
      },
    },
    {
      field: 'description',
      header: 'Description',
      formatter(rowData, colDef) {
        return rowData.description ? rowData.description : '';
      },
    },
    {
      field: 'authorizationCode',
      header: 'Authorization Code',
      formatter(rowData, colDef) {
        return rowData.authorizationCode;
      },
    },
    {
      field: 'attachment',
      header: 'View',
    },
  ];

  ngOnInit(): void {
    this.handleQueryParams();
    this.getDefaultProvider();
    this.formGroup.controls['groupByClient'].valueChanges.subscribe((val) => {
      if (val) {
        this.contractPaymentsService.searchSubject.next({
          params: {
            page: 0,
            size: 20,
            sort: 'contractPaymentTerm.contract.client.id,ASC',
          },
        });
        this.getContractPaymentConfirmation();
      } else {
        this.contractPaymentsService.searchSubject.next({
          params: {
            page: 0,
            size: 20,
            sort: '',
          },
        });
      }
    });
    this.formGroup.controls['contractNumberFilter'].valueChanges.subscribe(
      (val) => {
        this.errorDigit = '';
        if (!!val) {
          let regExp = new RegExp(/^\d*$/);
          if (!regExp.test(val)) {
            this.errorDigit = 'Invalid contract id.';
          }
        }
      }
    );
  }
  handleQueryParams() {
    if (this.route.snapshot.queryParams['contractNumberFilter']) {
      this.formGroup.controls['contractNumberFilter'].setValue(
        this.route.snapshot.queryParams['contractNumberFilter']
      );
      this.filterData.push({
        property: 'contractPaymentTerm.contract.id',
        operation: '=',
        value: this.formGroup.controls['contractNumberFilter'].value,
      });
    }
    if (this.route.snapshot.queryParams['clientNameFilter']) {
      this.formGroup.controls['clientNameFilter'].setValue(
        this.route.snapshot.queryParams['clientNameFilter']
      );
      this.filterData.push({
        property: 'contractPaymentTerm.contract.client.name',
        operation: '=',
        value: this.formGroup.controls['clientNameFilter'].value,
      });
    }
    if (this.route.snapshot.queryParams['sort']) {
      let sortArray = this.route.snapshot.queryParams['sort'].split(',');
      this.contractPaymentsService.searchSubject.next({
        params: {
          sort: `${sortArray[0]},${sortArray[1]}`,
          page: this.contractPaymentsService.searchSubject.getValue().params
            .page,
          size: this.contractPaymentsService.searchSubject.getValue().params
            .size,
        },
      });
    }
  }
  getDefaultProvider() {
    this.contractPaymentsService.CPgetDefaultProvider().subscribe((res) => {
      this.provider = res[0].value ?? 'CHECKOUT';
      this.getContractPaymentConfirmation();
    });
  }
  prepareFilters() {
    this.filterData = [];
    this.filterData = [
      { property: 'confirmed', operation: '=', value: false },
      { property: 'showOnERP', operation: '=', value: true },
      { property: 'paymentMethod', operation: '<>', value: 'DIRECT_DEBIT' },
    ];
    if (this.formGroup.controls['contractNumberFilter'].value) {
      this.filterData.push({
        property: 'contractPaymentTerm.contract.id',
        operation: '=',
        value: this.formGroup.controls['contractNumberFilter'].value,
      });
    }
    if (this.formGroup.controls['clientNameFilter'].value) {
      let clientNameFilter = {
        property: 'contractPaymentTerm.contract.client.name',
        operation:
          this.formGroup.controls['clientNameOp'].value == 'equals'
            ? '='
            : 'like',
        value:
          this.formGroup.controls['clientNameOp'].value == 'equals'
            ? this.formGroup.controls['clientNameFilter'].value
            : `%${this.formGroup.controls['clientNameFilter'].value}%`,
      };
      this.filterData.push(clientNameFilter);
    }
    if (this.formGroup.controls['transferReferenceFilter'].value) {
      let transferReferenceFilter = {
        property: 'transferReference',
        operation:
          this.formGroup.controls['transferRefOp'].value == 'equals'
            ? '='
            : 'like',
        value:
          this.formGroup.controls['transferRefOp'].value == 'equals'
            ? this.formGroup.controls['transferReferenceFilter'].value
            : `%${this.formGroup.controls['transferReferenceFilter'].value}%`,
      };
      this.filterData.push(transferReferenceFilter);
    }
    if (this.formGroup.controls['authorizationCodeFilter'].value) {
      let authorizationCodeFilter = {
        property: 'authorizationCode',
        operation: '=',
        value: this.formGroup.controls['authorizationCodeFilter'].value,
      };
      this.filterData.push(authorizationCodeFilter);
    }
    if (this.formGroup.controls['paymentDateFilter'].value) {
      let paymentDateFilter = {
        property: 'dateChangedToReceived',
        operation: !!this.formGroup.controls['paymentDateOp'].value
          ? this.formGroup.controls['paymentDateOp'].value
          : '=',
        value: this.formGroup.controls['paymentDateFilter'].value,
      };
      this.filterData.push(paymentDateFilter);
    }
  }
  filter() {
    if (this.errorDigit != '' && this.formGroup.controls['contractNumberFilter'].value) {
      this.notifications.notifyError('Please provide valid contract id');
    } else {
      this.prepareFilters();
      this.getContractPaymentConfirmation();
    }
  }
  resetForm() {
    this.filterData = [
      { property: 'confirmed', operation: '=', value: false },
      { property: 'showOnERP', operation: '=', value: true },
      { property: 'paymentMethod', operation: '<>', value: 'DIRECT_DEBIT' },
    ];
    this.formGroup.reset();
    this.getContractPaymentConfirmation();
  }
  getContractPaymentConfirmation() {
    this.selectedPayments = {};
    this.contractPaymentsService
      .getContractPaymentConfirmation(this.filterData)
      .subscribe((res: any) => {
        this.records = res;
      });
  }
  getNextPage(event: PageEvent) {
    this.contractPaymentsService.searchSubject.next({
      params: {
        page: event.pageIndex,
        size: event.pageSize,
        sort: this.contractPaymentsService.searchSubject.getValue().params.sort,
      },
    });
    this.getContractPaymentConfirmation();
  }
  onSortChange(event: Sort) {
    this.contractPaymentsService.searchSubject.next({
      params: {
        sort: `${event.active},${event.direction}`,
        page: this.contractPaymentsService.searchSubject.getValue().params.page,
        size: this.contractPaymentsService.searchSubject.getValue().params.size,
      },
    });
    this.getContractPaymentConfirmation();
  }
  confirmPayment(data: any) {
    if (data.paymentMethod.value !== 'WIRE_TRANSFER') {
      this.contractPaymentsService
        .CPconfirmPayment(data.id)
        .subscribe((res: any) => {
          this.notifications.notifySuccess('Payment Confirmed Successfully');
          
          this.getContractPaymentConfirmation();
        });
      return;
    } else {
      this.router.navigateByUrl(
        `accounting/expected-wire-transfer/matching-wire-transfer?date=${data.expectedDate}&amount=${data.totalAmount}&name=${data.clientName}&clientId=${data.clientId}&clientName=${data.clientName}&id=${data.id}`
      );
      return;
    }
  }
  cancelMultiple() {
    this.ccDialog.confirm(
      '',
      'Are you sure you want to cancel this payments',
      () => {
        this.cancelOperation(null, 'notDD');
      }
    );
  }
  cancelOperation(element: any, type: string) {
    if (type == 'notDD' && element == null) {
      let paymentIds: any[] = [];
      // Extract payment IDs from the selectedPayments object
      Object.keys(this.selectedPayments).forEach((key: string) => {
        if (this.selectedPayments[key]) {
          paymentIds.push(key);
        }
      });
      this.contractPaymentsService
        .CPcancelAllOperation(paymentIds.join(','))
        .subscribe((res: any) => {
          this.notifications.notifySuccess('Success');
          this.getContractPaymentConfirmation();
        });
    } else {
      console.log('element', element);
      this.contractPaymentsService
        .CPcancelAllOperation(element.id)
        .subscribe((res: any) => {
          this.notifications.notifySuccess('Success');
          this.getContractPaymentConfirmation();
        });
    }
  }
  exportExcelNotDD() {
    this.mediaService.downloadFile(`${API.exportExcelNotDD}`, '', {
      method: 'POST',
      body: this.filterData,
      params: {
        sort:
          this.contractPaymentsService.searchSubject.getValue().params.sort ||
          '',
      },
    });
  }
  getSelectedRowsCount() {
    let len: number = 0;
    if (this.selectedPayments) {
      Object.values(this.selectedPayments).forEach((value: boolean) => {
        if (value) {
          len++;
        }
      });
    }
    return len;
  }
  viewReceipt(index: number) {
    this.current_row = this.records.content[index];
    this.ccDialog.originalOpen(AttachmentsComponent, {
      data: {
        attachmentType: 'Attachments',
        attachments: this.current_row.attachments,
      },
    });
  }
  viewIban(uuid: string) {
    this.mediaService.downloadFile('public/download/' + uuid);
  }
  uploadStatements() {
    this.router.navigateByUrl(
      '/accounting/v2/payments-automation/contract-payments/credit-card-statements'
    );
  }
}
