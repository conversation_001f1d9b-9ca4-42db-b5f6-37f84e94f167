import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { ContractPaymentsService } from '../../services/contract-payments.service';
import { FormBuilder } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { PageEvent } from '@angular/material/paginator';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { API } from 'src/environments/api';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import * as moment from 'moment';
import { CCDialog } from '@maids/cc-lib/dialog';
import { SimilarPaymentsComponent } from '../similar-payments/similar-payments.component';
import { PaginationRequest } from '@maids/cc-lib/common';
import { Observable } from 'rxjs';
import { SelectOption } from '@maids/cc-lib/select-input';
@Component({
  selector: 'app-credit-card-statement-result',
  templateUrl: './credit-card-statement-result.component.html',
  styleUrls: ['./credit-card-statement-result.component.scss'],
})
export class CreditCardStatementResultComponent implements OnInit, OnDestroy {
  finishParsing: boolean = false;
  disableCreateTransaction: boolean = false;
  provider: string = '';
  formGroup = this.formBuilder.group({
    clientName: [''],
    contractId: [''],
    paymentId: [''],
    paymentType: [''],
    authorizationCode: [''],
    transferReference: [''],
  });
  dateChangedToReceived: boolean = true;
  unmatchedCheckoutRefundOnlyInBank: any;
  matchedCheckoutOnlineCardRecord: any;
  matchedCheckoutClientRefunds: any;
  unmatchedCheckoutOnlineCardRecord: any;
  unmatchedCheckoutInErpOnly: any;
  //////////////////////////////
  unmatchedPaytabsRefundOnlyInBank: any;
  matchedPaytabsOnlineCardRecord: any;
  matchedPaytabsClientRefunds: any;
  unmatchedPaytabsOnlineCardRecord: any;
  unmatchedPaytabsInErpOnly: any;
  allData: any;
  totalMatchedRefunds: number = 0;
  totalUnmatchedRefunds: number = 0;
  totalUnMatchedPayments: number = 0;
  readonly paymentTypeOptions = (
    pageReq: PaginationRequest
  ): Observable<any[]> => {
    return this.contractsPaymentsService.CPpaymentTypeOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  private intervalId: any;

  constructor(
    private contractsPaymentsService: ContractPaymentsService,
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private mediaService: MediaService,
    private ccDialog: CCDialog,
    private notifications: CCNotificationService
  ) {}

  ngOnInit(): void {
    this.getOnlineCardStatementFile();
    this.intervalId = setInterval(() => {
      if (!this.finishParsing) {
        this.getOnlineCardStatementFile();
      }
    }, 6000);
  }

  ngOnDestroy(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  }

  getOnlineCardStatementFile() {
    this.contractsPaymentsService
      .getOnlineCardStatementFile(this.route.snapshot.params['id'])
      .subscribe((res) => {
        if (res.resolved) {
          this.finishParsing = true;
          this.provider = !!res.provider ? res.provider : 'CHECKOUT';
          this.disableCreateTransaction = !!res.posTransactionId ? true : false;
          this.totalMatchedRefunds = res.totalMatchedRefunds;
          this.totalUnmatchedRefunds = res.totalUnmatchedRefunds;
          this.totalUnMatchedPayments = res.totalUnMatchedPayments;
          this.getTablesData();
        } else {
          this.finishParsing = false;
        }
      });
  }
  getTablesData(page: number = 0, size: number = 20) {
    let params: any = this.setFilterData(this.formGroup.value);
    params.page = page;
    params.size = size;
    this.contractsPaymentsService
      .CPviewResult(this.route.snapshot.params['id'], params)
      .subscribe((res: any) => {
        this.allData = res;
        if (this.provider == 'CHECKOUT') {
          this.unmatchedCheckoutRefundOnlyInBank =
            res.unmatchedRefundOnlyInBank;
          this.matchedCheckoutOnlineCardRecord = res.matchedOnlineCardRecord;
          this.matchedCheckoutClientRefunds = res.matchedClientRefunds;
          this.unmatchedCheckoutOnlineCardRecord =
            res.unmatchedOnlineCardRecord;
          this.unmatchedCheckoutInErpOnly = res.unmatchedRecordsInErpOnly;
        } else if (this.provider == 'PAYTABS') {
          this.unmatchedPaytabsRefundOnlyInBank = res.unmatchedRefundOnlyInBank;
          this.matchedPaytabsOnlineCardRecord = res.matchedOnlineCardRecord;
          this.matchedPaytabsClientRefunds = res.matchedClientRefunds;
          this.unmatchedPaytabsOnlineCardRecord = res.unmatchedOnlineCardRecord;
          this.unmatchedPaytabsInErpOnly = res.unmatchedRecordsInErpOnly;
        }
      });
  }
  getTableData(page: number = 0, size: number = 20, gridName: string = '') {
    let params: any = this.setFilterData(this.formGroup.value);
    params.page = page;
    params.size = size;
    params.gridName = gridName;
    this.contractsPaymentsService
      .CPsearchByGrid(this.route.snapshot.params['id'], params)
      .subscribe((res: any) => {
        if (gridName == 'MatchedClientRefunds') {
          if (this.provider == 'CHECKOUT' || this.provider == '') {
            this.matchedCheckoutClientRefunds = res;
          }
          if (this.provider == 'PAYTABS') {
            this.matchedPaytabsClientRefunds = res;
          }
        } else if (gridName == 'UnmatchedOnlineCardRecord') {
          if (this.provider == 'CHECKOUT' || this.provider == '') {
            this.unmatchedCheckoutOnlineCardRecord = res;
          }
          if (this.provider == 'PAYTABS') {
            this.unmatchedPaytabsOnlineCardRecord = res;
          }
        } else if (gridName == 'UnmatchedInErpOnly') {
          if (this.provider == 'CHECKOUT' || this.provider == '') {
            this.unmatchedCheckoutInErpOnly = res;
          }
          if (this.provider == 'PAYTABS') {
            this.unmatchedPaytabsInErpOnly = res;
          }
        } else if (gridName == 'MatchedOnlineCardRecord') {
          if (this.provider == 'CHECKOUT' || this.provider == '') {
            this.matchedCheckoutOnlineCardRecord = res;
          }
          if (this.provider == 'PAYTABS') {
            this.matchedPaytabsOnlineCardRecord = res;
          }
        } else if (gridName == 'UnmatchedRefundOnlineCardRecord') {
          if (this.provider == 'CHECKOUT' || this.provider == '') {
            this.unmatchedCheckoutRefundOnlyInBank = res;
          }
          if (this.provider == 'PAYTABS') {
            this.unmatchedPaytabsRefundOnlyInBank = res;
          }
        }
      });
  }

  MOCRCheckoutCols: CCGridColumn[] = [
    {
      field: 'paymentDate',
      header: 'Date Changed to Received',
      formatter(rowData, colDef) {
        return moment(rowData.paymentDate).format('YYYY-MM-DD');
      },
      width: '200px',
    },
    { field: 'paymentAmount', header: 'Payment Amount' },
    {
      field: 'paymentType',
      header: 'Payment Type',
    },
    {
      field: 'paymentId',
      header: 'Payment ID',
      formatter(rowData, colDef) {
        return rowData.payments ? rowData.payments.paymentId : '';
      },
    },
    { field: 'transferReference', header: 'Checkout Payment ID' },
    { field: 'clientName', header: 'Family Name' },
    { field: 'contractId', header: 'Contract ID' },
    {
      field: 'transactionId',
      header: 'Transaction Number',
      formatter(rowData, colDef) {
        return rowData.payments ? rowData.payments.transactionId : '';
      },
    },
  ];
  MOCRPaytabsCols: CCGridColumn[] = [
    {
      field: 'paymentDate',
      header: 'Date Changed to Received',
      formatter(rowData, colDef) {
        return moment(rowData.paymentDate).format('YYYY-MM-DD');
      },
      width: '200px',
    },
    { field: 'paymentAmount', header: 'Payment Amount' },
    {
      field: 'paymentType',
      header: 'Payment Type',
    },
    {
      field: 'paymentId',
      header: 'Payment ID',
      formatter(rowData, colDef) {
        return rowData.payments ? rowData.payments.paymentId : '';
      },
    },
    { field: 'authorizationCode', header: 'Authorization Code' },
    { field: 'transferReference', header: 'Tran Ref Number' },
    { field: 'clientName', header: 'Family Name' },
    { field: 'contractId', header: 'Contract ID' },
    {
      field: 'transactionId',
      header: 'Transaction Number',
      formatter(rowData, colDef) {
        return rowData.payments ? rowData.payments.transactionId : '';
      },
    },
  ];
  getNextMOCR(event: PageEvent) {
    this.getTableData(
      event.pageIndex,
      event.pageSize,
      'MatchedOnlineCardRecord'
    );
  }
  UOCRCheckoutCols: CCGridColumn[] = [
    {
      field: 'paymentDate',
      header: 'Date Changed to Received',
      formatter(rowData, colDef) {
        return moment(rowData.paymentDate).format('YYYY-MM-DD');
      },
      width: '200px',
    },
    { field: 'paymentAmount', header: 'Payment Amount' },
    { field: 'transferReference', header: 'Checkout Payment ID' },
  ];
  UOCRPaytabsCols: CCGridColumn[] = [
    {
      field: 'paymentDate',
      header: 'Date Changed to Received',
      formatter(rowData, colDef) {
        return moment(rowData.paymentDate).format('YYYY-MM-DD');
      },
      width: '200px',
    },
    { field: 'paymentAmount', header: 'Payment Amount' },
    { field: 'authorizationCode', header: 'Authorization Code' },
    { field: 'transferReference', header: 'Tran Ref Number' },
  ];
  getNextUOCR(event: PageEvent) {
    this.getTableData(
      event.pageIndex,
      event.pageSize,
      'UnmatchedOnlineCardRecord'
    );
  }
  MCRCheckoutCols: CCGridColumn[] = [
    {
      field: 'paymentDate',
      header: 'Date Changed to PDP',
      formatter(rowData, colDef) {
        return moment(rowData.paymentDate).format('YYYY-MM-DD');
      },
      width: '200px',
    },
    { field: 'paymentAmount', header: 'Payment Amount' },
    {
      field: 'paymentId',
      header: 'Payment ID',
      formatter(rowData, colDef) {
        return rowData.payments ? rowData.payments.paymentId : '';
      },
    },
    { field: 'transferReference', header: 'Checkout Payment ID' },
    { field: 'clientName', header: 'Family Name' },
    { field: 'contractId', header: 'Contract ID' },
    { field: 'transactionId', header: 'Transaction Number' },
  ];
  MCRPaytabsCols: CCGridColumn[] = [
    {
      field: 'paymentDate',
      header: 'Date Changed to PDP',
      formatter(rowData, colDef) {
        return moment(rowData.paymentDate).format('YYYY-MM-DD');
      },
      width: '200px',
    },
    { field: 'paymentAmount', header: 'Payment Amount' },
    {
      field: 'paymentId',
      header: 'Payment ID',
      formatter(rowData, colDef) {
        return rowData.payments ? rowData.payments.paymentId : '';
      },
    },
    { field: 'transferReference', header: 'Tran Ref Number' },
    { field: 'clientName', header: 'Family Name' },
    { field: 'contractId', header: 'Contract ID' },
    { field: 'transactionId', header: 'Transaction Number' },
  ];

  getMCRNext(event: PageEvent) {
    this.getTableData(event.pageIndex, event.pageSize, 'MatchedClientRefunds');
  }
  UROIBCheckoutCols: CCGridColumn[] = [
    {
      field: 'paymentDate',
      header: 'Date Changed to PDP',
      formatter(rowData, colDef) {
        return moment(rowData.paymentDate).format('YYYY-MM-DD');
      },
      width: '200px',
    },
    { field: 'paymentAmount', header: 'Payment Amount' },
    { field: 'transferReference', header: 'Checkout Payment ID' },
  ];
  UROIBPaytabsCols: CCGridColumn[] = [
    {
      field: 'paymentDate',
      header: 'Date Changed to PDP',
      formatter(rowData, colDef) {
        return moment(rowData.paymentDate).format('YYYY-MM-DD');
      },
      width: '200px',
    },
    { field: 'paymentAmount', header: 'Payment Amount' },
    { field: 'transferReference', header: 'Tran Ref Number' },
  ];
  getUROIBNext(event: PageEvent) {
    this.getTableData(
      event.pageIndex,
      event.pageSize,
      'UnmatchedRefundOnlineCardRecord'
    );
  }
  UIEOCheckoutCols: CCGridColumn[] = [
    {
      field: 'paymentDate',
      header: 'Date Changed to Received',
      formatter(rowData, colDef) {
        return moment(rowData.paymentDate).format('YYYY-MM-DD');
      },
      width: '200px',
    },
    { field: 'paymentAmount', header: 'Payment Amount' },
    {
      field: 'paymentType',
      header: 'Payment Type',
    },
    {
      field: 'paymentId',
      header: 'Payment ID',
      formatter(rowData, colDef) {
        return rowData.payments ? rowData.payments.paymentId : '';
      },
    },
    { field: 'transferReference', header: 'Checkout Payment ID' },
    { field: 'clientName', header: 'Family Name' },
    { field: 'contractId', header: 'Contract ID' },
  ];
  UIEOPaytabsCols: CCGridColumn[] = [
    {
      field: 'paymentDate',
      header: 'Date Changed to Received',
      formatter(rowData, colDef) {
        return moment(rowData.paymentDate).format('YYYY-MM-DD');
      },
      width: '200px',
    },
    { field: 'paymentAmount', header: 'Payment Amount' },

    {
      field: 'paymentId',
      header: 'Payment ID',
      formatter(rowData, colDef) {
        return rowData.payments ? rowData.payments.paymentId : '';
      },
    },
    { field: 'authorizationCode', header: 'Authorization Code' },
    { field: 'transferReference', header: 'Tran Ref Number' },
    { field: 'clientName', header: 'Family Name' },
    { field: 'contractId', header: 'Contract ID' },
  ];
  getNextUROCR(event: PageEvent) {
    this.getTableData(event.pageIndex, event.pageSize, 'UnmatchedInErpOnly');
  }
  exportExcel(gridName: string = '') {
    let params: any[] = [];
    console.log(this.formGroup.value);

    for (const key in this.formGroup.value) {
      if (this.formGroup.value[key]) {
        params.push(`${key}=${this.formGroup.value[key]}`);
      }
    }
    params.push(gridName ? `gridName=${gridName}` : `exportAll=${true}`);
    this.mediaService.downloadFile(
      `${API.CPgenerateCsv}/${this.route.snapshot.params['id']}?${params.join(
        '&'
      )}`
    );
  }
  setFilterData(data: any) {
    Object.keys(data).forEach((key) => {
      if (data[key] === null || data[key] === '') {
        delete data[key];
      }
    });
    return data;
  }
  MatchSimilarPayments(
    element: any,
    dateChangedToReceived: boolean,
    modalTitle: string,
    type: string
  ) {
    if (type == 'SP') {
      this.contractsPaymentsService
        .getTodosForMatchOnlineStatement(
          element.rId ? element.rId : element.recordId,
          { page: 0, size: 5 }
        )
        .subscribe((res: any) => {
          this.ccDialog
            .originalOpen(SimilarPaymentsComponent, {
              data: {
                element: element,
                response: res,
                dateChangedToReceived: dateChangedToReceived,
                title: modalTitle,
                provider: this.provider,
                type,
              },
            })
            .afterClosed()
            .subscribe((res: any) => {
              if (res) {
                this.getTablesData(0, 20);
              }
            });
        });
    } else if (type == 'PDP') {
      this.contractsPaymentsService
        .getTodosForMatchOnlineStatementForRefundPayment(
          element.rId ? element.rId : element.recordId,
          { page: 0, size: 5 }
        )
        .subscribe((res: any) => {
          this.ccDialog
            .originalOpen(SimilarPaymentsComponent, {
              data: {
                element: element,
                response: res,
                dateChangedToReceived: dateChangedToReceived,
                title: modalTitle,
                provider: this.provider,
                type,
              },
            })
            .afterClosed()
            .subscribe((res: any) => {
              if (res) {
                this.getTablesData(0, 20);
              }
            });
        });
    }
  }
  gotoFamily(id: any) {
    window.open('#!/client/payments/' + id, '_blank');
  }
  showConfirm() {
    this.ccDialog.confirm(
      'Confirmation',
      'Are you sure you want to Create Pos Transaction?',
      () => {
        this.contractsPaymentsService
          .getOnlineCardStatementFile(this.route.snapshot.params['id'])
          .subscribe((res: any) => {
            if (res.allowCreatePosTransaction) {
              this.contractsPaymentsService
                .createPosTransaction(this.route.snapshot.params['id'])
                .subscribe((res: any) => {
                  this.notifications.notifySuccess(
                    'Transaction Created Successfully'
                  );
                  this.disableCreateTransaction = true;
                });
            } else {
              this.notifications.notifyError(
                'Please match all statement records first'
              );
            }
          });
      }
    );
  }
}
