import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ActivatedRoute } from '@angular/router';
import { ContractPaymentsService } from '../../services/contract-payments.service';
import { CCGridColumn } from '@maids/cc-lib/datagrid/src/datagrid.model';
import * as moment from 'moment';
import { CCDialog, CCDialogRef } from '@maids/cc-lib/dialog';
import { CCNotificationService } from '@maids/cc-lib/services';
import { PageEvent } from '@angular/material/paginator';
import { ConfirmTransactionComponent } from '../confirm-transaction/confirm-transaction.component';
@Component({
  selector: 'app-similar-payments',
  templateUrl: './similar-payments.component.html',
  styleUrls: ['./similar-payments.component.scss'],
})
export class SimilarPaymentsComponent implements OnInit {
  SPCheckoutData: any;
  SPPaytabsData: any;
  SRPCheckoutData: any;
  SRPPaytabsData: any;
  selectedMatchedTodoId: any;
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private route: ActivatedRoute,
    private contractsPaymentsService: ContractPaymentsService,
    private ccDialog: CCDialog,
    private notifications: CCNotificationService,
    private ccDialogRef: CCDialogRef<SimilarPaymentsComponent>
  ) {}

  ngOnInit(): void {
    if (this.data.provider == 'CHECKOUT') {
      if (this.data.type == 'SP') {
        this.SPCheckoutData = this.data.response;
      } else if (this.data.type == 'PDP') {
        this.SRPCheckoutData = this.data.response;
      }
    } else if (this.data.provider == 'PAYTABS') {
      if (this.data.type == 'SP') {
        this.SPPaytabsData = this.data.response;
      } else if (this.data.type == 'PDP') {
        this.SRPPaytabsData = this.data.response;
      }
    }
  }
  getSimilarPaymentsTableData(page: number = 0, size: number = 5) {
    let params: any = {};
    params.page = page;
    params.size = size;
    this.selectedMatchedTodoId = null;
    this.contractsPaymentsService
      .getTodosForMatchOnlineStatement(this.data.element.recordId, params)
      .subscribe((res: any) => {
        if (this.data.provider == 'CHECKOUT') {
          if (this.data.type == 'SP') {
            this.SPCheckoutData = res;
          } else if (this.data.type == 'PDP') {
            this.SRPCheckoutData = res;
          }
        } else if (this.data.provider == 'PAYTABS') {
          if (this.data.type == 'SP') {
            this.SPPaytabsData = res;
          } else if (this.data.type == 'PDP') {
            this.SRPPaytabsData = res;
          }
        }
      });
  }
  SPCheckoutCols: CCGridColumn[] = [
    { field: 'select', header: '' },
    { field: 'clientName', header: 'Family Name' },
    {
      field: 'paymentDate',
      header: 'Date Changed to Received',
      formatter(rowData, colDef) {
        return moment(rowData.paymentDate).format('DD/MM/YYYY');
      },
      width: '200px',
    },
    { field: 'amount', header: 'Payment Amount' },
    { field: 'transferReference', header: 'Checkout Payment ID' },
  ];
  SPPaytabsCols: CCGridColumn[] = [
    { field: 'select', header: '' },
    { field: 'clientName', header: 'Family Name' },
    {
      field: 'paymentDate',
      header: 'Date Changed to Received',
      formatter(rowData, colDef) {
        return moment(rowData.paymentDate).format('DD/MM/YYYY');
      },
      width: '200px',
    },
    { field: 'amount', header: 'Payment Amount' },
    { field: 'authorizationCode', header: 'Authorization Code' },
    { field: 'transferReference', header: 'Tran Ref Number' },
  ];
  getNextSPPage(event: PageEvent) {
    this.getSimilarPaymentsTableData(event.pageIndex, event.pageSize);
  }
  SRPCheckoutCols: CCGridColumn[] = [
    { field: 'select', header: '' },
    { field: 'clientName', header: 'Family Name' },
    {
      field: 'paymentDate',
      header: 'Date Changed to PDP',
      formatter(rowData, colDef) {
        return moment(rowData.paymentDate).format('DD/MM/YYYY');
      },
      width: '200px',
    },
    { field: 'amount', header: 'Payment Amount' },
    { field: 'transferReference', header: 'Checkout Payment ID' },
  ];
  SRPPaytabsCols: CCGridColumn[] = [
    { field: 'select', header: '' },
    { field: 'clientName', header: 'Family Name' },
    {
      field: 'paymentDate',
      header: 'Date Changed to PDP',
      formatter(rowData, colDef) {
        return moment(rowData.paymentDate).format('DD/MM/YYYY');
      },
      width: '200px',
    },
    { field: 'amount', header: 'Payment Amount' },
    { field: 'authorizationCode', header: 'Authorization Code' },
    { field: 'transferReference', header: 'Tran Ref Number' },
  ];
  getNextSRPPage(event: PageEvent) {
    this.getSimilarPaymentsTableData(event.pageIndex, event.pageSize);
  }
  match() {
    let id: any;
    id = this.data.element.rId
      ? this.data.element.rId
      : this.data.element.recordId;
    let toMatch = null;
    if (this.data.dateChangedToReceived) {
      if (this.data.provider == 'CHECKOUT') {
        toMatch = this.SPCheckoutData.content.find(
          (x: any) => x.todoId == this.selectedMatchedTodoId
        );
      } else if (this.data.provider == 'PAYTABS') {
        toMatch = this.SPPaytabsData.content.find(
          (x: any) => x.todoId == this.selectedMatchedTodoId
        );
      }
    } else {
      if (this.data.provider == 'CHECKOUT') {
        toMatch = this.SRPCheckoutData.content.find(
          (x: any) => x.todoId == this.selectedMatchedTodoId
        );
      } else if (this.data.provider == 'PAYTABS') {
        toMatch = this.SRPPaytabsData.content.find(
          (x: any) => x.todoId == this.selectedMatchedTodoId
        );
      }
    }
    if (!!toMatch.transferReference) {
      this.ccDialog
        .originalOpen(ConfirmTransactionComponent, {
          data: {
            selectedMatchedTodoId: this.selectedMatchedTodoId,
            toMatchId: id,
            toMatch,
          },
        })
        .afterClosed()
        .subscribe((res: any) => {
          if (res) {
            this.ccDialogRef.close(true);
          }
        });
      return;
    } else {
      this.ccDialog.confirm(
        '',
        'Are you sure you want to match the selected payment?',
        () => {
          this.contractsPaymentsService
            .CPmatchWithoutUpdate(id, this.selectedMatchedTodoId)
            .subscribe((res: any) => {
              this.notifications.notifySuccess('Matched Successfully');
              this.ccDialogRef.close(true);
            });
        },
      );
    }
  }
  onTodoSelect(todoId: any) {
    this.selectedMatchedTodoId = todoId;
  }
  isChecked(todoId: any) {
    return this.selectedMatchedTodoId == todoId;
  }
}
