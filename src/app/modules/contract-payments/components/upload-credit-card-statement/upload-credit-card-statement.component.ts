import { Component, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { ContractPaymentsService } from '../../services/contract-payments.service';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import * as moment from 'moment';
import { MediaService } from '@maids/cc-lib/services';
import { PageEvent } from '@angular/material/paginator';
import { API } from 'src/environments/api';
import { Router } from '@angular/router';
import { CCDialog } from '@maids/cc-lib/dialog';
import { CCFileUploaderConfig } from '@maids/cc-lib/file-uploader';
@Component({
  selector: 'app-upload-credit-card-statement',
  templateUrl: './upload-credit-card-statement.component.html',
  styleUrls: ['./upload-credit-card-statement.component.scss']
})
export class UploadCreditCardStatementComponent implements OnInit {
  records: any;
  searchForm = this.formBuilder.group({
    fromDate: [''],
    toDate: [''],
    showDeletedFiles: [''],
  });
  uploadForm = this.formBuilder.group({
    onlineCardStatement: ['', Validators.required],
    provider: ['', Validators.required],
  });
  config: CCFileUploaderConfig = {
    acceptedFiles: '.xlsx, .xls, .csv',
    maxFilesize: 10,
  };
  constructor(
    private formBuilder: FormBuilder,
    private contractPaymentsService: ContractPaymentsService,
    private mediaService: MediaService,
    private router: Router,
    private ccDialog: CCDialog
  ) {}
  gridCols: CCGridColumn[] = [
    {
      field: 'uploadedDate',
      header: 'Upload Date',
      class: 'max',
      formatter(rowData, colDef) {
        return moment(rowData.uploadedDate).format('YYYY-MM-DD');
      },
    },
    { field: 'totalTransactions', header: 'Total Transactions', width: '150px' },
    { field: 'totalMatchedPayments', header: 'Total Matched payments', width: '150px' },
    { field: 'totalUnMatchedPayments', header: 'Total unmatched payments', width: '150px' },
    { field: 'totalRefundedPayments', header: 'Total Refunded payments', width: '150px' },
    { field: 'attachment', header: 'File Name', width: '150px' },
  ];

  ngOnInit(): void {
    this.contractPaymentsService.CPgetDefaultProvider().subscribe((res) => {
      this.uploadForm.controls['provider'].setValue(res[0].value ?? 'CHECKOUT');
    });
    this.getTableData();
  }
  getTableData(page: number = 0, size: number = 20) {
    this.contractPaymentsService
      .onlineCardStatementFileSearch({
        page,
        size,
        sort: 'uploadedDate,DESC',
        fromDate: this.searchForm.controls['fromDate'].value,
        toDate: this.searchForm.controls['toDate'].value,
        showDeletedFiles: this.searchForm.controls['showDeletedFiles'].value,
      })
      .subscribe((res: any) => {
        this.records = res;
      });
  }
  getNextPage(event: PageEvent) {
    this.getTableData(event.pageIndex, event.pageSize);
  }
  processTransactions() {
    let attachs: any[] = [];
    if (this.uploadForm.controls['onlineCardStatement'].value) {
      attachs.push({
        id: this.uploadForm.controls['onlineCardStatement'].value[0].id,
      });
    }
    this.contractPaymentsService
      .createOnlineCardStatementFile({
        attachments: attachs,
        provider: this.uploadForm.controls['provider'].value,
      })
      .subscribe((res: any) => {
        this.router.navigateByUrl(
          `/accounting/v2/payments-automation/contract-payments/credit-card-statements/credit-card-statement-result/${res}`
        );
      });
  }
  downloadAttachment(uuid: string) {
    this.mediaService.downloadFile(`public/download/${uuid}`);
  }
  gotoResults(id: string) {
    this.router.navigateByUrl(
      `/accounting/v2/payments-automation/contract-payments/credit-card-statements/credit-card-statement-result/${id}`
    );
  }
  deleteFile(id: string) {
    this.ccDialog.confirm(
      '',
      'Are you sure you want to delete this statement?',
      () => {
        this.contractPaymentsService
          .deleteOnlineCardStatementFile(id)
          .subscribe((res: any) => {
            this.getTableData(0, 20);
          });
      }
    );
  }
}
