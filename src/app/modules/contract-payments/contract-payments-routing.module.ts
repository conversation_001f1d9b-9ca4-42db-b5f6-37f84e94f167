import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ContractPaymentsComponent } from './components/contract-payments/contract-payments.component';
import { CreditCardStatementsComponent } from './components/credit-card-statements.component';
import { CreditCardStatementResultComponent } from './components/credit-card-statement-result/credit-card-statement-result.component';
import { UploadCreditCardStatementComponent } from './components/upload-credit-card-statement/upload-credit-card-statement.component';

const routes: Routes = [
  {
    path: '',
    component: ContractPaymentsComponent,
    data: { label: '' },
  },
  {
    path: 'credit-card-statements',
    component: CreditCardStatementsComponent,
    data: {
      label: 'Credit Card Statements',
      pageCode: 'accounting_credit-card-statements',
    },
    children: [
      { path: '', component: UploadCreditCardStatementComponent },
      {
        path: 'credit-card-statement-result/:id',
        component: CreditCardStatementResultComponent,
        data: {
          label: 'Statement parsing result',
          pageCode: 'accounting_credit-card-statement-result',
        },
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ContractPaymentsRoutingModule {}
