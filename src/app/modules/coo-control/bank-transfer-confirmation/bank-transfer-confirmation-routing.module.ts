import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { BankTransferConfirmationComponent } from './components/bank-transfer-confirmation/bank-transfer-confirmation.component';

const routes: Routes = [
  {
    path: '',
    component: BankTransferConfirmationComponent,
    data: { pageCode: 'accounting_BankTransferConfirmation' },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class BankTransferConfirmationRoutingModule {}
