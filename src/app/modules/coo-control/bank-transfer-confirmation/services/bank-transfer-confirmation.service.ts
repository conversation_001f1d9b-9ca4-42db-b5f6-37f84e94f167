import { HttpClient, HttpContext } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import {
  CCBackendEndpoint,
  REQ_SHOW_LOADING_ICON,
} from '@maids/cc-erp-services';
import { Observable, of } from 'rxjs';
import { API } from 'src/environments/api';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class BankTransferConfirmationService {
  constructor(
    private _http: HttpClient,
    @Inject(CCBackendEndpoint) private _api: CCBackendEndpoint
  ) {}
  usersOptions(page: number, size: number=50, search: string = '') {
    const context = new HttpContext().set(REQ_SHOW_LOADING_ICON, false);
    return this._http.get(`${this._api}/${API.BTCUsersOptions}`, {
      params: { page, size, search },
      context,
    }).pipe(
      map((res: any) => {
          return res.content.map((item: any) => {
          return { text: item.label, id: item.id };
        });
      })
    );
  }
  findPendingConfirmationTodos(page: number, size: number): Observable<any> {
    return this._http.get(`${this._api}/${API.findPendingConfirmationTodos}`, {
      params: { page, size },
    });
  }
  managerAction(payload: any): Observable<any> {
    return this._http.post(`${this._api}/${API.managerAction}`, payload);
  }
  ceoAction(payload: any): Observable<any> {
    return this._http.post(`${this._api}/${API.ceoAction}`, payload);
  }
  sendRejectionMail(params: any, payload: any): Observable<any> {
    return this._http.post(`${this._api}/${API.sendRejectionMail}`, payload, {
      params,
    });
  }
  // approveAll(): Observable<any> {
  //   return this._http.post(`${this._api}/${API.BTCapproveAll}`, {});
  // }
  approveList(payload:any[]): Observable<any> {  
    return this._http.post(`${this._api}/${API.BTCapproveList}`, payload);
  }
  create(payload: any): Observable<any> {
    return this._http.post(`${this._api}/${API.BTCcreate}`, payload);
  }
}
