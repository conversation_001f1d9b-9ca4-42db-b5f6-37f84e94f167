import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CooControlComponent } from './coo-control.component';

const routes: Routes = [
  { path: '', component: CooControlComponent },
  {
    path: 'freedom-request-details/:todoID',
    loadChildren: () =>
      import('./freedom-request-details/freedom-request-details.module').then(
        (m) => m.FreedomRequestDetailsModule
      ),
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class CooControlRoutingModule {}
