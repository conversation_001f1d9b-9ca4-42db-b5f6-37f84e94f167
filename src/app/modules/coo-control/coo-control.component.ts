import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CCAuthService } from '@maids/cc-erp-services';

@Component({
  selector: 'app-coo-control',
  templateUrl: './coo-control.component.html',
  styleUrls: ['./coo-control.component.scss'],
})
export class CooControlComponent implements OnInit {
  hasFullCOOPosition: boolean = false;
  hasCooViewPosition: boolean = false;
  hasBankViewPosition: boolean = false;
  hasNightViewPosition: boolean = false;
  hasFamilyRefundViewPosition: boolean = false;
  tabs: string[] = [];
  selectedTab: string = '';
  constructor(
    private authService: CCAuthService,
    private route: ActivatedRoute,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.initTabs();
    this.checkAllPagePositions();
  }
  checkAllPagePositions() {
    this.authService.loggedUser$.subscribe((user: any) => {
      this.hasFullCOOPosition =
        user.positions.filter(
          (p: any) => p.code === 'coo_page_tabs_full_control'
        ).length > 0;

      this.hasCooViewPosition =
        user.positions.filter(
          (p: any) => p.code === 'coo_page_tabs_pending_approvals'
        ).length > 0;

      this.hasBankViewPosition =
        user.positions.filter(
          (p: any) =>
            p.code === 'bank_transfer_coo_user_position' ||
            p.code === 'bank_transfer_reviewer_user_position'
        ).length > 0;

      this.hasNightViewPosition =
        user.positions.filter(
          (p: any) => p.code === 'night_review_coo_user_position'
        ).length > 0;

      this.hasFamilyRefundViewPosition =
        user.positions.filter(
          (p: any) => p.code === 'client_refund_coo_user_position'
        ).length > 0;
    });
  }
  initTabs() {
    this.tabs = [
      'COO_SCREEN',
      'BANK_TRANSFER_CONFIRMATION',
      'NIGHT_REVIEWS',
      'FAMILY_REFUNDS_APPROVALS',
    ];
    this.selectedTab =
      this.route.snapshot.queryParams['tab'] &&
      this.tabs.indexOf(this.route.snapshot.queryParams['tab']) != -1
        ? this.route.snapshot.queryParams['tab']
        : this.hasFullCOOPosition || this.hasCooViewPosition
        ? 'COO_SCREEN'
        : this.hasBankViewPosition
        ? 'BANK_TRANSFER_CONFIRMATION'
        : this.hasNightViewPosition
        ? 'NIGHT_REVIEWS'
        : this.hasFamilyRefundViewPosition
        ? 'FAMILY_REFUNDS_APPROVALS'
        : 'COO_SCREEN';
  }
  selectTab(tab: string) {
    this.selectedTab = tab;
    this.router.navigateByUrl(`/accounting/v2/coo-control?tab=${tab}`);
  }
}
