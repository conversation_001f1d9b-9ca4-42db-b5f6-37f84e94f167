import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { FreedomRequestDetailsRoutingModule } from './freedom-request-details-routing.module';
import { FreedomRequestDetailsComponent } from './freedom-request-details.component';

// Import CC Lib modules
import { CCDialogModule } from '@maids/cc-lib/dialog';

// Import bank transfer confirmation module for shared components
import { BankTransferConfirmationModule } from '../bank-transfer-confirmation/bank-transfer-confirmation.module';
import { CCButtonModule } from '@maids/cc-lib/button';

@NgModule({
  declarations: [FreedomRequestDetailsComponent],
  imports: [
    CommonModule,
    FormsModule,
    FreedomRequestDetailsRoutingModule,
    CCDialogModule,CCButtonModule,
    BankTransferConfirmationModule,
  ],
})
export class FreedomRequestDetailsModule {}
