import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { NightReviewsRoutingModule } from './night-reviews-routing.module';
import { NightReviewsComponent } from './night-reviews.component';
import { CCButtonModule } from '@maids/cc-lib/button';
import { CCDatagridModule } from '@maids/cc-lib/datagrid';
import { CCCheckboxModule } from '@maids/cc-lib/checkbox';
import { CCDialogModule } from '@maids/cc-lib/dialog';
import { CCTextareaModule } from '@maids/cc-lib/textarea';
import { CCSelectInputModule } from '@maids/cc-lib/select-input';
import { CCDatepickerModule } from '@maids/cc-lib/date';
import { CCIconModule } from '@maids/cc-lib/icon';

@NgModule({
  declarations: [NightReviewsComponent],
  imports: [
    CommonModule,
    NightReviewsRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    CCButtonModule,
    CCDatagridModule,
    CCCheckboxModule,
    CCDialogModule,
    CCTextareaModule,
    CCSelectInputModule,
    CCDatepickerModule,
    CCIconModule,
  ],
  exports: [NightReviewsComponent],
})
export class NightReviewsModule {}
