import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpErrorResponse,
  HttpEventType, HttpResponse
} from '@angular/common/http';
import { Observable, EMPTY } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { CCNotificationService } from '@maids/cc-lib';

@Injectable()
export class ApiErrorInterceptor implements HttpInterceptor {

  constructor(private _notifications: CCNotificationService) {}

  intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<unknown>> {
    return next.handle(request).pipe(

    )
  }
}


export function getNetworkErrorMessage(response: HttpResponse<any>) {
  return `${response?.body?.message}`;
}
