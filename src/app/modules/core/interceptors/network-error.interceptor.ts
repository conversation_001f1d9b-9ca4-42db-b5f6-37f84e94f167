import { Injectable } from '@angular/core';
import {
  <PERSON>ttpRe<PERSON>,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpErrorResponse, HttpEventType, HttpResponse, HttpContextToken
} from '@angular/common/http';
import { Observable, EMPTY ,throwError } from 'rxjs';
import {catchError, tap} from 'rxjs/operators';
import { CCNotificationService } from '@maids/cc-lib/services';
export const USER_DELETE_DEPENDENCIES_ERROR = new HttpContextToken<boolean>(() => false);
export const MESSAGE_TEMPLATE_UNIQUENAME_ERROR = new HttpContextToken<boolean>(() => false);
export const HIDE_ERROR_ALERT = new HttpContextToken<boolean>(() => false);
@Injectable()
export class NetworkErrorInterceptor implements HttpInterceptor {

  constructor(private _notifications: CCNotificationService) {}

  intercept(request: HttpRequest<any>, next: <PERSON>tt<PERSON><PERSON><PERSON><PERSON>): Observable<HttpEvent<any>> {
    return next.handle(request)
      .pipe(
        catchError((error: HttpErrorResponse) => {
          let errorMsg = `Error Code: ${error.status},  Message: ${error.message}`;
          if ( error.error && error.error.message ) { //client side error
            errorMsg = `${error.error.message}`;
          }
          if (request.context.get(USER_DELETE_DEPENDENCIES_ERROR) === true) {
            errorMsg = "Error: There are dependencies with this user and can not delete it";
          }
          if(request.context.get(MESSAGE_TEMPLATE_UNIQUENAME_ERROR)===true && errorMsg.includes("NAME_UNIQUE")){
            errorMsg = "Error: Duplicate Message Template Name!";
          }
          console.log(errorMsg);
          if(request.context.get(HIDE_ERROR_ALERT)===false){
            this._notifications.notifyError(errorMsg)
          }
          return throwError(errorMsg);
        })
      )
  }
}


export function getNetworkErrorMessage(error: HttpErrorResponse) {
  if(error.error){
    return `API Error: | path = ${error.error.path||''} | msg = ${error.error.message?error.error.message:error.error.error} | status = ${error.error.status||''}`;
  }
  console.log('network error ');
  console.log(error.url);
  return `Network Error: couldn't not connect to server (error code ${error.status})`;
}

