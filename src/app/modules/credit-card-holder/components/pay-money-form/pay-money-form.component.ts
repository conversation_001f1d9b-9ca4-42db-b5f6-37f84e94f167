import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CreditCardHolderService } from '../../services/credit-card-holder.service';
import { map, Observable } from 'rxjs';
import { SelectOption } from '@maids/cc-lib/select-input';
import { PaginationRequest } from '@maids/cc-lib/common';
import { FormBuilder, Validators } from '@angular/forms';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { CCFileUploaderConfig } from '@maids/cc-lib/file-uploader';

@Component({
  selector: 'app-pay-money-form',
  templateUrl: './pay-money-form.component.html',
  styleUrls: ['./pay-money-form.component.scss'],
})
export class PayMoneyFormComponent implements OnInit {
  filteredAttachments: any | null = null;
  formGroup = this.formBuilder.group({
    beneficiaryName: [null],
    amount: [null],
    description: [null],
    instructions: [null],
    selectedCreditCardBucket: [null, [Validators.required]],
    paymentInvoiceAttachement: [null, [Validators.required]],
    invoiceContainVat: [null, [Validators.required]],
    vatAmount: [null],
    attachedValidVatInvoice: [null],
    paymentVATInvoiceAttachement: [null],
  });
  config: CCFileUploaderConfig = {
    maxFilesize: 5,
  };
  readonly getBuckets = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.creditCardHolderService
      .getCreditCardBuckets(pageReq.page, pageReq.size, pageReq.searchString)
      .pipe(
        map((data) =>
          data.content.map(
            (opt: any) => ({ id: opt.id, text: opt.name } as SelectOption)
          )
        )
      );
  };
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private creditCardHolderService: CreditCardHolderService,
    private formBuilder: FormBuilder,
    private mediaService: MediaService,
    public readonly notifications: CCNotificationService
  ) {}

  ngOnInit(): void {
    const id = this.route.snapshot.params['id'];
    this.creditCardHolderService
      .getCreditCardPaymentInfo(id)
      .subscribe((res: any) => {
        this.formGroup.patchValue(res);
        this.formGroup.controls['beneficiaryName'].disable();
        this.formGroup.controls['amount'].disable();
        this.formGroup.controls['description'].disable();
        this.formGroup.controls['instructions'].disable();
        this.filteredAttachments = res.attachments.filter(
          (_: any) => !_.tag.includes('SIGNATURE')
        );
        this.formGroup.controls['paymentInvoiceAttachement'].setValue(
          res.attachments.filter((_: any) =>
            _.tag.includes('EXPENSE_PAYMENT_INVOICE')
          )
        );
        if (res.taxable === true) {
          this.formGroup.controls['invoiceContainVat'].setValue('1');
          this.formGroup.controls['vatAmount'].setValue(res.vatAmount);
        } else {
          if (res.taxable === false) {
            this.formGroup.controls['invoiceContainVat'].setValue('0');
            this.formGroup.controls['invoiceContainVat'].disable();
          } else {
            this.formGroup.controls['invoiceContainVat'].setValue(null);
            this.formGroup.controls['invoiceContainVat'].enable();
          }
        }
      });
  }
  download(uuid: string) {
    this.mediaService.downloadFile('public/download/' + uuid);
  }
  cancel() {
    this.router.navigateByUrl('/accounting/v2/credit-card-holder');
  }
  submit() {
    let payload: any;
    payload = {
      fromBucket: {
        id: this.formGroup.controls['selectedCreditCardBucket'].value,
      },
      vatAmount: this.formGroup.controls['vatAmount'].value,
    };
    let attachments: any[] = [];
    if (this.formGroup.controls['paymentInvoiceAttachement'].value) {
      attachments.push({
        id: this.formGroup.controls['paymentInvoiceAttachement'].value[0].id,
      });
    }
    if (this.formGroup.controls['paymentVATInvoiceAttachement'].value) {
      attachments.push({
        id: this.formGroup.controls['paymentVATInvoiceAttachement'].value[0].id,
      });
    }
    payload.attachments = attachments;
    console.log(payload);
    this.creditCardHolderService
      .payCreditCard(this.route.snapshot.params['id'], payload)
      .subscribe({
        next: (res: any) => {
          this.router.navigateByUrl('/accounting/v2/credit-card-holder');
          this.notifications.notifySuccess('Done Successfully');
        },
        error: (err) => {
          this.notifications.notifyError(err.error.message);
        },
      });
  }
}
