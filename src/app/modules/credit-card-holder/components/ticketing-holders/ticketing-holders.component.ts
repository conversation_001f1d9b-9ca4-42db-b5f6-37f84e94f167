import { Component, Input, OnInit } from '@angular/core';
import { CreditCardHolderService } from '../../services/credit-card-holder.service';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { PageEvent } from '@angular/material/paginator';
import { Router } from '@angular/router';
@Component({
  selector: 'app-ticketing-holders',
  templateUrl: './ticketing-holders.component.html',
  styleUrls: ['./ticketing-holders.component.scss'],
})
export class TicketingHoldersComponent implements OnInit {
  ticketingRecords: any | null = null;
  constructor(
    private creditCardHolderService: CreditCardHolderService,
    private router: Router
  ) {}

  gridCols: CCGridColumn[] = [
    { field: 'beneficiary', header: 'Beneficiary' },
    { field: 'description', header: 'Description' },
    { field: 'amount', header: 'Amount' },
    { field: 'currency', header: 'Currency' },
    { field: 'creationDate', header: 'Creation Date', class: 'date' },
  ];
  ngOnInit(): void {
    this.getTicketingRecords();
  }
  getTicketingRecords(page: number = 0, size: number = 20) {
    this.creditCardHolderService
      .getPendingCreditCardPayments({
        page,
        size,
        type: 'TICKETING',
      })
      .subscribe((res) => {
        this.ticketingRecords = res;
      });
  }
  getNextPage(event: PageEvent) {
    this.getTicketingRecords(event.pageIndex, event.pageSize);
  }
  pay(element: any) {
    this.router.navigateByUrl(
      `/accounting/v2/credit-card-holder/pay-form/${element.id}`
    );
  }
}
