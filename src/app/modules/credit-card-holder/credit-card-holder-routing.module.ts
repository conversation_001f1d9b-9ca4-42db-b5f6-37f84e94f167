import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CreditCardHolderListComponent } from './components/credit-card-holder-list/credit-card-holder-list.component';
import { PayMoneyFormComponent } from './components/pay-money-form/pay-money-form.component';

const routes: Routes = [
  {
    path: '',
    component: CreditCardHolderListComponent,
    data: { label: '' },
  },
  {
    path: 'pay-form/:id',
    component: PayMoneyFormComponent,
    data: {
      label: 'Pay Money',
      pageCode: 'accounting_CreditCardHolderPayForm',
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class CreditCardHolderRoutingModule {}
