import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { AbstractControl, FormBuilder, FormControl, FormGroup, ValidationErrors, Validators } from '@angular/forms';
import { ConfirmPageService } from '../../services/confirm-page.service';
import { SelectOption } from '@maids/cc-lib/select-input';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { debounceTime, distinctUntilChanged, map, startWith, switchMap } from 'rxjs/operators';
import { CCDialog } from '@maids/cc-lib/dialog';
import { CCPreviewAttachmentComponent } from '@maids/cc-lib/preview-attachment';
import { environment } from 'src/environments/environment';
import { PaginationRequest } from '@maids/cc-lib/common';
import { Observable, of } from 'rxjs';
import { CCValidatorFn } from '@maids/cc-lib/validation';
import { BreadcrumbLink } from '@maids/cc-lib/layout';

@Component({
  selector: 'app-confirm-page-form',
  templateUrl: './confirm-page-form.component.html',
  styleUrls: ['./confirm-page-form.component.scss'],
})
export class ConfirmPageFormComponent implements OnInit {
  confirmForm!: FormGroup;
  id: string | null = null;
  ibanInfo: any = {};
  accountNameDropdownOptions: SelectOption[] = [
    { id: 'bank_info_account_name', text: 'Account Name' },
    { id: 'bank_info_iban', text: 'IBAN' },
    { id: 'bank_info_eid', text: 'EID' },
  ];
  enableAccountName: boolean = true;
  ibanDropdownOptions: SelectOption[] = [];
  eidDropdownOptions: SelectOption[] = [];
  bankNameDropdownOptions: SelectOption[] = [];
  ibanFile: any;
  eidFile: any;
  accountNameFile: any;
  fileUrl = [environment.apiBase, 'public', 'download'].join('/');
  EidSrc: any;
  IbanSrc: any;
  AccountNameSrc: any;
  clientStatus: string = '';
  clientStatusMsg: string = '';
  private imageTagUUIDMap: { [key: string]: () => string | undefined } = {
    bank_info_account_name: () => this.accountNameFile?.uuid,
    bank_info_iban: () => this.ibanFile?.uuid,
    bank_info_eid: () => this.eidFile?.uuid,
  };

  private imageTagDropdownModelMap: { [key: string]: string } = {
    bank_info_account_name: 'accountNameDropdownModel',
    bank_info_iban: 'ibanDropdownModel',
    bank_info_eid: 'eidDropdownModel',
  };

  private dropdownModel2ImageTagMap: { [key: string]: string } = {};
  mask = '-efgh-0000000-0';
  patterns = {
    e: { pattern: /(1|2)/ },
    f: { pattern: /(9|0)/ },
    g: { pattern: /[0-9]/ },
    h: { pattern: /[0-9]/ },
    '0000000': { pattern: /[0-9]/ },
    '0': { pattern: /[0-9]/ },
  };
  directDebitInfo: any;

  /**
   * Validator to ensure IBAN length is valid after stripping spaces.
   * Allows empty values (required handled by UI logic), but when provided,
   * validates that the space-insensitive length equals 23.
   */
  ibanValidator: CCValidatorFn = (control) => {
    const raw = (control?.value ?? '') as string;
    const sanitized = this.sanitizeIban(raw);
    const parent = control?.parent as FormGroup | null;
    const isRelaxed = !!(
      parent?.get('wrongPhotoAccountName')?.value ||
      parent?.get('wrongPhotoIban')?.value ||
      parent?.get('wrongPhotoEid')?.value
    );

    if (!sanitized) {
      return isRelaxed ? null : ({ required: true } as ValidationErrors);
    }
    return sanitized.length === 23 ? null : ({ invalidLength: 'Please enter valid IBAN' } as ValidationErrors);
  };

  constructor(
    private route: ActivatedRoute,
    private fb: FormBuilder,
    private confirmService: ConfirmPageService,
    private mediaService: MediaService,
    private _ccDialog: CCDialog,
    private router: Router,
    private cdr: ChangeDetectorRef,
    private ccNotification: CCNotificationService
  ) {}

  ngOnInit(): void {
    this.initForm();
    this.ibanDropdownOptions = this.accountNameDropdownOptions;
    this.eidDropdownOptions = this.accountNameDropdownOptions;
    this.dropdownModel2ImageTagMap = Object.entries(this.imageTagDropdownModelMap).reduce(
      (acc, [key, value]) => ({ ...acc, [value]: key }),
      {}
    );
    this.route.paramMap
      .pipe(
        switchMap((params: any) => {
          this.id = params.get('id');
          if (this.id) {
            this.getFormData(this.id);
          }
          return [];
        })
      )
      .subscribe();
    // Combined value change subscription for all dropdown model controls
    this.confirmForm
      .get('accountNameDropdownModel')
      ?.valueChanges.pipe(distinctUntilChanged())
      .subscribe((newValue) => {
        this.handleDropdownModelChanges('accountNameDropdownModel', newValue.id);
      });

    this.confirmForm
      .get('ibanDropdownModel')
      ?.valueChanges.pipe(distinctUntilChanged())
      .subscribe((newValue) => {
        this.handleDropdownModelChanges('ibanDropdownModel', newValue.id);
      });

    this.confirmForm
      .get('eidDropdownModel')
      ?.valueChanges.pipe(distinctUntilChanged())
      .subscribe((newValue) => {
        this.handleDropdownModelChanges('eidDropdownModel', newValue.id);
      });
    // Uppercase IBAN while preserving spaces
    this.confirmForm
      .get('IBAN')
      ?.valueChanges.pipe(distinctUntilChanged())
      .subscribe((value) => {
        if (value && typeof value === 'string') {
          const upperValue = value.toUpperCase();
          if (upperValue !== value) {
            this.confirmForm.get('IBAN')?.setValue(upperValue, { emitEvent: false });
          }
        }
      });
    this.confirmForm
      .get('IBAN')
      ?.valueChanges.pipe(debounceTime(1000), distinctUntilChanged())
      .subscribe((value) => {
        if (value) {
          this.ibanInfo = {};
          this.confirmForm.get('bankName')?.setValue('');
          this.confirmForm.get('bankNameSelect')?.setValue('');
          this.requestValidateClient();
        }
      });
    this.confirmForm
      .get('EID')
      ?.valueChanges.pipe(debounceTime(1000), distinctUntilChanged())
      .subscribe((value) => {
        if (value && this.confirmForm.get('EID')?.valid) {
          this.requestValidateClient();
        }
      });

    // Transform accountNameIbanSection to uppercase as user types
    this.confirmForm
      .get('accountNameIbanSection')
      ?.valueChanges.pipe(distinctUntilChanged())
      .subscribe((value) => {
        if (value && typeof value === 'string') {
          const upperValue = value.toUpperCase();
          if (upperValue !== value) {
            this.confirmForm.get('accountNameIbanSection')?.setValue(upperValue, { emitEvent: false });
          }
        }
      });
    this.confirmForm
      .get('wrongPhotoAccountName')
      ?.valueChanges.pipe(distinctUntilChanged())
      .subscribe((value) => {
        this.updateAccountNameRejectionReasonValidation(value);
        this.cdr.detectChanges();
      });
      this.confirmForm
      .get('wrongPhotoIban')
      ?.valueChanges.pipe(distinctUntilChanged())
      .subscribe((value) => {
        this.updateIbanRejectionReasonValidation(value);
        this.cdr.detectChanges();
      });
      this.confirmForm
      .get('wrongPhotoEid')
      ?.valueChanges.pipe(distinctUntilChanged())
      .subscribe((value) => {
        this.updateEidRejectionReasonValidation(value);
        this.cdr.detectChanges();
      });
    this.cdr.detectChanges();
  }
  handleDropdownModelChanges(dropdownModelName: string, newValue: string) {
    // Get the source tag name from the dropdown model
    const sourceTagName = this.dropdownModel2ImageTagMap[dropdownModelName];
    if (!sourceTagName) return;

    // Get the source UUID
    const sourceUUID = this.imageTagUUIDMap[sourceTagName]();
    if (!sourceUUID) return;

    // Get the target tag name from the new dropdown value
    const targetTagName = newValue;
    if (!targetTagName || targetTagName === sourceTagName) return;

    // Get the target UUID
    // Find which dropdown currently has the target tag value
    const targetDropdownModelName = this.imageTagDropdownModelMap[targetTagName];
    if (!targetDropdownModelName) return;
    // Get the current value of the target dropdown
    const currentTargetValue = this.confirmForm.get(targetDropdownModelName)?.value;
    if (!currentTargetValue) return;
    // Get the target UUID using the current target value
    const targetUUID = this.imageTagUUIDMap[currentTargetValue]();
    if (!targetUUID) return;

    // Perform the image switching
    return this.switchImages(sourceTagName, sourceUUID, targetTagName, targetUUID);
  }

  private switchImages(sourceTag: string, sourceUUID: string, destTag: string, destUUID: string) {
    if (!sourceTag || !destTag || !sourceUUID || !destUUID) {
      console.error('Parameters incomplete for image switching');
      return;
    }
    const data = {
      [sourceUUID]: destTag,
      [destUUID]: sourceTag,
    };

    this.confirmService.swapBankInfoAttachments(data).subscribe({
      next: () => {
        // Reload the page to reflect the changes
        location.reload();
      },
      error: (error) => {
        console.error('Error switching images:', error);
        this.ccNotification.notifyError('Failed to switch images');
      },
    });
  }
  getFormData(id: string | null) {
    this.confirmService.getFormData(id).subscribe((res) => {
      this.directDebitInfo = res;
      // Set IBAN if exists
      if (this.directDebitInfo.ibanNumber) {
        this.confirmForm.patchValue({
          IBAN: this.sanitizeIban(this.directDebitInfo.ibanNumber),
        });
      }
      // Set client details
      if (this.directDebitInfo.directDebit?.contractPaymentTerm?.contract?.client) {
        const client = this.directDebitInfo.directDebit.contractPaymentTerm.contract.client;
        this.confirmForm.patchValue({
          name: client.name,
        });
        // Set title if exists
        if (client.title) {
          this.confirmForm.patchValue({
            title: client.title.id,
          });
        }
        // Set nationality if exists
        if (client.nationality) {
          this.confirmForm.patchValue({
            nationality: client.nationality.id,
          });
        }
      }
      // Set bank details
      setTimeout(() => {
        this.confirmForm.patchValue({
          bankName: this.directDebitInfo.bankName,
        });
        if (this.directDebitInfo.bank) {
          this.confirmForm.patchValue({
            bankNameSelect: this.directDebitInfo.bank.id,
          });
          this.bankNameDropdownOptions = [
            {
              id: this.directDebitInfo.bank.id,
              text: this.directDebitInfo.bank.label,
            },
          ];
        }
      });

      // Set account details
      this.confirmForm.patchValue({
        accountName: this.directDebitInfo.accountName,
        accountNameIbanSection: this.directDebitInfo.accountName,
        EID: this.directDebitInfo.eid,
      });
      this.enableAccountName = res.enableAccountName;

      this.handleAttachments(res);
      if (!this.enableAccountName) {
        this.ibanDropdownOptions = this.ibanDropdownOptions.filter((option) => option.id !== 'bank_info_account_name');
        this.eidDropdownOptions = this.eidDropdownOptions.filter((option) => option.id !== 'bank_info_account_name');
      }
      this.requestValidateClient();
    });
  }

  private handleAttachments(directDebitInfo: any) {
    if (directDebitInfo && directDebitInfo.attachments && directDebitInfo.attachments.length > 0) {
      const attachments = directDebitInfo.attachments;
      const recognizedAttachments = attachments.filter((item: any) => item.tag !== 'pending_ocr');

      /** map from file tag to variable name for image source */
      const tag2VariableNameMap: { [key: string]: string } = {
        bank_info_iban: 'ibanFile',
        bank_info_eid: 'eidFile',
        bank_info_account_name: 'accountNameFile',
      };

      const fileName2DropdownModelMap: { [key: string]: string } = {
        eidFile: 'eidDropdownModel',
        ibanFile: 'ibanDropdownModel',
        accountNameFile: 'accountNameDropdownModel',
      };

      // Process recognized attachments
      recognizedAttachments.forEach((item: any) => {
        item.isPdf = item.fileType.toLowerCase() === 'pdf';
        const variableName = tag2VariableNameMap[item.tag];
        if (variableName) {
          (this as any)[variableName] = item;
        }
      });

      // Assign unrecognized attachments to remaining variables
      const unrecognizedAttachments = attachments.filter((item: any) => item.tag === 'pending_ocr');
      const unassignedVariables = Object.values(tag2VariableNameMap).filter((variable) => !(this as any)[variable]);

      let idx = 0;
      unrecognizedAttachments.forEach((item: any) => {
        if (idx >= unassignedVariables.length) return;

        const unassignedFileVar = unassignedVariables[idx];
        const unrecognizedDropdownModel = fileName2DropdownModelMap[unassignedFileVar];

        (this as any)[unassignedFileVar] = item;

        // For all unrecognized images, remove the document type from the dropdown menu
        if (unrecognizedDropdownModel && this.confirmForm) {
          this.confirmForm.get(unrecognizedDropdownModel)?.setValue('');
        }

        idx += 1;
      });

      // Set file sources for display
      if (this.eidFile) {
        this.EidSrc = this.fileUrl + '/' + this.eidFile?.uuid;
      }
      if (this.ibanFile) {
        this.IbanSrc = this.fileUrl + '/' + this.ibanFile?.uuid;
      }
      if (this.accountNameFile) {
        this.AccountNameSrc = this.fileUrl + '/' + this.accountNameFile?.uuid;
      }
    }
  }
  preview(uuid?: any) {
    return this.mediaService.getFile('public/download/' + uuid).subscribe((data: any) => {
      this.previewFile(data);
    });
  }
  previewFile(data: any) {
    let blob = new Blob([data], { type: data.type });
    const blobUrl = URL.createObjectURL(blob);
    let file_type = data.type.split('/')[1];
    this._ccDialog.originalOpen(CCPreviewAttachmentComponent, {
      width: '100%',
      data: {
        url: blobUrl,
        blob: blob,
        type: file_type,
      },
    });
  }
  requestValidateClient() {
    let params: any = {
      eid: this.confirmForm.get('EID')?.value || '',
      iban: this.sanitizeIban(this.confirmForm.get('IBAN')?.value || ''),
    };
    this.confirmService.validateForm(this.id, params).subscribe((res) => {
      this.clientStatus = res.status;
      this.clientStatusMsg = res.msg;
      switch (res.status) {
        case 'blocked':
          this.ccNotification.notifyError(res.msg);
          break;
        case 'fired':
          this._ccDialog.alert('Attention', res.msg);
          break;
      }
    });
  }
  initForm() {
    this.confirmForm = this.fb.group({
      accountNameDropdownModel: new FormControl('bank_info_account_name'),
      wrongPhotoAccountName: new FormControl(false),
      wrongPhotoIban: new FormControl(false),
      wrongPhotoEid: new FormControl(false),
      accountNameRejectionReasonId: new FormControl(),
      accountName: new FormControl(),
      ibanDropdownModel: new FormControl('bank_info_iban'),
      ibanRejectionReasonId: new FormControl(),
      IBAN: new FormControl('', [this.ibanValidator]),
      bankName: new FormControl(),
      bankNameSelect: new FormControl(''),
      accountNameIbanSection: new FormControl(),
      eidDropdownModel: new FormControl('bank_info_eid'),
      eidRejectionReasonId: new FormControl(),
      EID: new FormControl('784'),
      bankNameEidSection: new FormControl(),
      title: new FormControl(),
      name: new FormControl(),
      nationality: new FormControl(),
    });
  }
  readonly fetchAccountNameRejectionReason = (pageReq: PaginationRequest) => {
    const params: any = {
      search: pageReq.searchString || '',
    };
    return this.confirmService
      .getAccountNameRejectionReason(params)
      .pipe(map((val: any) => val.map((option: any) => ({ id: option.id, text: option.label } as SelectOption))));
  };
  readonly fetchIbanRejectionReason = (pageReq: PaginationRequest) => {
    const params: any = {
      search: pageReq.searchString || '',
    };
    return this.confirmService
      .getIbanRejectionReason(params)
      .pipe(map((val: any) => val.map((option: any) => ({ id: option.id, text: option.label } as SelectOption))));
  };
  readonly fetchEidRejectionReason = (pageReq: PaginationRequest) => {
    const params: any = {
      search: pageReq.searchString || '',
    };
    return this.confirmService
      .getEidRejectionReason(params)
      .pipe(map((val: any) => val.map((option: any) => ({ id: option.id, text: option.label } as SelectOption))));
  };
  readonly fetchBankName = (pageReq: PaginationRequest) => {
    const params: any = {
      search: pageReq.searchString || '',
      page: 0,
      size: 100,
    };
    return this.confirmService
      .getBankName(params)
      .pipe(map((val: any) => val.map((option: any) => ({ id: option.id, text: option.label } as SelectOption))));
  };
  readonly fetchTitle = (pageReq: PaginationRequest) => {
    const params: any = {
      search: pageReq.searchString || '',
    };
    return this.confirmService
      .getTitle(params)
      .pipe(map((val: any) => val.map((option: any) => ({ id: option.id, text: option.label } as SelectOption))));
  };

  readonly fetchNationality = (pageReq: PaginationRequest) => {
    const params: any = {
      search: pageReq.searchString || '',
      page: 0,
      size: 100,
    };
    return this.confirmService
      .getNationality(params)
      .pipe(map((val: any) => val.map((option: any) => ({ id: option.id, text: option.label } as SelectOption))));
  };
  validateIban() {
    this.confirmService.validateIban(this.sanitizeIban(this.confirmForm.get('IBAN')?.value)).subscribe((res) => {
      this.ibanInfo = res;

      // Set bank name from bank_data
      if (this.ibanInfo && this.ibanInfo?.bank_data) {
        this.confirmForm.patchValue({
          bankName: this.ibanInfo.bank_data.bank,
        });
      }
      // Handle picklist item info for bank name select
      if (this.ibanInfo && this.ibanInfo?.picklistItemInfo) {
        this.confirmForm.patchValue({
          bankNameSelect: this.ibanInfo.picklistItemInfo.id,
        });
        // Update bank name dropdown options with the picklist item
        this.bankNameDropdownOptions = [
          {
            id: this.ibanInfo.picklistItemInfo.id,
            text: this.ibanInfo.picklistItemInfo.name,
          },
        ];
      } else {
        this.confirmForm.patchValue({
          bankNameSelect: '',
        });
      }
      // Handle validation status
      if (this.ibanInfo && this.ibanInfo?.validations && this.ibanInfo?.validations?.iban?.code !== '001') {
        this.ibanInfo.error = this.ibanInfo.validations.iban.message;
        this.ibanInfo.success = false;
      } else {
        this.ibanInfo.success = true;
      }
      console.log(this.confirmForm.get('bankNameSelect')?.value);
      
    });
  }

  saveChanges() {
    const maskedEid = this.formatEidWithDashes(this.confirmForm.get('EID')?.value || '');
    const invalidControls = Object.keys(this.confirmForm.controls)
      .filter((key) => this.confirmForm.get(key)?.invalid)
      .map((key) => ({
        field: key,
        errors: this.confirmForm.get(key)?.errors,
      }));

    console.warn('Form validation errors:', invalidControls);
    const wrongPhotos: string[] = [];
    const wrongPhotoControls = {
      bank_info_account_name: this.confirmForm.get('wrongPhotoAccountName')?.value,
      bank_info_iban: this.confirmForm.get('wrongPhotoIban')?.value,
      bank_info_eid: this.confirmForm.get('wrongPhotoEid')?.value,
    };

    for (const wrongPhotoKey in wrongPhotoControls) {
      if (wrongPhotoControls[wrongPhotoKey as keyof typeof wrongPhotoControls]) {
        wrongPhotos.push(wrongPhotoKey);
      }
    }

    // Check if any files are marked as wrong
    if (
      (this.accountNameFile && wrongPhotoControls[this.accountNameFile.tag as keyof typeof wrongPhotoControls]) ||
      (this.ibanFile && wrongPhotoControls[this.ibanFile.tag as keyof typeof wrongPhotoControls]) ||
      (this.eidFile && wrongPhotoControls[this.eidFile.tag as keyof typeof wrongPhotoControls])
    ) {
      const tags = wrongPhotos.join(',');
      this.removeWrongPhotos(tags);
      return;
    }

    if (this.confirmForm.invalid) {
      this.confirmForm.markAllAsTouched();
      this.ccNotification.notifyError('Please fill all the required fields');

      return;
    }
    let params = {
      eid: maskedEid,
      iban: this.sanitizeIban(this.confirmForm.get('IBAN')?.value),
      bankName: this.confirmForm.get('bankName')?.value,
      bank: this.confirmForm.get('bankNameSelect')?.value,
      clientName: this.confirmForm.get('name')?.value,
      clientTitleId: this.confirmForm.get('title')?.value || '',
      nationalityId: this.confirmForm.get('nationality')?.value || '',
      accountName: this.enableAccountName
        ? this.confirmForm.get('accountName')?.value
        : this.confirmForm.get('accountNameIbanSection')?.value?.toUpperCase(),
    };

    this.confirmService.saveChanges(this.id, params).subscribe((res) => {
      if (res) {
        this.clientStatus = res.status;
        this.clientStatusMsg = res.msg;
        if (this.clientStatus === 'blocked') {
          this.ccNotification.notifyError(this.clientStatusMsg);
        } else if (this.clientStatus === 'fired') {
          this._ccDialog.alert('Attention', this.clientStatusMsg);
        } else {
          this.ccNotification.notifySuccess('The Direct Debit Payments Updated successfully');
          this.goToReturnPage();
        }
      } else {
        this.ccNotification.notifySuccess('The Direct Debit Payments Updated successfully');
        this.goToReturnPage();
      }
    });
  }
  removeWrongPhotos(tag: any) {
    if (this.confirmForm.invalid) {
      this.cdr.detectChanges();
      this.confirmForm.markAllAsTouched();
      this.ccNotification.notifyError('Please fill all the required fields');
      return;
    }
    let params: any = {
      photoTags: tag,
      ibanRejectionReasonId: this.confirmForm.get('ibanRejectionReasonId')?.value || '',
      accountNameRejectionReasonId: this.confirmForm.get('accountNameRejectionReasonId')?.value || '',
      eidRejectionReasonId: this.confirmForm.get('eidRejectionReasonId')?.value || '',
    };
    this.confirmService.removeWrongPhotos(this.id, params).subscribe((res) => {
      this.ccNotification.notifySuccess('Done Successfully');
      location.reload();
    });
  }
  goToReturnPage() {
    this.router.navigateByUrl('/accounting/v2/payments-automation/dd-data-entry');
  }
  maskValidator: CCValidatorFn = (control) => {
    if (!control.value || control.value === '784') {
      return null;
    }
    var mask = '784' + this.mask.replace(/-/g, '');

    var value = control.value.replace(/-/g, '');

    if (!value.startsWith('784')) {
      value = '784' + value;
    }

    if (value.length !== mask.length) {
      this.confirmForm.get('EID')?.markAsTouched();
      return { invalidLength: 'Please enter valid EID' };
    }
    return null;
  };
  private formatEidWithDashes(raw: string): string {
    if (!raw) {
      return raw;
    }
    // Remove existing dashes/spaces
    const digits = raw.replace(/[^0-9]/g, '');
    // Ensure prefix 784
    let normalized = digits.startsWith('784') ? digits : `784${digits}`;
    // Target lengths: 3 + 4 + 7 + 1 = 15
    if (normalized.length < 15) {
      return raw; // do not alter if incomplete; keep what user typed
    }
    // Slice into 784-XXXX-XXXXXXX-X
    const p1 = normalized.slice(0, 3);
    const p2 = normalized.slice(3, 7);
    const p3 = normalized.slice(7, 14);
    const p4 = normalized.slice(14, 15);
    return `${p1}-${p2}-${p3}-${p4}`;
  }
  private sanitizeIban(iban: string): string {
    return (iban || '').replace(/\s+/g, '').toUpperCase();
  }
  getSaveChangesBtnDisabled() {
    const isDisabledConfirm =
      (!this.confirmForm.get('ibanDropdownModel')?.value || !this.confirmForm.get('eidDropdownModel')?.value) &&
      this.eidFile?.tag === 'pending_ocr' &&
      this.ibanFile?.tag === 'pending_ocr';
    return this.clientStatus !== 'active' || isDisabledConfirm;
  }
  getWrongPhotoControlsValidators() {
    const wrongPhotoAccountName = this.confirmForm.get('wrongPhotoAccountName')?.value;
    const wrongPhotoIban = this.confirmForm.get('wrongPhotoIban')?.value;
    const wrongPhotoEid = this.confirmForm.get('wrongPhotoEid')?.value;

    // Set required validators based on wrong photo selections
    if (wrongPhotoAccountName || wrongPhotoIban || wrongPhotoEid) {
      return true;
    }
    return false;
  }

  private updateIbanRejectionReasonValidation(isRequired: boolean) {
    const ibanRejectionReasonControl = this.confirmForm.get('ibanRejectionReasonId');
    if (ibanRejectionReasonControl) {
      if (isRequired) {
        ibanRejectionReasonControl.setValidators([Validators.required]);
      } else {
        ibanRejectionReasonControl.clearValidators();
        ibanRejectionReasonControl.setValue(null);
      }
      ibanRejectionReasonControl.updateValueAndValidity();
    }
  }

  private updateAccountNameRejectionReasonValidation(isRequired: boolean) {
    const accountNameRejectionReasonControl = this.confirmForm.get('accountNameRejectionReasonId');
    if (accountNameRejectionReasonControl) {
      if (isRequired) {
        accountNameRejectionReasonControl.setValidators([Validators.required]);
      } else {
        accountNameRejectionReasonControl.clearValidators();
        accountNameRejectionReasonControl.setValue(null);
      }
      accountNameRejectionReasonControl.updateValueAndValidity();
    }
  }

  private updateEidRejectionReasonValidation(isRequired: boolean) {
    const eidRejectionReasonControl = this.confirmForm.get('eidRejectionReasonId');
    if (eidRejectionReasonControl) {
      if (isRequired) {
        eidRejectionReasonControl.setValidators([Validators.required]);
      } else {
        eidRejectionReasonControl.clearValidators();
        eidRejectionReasonControl.setValue(null);
      }
      eidRejectionReasonControl.updateValueAndValidity();
    }
  }

}
