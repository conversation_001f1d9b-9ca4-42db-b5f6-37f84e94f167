<div class="container-fluid p-5">
  <form [formGroup]="messageForm">
    <div class="row">
      <cc-select
        class="col-md-12"
        label="Event"
        formControlName="selectedEvent"
        [data]="eventOptions"
        placeholder="Select Event"></cc-select>
    </div>
    <div
      class="row"
      *ngIf="messageForm.get('selectedEvent')?.value == 'DirectDebitRejected' && messageForm.get('selectedEvent')?.value != 'Termination'">
      <span class="col-md-2 pl-3">Direct Debit Type:</span>
      <div class="col-md-10 d-flex gap-3">
        <cc-checkbox formControlName="DDTypeA">A</cc-checkbox>
        <cc-checkbox formControlName="DDTypeB">B</cc-checkbox>
      </div>
    </div>
    <div class="row">
      <span class="col-md-2 pl-3">Contract Type:</span>
      <div class="col-md-10 d-flex gap-3">
        <cc-checkbox formControlName="Maidvisa">Maidvisa</cc-checkbox>
        <cc-checkbox formControlName="Maidscc">Maids.cc</cc-checkbox>
      </div>
    </div>
    <div class="row" *ngIf="messageForm.get('selectedEvent')?.value != 'Termination'">
      <cc-select
        class="col-md-12"
        label="Sub Event"
        formControlName="selectedAfterCashSubtype"
        [data]="afterCashSubtypeOptions"
        placeholder="Select Reason"></cc-select>
    </div>
    <div
      class="row"
      *ngIf="
        (messageForm.get('selectedEvent')?.value == 'DirectDebitRejected' ||
          (messageForm.get('selectedEvent')?.value === 'ClientsPayingViaCreditCard' &&
            messageForm.get('selectedAfterCashSubtype')?.value === 'DD_Rejection')) &&
        messageForm.get('selectedEvent')?.value != 'Termination'
      ">
      <cc-select
        class="col-md-12"
        label="DD Rejection Reason"
        formControlName="selectedDDRejectionReason"
        placeholder="Select Reason"
        [data]="ddRejectionReasonOptions">
      </cc-select>
    </div>
    <div
      class="row"
      *ngIf="
        messageForm.get('selectedEvent')?.value == 'IncompleteDDRejectedByDataEntry' &&
        messageForm.get('selectedEvent')?.value != 'Termination'
      ">
      <cc-select
        class="col-md-12"
        label="Accountant Rejection Reason"
        formControlName="selectedAccountantRejectionReason"
        placeholder="Select Reason"
        [data]="accountantRejectionReasonOptions">
      </cc-select>
    </div>
    <div
      class="row"
      *ngIf="messageForm.get('selectedEvent')?.value == 'BouncedPayment' && messageForm.get('selectedEvent')?.value != 'Termination'">
      <cc-select
        class="col-md-12"
        label="Bounced payment status"
        formControlName="selectedBouncedPaymentStatus"
        placeholder="Select Event"
        [data]="bouncedPaymentStatusOptions">
      </cc-select>
    </div>
    <div
      class="row"
      *ngIf="
        messageForm.get('selectedEvent')?.value == 'OnlineCreditCardPaymentReminders' ||
        (messageForm.get('selectedEvent')?.value == 'ClientsPayingViaCreditCard' &&
          (messageForm.get('selectedAfterCashSubtype')?.value === 'INITIAL_FLOW_FOR_DDA' ||
            messageForm.get('selectedAfterCashSubtype')?.value === 'INITIAL_FLOW_FOR_DDB'))
      ">
      <cc-select class="col-md-12" label="Payment structure" formControlName="selectedPaymentStructure" [data]="paymentStructureOptions">
      </cc-select>
    </div>
    <div class="row pt-2 pb-2" *ngIf="showTrialsForEvent() && messageForm.get('selectedEvent')?.value != 'Termination'">
      <p class="col-md-2">Trials:</p>
      <div class="col-md-1" *ngFor="let trial of trials; let i = index">
        <cc-checkbox [formControl]="getTrialFormControl(i)">
          {{ messageForm.get('selectedEvent')?.value == 'ClientPaidCashAndNoSignatureProvided' ? i + 1 : i }}
        </cc-checkbox>
      </div>
    </div>
    <!-- Reminders Section -->
    <div class="row pt-2 pb-2" *ngIf="showRemindersForEvent() && messageForm.controls['selectedEvent'].value != 'Termination'">
      <label class="col-md-2">Reminders:</label>
      <div class="col-md-1" *ngFor="let reminder of reminders; let i = index">
        <cc-checkbox [formControl]="getReminderFormControl(i)">
          {{ messageForm.controls['selectedEvent'].value == 'ClientPaidCashAndNoSignatureProvided' ? i + 1 : i }}
        </cc-checkbox>
      </div>
    </div>
    <!-- After (days) Radio Section -->
    <div class="row" *ngIf="messageForm.get('selectedEvent')?.value == 'ClientPaidCashAndNoSignatureProvided'">
      <p class="col-md-2">After (days):</p>
      <cc-radio-group class="col-md-8 d-flex gap-3" formControlName="whenToStartSending" color="primary">
        <cc-radio-button value="DIRECTLY_ON_CONTACT_CREATION"> Directly On Contact Creation </cc-radio-button>
        <cc-radio-button value="FIRST_OF_NEXT_MONTH_OF_CONTACT_CREATION"> 1st of next month of contract creation </cc-radio-button>
      </cc-radio-group>
    </div>
    <!-- After (days) Number Input -->
    <div
      class="row"
      *ngIf="
        messageForm.get('selectedEvent')?.value === 'ClientPaidCashAndNoSignatureProvided' &&
        messageForm.get('whenToStartSending')?.value === 'FIRST_OF_NEXT_MONTH_OF_CONTACT_CREATION'
      ">
      <cc-input class="col-md-12" label="After (days):" type="number" formControlName="afterDays" [disabled]="true"></cc-input>
    </div>
    <!-- SMS sent at Time Picker -->
    <div class="row">
      <cc-timepicker class="col-md-12" label="SMS sent at" formControlName="sentAt"></cc-timepicker>
    </div>
    <!-- Scheduled Termination Date Section -->
    <div class="row pt-2 pb-2">
      <p class="col-md-2">Scheduled termination date:</p>
      <cc-radio-group class="col-md-8 d-flex gap-3" formControlName="scheduledTerminationDate" color="primary">
        <cc-radio-button value="None">None</cc-radio-button>
        <cc-radio-button value="GToday">&gt; Today</cc-radio-button>
        <cc-radio-button value="EToday">= Today</cc-radio-button>
      </cc-radio-group>
    </div>
    <div
      class="row pt-2"
      *ngIf="
        messageForm.get('selectedEvent')?.value === 'ClientPaidCashAndNoSignatureProvided' &&
        messageForm.get('whenToStartSending')?.value === 'FIRST_OF_NEXT_MONTH_OF_CONTACT_CREATION'
      ">
      <cc-checkbox formControlName="sendPayTabMessage">Send Paytab Link</cc-checkbox>
    </div>
    <!-- SMS Priority Section -->
    <div class="row pt-2 pb-2">
      <p class="col-md-2">SMS priority:</p>
      <div class="col-md-10">
        <div>{{ clientTemplate?.priority }}</div>
      </div>
    </div>
    <!-- Default Message Accordion -->
    <cc-accordion class="pt-2">
      <cc-panel [expanded]="true">
        <cc-panel-title>Default Message</cc-panel-title>
        <cc-panel-body>
          <!-- Family Message Section -->
          <div class="row">
            <p class="col-md-2 pl-3">Family Message:</p>
            <div class="col-md-8 d-flex gap-3">
              <cc-checkbox formControlName="sendToClient">Send to Family</cc-checkbox>
              <cc-checkbox formControlName="sendToSpouse">Send to Family Spouse</cc-checkbox>
              <a
                class="cc-secondary mt-1"
                *ngIf="id && clientTemplateId"
                [routerLink]="['/admin/templates/newUpdate', clientTemplateId]"
                target="_blank"
                >Manage Interaction</a
              >
            </div>
          </div>

          <div class="row" *ngIf="!editMode">
            <div class="col-md-6">
              <cc-textarea
                rows="3"
                formControlName="clientMessage"
                [disabled]="!messageForm.controls['sendToClient'].value || !messageForm.controls['sendToSpouse'].value"></cc-textarea>
            </div>
            <div class="col-md-4">
              <cc-select
                label="Select Parameters"
                placeholder="Select Parameters"
                [multiple]="true"
                [data]="variablesOptions"
                formControlName="selectedNotificationClientVariables"
                [disabled]="!messageForm.controls['sendToClient'].value || !messageForm.controls['sendToSpouse'].value"></cc-select>
            </div>
          </div>
          <div class="row" *ngIf="editMode">
            <div class="col-md-2"></div>
            <div class="col-md-5">
              <cc-textarea
                readonly
                rows="3"
                label="SMS Content"
                formControlName="smsText"
                [disabled]="!messageForm.controls['sendToClient'].value || !messageForm.controls['sendToSpouse'].value"></cc-textarea>
            </div>
            <div class="col-md-4">
              <cc-select
                label="Select Parameters"
                placeholder="Select Parameters"
                [multiple]="true"
                [data]="variablesOptions"
                formControlName="selectedSmsClientVariables"
                [disabled]="!messageForm.controls['sendToClient'].value || !messageForm.controls['sendToSpouse'].value"></cc-select>
            </div>
            <div class="col-md-2"></div>
            <div class="col-md-5">
              <cc-textarea
                readonly
                rows="3"
                label="Notification Content"
                formControlName="clientMessage"
                [disabled]="!messageForm.controls['sendToClient'].value || !messageForm.controls['sendToSpouse'].value"></cc-textarea>
            </div>
            <div class="col-md-4">
              <cc-select
                [multiple]="true"
                [data]="variablesOptions"
                formControlName="selectedNotificationClientVariables"
                [disabled]="!messageForm.controls['sendToClient'].value || !messageForm.controls['sendToSpouse'].value"
                label="Select Parameters"
                placeholder="Select Parameters"></cc-select>
            </div>
            <div class="col-md-2"></div>

            <div class="col-md-9">
              <cc-checkbox formControlName="sendAsEmail">Send as Email</cc-checkbox>
            </div>
            <div class="col-md-2"></div>

            <div class="col-md-9">
              <cc-input label="Email Subject" formControlName="emailSubject" [disabled]="!messageForm.get('sendAsEmail')?.value"></cc-input>
            </div>
          </div>
          <div class="row">
            <p class="col-md-2 pl-3">Maid Message:</p>
            <div class="col-md-8 d-flex gap-3">
              <cc-checkbox formControlName="sendToMaid">Send to Maids</cc-checkbox>
              <a
                class="cc-secondary mt-1"
                *ngIf="id && maidTemplateId"
                [routerLink]="['/admin/templates/newUpdate', maidTemplateId]"
                target="_blank"
                >Manage Interaction</a
              >
            </div>
          </div>
          <div class="row">
            <div class="col-md-2"></div>

            <div class="col-md-5">
              <cc-textarea
                rows="3"
                style="width: 100%"
                formControlName="maidMessage"
                [disabled]="!messageForm.get('sendToMaid')?.value||(editMode && maidTemplateId)"></cc-textarea>
            </div>
            <div class="col-md-4">
              <cc-select
                [multiple]="true"
                [data]="variablesOptions"
                formControlName="selectedMaidVariables"
                [disabled]="!messageForm.get('sendToMaid')?.value"
                label="Select Parameters"
                placeholder="Select Parameters"></cc-select>
            </div>
          </div>
          <div class="row" *ngIf="!id || messageForm.get('scheduledTerminationDate')?.value !== 'None'">
            <div class="col-md-2"></div>

            <div class="col-md-10 d-flex gap-3">
              <cc-checkbox formControlName="sendToMaidWhenRetractCancellation">Send to Maids when retract cancellation</cc-checkbox>
              <a
                class="cc-secondary mt-1"
                *ngIf="id && maidWhenRetractCancellationTemplate"
                [routerLink]="['/admin/templates/newUpdate', maidWhenRetractCancellationTemplate]"
                target="_blank"
                >Manage Interaction</a
              >
            </div>
            <div class="col-md-2"></div>

            <div class="col-md-5">
              <cc-textarea
                rows="3"
                formControlName="maidWhenRetractCancellationMessage"
                [disabled]="!messageForm.controls['sendToMaidWhenRetractCancellation'].value ||(editMode && maidWhenRetractCancellationTemplate)"></cc-textarea>
            </div>
            <div class="col-md-4">
              <cc-select
                [multiple]="true"
                [data]="variablesOptions"
                formControlName="selectedMaidwhenRetractCancellationVariables"
                [disabled]="!messageForm.controls['sendToMaidWhenRetractCancellation'].value"></cc-select>
            </div>
          </div>
          <div class="d-flex justify-content-end p-3" *ngIf="id">
            <button cc-raised-button color="accent" class="" (click)="addNewBankUniqueMessage()">Add new bank unique message</button>
          </div>
        </cc-panel-body>
      </cc-panel>
    </cc-accordion>

    <!-- Unique Bank Message -->
    <cc-accordion class="pt-2" *ngFor="let form of uniqueMessage.formList; let i = index">
      <cc-panel [expanded]="true">
        <cc-panel-title>Unique Bank Message {{ i + 1 }}</cc-panel-title>
        <cc-panel-body>
          <!-- Bank Selection Section -->
          <div class="row">
            <div class="col-md-2"></div>
            <div class="col-md-9">
              <cc-select
                [data]="bankNameOptions"
                [(ngModel)]="form.banks"
                [ngModelOptions]="{ standalone: true }"
                placeholder="Select Bank"
                [multiple]="true"
                label="Select Banks"></cc-select>
            </div>
          </div>

          <!-- Family Message Section -->
          <div class="row">
            <p class="col-md-2 pl-3">Family Message:</p>
            <div class="col-md-8 d-flex gap-3">
              <cc-checkbox id="checkbox-sendToClient-{{ i }}" [(ngModel)]="form.sendToClient" [ngModelOptions]="{ standalone: true }"
                >Send to Family</cc-checkbox
              >
              <cc-checkbox id="checkbox-sendToSpouse-{{ i }}" [(ngModel)]="form.sendToSpouse" [ngModelOptions]="{ standalone: true }"
                >Send to Family Spouse</cc-checkbox
              >
              <a
                class="cc-secondary mt-1"
                *ngIf="id && form.clientTemplate?.id"
                [routerLink]="['/admin/templates/newUpdate', form.clientTemplate.id]"
                target="_blank"
                >Manage Interaction</a
              >
            </div>
          </div>

          <!-- SMS and Notification Content for Edit Mode -->
          <div *ngIf="editMode">
            <div class="row">
              <div class="col-md-2"></div>
              <div class="col-md-5">
                <cc-textarea
                  [(ngModel)]="form.clientSmsMessage"
                  rows="3"
                  label="SMS Content"
                  [disabled]="!form.sendToClient || !form.sendToSpouse || (form.id != '' && id && form.clientTemplate)"
                  [ngModelOptions]="{ standalone: true }"></cc-textarea>
              </div>
              <div class="col-md-4">
                <cc-select
                  [data]="variablesOptions"
                  [(ngModel)]="form.selectedSmsClientVariables"
                  [ngModelOptions]="{ standalone: true }"
                  (ngModelChange)="onSmsClientVariablesChange($event, form)"
                  label="Select Parameters"
                  placeholder="Select Parameters"
                  [multiple]="true"
                  [disabled]="!form.sendToClient || !form.sendToSpouse || (form.id != '' && id && form.clientTemplate)"></cc-select>
              </div>
            </div>
            <div class="row">
              <div class="col-md-2"></div>
              <div class="col-md-5">
                <cc-textarea
                  [(ngModel)]="form.clientMessage"
                  rows="3"
                  label="Notification Content"
                  [disabled]="!form.sendToClient || !form.sendToSpouse || (form.id != '' && id && form.clientTemplate)"
                  [ngModelOptions]="{ standalone: true }"></cc-textarea>
              </div>
              <div class="col-md-4">
                <cc-select
                  [data]="variablesOptions"
                  [(ngModel)]="form.selectedNotificationClientVariables"
                  [ngModelOptions]="{ standalone: true }"
                  (ngModelChange)="onNotificationClientVariablesChange($event, form)"
                  label="Select Parameters"
                  placeholder="Select Parameters"
                  [multiple]="true"
                  [disabled]="!form.sendToClient || !form.sendToSpouse || (form.id != '' && id && form.clientTemplate)"></cc-select>
              </div>
            </div>
            <div class="row">
              <div class="col-md-2"></div>
              <div class="col-md-9">
                <cc-checkbox id="checkbox-sendAsEmail-{{ i }}" [(ngModel)]="form.sendAsEmail" [ngModelOptions]="{ standalone: true }"
                  >Send as Email</cc-checkbox
                >
              </div>
            </div>
            <div class="row">
              <div class="col-md-2"></div>
              <div class="col-md-9">
                <cc-input
                  [ngModelOptions]="{ standalone: true }"
                  [(ngModel)]="form.emailSubject"
                  [disabled]="!form.sendAsEmail"
                  label="Email Subject"></cc-input>
              </div>
            </div>
          </div>

          <!-- Maid Message Section -->
          <div class="row">
            <p class="col-md-2 pl-3">Maid Message:</p>
            <div class="col-md-8 d-flex gap-3">
              <cc-checkbox id="checkbox-sendToMaid-{{ i }}" [(ngModel)]="form.sendToMaid" [ngModelOptions]="{ standalone: true }"
                >Send to Maids</cc-checkbox
              >
              <a
                class="cc-secondary mt-1"
                *ngIf="id && form.maidTemplate?.id"
                [routerLink]="['/admin/templates/newUpdate', form.maidTemplate.id]"
                target="_blank"
                >Manage Interaction</a
              >
            </div>
          </div>
          <div class="row">
            <div class="col-md-2"></div>
            <div class="col-md-5">
              <cc-textarea
                [(ngModel)]="form.maidMessage"
                rows="3"
                [disabled]="!form.sendToMaid || (form.id != '' && id && form.maidTemplate)"
                [ngModelOptions]="{ standalone: true }"></cc-textarea>
            </div>
            <div class="col-md-4">
              <cc-select
                [data]="variablesOptions"
                [(ngModel)]="form.selectedMaidVariables"
                [ngModelOptions]="{ standalone: true }"
                label="Select Parameters"
                placeholder="Select Parameters"
                [multiple]="true"
                (ngModelChange)="onMaidVariablesChange($event, form)"
                [disabled]="!form.sendToMaid || (form.id != '' && id && form.maidTemplate)"></cc-select>
            </div>
          </div>

          <!-- Maid When Retract Cancellation Section -->
          <div class="row" *ngIf="!id || messageForm.controls['scheduledTerminationDate'].value !== 'None'">
            <div class="col-md-2"></div>

            <div class="col-md-10 d-flex gap-3">
              <cc-checkbox
                id="checkbox-sendToMaidWhenRetractCancellation-{{ i }}"
                [(ngModel)]="form.sendToMaidWhenRetractCancellation"
                [ngModelOptions]="{ standalone: true }"
                >Send to Maids when retract cancellation</cc-checkbox
              >
              <a
                class="cc-secondary mt-1"
                *ngIf="id && form.maidWhenRetractCancellationTemplate?.id"
                [routerLink]="['/admin/templates/newUpdate', form.maidWhenRetractCancellationTemplate.id]"
                target="_blank"
                >Manage Interaction</a
              >
            </div>
            <div class="col-md-2"></div>
            <div class="col-md-5">
              <cc-textarea
                [(ngModel)]="form.maidWhenRetractCancellationMessage"
                rows="3"
                [disabled]="!form.sendToMaidWhenRetractCancellation || (form.id != '' && id && form.maidWhenRetractCancellationTemplate)"
                [ngModelOptions]="{ standalone: true }"></cc-textarea>
            </div>
            <div class="col-md-4">
              <cc-select
                [data]="variablesOptions"
                [(ngModel)]="form.selectedMaidwhenRetractCancellationVariables"
                [ngModelOptions]="{ standalone: true }"
                label="Select Parameters"
                placeholder="Select Parameters"
                [multiple]="true"
                (ngModelChange)="onMaidWhenRetractCancellationVariablesChange($event, form)"
                [disabled]="
                  !form.sendToMaidWhenRetractCancellation || (form.id != '' && id && form.maidWhenRetractCancellationTemplate)
                "></cc-select>
            </div>
          </div>

          <!-- Delete Button -->
          <div class="d-flex justify-content-end p-3">
            <button cc-raised-button color="warn" (click)="deleteUniqueBankMessage(form)">Delete</button>
          </div>
        </cc-panel-body>
      </cc-panel>
    </cc-accordion>
    <div class="row p-3">
      <p class="pt-1 col-md-2">Create todo?</p>

      <div class="col-md-7">
        <cc-radio-group class="d-flex gap-3" formControlName="createtodoType">
          <cc-radio-button value="noCreateTodo"> Don't create todo </cc-radio-button>
          <cc-radio-button value="createClientToDo"> Clients todo </cc-radio-button>
        </cc-radio-group>
      </div>
    </div>
    <div class="row p-3">
      <div class="col-md-9">
        <cc-checkbox formControlName="alertForVip">Alert For VIP</cc-checkbox>
      </div>
    </div>
    <div class="d-flex justify-content-end gap-3 p-3">
      <button cc-raised-button (click)="goToReturnPage()">Cancel</button>&nbsp;
      <button cc-raised-button type="submit" color="primary" (click)="save()">Save</button>
    </div>
  </form>
</div>
