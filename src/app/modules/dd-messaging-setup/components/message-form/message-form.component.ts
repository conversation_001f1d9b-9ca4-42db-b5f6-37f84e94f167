import { DdMessagingSetupService } from './../../services/dd-messaging-setup.service';
import { Component, OnInit, OnDestroy, ChangeDetectorRef } from '@angular/core';
import { FormBuilder, FormGroup, FormArray, FormControl } from '@angular/forms';
import { SelectOption } from '@maids/cc-lib/select-input';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject, takeUntil, combineLatest, BehaviorSubject } from 'rxjs';
import { CCDialog } from '@maids/cc-lib/dialog';
import { CCNotificationService } from '@maids/cc-lib/services';
import { API } from 'src/environments/api';

@Component({
  selector: 'app-message-form',
  templateUrl: './message-form.component.html',
  styleUrls: ['./message-form.component.scss'],
})
export class MessageFormComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  eventOptions: SelectOption[] = [];
  afterCashSubtypeOptions: SelectOption[] = [];
  bouncedPaymentStatusOptions: SelectOption[] = [];
  id?: any;

  editMode = false;
  showUnique = false;

  // Template-related properties
  clientTemplateId: any = null;
  clientTemplate: any = null;
  maidTemplateId: any = null;
  maidWhenRetractCancellationTemplate: any = null;
  bouncedPaymentStatus: any = null;

  ddRejectionReasonOptions: SelectOption[] = [
    { id: 'Signature', text: 'Signature' },
    { id: 'Account', text: 'Account' },
    { id: 'EID', text: 'EID' },
    { id: 'Compliance', text: 'Compliance' },
    // {id: 'ComplianceORAccount', text: "ComplianceORAccount"},
    { id: 'Authorization', text: 'Authorization' },
    { id: 'Invalid_Account', text: 'Invalid account' },
    { id: 'Other', text: 'Other' },
  ];

  accountantRejectionReasonOptions: SelectOption[] = [
    { id: 'WrongEID', text: 'Wrong EID' },
    { id: 'WrongIBAN', text: 'Wrong IBAN' },
    { id: 'WrongAccountName', text: 'Wrong account name' },
    { id: 'WrongEIDAndIBAN', text: 'Wrong EID and IBAN' },
    { id: 'WrongEIDAndAccountName', text: 'Wrong EID and account name' },
    { id: 'WrongIBANAndAccountName', text: 'Wrong IBAN and account name' },
    { id: 'AllDataIncorrect', text: 'All data incorrect' },
  ];

  // Arrays for trials and reminders
  trials: boolean[] = Array(10).fill(false);
  reminders: boolean[] = Array(10).fill(false);

  relatedMessageId = '';

  // Events that should show trials
  showTrialsWhenEventValue: string[] = [
    'DirectDebitRejected',
    'BouncedPayment',
    'IncompleteDDRejectedByDataEntry',
    'IncompleteDDClientHasNoApprovedSignature',
    'ClientPaidCashAndNoSignatureProvided',
    'ClientsPayingViaCreditCard',
  ];

  showRemindersWhenEventValue = [
    'DirectDebitRejected',
    'BouncedPayment',
    'IncompleteDDRejectedByDataEntry',
    'IncompleteDDClientHasNoApprovedSignature',
    'OnlineCreditCardPaymentReminders',
  ];

  variablesOptions: SelectOption[] = [
    { id: '@client_first_name@', text: '@client_first_name@' },
    { id: '@maid_name@', text: '@maid_name@' },
    { id: '@Account_holder_name@', text: '@Account_holder_name@' },
    { id: '@adjusted_end_date@', text: '@adjusted_end_date@' },
    { id: '@adjusted_end_date - 1 day@', text: '@adjusted_end_date - 1 day@' },
    { id: '@link_send_dd_details@', text: '@link_send_dd_details@' },
    { id: '@center_location@', text: '@center_location@' },
    { id: '@accommodation_location@', text: '@accommodation_location@' },
    {
      id: '@Local_Recruitment_Whatsapp_Number@',
      text: '@Local_Recruitment_Whatsapp_Number@',
    },
    {
      id: '@visa_cancellation_paper_link@',
      text: '@visa_cancellation_paper_link@',
    },
    { id: '@min_amount_in_bank@', text: '@min_amount_in_bank@' },
    {
      id: '@scheduled_termination_date@',
      text: '@scheduled_termination_date@',
    },
    { id: '@latest_bounced_amount@', text: '@latest_bounced_amount@' },
    { id: '@greetings@', text: '@greetings@' },
    { id: '@dear_receiver_name@', text: '@dear_receiver_name@' },
    {
      id: '@scheduled_termination_date - 1 day@',
      text: '@scheduled_termination_date - 1 day@',
    },
    { id: '@monthly_payment@', text: '@monthly_payment@' },
    { id: '@spouse_phone_number@', text: '@spouse_phone_number@' },
    { id: '@maid_first_name@', text: '@maid_first_name@' },
    { id: '@payment_options_link@', text: '@payment_options_link@' },
  ];

  bankNameOptions: SelectOption[] = [
    { id: 'Bank1', text: 'Bank 1' },
    { id: 'Bank2', text: 'Bank 2' },
    { id: 'Bank3', text: 'Bank 3' },
    // Add more banks as needed
  ];
  paymentStructureOptions: any = [];
  // Add properties for form controls and options
  confirmedDeletedMessageFrom: any = '';
  uiqueMessageListToCreate: any[] = [];
  uiqueMessageListToUpdate: any[] = [];
  eventFired: number = 0;

  noPredefinedClientMsg: string = 'No predefined client message available.';

  constructor(
    private _fb: FormBuilder,
    private _ddMessagingSetupService: DdMessagingSetupService,
    private _route: ActivatedRoute,
    private _router: Router,
    private _dialog: CCDialog,
    private _notifications: CCNotificationService,
    private cdr: ChangeDetectorRef
  ) {
    this.messageForm = this._fb.group({
      DDTypeA: [false],
      DDTypeB: [false],
      Maidvisa: [false],
      Maidscc: [false],
      sendToClient: [false],
      sendToSpouse: [false],
      clientMessage: [''],
      selectedNotificationClientVariables: [[]],
      selectedPaymentStructure: [''],
      selectedSmsClientVariables: [[]],
      sendAsEmail: [false],
      emailSubject: [''],
      sendToMaid: [false],
      sendToMaidWhenRetractCancellation: [false],
      selectedMaidVariables: [[]],
      selectedMaidwhenRetractCancellationVariables: [[]],
      maidWhenRetractCancellationMessage: [''],
      maidMessage: [''],
      afterDays: [0],
      whenToStartSending: ['DIRECTLY_ON_CONTACT_CREATION'],
      sentAt: [''],
      scheduledTerminationDate: ['None'],
      trials: this._fb.array(
        Array(10)
          .fill(false)
          .map(() => this._fb.control(false))
      ),
      reminders: this._fb.array(
        Array(10)
          .fill(false)
          .map(() => this._fb.control(false))
      ),
      selectedEvent: [''],
      selectedDDRejectionReason: [''],
      selectedAfterCashSubtype: [''],
      selectedAccountantRejectionReason: [''],
      selectedBouncedPaymentStatus: [''],
      createtodoType: ['noCreateTodo'],
      smsText: [''],
      sendPayTabMessage: [false],
      alertForVip: [false],
    });
  }

  messageForm: FormGroup;

  uniqueMessage = {
    formList: [] as any[],
    ddMessaging: '' as any,
  };
  itemIndex = 0;

  callCreate = new BehaviorSubject(false);
  callUpdatete = new BehaviorSubject(false);
  successUpdateAllBankMessage = new BehaviorSubject(false);
  successAddAllBankMessage = new BehaviorSubject(false);

  ngOnInit(): void {
    this.id = this._route.snapshot.paramMap.get('id');
    this.editMode = !!this.id;
    this.loadEventOptions();
    this.getpaymentStructureOption();

    this.loadBouncedPaymentStatusOptions();
    this.initializeForm();

    if (this.id) {
      this.getForm();
      this.getBankName();
    }

    // Watch for event changes to load subtypes
    this.messageForm
      .get('selectedEvent')
      ?.valueChanges.pipe(takeUntil(this.destroy$))
      .subscribe((selectedEvent) => {
        if (!selectedEvent) {
          this.afterCashSubtypeOptions = [];
          return;
        }
        this.loadSubTypeOptions(selectedEvent);
      });

    // Watch for event and bounced payment status changes to update variables options
    combineLatest([this.messageForm.get('selectedBouncedPaymentStatus')!.valueChanges, this.messageForm.get('selectedEvent')!.valueChanges])
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.updateVariablesBasedOnBouncedPaymentStatus();
      });

    this.cdr.detectChanges();
  }
  getpaymentStructureOption() {
    this._ddMessagingSetupService.getpaymentStructureOption().subscribe((response: any) => {
      this.paymentStructureOptions = response.map((item: any) => ({
        id: item.value,
        text: item.label,
      }));
    });
  }
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadEventOptions(): void {
    this._ddMessagingSetupService.getDDMessagingSetupEvent().subscribe((response: any) => {
      this.eventOptions = Object.entries(response).map(([key, value]) => ({
        id: key,
        text: value as string,
      }));
    });
  }

  loadSubTypeOptions(event: string): void {
    this._ddMessagingSetupService
      .getSubTypeOptions(event)
      .pipe(takeUntil(this.destroy$))
      .subscribe((response: any) => {
        this.afterCashSubtypeOptions = Object.entries(response).map(([key, value]) => ({
          id: key,
          text: value as string,
        }));
      });
  }

  getForm() {
    this._ddMessagingSetupService.getMessageForm(+this.id!).subscribe((response: any) => {
      // Set event and event options if not already loaded
      this.messageForm.get('selectedEvent')?.setValue(response.event.value);
      if (this.eventOptions.length === 0) {
        this.eventOptions = [
          {
            id: response.event.value,
            text: response.event.label,
          },
        ];
      }

      // Set subtype and subtype options
      this.messageForm.get('selectedAfterCashSubtype')?.setValue(response.subType.value);
      this.afterCashSubtypeOptions = [
        {
          id: response.subType.value,
          text: response.subType.label,
        },
      ];

      // Set basic fields
      this.messageForm.get('sentAt')?.setValue(response.sendTime);
      this.messageForm.get('scheduledTerminationDate')?.setValue(response.scheduleTermCategory.value);
      this.messageForm.get('sendToClient')?.setValue(response.sendToClient);
      this.messageForm.get('sendToSpouse')?.setValue(response.sendToSpouse);
      this.messageForm.get('sendAsEmail')?.setValue(response.sendAsEmail);
      this.messageForm.get('sendToMaid')?.setValue(response.sendToMaid);
      this.messageForm.get('selectedPaymentStructure')?.setValue(response.paymentStructure.value);

      // Set DD Type checkboxes
      if (response.ddCategory === 'A,B') {
        this.messageForm.get('DDTypeB')?.setValue(true);
      } else {
        this.messageForm.get('DDTypeA')?.setValue(true);
      }
      this.messageForm.get('DDTypeA')?.setValue(response.ddCategory.indexOf('A') !== -1);
      this.messageForm.get('DDTypeB')?.setValue(response.ddCategory.indexOf('B') !== -1);

      // Set Contract Type checkboxes
      this.messageForm.get('Maidvisa')?.setValue(response.contractProspectTypes.indexOf('maidvisa.ae_prospect') !== -1);
      this.messageForm.get('Maidscc')?.setValue(response.contractProspectTypes.indexOf('maids.cc_prospect') !== -1);

      // Set template IDs
      this.maidTemplateId = response.maidTemplate?.id;
      this.messageForm.get('sendToMaidWhenRetractCancellation')?.setValue(response.sendToMaidWhenRetractCancellation);
      this.messageForm.get('maidWhenRetractCancellationMessage')?.setValue(response.maidWhenRetractCancellationMessage);
      this.maidWhenRetractCancellationTemplate = response.maidWhenRetractCancellationTemplate?.id;
      this.clientTemplateId = response.clientTemplate?.id;
      this.clientTemplate = response.clientTemplate;

      // Set message content
      this.messageForm.get('clientMessage')?.setValue(response.clientMessage);
      this.messageForm.get('emailSubject')?.setValue(response.emailSubject);
      this.messageForm.get('maidMessage')?.setValue(response.maidMessage);

      // Set rejection reasons
      this.messageForm.get('selectedDDRejectionReason')?.setValue(response.rejectCategory.value);
      this.messageForm.get('selectedAfterCashSubtype')?.setValue(response.subType.value);
      this.messageForm.get('selectedAccountantRejectionReason')?.setValue(response.dataEntryRejectCategory.value);

      // Set bounced payment status
      this.bouncedPaymentStatus = response.bouncedPaymentStatus;
      this.messageForm.get('selectedBouncedPaymentStatus')?.setValue(response.bouncedPaymentStatus.id);

      // Set timing settings
      this.messageForm.get('whenToStartSending')?.setValue(response.whenToStartSending ? response.whenToStartSending.value : '');
      this.messageForm.get('afterDays')?.setValue(response.afterDays);

      // Set SMS content
      this.messageForm.get('smsText')?.setValue(response.smsText);
      this.messageForm.get('sendPayTabMessage')?.setValue(response.sendPayTabMessage);

      // Set todo type
      if (response.createClientToDo) {
        this.messageForm.get('createtodoType')?.setValue('createClientToDo');
      }

      // Set trials
      if (response.trials) {
        const trials = response.trials.split(',');
        const trialsArray = this.messageForm.get('trials') as FormArray;

        trials.forEach((item: string) => {
          // for ClientPaidCashAndNoSignatureProvided it's starts from 1-> 9
          if (this.messageForm.get('selectedEvent')?.value === 'ClientPaidCashAndNoSignatureProvided') {
            const index = parseInt(item) - 1;
            if (index >= 0 && index < trialsArray.length) {
              trialsArray.at(index).setValue(true);
            }
          } else {
            // the others from 0 -> 8
            const index = parseInt(item);
            if (index >= 0 && index < trialsArray.length) {
              trialsArray.at(index).setValue(true);
            }
          }
        });
      }

      // Set reminders
      if (response.reminders) {
        const reminders = response.reminders.split(',');
        const remindersArray = this.messageForm.get('reminders') as FormArray;

        reminders.forEach((item: string) => {
          if (this.messageForm.get('selectedEvent')?.value === 'ClientPaidCashAndNoSignatureProvided') {
            const index = parseInt(item) - 1;
            if (index >= 0 && index < remindersArray.length) {
              remindersArray.at(index).setValue(true);
            }
          } else {
            const index = parseInt(item);
            if (index >= 0 && index < remindersArray.length) {
              remindersArray.at(index).setValue(true);
            }
          }
        });
      }

      // Set VIP alert
      this.messageForm.get('alertForVip')?.setValue(response.alertForVip);
      this.cdr.detectChanges();
    });
  }

  loadBouncedPaymentStatusOptions(): void {
    this._ddMessagingSetupService.getBouncedPaymentStatusOptions().subscribe((response: any) => {
      this.bouncedPaymentStatusOptions = response.map((item: any) => ({
        id: item.id,
        text: item.name,
        code: item.code,
      }));

      // If we already have bouncedPaymentStatus, set the selected value
      if (this.bouncedPaymentStatus) {
        this.messageForm.get('selectedBouncedPaymentStatus')?.setValue(this.bouncedPaymentStatus.id);
        // this.messageForm.get('selectedBouncedPaymentStatus')?.setValue(this.bouncedPaymentStatus.code);
      }
    });
  }

  // Helper methods to check if trials should be shown
  showTrialsForEvent(): boolean {
    const event = this.messageForm.get('selectedEvent')?.value;
    return this.showTrialsWhenEventValue.includes(event);
  }

  // Helper methods to get form controls for trials
  getTrialFormControl(index: number): FormControl {
    return (this.messageForm.get('trials') as FormArray).at(index) as FormControl;
  }

  /**
   * Get trials as a comma-separated string
   * Format depends on the selected event
   */
  getTrials(): string {
    const trialsArray = this.messageForm.get('trials') as FormArray;
    let str = '';

    for (let i = 0; i < trialsArray.length; i++) {
      if (trialsArray.at(i).value === true) {
        if (this.messageForm.get('selectedEvent')?.value === 'ClientPaidCashAndNoSignatureProvided') {
          // For this event, use 1-based indexing (index + 1)
          str += str === '' ? i + 1 : ',' + (i + 1);
        } else {
          // For other events, use 0-based indexing
          str += str === '' ? i : ',' + i;
        }
      }
    }

    return str;
  }

  // Helper methods to check if reminders should be shown
  showRemindersForEvent(): boolean {
    const event = this.messageForm.get('selectedEvent')?.value;
    return this.showRemindersWhenEventValue.includes(event);
  }

  // Helper methods to get form controls for reminders
  getReminderFormControl(index: number): FormControl {
    return (this.messageForm.get('reminders') as FormArray).at(index) as FormControl;
  }

  /**
   * Get reminders as a comma-separated string
   * Format depends on the selected event
   */
  getReminders(): string {
    const remindersArray = this.messageForm.get('reminders') as FormArray;
    let str = '';

    for (let i = 0; i < remindersArray.length; i++) {
      if (remindersArray.at(i).value === true) {
        if (this.messageForm.get('selectedEvent')?.value === 'ClientPaidCashAndNoSignatureProvided') {
          // For this event, use 1-based indexing (index + 1)
          str += str === '' ? i + 1 : ',' + (i + 1);
        } else {
          // For other events, use 0-based indexing
          str += str === '' ? i : ',' + i;
        }
      }
    }

    return str;
  }

  private updateVariablesBasedOnBouncedPaymentStatus(): void {
    // Remove existing soonest_payroll if present
    this.variablesOptions = this.variablesOptions.filter((v) => v.id !== '@soonest_payroll@');

    const event = this.messageForm.get('selectedEvent')?.value;
    if (event !== 'BouncedPayment') {
      return;
    }
    const statusId =
      this.messageForm.get('selectedBouncedPaymentStatus')?.value?.id ?? this.messageForm.get('selectedBouncedPaymentStatus')?.value;
    if (!statusId) return;
    const status = this.bouncedPaymentStatusOptions.find((opt) => opt.id === statusId);
    if (status && status.text === 'Bounced payment received - including worker salary') {
      this.variablesOptions = [...this.variablesOptions, { id: '@soonest_payroll@', text: '@soonest_payroll@' }];
    }
  }

  initializeForm(): void {
    this.messageForm = this._fb.group({
      DDTypeA: [false],
      DDTypeB: [false],
      Maidvisa: [false],
      Maidscc: [false],
      sendToClient: [false],
      sendToSpouse: [false],
      clientMessage: [''],
      selectedNotificationClientVariables: [[]],
      selectedPaymentStructure: [''],
      selectedSmsClientVariables: [[]],
      sendAsEmail: [false],
      emailSubject: [''],
      sendToMaid: [false],
      sendToMaidWhenRetractCancellation: [false],
      selectedMaidVariables: [[]],
      selectedMaidwhenRetractCancellationVariables: [[]],
      maidWhenRetractCancellationMessage: [''],
      maidMessage: [''],
      afterDays: [0],
      whenToStartSending: ['DIRECTLY_ON_CONTACT_CREATION'],
      sentAt: [''],
      scheduledTerminationDate: ['None'],
      trials: this._fb.array(
        Array(10)
          .fill(false)
          .map(() => this._fb.control(false))
      ),
      reminders: this._fb.array(
        Array(10)
          .fill(false)
          .map(() => this._fb.control(false))
      ),
      selectedEvent: [''],
      selectedDDRejectionReason: [''],
      selectedAfterCashSubtype: [''],
      selectedAccountantRejectionReason: [''],
      selectedBouncedPaymentStatus: [''],
      createtodoType: ['noCreateTodo'],
      smsText: [''],
      sendPayTabMessage: [false],
      alertForVip: [false],
    });
  }

  getBankName(): void {
    this._ddMessagingSetupService.getBankNames().subscribe((response: any) => {
      this.bankNameOptions = response.map((item: any) => ({
        id: item.id,
        text: item.name,
      }));
      if (this.id) {
        this.getDDBankMessagingList(this.id);
      }
    });
  }

  getDDBankMessagingList(ddMessagingId: string): void {
    this._ddMessagingSetupService.getDDBankMessaging(ddMessagingId).subscribe((response: any) => {
      if (response.length > 0) {
        this.showUnique = true;
        this.uniqueMessage.formList = response.map((item: any) => ({
          ...item,
          banks: item.banks.map((bank: any) => bank.id),
          selectedNotificationClientVariables: null,
          selectedSmsClientVariables: null,
          selectedMaidVariables: null,
          selectedMaidwhenRetractCancellationVariables: null,
        }));
        this.cdr.detectChanges();
      }
    });
  }

  addNewBankUniqueMessage(): void {
    this.showUnique = true;
    this.uniqueMessage.formList.push({
      itemIndex: this.itemIndex,
      id: '',
      banks: [],
      banksList: [],
      sendToClient: this.messageForm.get('sendToClient')?.value,
      sendToSpouse: this.messageForm.get('sendToSpouse')?.value,
      clientMessage: this.messageForm.get('clientMessage')?.value,
      clientSmsMessage: this.messageForm.get('smsText')?.value,
      sendAsEmail: this.messageForm.get('sendAsEmail')?.value,
      emailSubject: this.messageForm.get('emailSubject')?.value,
      sendToMaid: this.messageForm.get('sendToMaid')?.value,
      maidMessage: this.messageForm.get('maidMessage')?.value,
      sendToMaidWhenRetractCancellation: this.messageForm.get('sendToMaidWhenRetractCancellation')?.value,
      maidWhenRetractCancellationMessage: this.messageForm.get('maidWhenRetractCancellationMessage')?.value,
      selectedNotificationClientVariables: null,
      selectedSmsClientVariables: null,
      selectedMaidVariables: null,
      selectedMaidwhenRetractCancellationVariables: null,
      clientTemplate: null,
      maidTemplate: null,
      maidWhenRetractCancellationTemplate: null,
    });
    this.itemIndex += 1;
    this.cdr.detectChanges();
  }

  deleteUniqueBankMessage(form: any): void {
    this.confirmedDeletedMessageFrom = form;
    this._dialog.confirm(
      'Delete Unique Message',
      'Are you sure you want to delete this unique message?',
      () => {
        const index = this.uniqueMessage.formList.indexOf(form);

        if (index == -1) {
          this.uniqueMessage.formList.splice(index, 1);
          this._notifications.notifySuccess('Unique Message Deleted Successfully');
        } else {
          this.confirmDeleteUniqueBankMessage();
        }
      },
      () => {},
      'Confirm',
      'Cancel'
    );
  }

  openRelatedMessage() {
    if (this.relatedMessageId) {
      window.open(`#!/accounting/v2/dd-messaging-setup/message-form/${this.relatedMessageId}`, '_blank');
    }
  }

  goToReturnPage(): void {
    // Assuming you have a router instance to navigate
    this._router.navigate(['/accounting/v2/dd-messaging-setup']);
  }

  confirmDeleteUniqueBankMessage(): void {
    let uiqueMessageListToDelete: any = [];
    uiqueMessageListToDelete.push(this.confirmedDeletedMessageFrom);
    this.uniqueMessage.formList = this.uniqueMessage.formList.filter((el) => !uiqueMessageListToDelete.includes(el));

    if (this.confirmedDeletedMessageFrom?.id) {
      this._ddMessagingSetupService.deleteDDBankMessaging(this.confirmedDeletedMessageFrom.id).subscribe((response: any) => {
        this._notifications.notifySuccess('Delete successful', response);
      });
    }
  }

  save(): void {
    const data: any = {
      event: this.messageForm.get('selectedEvent')?.value,
      // sendTime:
      //   this.messageForm.get('sentAt')?.value?.length == 8
      //     ? this.messageForm.get('sentAt')?.value
      //     : this.messageForm.get('sentAt')?.value + ':00',
      scheduleTermCategory: this.messageForm.get('scheduledTerminationDate')?.value,
      sendToClient: this.messageForm.get('sendToClient')?.value,
      // sendToSpouse: this.messageForm.get('sendToSpouse')?.value,
      sendAsEmail: this.messageForm.get('sendAsEmail')?.value,
      sendToMaid: this.messageForm.get('sendToMaid')?.value,
      sendToMaidWhenRetractCancellation: this.messageForm.get('sendToMaidWhenRetractCancellation')?.value,
      maidWhenRetractCancellationMessage: this.messageForm.get('maidWhenRetractCancellationMessage')?.value,
      parameterToUsed: '@hello@,@bye@',
      sendPayTabMessage: this.messageForm.get('sendPayTabMessage')?.value,
      alertForVip: this.messageForm.get('alertForVip')?.value,
      createClientToDo: this.messageForm.get('createtodoType')?.value === 'createClientToDo',
    };
    data.sendTime = null;
    if (this.messageForm.get('sentAt')?.value) {
      if (this.messageForm.get('sentAt')?.value.length == 8) {
        data.sendTime = this.messageForm.get('sentAt')?.value;
      } else {
        data.sendTime = this.messageForm.get('sentAt')?.value + ':00';
      }
    }
    if (this.id) {
      data.id = this.id;
    }

    data.afterDays = this.messageForm.get('afterDays')?.value;
    data.whenToStartSending = this.messageForm.get('whenToStartSending')?.value;

    let contractProspectTypes = '';
    if (this.messageForm.get('Maidvisa')?.value) {
      contractProspectTypes = 'maidvisa.ae_prospect';
      if (this.messageForm.get('Maidscc')?.value) {
        contractProspectTypes = 'maidvisa.ae_prospect,maids.cc_prospect';
      }
    }
    if (this.messageForm.get('Maidscc')?.value && !this.messageForm.get('Maidvisa')?.value) {
      contractProspectTypes = 'maids.cc_prospect';
    }
    data.contractProspectTypes = contractProspectTypes;

    if (this.messageForm.get('selectedEvent')?.value === 'DirectDebitRejected') {
      let ddCategory = '';
      if (this.messageForm.get('DDTypeA')?.value) {
        ddCategory = 'A';
        if (this.messageForm.get('DDTypeB')?.value) {
          ddCategory = 'A,B';
        }
      }
      if (this.messageForm.get('DDTypeB')?.value && !this.messageForm.get('DDTypeA')?.value) {
        ddCategory = 'B';
      }
      data.ddCategory = ddCategory;
      data.rejectCategory = this.messageForm.get('selectedDDRejectionReason')?.value;
    }

    if (
      this.messageForm.get('selectedEvent')?.value === 'ClientsPayingViaCreditCard' &&
      this.messageForm.get('selectedAfterCashSubtype')?.value === 'DD_Rejection'
    ) {
      data.rejectCategory = this.messageForm.get('selectedDDRejectionReason')?.value;
    }

    if (this.messageForm.get('selectedEvent')?.value === 'IncompleteDDRejectedByDataEntry') {
      data.dataEntryRejectCategory = this.messageForm.get('selectedAccountantRejectionReason')?.value;
    }

    if (this.messageForm.get('selectedAfterCashSubtype')?.value && this.afterCashSubtypeOptions.length) {
      data.subType = this.messageForm.get('selectedAfterCashSubtype')?.value;
    }

    if (this.messageForm.get('selectedEvent')?.value === 'BouncedPayment') {
      data.bouncedPaymentStatus = {
        id: this.messageForm.get('selectedBouncedPaymentStatus')?.value?.id ?? this.messageForm.get('selectedBouncedPaymentStatus')?.value,
        code: this.messageForm?.get('selectedBouncedPaymentStatus')?.value?.code ?? this.bouncedPaymentStatus?.code,
      };
    }

    // Add paymentStructure for OnlineCreditCardPaymentReminders event
    if (this.messageForm.get('selectedEvent')?.value === 'OnlineCreditCardPaymentReminders') {
      data.paymentStructure = this.messageForm.get('selectedPaymentStructure')?.value;
    }

    if (this.showTrialsWhenEventValue.includes(this.messageForm.get('selectedEvent')?.value)) {
      data.trials = this.getTrials();
    }

    if (this.showRemindersWhenEventValue.includes(this.messageForm.get('selectedEvent')?.value)) {
      data.reminders = this.getReminders();
    }

    if (this.messageForm.get('sendToClient')?.value || this.messageForm.get('sendToSpouse')?.value) {
      data.clientMessage = this.messageForm.get('clientMessage')?.value;
    }

    if (this.messageForm.get('sendAsEmail')?.value) {
      data.emailSubject = this.messageForm.get('emailSubject')?.value;
    }

    if (this.messageForm.get('sendToMaid')?.value) {
      data.maidMessage = this.messageForm.get('maidMessage')?.value;
    }

    let messages = [];
    if (data.clientMessage) {
      messages.push(data.clientMessage);
    }
    if (data.maidMessage) {
      messages.push(data.maidMessage);
    }
    if (data.maidWhenRetractCancellationMessage) {
      messages.push(data.maidWhenRetractCancellationMessage);
    }

    if (
      messages.length &&
      messages.some((msg) => msg.includes('@soonest_payroll@')) &&
      (this.messageForm.get('selectedEvent')?.value !== 'BouncedPayment' ||
        (this.bouncedPaymentStatus && this.bouncedPaymentStatus.text !== 'Bounced payment received - including worker salary'))
    ) {
      this._notifications.notifyError(
        "You can't use @soonest_payroll@ in messages when event is not 'Bounced Payment' or bounced payment status not 'Bounced payment received - including worker salary'"
      );
      return;
    }
    // Convert empty strings to null in data object
    Object.keys(data).forEach((key) => {
      if (data[key] === '') {
        data[key] = null;
      }
    });
    let url = API.DDMessageCreateUpdate;
    if (this.id) {
      url += '/update';
      this.uniqueMessage.ddMessaging = { id: parseInt(this.id) };
    } else {
      url += '/create';
    }

    if (this.uniqueMessage.formList.length === 0 || !this.id) {
      this.callCreateUpdateDDMessage(url, data);
    } else {
      let invalid = 0;
      for (let i = 0; i < this.uniqueMessage.formList.length; i++) {
        const element = this.uniqueMessage.formList[i];
        if (element.banks.length === 0) {
          this._notifications.notifyError(`Please fill the bank name field for the Unique Bank Message ${i + 1} before Submit`);
          invalid += 1;
          break;
        }
        if (this.checkValidationForDDBankMessage(element, i)) {
          invalid += 1;
          break;
        }
      }
      if (invalid === 0) {
        this.callCreateUpdateDDMessage(url, data);
      }
    }
  }

  callCreateUpdateDDMessage(url: string, data: any): void {
    this._ddMessagingSetupService.createOrUpdateDDMessage(url, data).subscribe((response: any) => {
      if (typeof response === 'string') {
        this.noPredefinedClientMsg = response;
        // this._notifications.notifyError('No predefined client message available.');
        this._dialog.alert('Error!', this.noPredefinedClientMsg, () => {});
        // shpuld call no-predefined-client-modal
        return;
      }

      if (response.isDuplicated) {
        this.relatedMessageId = response.id;
        // this._notifications.notifyError('Duplicate message detected.');
        this._dialog.confirm(
          'Can not add duplicated messages',
          'A message with the same conditions already exist',
          () => {
            this.openRelatedMessage();
          },
          () => {},
          'Open Related Message',
          'Close'
        );
        // shpuld call confirm_modal
      } else {
        this._notifications.notifySuccess('SMS Updated Successfully');
        if (response.id) {
          this.uniqueMessage.ddMessaging = response.id.toString();
        }
        this.handleUniqueMessageOperations();
      }
    });
  }

  checkValidationForDDBankMessage(element: any, i: number): boolean {
    const seenBankIds = new Set();
    let hasDuplicates = false;
    for (const form of this.uniqueMessage.formList) {
      if (form.bankList && form.bankList.length > 0) {
        for (const bank of form.banksList) {
          if (seenBankIds.has(bank.id)) {
            this._notifications.notifyError(`Duplicate message for bank ${bank.text}`);
            hasDuplicates = true;
            break;
          }
          seenBankIds.add(bank.id);
        }
      }
      if (hasDuplicates) return true;
    }

    if (
      !element.sendToClient &&
      !element.sendToSpouse &&
      !element.sendAsEmail &&
      !element.sendToMaid &&
      !element.sendToMaidWhenRetractCancellation
    ) {
      this._notifications.notifyError(`Please fill at least one of the messages for Unique Bank Message ${i + 1}`);
      return true;
    }
    if ((element.sendToClient || element.sendToSpouse) && element.clientSmsMessage === '') {
      this._notifications.notifyError(`Please fill Client SMS Message field for the Unique Bank Message ${i + 1} before Submit`);
      return true;
    }
    if ((element.sendToClient || element.sendToSpouse) && element.clientMessage === '') {
      this._notifications.notifyError(`Please fill Notification Content field for the Unique Bank Message ${i + 1} before Submit`);
      return true;
    }
    if (element.sendAsEmail && element.emailSubject === '') {
      this._notifications.notifyError(`Please fill Email Message field for the Unique Bank Message ${i + 1} before Submit`);
      return true;
    }
    if (element.sendToMaid && element.maidMessage === '') {
      this._notifications.notifyError(`Please fill Maid Message field for the Unique Bank Message ${i + 1} before Submit`);
      return true;
    }
    return false;
  }

  handleUniqueMessageOperations(): void {
    this.uiqueMessageListToCreate = [];
    this.uiqueMessageListToUpdate = [];

    this.uniqueMessage.formList.forEach((element) => {
      if (!element.id) {
        this.callCreate.next(true);
        this.uiqueMessageListToCreate.push(element);
      }
      if (element.id) {
        this.callUpdatete.next(true);
        this.uiqueMessageListToUpdate.push(element);
      }
    });

    if (this.callCreate.value) {
      this.createDDBankMessaging();
    }
    if (this.callUpdatete.value) {
      this.updateDDBankMessaging();
    }
    if (!this.callCreate.value && !this.callUpdatete.value) {
      this.goToReturnPage();
    }
  }

  createDDBankMessaging(): void {
    const createList = this.uiqueMessageListToCreate.map((element) => ({
      ddMessaging: this.uniqueMessage.ddMessaging,
      banks: element.banks.map((bank: any) => ({ id: parseInt(bank) })),
      sendToClient: element.sendToClient,
      // sendToSpouse: element.sendToSpouse,
      clientMessage: element.clientMessage,
      clientSmsMessage: element.clientSmsMessage,
      sendAsEmail: element.sendAsEmail,
      emailSubject: element.emailSubject,
      sendToMaid: element.sendToMaid,
      maidMessage: element.maidMessage,
      sendToMaidWhenRetractCancellation: element.sendToMaidWhenRetractCancellation,
      maidWhenRetractCancellationMessage: element.maidWhenRetractCancellationMessage,
    }));

    this._ddMessagingSetupService.createOrUpdateDDMessage('accounting/ddBankMessaging/createAll', createList).subscribe(() => {
      this.successAddAllBankMessage.next(true);
      this.uniqueMessage.formList = this.uniqueMessage.formList.filter((el) => !this.uiqueMessageListToCreate.includes(el));
      if (
        (this.callCreate.value &&
          this.callUpdatete.value &&
          this.successAddAllBankMessage.value &&
          this.successUpdateAllBankMessage.value) ||
        (this.callCreate.value && this.successAddAllBankMessage.value && !this.callUpdatete.value) ||
        (this.callUpdatete.value && this.successUpdateAllBankMessage.value && !this.callCreate.value)
      ) {
        this.goToReturnPage();
      }
    });
  }

  updateDDBankMessaging(): void {
    const updateList = this.uiqueMessageListToUpdate.map((element) => ({
      id: element.id,
      ddMessaging: this.uniqueMessage.ddMessaging,
      banks: element.banks.map((bank: any) => ({ id: parseInt(bank) })),
      sendToClient: element.sendToClient,
      // sendToSpouse: element.sendToSpouse,
      clientMessage: element.clientMessage,
      clientSmsMessage: element.clientSmsMessage,
      sendAsEmail: element.sendAsEmail,
      emailSubject: element.emailSubject,
      sendToMaid: element.sendToMaid,
      maidMessage: element.maidMessage,
      sendToMaidWhenRetractCancellation: element.sendToMaidWhenRetractCancellation,
      maidWhenRetractCancellationMessage: element.maidWhenRetractCancellationMessage,
    }));

    this._ddMessagingSetupService.createOrUpdateDDMessage('accounting/ddBankMessaging/updateAll', updateList).subscribe(() => {
      this.successUpdateAllBankMessage.next(true);
      this.uniqueMessage.formList = this.uniqueMessage.formList.filter((el) => !this.uiqueMessageListToCreate.includes(el));
      if (
        (this.callCreate.value &&
          this.callUpdatete.value &&
          this.successAddAllBankMessage.value &&
          this.successUpdateAllBankMessage.value) ||
        (this.callCreate.value && this.successAddAllBankMessage.value && !this.callUpdatete.value) ||
        (this.callUpdatete.value && this.successUpdateAllBankMessage.value && !this.callCreate.value)
      ) {
        this.goToReturnPage();
      }
    });
  }

  onSmsClientVariablesChange(selectedVariables: any[], form: any): void {
    if (!form.clientSmsMessage) {
      form.clientSmsMessage = '';
    }

    // Get all available variables from the options
    const allVariables = this.variablesOptions.map((option) => option.id);

    // Remove all variables from the message first
    let currentMessage = form.clientSmsMessage;
    allVariables.forEach((variable) => {
      currentMessage = currentMessage.replace(new RegExp(variable.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), '');
    });

    // Add selected variables back with proper spacing
    selectedVariables.forEach((variable, index) => {
      if (index === 0) {
        currentMessage += variable;
      } else {
        currentMessage += ' ' + variable;
      }
    });
    form.clientSmsMessage = form.clientSmsMessage.trim();

    // Update the message, trimming any extra spaces
    form.clientSmsMessage = currentMessage.trim();
  }
  onNotificationClientVariablesChange(selectedVariables: any[], form: any): void {
    if (!form.clientMessage) {
      form.clientMessage = '';
    }

    // Get all available variables from the options
    const allVariables = this.variablesOptions.map((option) => option.id);

    // Remove all variables from the message first
    let currentMessage = form.clientMessage;
    allVariables.forEach((variable) => {
      currentMessage = currentMessage.replace(new RegExp(variable.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), '');
    });

    // Add only the selected variables back with proper spacing
    selectedVariables.forEach((variable, index) => {
      if (index === 0) {
        currentMessage += variable;
      } else {
        currentMessage += ' ' + variable;
      }
    });
    form.clientMessage = form.clientMessage.trim();

    // Clean up any extra spaces and update the message
    form.clientMessage = currentMessage.trim();
  }
  onMaidVariablesChange(selectedVariables: any[], form: any): void {
    if (!form.maidMessage) {
      form.maidMessage = '';
    }

    // Get all available variables from the options
    const allVariables = this.variablesOptions.map((option) => option.id);

    // Remove all variables from the message first
    let currentMessage = form.maidMessage;
    allVariables.forEach((variable) => {
      currentMessage = currentMessage.replace(new RegExp(variable.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), '');
    });

    // Add only the selected variables back with proper spacing
    selectedVariables.forEach((variable, index) => {
      if (index === 0) {
        currentMessage += variable;
      } else {
        currentMessage += ' ' + variable;
      }
    });
    form.maidMessage = form.maidMessage.trim();
    // Clean up any extra spaces and update the message
    form.maidMessage = currentMessage.trim();
  }
  onMaidWhenRetractCancellationVariablesChange(selectedVariables: any[], form: any): void {
    if (!form.maidWhenRetractCancellationMessage) {
      form.maidWhenRetractCancellationMessage = '';
    }
    // Get all available variables from the options
    const allVariables = this.variablesOptions.map((option) => option.id);

    // Remove all variables from the message first
    let currentMessage = form.maidWhenRetractCancellationMessage;
    allVariables.forEach((variable) => {
      currentMessage = currentMessage.replace(new RegExp(variable.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), '');
    });

    // Add only the selected variables back with proper spacing
    selectedVariables.forEach((variable, index) => {
      if (index === 0) {
        currentMessage += variable;
      } else {
        currentMessage += ' ' + variable;
      }
    });
    form.maidWhenRetractCancellationMessage = form.maidWhenRetractCancellationMessage.trim();
    // Clean up any extra spaces and update the message
    form.maidWhenRetractCancellationMessage = currentMessage.trim();
  }
}
