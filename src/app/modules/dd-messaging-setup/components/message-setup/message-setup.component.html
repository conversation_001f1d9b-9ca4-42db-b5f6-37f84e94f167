<cc-accordion class="p-3">
  <cc-panel [expanded]="isExpanded">
    <cc-panel-title> <cc-icon class="dd-message-setup-filter-icon">filter_alt</cc-icon> Filter </cc-panel-title>
    <cc-panel-body>
      <form [formGroup]="filterForm" class="filter-form ">
        <div class="row">
          <div class="col-sm-4">
            <div class="form-group">
              <cc-select label="Event" formControlName="event" [data]="eventsOptions" (userSelect)="getSubTypeOptions($event)"> </cc-select>
            </div>
          </div>
          <div class="col-sm-4">
            <div class="form-group">
              <cc-select label="Sub Event" formControlName="subType" [data]="subTypeOptions"> </cc-select>
            </div>
          </div>
          <div class="col-sm-4">
            <div class="form-group">
              <cc-select formControlName="contractType" [data]="contractTypeOptions" label="Contract Type"> </cc-select>
            </div>
          </div>
          <div class="col-sm-4">
            <div class="form-group">
              <cc-select formControlName="ddCategory" [data]="ddCategoryOptions" label="Direct Debit Type"> </cc-select>
            </div>
          </div>
          <div class="col-sm-4">
            <div class="form-group">
              <cc-select formControlName="rejectCategory" [data]="rejectCategoryOptions" label="DD Rejection Reason"> </cc-select>
            </div>
          </div>
          <div class="col-sm-4">
            <div class="form-group">
              <cc-select
                formControlName="dataEntryRejectCategory"
                [data]="dataEntryRejectCategoryOptions"
                label="Accountant Rejection Reason">
              </cc-select>
            </div>
          </div>
          <div class="col-sm-4">
            <div class="form-group">
              <cc-select formControlName="bouncedPaymentStatus" [data]="bouncedPaymentStatusOptions" label="Bounced payment status">
              </cc-select>
            </div>
          </div>
          <div class="col-sm-4">
            <div class="form-group">
              <cc-select label="Schedule Termination Date" formControlName="scheduleTermCategory" [data]="scheduleTermCategoryOptions">
              </cc-select>
            </div>
          </div>
          <div class="col-sm-4">
            <div class="form-group">
              <cc-input label="Trials" type="number" formControlName="trial"></cc-input>
            </div>
          </div>
          <div class="col-sm-4">
            <div class="form-group">
              <cc-input label="Reminders" type="number" formControlName="reminder"></cc-input>
            </div>
          </div>
          <div class="col-sm-4">
            <div class="form-group">
              <cc-input label="Message ID" type="number" formControlName="messageID"></cc-input>
            </div>
          </div>
          <div class="col-sm-4">
            <div class="form-group">
              <cc-select label="Payment Structure" formControlName="paymentStructure" [data]="paymentStructureOptions"></cc-select>
            </div>
          </div>
        </div>
        <div class="d-flex justify-content-center gap-3 ">
          <button cc-raised-button color="primary" (click)="search()">
            <cc-icon class="dd-message-setup-search-icon">search</cc-icon>
            Search
          </button>
          <button cc-raised-button color="basic" (click)="reset()">
            <cc-icon class="dd-message-setup-reset-icon">refresh</cc-icon>
            Reset
          </button>
        </div>
      </form>
    </cc-panel-body>
  </cc-panel>
</cc-accordion>

<div class="p-3">
  <cc-datagrid
    [columns]="gridCols"
    [data]="gridData?.content ?? []"
    [length]="gridData?.totalElements ?? 0"
    [pageSize]="gridData?.size ?? 0"
    [pageIndex]="gridData?.number ?? 0"
    [pageSizeOptions]="[20]"
    (page)="getNextPage($event)"
    [stickyHeader]="true">
    <cc-grid-actions-list
      *ccActionData="let ctx of gridData?.content; row as row"
      [renderedActionsCount]="2"
      style="width: fit-content; gap: 4px">
      <button *cc-action cc-raised-button color="primary" (click)="edit(row)">Edit</button>
      <ng-container *ngIf="row.active">
        <button *cc-action cc-raised-button color="accent" (click)="updateMessageDisabilityStatus(true, row)">Disable</button>
      </ng-container>
      <ng-container *ngIf="!row.active">
        <button *cc-action cc-raised-button color="accent" (click)="updateMessageDisabilityStatus(false, row)">Enable</button>
      </ng-container>
    </cc-grid-actions-list>
  </cc-datagrid>
</div>
