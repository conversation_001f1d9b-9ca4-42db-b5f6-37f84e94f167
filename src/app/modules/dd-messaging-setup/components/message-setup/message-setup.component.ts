import { ChangeDetectorRef, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { Subject, takeUntil, map, catchError, of } from 'rxjs';
import { DdMessagingSetupService } from '../../services/dd-messaging-setup.service';
import { SelectOption } from '@maids/cc-lib/select-input';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { DDMessageSetup } from '../../models/dd-message-setup.model';
import { PaginatedEntity } from '@maids/cc-lib/common';
import { PageEvent } from '@angular/material/paginator';
import { CCNotificationService } from '@maids/cc-lib/services';
import { Router } from '@angular/router';

@Component({
  selector: 'app-message-setup',
  templateUrl: './message-setup.component.html',
  styleUrls: ['./message-setup.component.scss'],
})
export class MessageSetupComponent implements OnInit, OnDestroy {
  destroy$ = new Subject<void>();
  gridData?: PaginatedEntity<DDMessageSetup>;

  isExpanded: boolean = false;
  gridCols: CCGridColumn[] = [
    { field: 'id', header: 'Message DB ID', width: '150px' },
    {
      field: 'event',
      header: 'Event',
      formatter: (rowData: any) => (rowData.event ? rowData.event.label : ''),
    },
    { field: 'trials', header: 'Trials' },
    { field: 'reminders', header: 'Reminders' },
    { field: 'clientMessage', header: 'Family Notification' },
    { field: 'smsText', header: 'Family SMS' },
    { field: 'maidMessage', header: 'Maid Message' },
  ];

  // Options for select inputs
  eventsOptions: SelectOption[] = [];
  subTypeOptions: SelectOption[] = [];
  contractTypeOptions: SelectOption[] = [
    { id: 'maidvisa.ae_prospect', text: 'Maidvisa ae prospect' },
    { id: 'maids.cc_prospect', text: 'Maids cc prospect' },
  ];
  ddCategoryOptions: SelectOption[] = [
    { id: 'A', text: 'A' },
    { id: 'B', text: 'B' },
  ];
  rejectCategoryOptions: SelectOption[] = [
    { id: 'Signature', text: 'Signature' },
    { id: 'Account', text: 'Account' },
    { id: 'EID', text: 'EID' },
    { id: 'Compliance', text: 'Compliance' },
    // {id: 'ComplianceORAccount', text: "ComplianceORAccount"},
    { id: 'Authorization', text: 'Authorization' },
    { id: 'Invalid_Account', text: 'Invalid account' },
    { id: 'Other', text: 'Other' },
  ];
  dataEntryRejectCategoryOptions: SelectOption[] = [
    { id: 'WrongEID', text: 'Wrong EID' },
    { id: 'WrongIBAN', text: 'Wrong IBAN' },
    { id: 'WrongAccountName', text: 'Wrong account name' },
    { id: 'WrongEIDAndIBAN', text: 'Wrong EID and IBAN' },
    { id: 'WrongEIDAndAccountName', text: 'Wrong EID and account name' },
    { id: 'WrongIBANAndAccountName', text: 'Wrong IBAN and account name' },
    { id: 'AllDataIncorrect', text: 'All data incorrect' },
  ];
  bouncedPaymentStatusOptions: SelectOption[] = [];
  scheduleTermCategoryOptions: SelectOption[] = [
    { id: 'None', text: 'None' },
    { id: 'GToday', text: 'GToday' },
    { id: 'EToday', text: 'EToday' },
  ];
  paymentStructureOptions: SelectOption[] = [];
  constructor(
    private _fb: FormBuilder,
    private _ddMessagingSetupService: DdMessagingSetupService,
    private _cdr: ChangeDetectorRef,
    private _notifications: CCNotificationService,
    private _router: Router
  ) {}

  filterForm = this._fb.group({
    event: [''],
    subType: [''],
    contractType: [''],
    ddCategory: [''],
    rejectCategory: [''],
    dataEntryRejectCategory: [''],
    bouncedPaymentStatus: [''],
    scheduleTermCategory: [''],
    trial: [''],
    reminder: [''],
    messageID: [''],
    paymentStructure: [''],
  });

  ngOnInit(): void {
    // Initialize main event dropdown data
    this.getDDMessagingSetupEvent();
    this.getBouncedPaymentStatusOptions();
    this.getPaymentStructureOptions();
    this.getDGdata();

    // Listen for event changes to update subType options
    this.filterForm
      .get('event')
      ?.valueChanges.pipe(takeUntil(this.destroy$))
      .subscribe((eventId) => {
        this.filterForm.get('subType')?.setValue('');
        this.subTypeOptions = [];
        if (eventId) {
          // Reset subType when event changes
          this.getSubTypeOptions(eventId);
        }
      });
  }

  getDGdata(pageIndex: number = 0): void {
    let params: any = {
      page: pageIndex,
    };
    params = {
      ...params,
      ...this.filterForm.value,
    };
    // Convert null values to empty strings in params
    Object.keys(params).forEach((key) => {
      if (params[key] === null) {
        params[key] = '';
      }
    });
    this._ddMessagingSetupService
      .searchMessageSetup(params)
      .pipe(takeUntil(this.destroy$))
      .subscribe((response) => {
        this.gridData = response;
        this._cdr.markForCheck();
      });
  }

  search(): void {
    this.getDGdata(0);
  }

  reset(): void {
    this.filterForm.patchValue({
      event: '',
      subType: '',
      contractType: '',
      ddCategory: '',
      rejectCategory: '',
      dataEntryRejectCategory: '',
      bouncedPaymentStatus: '',
      scheduleTermCategory: '',
      trial: '',
      reminder: '',
      messageID: '',
      paymentStructure: '',
    });
    this.subTypeOptions = [];
  }
  getPaymentStructureOptions() {
    this._ddMessagingSetupService.getpaymentStructureOption().subscribe((response) => {
      this.paymentStructureOptions = response.map((item: any) => ({
        id: item.value,
        text: item.label,
      }));
    });
  }
  /**
   * Maps any object response to SelectOption array format
   * @param response The response object with key-value pairs
   * @returns An array of SelectOption objects
   */
  private mapToSelectOptions(response: any): SelectOption[] {
    return Object.entries(response).map(([key, value]) => {
      return {
        id: key,
        text: value,
      } as SelectOption;
    });
  }

  getDDMessagingSetupEvent() {
    this._ddMessagingSetupService
      .getDDMessagingSetupEvent()
      .pipe(
        takeUntil(this.destroy$),
        map((response: any) => this.mapToSelectOptions(response)),
        catchError((error) => {
          console.error('Error loading events:', error);
          return of([]);
        })
      )
      .subscribe((options: SelectOption[]) => {
        this.eventsOptions = options;
      });
  }

  getSubTypeOptions(eventId: string) {
    if (!eventId) return;

    this._ddMessagingSetupService
      .getSubTypeOptions(eventId)
      .pipe(
        takeUntil(this.destroy$),
        map((response: any) => this.mapToSelectOptions(response))
      )
      .subscribe((options: SelectOption[]) => {
        this.subTypeOptions = options;
      });
  }

  getBouncedPaymentStatusOptions(searchTerm?: string) {
    this._ddMessagingSetupService
      .getBouncedPaymentStatusOptions(searchTerm)
      .pipe(
        takeUntil(this.destroy$),
        map((data: any) => {
          return data.map((item: any) => ({
            id: item.id,
            text: item.name,
          }));
        }),
        catchError((error) => {
          console.error('Error loading bounced payment statuses:', error);
          return of([]);
        })
      )
      .subscribe((options: SelectOption[]) => {
        this.bouncedPaymentStatusOptions = options;
      });
  }

  getNextPage(e: PageEvent) {
    this.getDGdata(e.pageIndex);
  }

  edit(row: DDMessageSetup) {
    this._router.navigate(['/accounting/v2/dd-messaging-setup/message-form', row.id]);
  }

  updateMessageDisabilityStatus(disable: boolean, row: DDMessageSetup) {
    if (disable) {
      this._ddMessagingSetupService.updateMessageStatus(+row.id, false).subscribe((response) => {
        this.getDGdata();
        this._notifications.notifySuccess('SMS Disabled Successfully');
      });
    } else {
      this._ddMessagingSetupService.updateMessageStatus(+row.id, true).subscribe((response) => {
        this.getDGdata();
        this._notifications.notifySuccess('SMS Enabled Successfully');
      });
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
