<div class="acc-7738">
  <cc-dialog-header>
    <h1 cc-dialog-title>Attachments</h1>
    <a
      role="button"
      type="button"
      cc-icon-button
      cc-dialog-close-button
      cc-dialog-close
    ></a
  ></cc-dialog-header>
  <cc-dialog-content>
    <cc-datagrid
      [data]="data"
      [columns]="gridCols"
      [showPaginator]="false"
      [cellTemplate]="{ action: action }"
    >
    </cc-datagrid>
    <ng-template #action let-row let-index="index" let-col="colDef">
      <button style="padding-left: 40px;" cc-button (click)="preview(row)">
        <cc-icon class="icon-2">folder_alt</cc-icon>Open
      </button>
      <button style="padding-left: 40px;"cc-button color="accent" (click)="Delete(row)">
        <cc-icon class="icon-2">close</cc-icon>Delete
      </button>
    </ng-template>
  </cc-dialog-content>
</div>
