<div class="acc-7738">
  <cc-dialog-header>
    <h1 cc-dialog-title>{{ data.deposit ? "Edit" : "New" }} Deposite</h1>
    <a
      role="button"
      type="button"
      cc-icon-button
      cc-dialog-close-button
      cc-dialog-close
    ></a>
  </cc-dialog-header>

  <cc-dialog-content>
    <form [formGroup]="formGroup" (ngSubmit)="save()">
      <div class="container w-100">
        <div class="row align-items-center">
          <cc-input
            class="input w-100"
            label="Deposit Name"
            type="text"
            formControlName="name"
            [required]="true"
          ></cc-input>
        </div>
        <div class="row align-items-center">
          <cc-input
            class="input w-100"
            label="Amount"
            type="number"
            formControlName="amount"
            [required]="true"
          ></cc-input>
        </div>
        <div class="row align-items-center">
          <cc-select
            class="w-100"
            label="Payment Method"
            formControlName="paymentMethod"
            [data]="paymentMethods"
          ></cc-select>
        </div>
        <div class="row align-items-center">
          <cc-datepicker
            class="w-100"
            label="Payment Date"
            formControlName="paymentDate"
            [color]="'accent'"
          ></cc-datepicker>
        </div>
        <div class="row align-items-center">
          <cc-textarea
            label="Notes"
            class="input w-100"
            type="text"
            formControlName="notes"
          ></cc-textarea>
        </div>
        <div class="row align-items-start">
          <cc-file-uploader
            class="w-100"
            label="Attachments"
            formControlName="attachments"
            name="file"
          ></cc-file-uploader>
        </div>
      </div>
    </form>
  </cc-dialog-content>
  <div mat-dialog-actions class="d-flex float-right">
    <button cc-button (click)="cancel()">Cancel</button>
    <button
      cc-button
      (click)="save()"
      [disabled]="!formGroup.valid"
      color="accent"
    >
      Save
    </button>
  </div>
</div>
