import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { DepositsService } from '../../service/deposits.service';
import { selectThemeSettingsState } from '@maids/cc-lib/theme/src/store/theme-setting.selectors';
import { map } from 'rxjs';
import { SelectOption } from '@maids/cc-lib/select-input';
import { CCDialogRef } from '@maids/cc-lib/dialog';
import { CCNotificationService } from '@maids/cc-lib/services';
@Component({
  selector: 'app-deposit-add-edit',
  templateUrl: './deposit-add-edit.component.html',
  styleUrls: ['./deposit-add-edit.component.scss'],
})
export class DepositAddEditComponent implements OnInit {
  formGroup!: FormGroup;
  paymentMethods: any[] = [];
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private formBuilder: FormBuilder,
    private depositsService: DepositsService,
    private ccDialogRef: CCDialogRef<DepositAddEditComponent>,
    public readonly notifications: CCNotificationService
  ) {}

  ngOnInit(): void {
    this.formGroup = this.formBuilder.group({
      id: [this.data.deposit?.id],
      name: [this.data?.deposit?.name, Validators.required],
      amount: [this.data?.deposit?.amount, Validators.required],
      paymentDate: [this.data?.deposit?.paymentDate],
      paymentMethod: [this.data?.deposit?.paymentMethod.value],
      notes: [this.data?.deposit?.notes],
      attachments: [this.data?.deposit?.attachments],
    });
    this.getPaymentMethod();
  }
  getPaymentMethod() {
    this.depositsService
      .getPaymentMethods()
      .pipe(
        map((val: any[]) => {
          return val.map(
            (opt) => ({ id: opt.value, text: opt.label } as SelectOption)
          );
        })
      )
      .subscribe((res: SelectOption[]) => {
        this.paymentMethods = res;
      });
  }
  save() {
    if (this.data.deposit) {
      this.depositsService.updateDeposits(this.formGroup.value).subscribe({
        next: (res) => {
          this.notifications.notifySuccess(
            'Deposit updated successfully',
            2000
          );
          this.ccDialogRef.close(true);
        },
        error: (err) => {
          this.notifications.notifyError(err.error.message, 2000);
        },
      });
    } else {
      this.depositsService.createDeposits(this.formGroup.value).subscribe({
        next: (res) => {
          this.notifications.notifySuccess(
            'Deposit created successfully',
            2000
          );
          this.ccDialogRef.close(true);
        },
        error: (err) => {
          this.notifications.notifyError(err.error.message, 2000);
        },
      });
    }
  }
  cancel() {
    this.ccDialogRef.close();
  }
}
