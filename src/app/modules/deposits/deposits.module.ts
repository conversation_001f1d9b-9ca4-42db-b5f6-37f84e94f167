import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { DepositsRoutingModule } from './deposits-routing.module';
import { DepositsListComponent } from './components/deposits-list/deposits-list.component';
import { DepositAddEditComponent } from './components/deposit-add-edit/deposit-add-edit.component';
import { CCDatagridModule } from '@maids/cc-lib/datagrid';
import { CCButtonModule } from '@maids/cc-lib/button';
import { CCDialogModule } from '@maids/cc-lib/dialog';
import { DepositsStoreModule } from './store/deposits-store.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CCIconModule } from '@maids/cc-lib/icon';
import { CCAdvancedSearchModule } from '@maids/cc-lib/advanced-search';
import { CCFileUploaderModule } from '@maids/cc-lib/file-uploader';
import { CCInputModule } from '@maids/cc-lib/input';
import { CCSelectInputModule } from '@maids/cc-lib/select-input';
import { CCTextareaModule } from '@maids/cc-lib/textarea';
import { CCDatepickerModule } from '@maids/cc-lib/date';
import { MatDialogModule } from '@angular/material/dialog';
import { AttachmentsComponent } from './components/attachments/attachments.component';
@NgModule({
  declarations: [DepositsListComponent, DepositAddEditComponent,AttachmentsComponent],
  imports: [
    CommonModule,
    DepositsRoutingModule,
    DepositsStoreModule,
    CCDatagridModule,
    CCButtonModule,
    CCIconModule,
    CCDialogModule,
    CCSelectInputModule,
    ReactiveFormsModule,
    CCAdvancedSearchModule,
    CCInputModule,
    FormsModule,
    CCTextareaModule,
    CCFileUploaderModule,
    CCDatepickerModule,
    MatDialogModule,
  ],
})
export class DepositsModule {}
