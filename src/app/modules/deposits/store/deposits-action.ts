import { PageableResponseModel, SearchModel } from '@maids/cc-lib/common';
import { createAction, props } from '@ngrx/store';
import { DepositsModel } from '../model/deposits.model';
import { SortDirection } from '@angular/material/sort';

export const fetchDepositsList = createAction(
  '[Deposits] Fetch Deposits List',
  props<{ search: SearchModel<any> }>()
);
export const updateSearch = createAction(
  '[Deposits] Update Search',
  props<{ search: SearchModel<any> }>()
);
export const updateDepositsListParams = createAction(
  '[Deposits] Update Events Config List Params',
  props<{ event: SearchModel<any> }>()
);
export const updateSearchSortQueryParams = createAction(
  '[Deposits] Update Search Sort Query Params',
  props<{ event: { active: string; direction: SortDirection } }>()
);
export const fetchDepositsListSuccess = createAction(
  '[Deposits] Fetch Deposits List Success',
  props<{ data: PageableResponseModel<DepositsModel> }>()
);
export const resetDepositsList = createAction('[Deposits] Reset Deposits List');
