import { ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { CC_DIALOG_DATA, CCDialogRef } from '@maids/cc-lib/dialog';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { DirectDebitFormService } from '../../../services/direct-debit-form.service';
import { CCNotificationService } from '@maids/cc-lib/services';
import { CCFileUploaderConfig } from '@maids/cc-lib/file-uploader';

@Component({
  selector: 'app-has-front-eid-doc-dialog',
  templateUrl: './has-front-eid-doc-dialog.component.html',
  styleUrls: ['./has-front-eid-doc-dialog.component.scss'],
})
export class HasFrontEidDocDialogComponent implements OnInit {
  form!: FormGroup;
  constructor(
    @Inject(CC_DIALOG_DATA) public data: any,
    private cdr: ChangeDetectorRef,
    private dialogRef: CCDialogRef<HasFrontEidDocDialogComponent>,
    private fb: FormBuilder,
    private service: DirectDebitFormService,
    private notification: CCNotificationService
  ) {}
  config: CCFileUploaderConfig = {
    maxFilesize: 25,
  };
  ngOnInit(): void {
    this.form = this.fb.group({
      idFront: new FormControl(null),
    });
  }
  saveFrontEIDDocument(isSave: boolean) {
    if (!isSave) {
      this.dialogRef.close({ isSend: false, skipFrontEIDDocument: true,isSave:false });
    }
    let type = this.data.clientDocumentsTypes.find((item: any) => item.code == 'emirates_id_front_side');
    var data={
      attachments:[{id:this.form.get('idFront')?.value.id}],
      client:{id:this.data.clientId},
      type:type.id,
    }
    this.service.addClientDocument(data).subscribe((res: any) => {
      this.notification.notifySuccess('The file saved successfully');
      this.dialogRef.close({ isSend: true, clientHasFrontEIDDocument: true,isSave:true });
    });
  }
}
