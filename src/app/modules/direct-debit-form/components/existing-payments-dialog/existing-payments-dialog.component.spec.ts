import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ExistingPaymentsDialogComponent } from './existing-payments-dialog.component';

describe('ExistingPaymentsDialogComponent', () => {
  let component: ExistingPaymentsDialogComponent;
  let fixture: ComponentFixture<ExistingPaymentsDialogComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ExistingPaymentsDialogComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ExistingPaymentsDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
