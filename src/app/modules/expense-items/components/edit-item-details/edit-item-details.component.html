<cc-dialog-header>
  <h1 cc-dialog-title>Item Details</h1>
  <a
    role="button"
    type="button"
    cc-icon-button
    cc-dialog-close-button
    cc-dialog-close
  ></a
></cc-dialog-header>
<cc-dialog-content>
  <form [formGroup]="formGroup">
    <div class="row align-items-center">
      <cc-input
        label="Item Name"
        [required]="true"
        formControlName="name"
        class="w-100"
        [disabled]="true"
      ></cc-input>
    </div>
    <div class="row align-items-center">
      <cc-select
        label="Measure of Consumption"
        class="w-100"
        [required]="true"
        formControlName="measureOfConsumption"
        [lazyPageFetcher]="measureOfConsumption"
        [(ngModel)]="measureOfConsumptionModel"
        [emitFullSelectOption]="true"
      ></cc-select>
    </div>
    <div class="row align-items-center">
      <cc-input
        label="Consumption Rate"
        type="number"
        [required]="true"
        formControlName="consumptionRate"
        class="w-100"
      ></cc-input>
    </div>
    <div class="row align-items-center">
      <cc-input
        label="Initial Cycle Inventory"
        type="number"
        [required]="true"
        formControlName="initialCycleInventory"
        class="w-100"
        [disabled]="true"
      ></cc-input>
    </div>
  </form>
</cc-dialog-content>
<cc-dialog-actions>
  <button cc-raised-button cc-dialog-close>Cancel</button>
  <button
    cc-raised-button
    (click)="save()"
    [disabled]="!formGroup.valid"
    color="accent"
    class="ml-2"
  >
    Save
  </button>
</cc-dialog-actions>
