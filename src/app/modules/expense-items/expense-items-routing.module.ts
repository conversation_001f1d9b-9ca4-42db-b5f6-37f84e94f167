import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ItemsListComponent } from './components/items-list/items-list.component';

const routes: Routes = [
  {
    path: ':categoryId',
    component: ItemsListComponent,
    data: { label: 'Items', pageCode: 'accounting_ExpenseItems' },
  },
  { path: '', component: ItemsListComponent, data: { label: 'Items' } },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ExpenseItemsRoutingModule {}
