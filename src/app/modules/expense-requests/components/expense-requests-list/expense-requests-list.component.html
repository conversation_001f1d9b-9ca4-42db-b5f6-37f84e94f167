<div class="mt-3">
  <cc-accordion>
    <cc-panel>
      <cc-panel-title class="d-flex align-items-center">
        <cc-icon class="icon_filter">filter_alt</cc-icon>
        <span> Filters</span>
      </cc-panel-title>
      <cc-panel-body>
        <form [formGroup]="form" class="d-flex justify-content-around">
          <div class="col-6">
            <div class="form-group">
              <cc-select
                [formControl]="form.get('expenseId')"
                [lazyPageFetcher]="expensePageFetcher"
                label="Expense"
                placeholder="Select Expense"
                [multiple]="false"
              ></cc-select>
            </div>
            <div class="form-group">
              <cc-select
                [data]="statusOptions"
                formControlName="status"
                (userSelect)="onStatusChange($event)"
                label="Status"
              ></cc-select>
            </div>
            <div class="form-group">
              <div class="row">
                <div class="col-4">
                  <cc-select
                    [data]="searchNumericOptions"
                    formControlName="amountOperator"
                    label="Amount Operator"
                  ></cc-select>
                </div>
                <div class="col-8">
                  <cc-amount-input
                    formControlName="amount"
                    label="Amount"
                    suffixSymbol=""
                    symbol=""
                  ></cc-amount-input>
                </div>
              </div>
            </div>
            <div class="form-group">
              <cc-select
                [data]="paymentMethodOptions"
                formControlName="paymentMethod"
                label="Payment Method"
              ></cc-select>
            </div>
            <div class="form-group">
              <cc-select
                [formControl]="form.get('bucketName')"
                [lazyPageFetcher]="bucketPageFetcher"
                label="Bucket From"
                placeholder="Select Bucket"
                [multiple]="false"
              ></cc-select>
            </div>
          </div>
          <div class="col-6">
            <div class="form-group">
              <div class="row">
                <div class="col-4">
                  <cc-select
                    [data]="beneficiaryTypeOptions"
                    formControlName="beneficiaryType"
                    (userSelect)="onBeneficiaryTypeChange($event)"
                    label="Beneficiary"
                  ></cc-select>
                </div>
                <div class="col-8">
                  <cc-select
                    *ngIf="form.get('beneficiaryType')?.value === 'SUPPLIER'"
                    [formControl]="form.get('supplierId')"
                    [lazyPageFetcher]="supplierPageFetcher"
                    label="Supplier"
                    placeholder="Select Supplier"
                  ></cc-select>
                  <cc-select
                    *ngIf="form.get('beneficiaryType')?.value === 'MAID'"
                    [formControl]="form.get('maidId')"
                    [lazyPageFetcher]="housemaidPageFetcher"
                    label="Maid"
                    placeholder="Select Maid"
                  ></cc-select>
                  <cc-select
                    *ngIf="
                      form.get('beneficiaryType')?.value === 'OFFICE_STAFF'
                    "
                    [formControl]="form.get('officeStaffId')"
                    [lazyPageFetcher]="officeStaffPageFetcher"
                    label="Office Staff"
                    placeholder="Select Office Staff"
                  ></cc-select>
                </div>
              </div>
            </div>
            <div class="form-group">
              <div class="row">
                <div class="col-4">
                  <cc-select
                    [data]="relatedToTypeOptions"
                    formControlName="relatedToType"
                    (userSelect)="onRelatedToTypeChange($event)"
                    label="Related To"
                  ></cc-select>
                </div>
                <div class="col-8">
                  <cc-select
                    *ngIf="form.get('relatedToType')?.value === 'APPLICANT'"
                    [formControl]="form.get('applicantId')"
                    [lazyPageFetcher]="applicantPageFetcher"
                    label="Applicant"
                    placeholder="Select Applicant"
                  ></cc-select>
                  <cc-select
                    *ngIf="form.get('relatedToType')?.value === 'MAID'"
                    [formControl]="form.get('relatedMaidId')"
                    [lazyPageFetcher]="housemaidPageFetcher"
                    label="Housemaid"
                    placeholder="Select Housemaid"
                  ></cc-select>
                  <cc-select
                    *ngIf="form.get('relatedToType')?.value === 'OFFICE_STAFF'"
                    [formControl]="form.get('relatedOfficeStaffId')"
                    [lazyPageFetcher]="officeStaffPageFetcher"
                    label="Office Staff"
                    placeholder="Select Office Staff"
                  ></cc-select>
                </div>
              </div>
            </div>
            <div class="form-group">
              <div class="row">
                <div class="col-4">
                  <cc-select
                    [data]="searchDateOptions"
                    formControlName="dateOperator"
                    (userSelect)="onDateOperatorChange($event)"
                    label="Date Operator"
                  ></cc-select>
                </div>
                <div class="col-8">
                  <cc-datepicker
                    formControlName="date1"
                    [label]="form.get('dateOperator')?.value === 'between' ? 'From Date' : 'Date'"
                  ></cc-datepicker>
                  <cc-datepicker
                    *ngIf="form.get('dateOperator')?.value === 'between'"
                    formControlName="date2"
                    label="To Date"
                  ></cc-datepicker>
                </div>
              </div>
            </div>
            <div class="form-group">
              <cc-select
                [formControl]="form.get('requesterId')"
                [lazyPageFetcher]="userPageFetcher"
                label="Requestor"
                placeholder="Select Requester"
                [multiple]="false"
              ></cc-select>
            </div>
            <div
              class="form-group"
              *ngIf="form.get('status')?.value === 'PENDING'"
            >
              <cc-select
                [data]="pendingOptions"
                formControlName="pendingForApproval"
                label="Pending For Approval"
                placeholder="Select Approval Type"
              ></cc-select>
            </div>
          </div>
        </form>
        <div class="row justify-content-end mx-2 ">
          <div class="col-md-auto px-2">
            <button cc-raised-button color="primary" (click)="filterDataGrid()">
              <cc-icon class="icon" style="line-height: 0.95">search</cc-icon>
              Search
            </button>
          </div>
        </div>
      </cc-panel-body>
    </cc-panel>
  </cc-accordion>

  <div class="d-flex actions justify-content-between my-2">
    <div class="col-md-auto px-0" style="font-weight: 500; padding-top: 16px">
      <div>
        Paid:
        <span class="cc-secondary">{{ formatNumber(paymentData?.paid) }}</span
        >, Pending:
        <span class="cc-secondary">{{
          formatNumber(paymentData?.pending)
        }}</span
        >, Pending Payment:
        <span class="cc-secondary">{{
          formatNumber(paymentData?.pendingPayment)
        }}</span>
      </div>
    </div>
    <div class="col-md-auto px-0">
      <button cc-raised-button color="primary" (click)="exportCSV()">
        Export CSV
      </button>
    </div>
  </div>

  <cc-datagrid
    [data]="records?.content ?? []"
    [columns]="gridCols"
    [length]="records?.totalElements ?? 0"
    [pageOnFront]="false"
    [pageIndex]="records?.number ?? 0"
    [pageSize]="records?.size ?? 0"
    [pageSizeOptions]="[20]"
    (columnChange)="onColumnChange($event)"
    (page)="getNextPage($event)"
    (sortChange)="onSortChange($event)"
    [sortOnFront]="false"
    [stickyHeader]="true"
    [showColumnMenuButton]="true"
    [showColumnMenuHeader]="false"
    [columnMenuButtonIcon]="'settings'"
    [columnMovable]="true"
    [noResultTemplate]="noResultTemplate"
    [cellTemplate]="{ attachments: attachTmpl, 'expense.name': expenseTmpl }"
  >
    <cc-grid-actions-list
      *ccActionData="let ctx of records?.content; row as row"
      [renderedActionsCount]="1"
      style="width: fit-content"
    >
      <ng-container *ngIf="row.payment?.id">
        <button
          *cc-action
          cc-raised-button
          color="primary"
          (click)="viewPaymentDetails(row)"
        >
          Payment Details
        </button>
      </ng-container>
      <ng-container *ngIf="row.canBeRefunded">
        <button
          *cc-action
          cc-raised-button
          color="accent"
          (click)="requestRefund(row)"
        >
          Refund Request
        </button>
      </ng-container>
      <ng-container *ngIf="row.status?.value == 'PENDING'">
        <button *cc-action cc-raised-button (click)="deleteExpense(row)">
          Delete
        </button>
      </ng-container>
    </cc-grid-actions-list>
  </cc-datagrid>
  <ng-template #attachTmpl let-data let-index="index">
    <a
      class="cc-secondary"
      *ngFor="let attachment of data.attachments"
      (click)="downloadAttachment(attachment)"
      >{{ attachment.name }}</a
    >
  </ng-template>
  <ng-template #expenseTmpl let-data let-index="index">
    <a class="cc-secondary" (click)="openExpenseDetails(data)">{{
      data.expense?.name
    }}</a>
  </ng-template>
  <ng-template #noResultTemplate></ng-template>
</div>
