import { Component, OnInit } from '@angular/core';
import { ExpenseRequestsService } from '../../services/expense-requests.service';
import { FormBuilder, Validators } from '@angular/forms';
import { PaginationRequest, SearchModel } from '@maids/cc-lib/common';
import { Observable, of } from 'rxjs';
import {
  CCGridColumn,
  CCGridColumnSelectionItem,
} from '@maids/cc-lib/datagrid';
import { PageEvent } from '@angular/material/paginator';
import { Sort } from '@angular/material/sort';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { SelectOption } from '@maids/cc-lib/select-input';
import { CCPicklistService } from '@maids/cc-erp-services';
import { map, tap } from 'rxjs';
import { CCPreviewAttachmentComponent } from '@maids/cc-lib/preview-attachment';
import { CCDialog } from '@maids/cc-lib/dialog';
import { PaymentDetailsComponent } from '../payment-details/payment-details.component';
import { RefundExpenseComponent } from '../refund-expense/refund-expense.component';
import { DeleteExpenseComponent } from '../delete-expense/delete-expense.component';
import { ExpenseDetailsComponent } from '../expense-details/expense-details.component';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-expense-requests-list',
  templateUrl: './expense-requests-list.component.html',
  styleUrls: ['./expense-requests-list.component.scss'],
})
export class ExpenseRequestsListComponent implements OnInit {
  records: any;
  shownColumns: any[] = [];
  form: any;
  currentSort: string = 'id,DESC';
  paymentData: any = {
    paid: '00:00',
    pending: '00:00',
    pendingPayment: '00:00',
  };

  // متغيرات لتخزين معاملات البحث
  private searchParams = {
    page: 0,
    size: 20,
    sort: 'id,DESC',
  };

  // Search options
  searchTextOptions: SelectOption[] = [
    { id: 'contains', text: 'Contains' },
    { id: 'equals', text: 'Equals' },
    { id: 'starts_with', text: 'Starts With' },
    { id: 'ends_with', text: 'Ends With' },
  ];

  searchDateOptions: SelectOption[] = [
    { id: '=', text: 'Equal' },
    { id: '<', text: 'Before' },
    { id: '>', text: 'After' },
    { id: 'between', text: 'Between' },
  ];

  searchNumericOptions: SelectOption[] = [
    { id: '=', text: 'Equals' },
    { id: '>', text: 'More' },
    { id: '<', text: 'Less' },
    { id: '!=', text: 'Not Equals' },
  ];

  statusOptions: SelectOption[] = [
    { id: 'PENDING', text: 'Pending' },
    { id: 'PENDING_PAYMENT', text: 'Pending Payment' },
    { id: 'REJECTED', text: 'Rejected' },
    { id: 'PAID', text: 'Paid' },
  ];

  paymentMethodOptions: SelectOption[] = [
    { id: 'CASH', text: 'Cash' },
    { id: 'CREDIT_CARD', text: 'Credit Card' },
    { id: 'BANK_TRANSFER', text: 'Bank Transfer' },
    { id: 'MONEY_TRANSFER', text: 'Money Transfer' },
    { id: 'CHEQUE', text: 'Cheque' },
    { id: 'SALARY', text: 'Salary' },
    { id: 'INVOICED', text: 'Invoiced' },
  ];

  pendingOptions: SelectOption[] = [
    { id: 'FINANCE', text: 'Finance' },
    { id: 'MANAGER', text: 'Manager' },
    { id: 'ADMIN', text: 'Admin' },
  ];

  beneficiaryTypeOptions: SelectOption[] = [
    { id: 'SUPPLIER', text: 'Supplier' },
    { id: 'MAID', text: 'Maid' },
    { id: 'TAXI_DRIVER', text: 'Taxi Driver' },
    { id: 'OFFICE_STAFF', text: 'Office Staff' },
    { id: 'NOT_DETERMINED', text: 'Not Determined' },
  ];

  relatedToTypeOptions: SelectOption[] = [
    { id: 'APPLICANT', text: 'Applicant' },
    { id: 'MAID', text: 'Housemaid' },
    { id: 'TEAM', text: 'Team' },
    { id: 'OFFICE_STAFF', text: 'Office Staff' },
    { id: 'NOT_DETERMINED', text: 'Not Determined' },
  ];

  expenseTmpl = `
    <a href="javascript:void(0)" (click)="openExpenseDetails(row)">{{row.expense?.name}}</a>
  `;

  gridCols: CCGridColumn[] = [
    {
      field: 'expense.name',
      header: 'Expense',
      sortable: true,
    },
    {
      field: 'payment.transaction.id',
      header: 'Transaction',
      sortable: false,
      formatter(rowData, colDef) {
        return rowData.payment?.transaction?.id
          ? `<a class="cc-secondary" href="#!/accounting/add-edit-transactions/${rowData.payment?.transaction?.id}" target="_blank">${rowData.payment?.transaction?.id}</a>`
          : '';
      },
    },
    {
      field: 'relatedToInfo',
      header: 'Related To',
      sortable: true,
      sortProp: {
        arrowPosition: 'before',
        id: 'relatedToInfo',
        start: 'asc',
      },
    },
    {
      field: 'benefeciaryInfo',
      header: 'Beneficiary',
      sortable: true,
      sortProp: {
        arrowPosition: 'before',
        id: 'benefeciaryInfo',
        start: 'asc',
      },
    },
    {
      field: 'amount',
      header: 'Amount',
      sortable: true,
      formatter: (row: any) =>
        isNaN(parseFloat(row.amount))
          ? row.amount
          : this.formatNumber(Math.floor(parseFloat(row.amount))) +
            ' ' +
            (row.currency ? row.currency.label : ''),
      sortProp: {
        arrowPosition: 'before',
        id: 'amountInLocalCurrency',
        start: 'asc',
      },
    },
    {
      field: 'paymentMethod',
      header: 'Payment Method',
      sortable: true,
      formatter: (row: any) => row.paymentMethod?.label || '',
      sortProp: {
        arrowPosition: 'before',
        id: 'paymentMethod',
        start: 'asc',
      },
    },
    {
      field: 'bucket',
      header: 'Bucket From',
      sortable: true,
      formatter: (row: any) => row.bucket?.name || '',
      sortProp: {
        arrowPosition: 'before',
        id: 'expensePayment.fromBucket',
        start: 'asc',
      },
    },
    {
      field: 'status',
      header: 'Status',
      sortable: true,
      formatter: (row: any) => row.status?.label || '',
      type: 'tag',
      tag: {
        PENDING: { text: 'Pending', color: '#FFA500' },
        APPROVED: { text: 'Approved', color: '#008000' },
        REJECTED: { text: 'Rejected', color: '#FF0000' },
        PAID: { text: 'Paid', color: '#0000FF' },
        PENDING_PAYMENT: { text: 'Pending Payment', color: '#800080' },
      },
      sortProp: {
        arrowPosition: 'before',
        id: 'status',
        start: 'asc',
      },
    },
    {
      field: 'pendingForApproval',
      header: 'Pending For Approval',
      sortable: true,
      width: '150px',
      sortProp: {
        arrowPosition: 'before',
        id: 'pendingForApproval',
        start: 'asc',
      },
    },
    {
      field: 'requestedBy',
      header: 'Requested By',
      sortable: true,
      formatter: (row: any) => row.requestedBy?.name || '',
      sortProp: {
        arrowPosition: 'before',
        id: 'requestedBy.fullName',
        start: 'asc',
      },
    },
    {
      field: 'approvedBy',
      header: 'Approved By',
      sortable: true,
      sortProp: {
        arrowPosition: 'before',
        id: 'approvedBy',
        start: 'asc',
      },
    },
    {
      field: 'creationDate',
      header: 'Date',
      sortable: true,
      type: 'date',
      date: {
        format: 'yyyy-MM-dd',
      },
      sortProp: {
        arrowPosition: 'before',
        id: 'creationDate',
        start: 'asc',
      },
    },
    {
      field: 'attachments',
      header: 'Attachment',
    },
  ];

  expenseOptions: SelectOption[] = [];
  bucketFromOptions: SelectOption[] = [];
  requestorOptions: SelectOption[] = [];

  readonly expensePageFetcher = (pagReq: PaginationRequest) =>
    this.expenseRequestsService
      .searchExpenses({
        params: {
          page: pagReq.page || 0,
          size: pagReq.size || 50,
        },
        search: pagReq.searchString || '',
      })
      .pipe(
        map((response: any) =>
          response.content.map(
            (item: any) => ({ id: item.id, text: item.name } as SelectOption)
          )
        )
      );

  readonly bucketPageFetcher = (pagReq: PaginationRequest) =>
    this.expenseRequestsService
      .searchBuckets({
        params: {
          page: pagReq.page || 0,
          size: pagReq.size || 50,
        },
        search: pagReq.searchString || '',
      })
      .pipe(
        map((response: any) =>
          response.content.map(
            (item: any) => ({ id: item.name, text: item.name } as SelectOption)
          )
        )
      );

  readonly userPageFetcher = (pagReq: PaginationRequest) =>
    this.expenseRequestsService
      .getUsers({
        params: {
          page: pagReq.page || 0,
          size: pagReq.size || 50,
        },
        search: pagReq.searchString || '',
      })
      .pipe(
        map((response: any) =>
          response.content.map(
            (item: any) => ({ id: item.id, text: item.label } as SelectOption)
          )
        )
      );

  readonly supplierPageFetcher = (pagReq: PaginationRequest) =>
    this.expenseRequestsService
      .getSuppliers({
        params: {
          page: pagReq.page || 0,
          size: pagReq.size || 50,
        },
        search: pagReq.searchString || '',
      })
      .pipe(
        tap(console.log),
        map((response: any) =>
          response.content.map(
            (item: { id: number; name: string }) =>
              ({ id: item.id, text: item.name } as SelectOption)
          )
        )
      );

  readonly housemaidPageFetcher = (pagReq: PaginationRequest) =>
    this.expenseRequestsService
      .searchHousemaid({
        params: {
          page: pagReq.page || 0,
          size: pagReq.size || 100,
        },
        search: pagReq.searchString || '',
      })
      .pipe(
        map((response: any) =>
          response.content.map(
            (item: { id: number; label: string }) =>
              ({ id: item.id, text: item.label } as SelectOption)
          )
        )
      );

  readonly officeStaffPageFetcher = (pagReq: PaginationRequest) =>
    this.expenseRequestsService
      .getOfficeStaff({
        params: {
          page: pagReq.page || 0,
          size: pagReq.size || 50,
          sort: 'name,ASC',
        },
        search: pagReq.searchString || '',
      })
      .pipe(
        map((response: any) =>
          response.content.map(
            (item: { id: number; name: string }) =>
              ({ id: item.id, text: item.name } as SelectOption)
          )
        )
      );

  readonly applicantPageFetcher = (pagReq: PaginationRequest) =>
    this.expenseRequestsService
      .getApplicants({
        params: {
          page: pagReq.page || 0,
          size: pagReq.size || 50,
        },
        search: pagReq.searchString || '',
      })
      .pipe(
        map((response: any) =>
          response.content.map(
            (item: { id: number; name: string }) =>
              ({ id: item.id, text: item.name } as SelectOption)
          )
        )
      );
  constructor(
    private expenseRequestsService: ExpenseRequestsService,
    private fb: FormBuilder,
    private notificationService: CCNotificationService,
    private mediaService: MediaService,
    private picklist: CCPicklistService,
    private dialog: CCDialog,
    public readonly notifications: CCNotificationService
  ) {
    this.form = this.fb.group({
      expenseId: [''],
      status: [''],
      amountOperator: [''],
      amount: [''],
      paymentMethod: [''],
      beneficiaryType: [''],
      supplierId: [''],
      maidId: [''],
      taxiDriverId: [''],
      officeStaffId: [''],
      relatedToType: [''],
      applicantId: [''],
      relatedMaidId: [''],
      teamId: [''],
      relatedOfficeStaffId: [''],
      date1: [''],
      date2: [''],
      dateOperator: [''],
      requesterId: [''],
      bucketName: [''],
      pendingForApproval: [''],
    });
  }

  ngOnInit(): void {
    this.loadData(true);
  }

  private loadData(resetPage: boolean = false) {
    if (resetPage) {
      this.searchParams.page = 0;
    }

    const searchModel = this.form.value;
    const mappedPayload = this.mapSearchPayload(searchModel);

    this.expenseRequestsService
      .searchExpenseRequests({
        params: this.searchParams,
        search: mappedPayload,
      })
      .subscribe({
        next: (response) => {
          this.records = response.todos;
          this.paymentData = response.paymentData;
        },
        error: (error) => {
          this.notificationService.notifyError(
            'Failed to load expense requests'
          );
        },
      });
  }

  onColumnChange(columns: CCGridColumnSelectionItem[]): void {
    this.shownColumns = columns.filter((col) => col.show);
  }

  getNextPage(event: PageEvent): void {
    this.searchParams.page = event.pageIndex;
    this.searchParams.size = event.pageSize;
    this.loadData();
  }

  onSortChange(event: Sort): void {
    this.searchParams.sort = event.direction
      ? `${event.active},${event.direction}`
      : 'id,DESC';
    this.loadData(true);
  }

  filterDataGrid(): void {
    const searchModel = { ...this.form.value };
    const mappedPayload = this.mapSearchPayload(searchModel);
    this.loadData(true);
  }

  private mapSearchPayload(searchModel: any): any {
    const payload = {
      expenseId: searchModel.expenseId || null,
      status: searchModel.status || null,
      amountOperator: searchModel.amountOperator || null,
      amount: searchModel.amount || null,
      paymentMethod: searchModel.paymentMethod || null,
      beneficiaryType: searchModel.beneficiaryType || null,
      beneficiaryId: null,
      relatedToId: null,
      relatedToType: searchModel.relatedToType || null,
      date1: searchModel.date1 || null,
      date2: searchModel.date2 || null,
      dateOperator: searchModel.dateOperator || null,
      requesterId: searchModel.requesterId || null,
      bucketName: searchModel.bucketName || null,
      pendingForApproval: searchModel.pendingForApproval || null,
    };

    // Map beneficiary fields
    if (searchModel.supplierId) {
      payload.beneficiaryId = searchModel.supplierId;
    } else if (searchModel.maidId) {
      payload.beneficiaryId = searchModel.maidId;
    } else if (searchModel.taxiDriverId) {
      payload.beneficiaryId = searchModel.taxiDriverId;
    } else if (searchModel.officeStaffId) {
      payload.beneficiaryId = searchModel.officeStaffId;
    }

    // Map related to fields
    if (searchModel.applicantId) {
      payload.relatedToId = searchModel.applicantId;
    } else if (searchModel.relatedMaidId) {
      payload.relatedToId = searchModel.relatedMaidId;
    } else if (searchModel.teamId) {
      payload.relatedToId = searchModel.teamId;
    } else if (searchModel.relatedOfficeStaffId) {
      payload.relatedToId = searchModel.relatedOfficeStaffId;
    }

    // Handle date fields
    if (payload.dateOperator !== 'between' || !payload.dateOperator) {
      payload.date2 = null;
    }

    if (payload.date1) {
      payload.date1 = payload.date1 + ' 00:00:00';
    }

    if (payload.date2) {
      payload.date2 = payload.date2 + ' 00:00:00';
    }

    // Handle requester ID
    if (payload.requesterId) {
      payload.requesterId = +payload.requesterId;
    }

    // Handle pending for approval
    if (payload.status !== 'PENDING') {
      payload.pendingForApproval = null;
    }

    return payload;
  }

  resetFilters(): void {
    this.form.reset();
    this.loadData(true);
  }

  viewPaymentDetails(data: any) {
    this.expenseRequestsService.getExpensePayment(data.payment.id).subscribe({
      next: (paymentDetails) => {
        this.dialog.originalOpen(PaymentDetailsComponent, {
          panelClass: ['col-md-6'],
          data: {
            payment: paymentDetails,
          },
        });
      },
      error: (error) => {
        this.notificationService.notifyError('Failed to load payment details');
      },
    });
  }

  deleteExpense(data: any) {
    this.dialog
      .originalOpen(DeleteExpenseComponent, {
        panelClass: ['col-md-6'],
      })
      .afterClosed()
      .subscribe((result) => {
        if (result) {
          this.expenseRequestsService
            .deleteExpense(data.id, JSON.stringify(result))
            .subscribe({
              next: () => {
                this.notificationService.notifySuccess(
                  'Expense Deleted Successfully!'
                );
                this.loadData();
              },
              error: (error) => {
                this.notificationService.notifyError(
                  'Failed to delete expense'
                );
              },
            });
        }
      });
  }

  requestRefund(data: any) {
    this.dialog
      .originalOpen(RefundExpenseComponent, {
        panelClass: ['col-md-6'],
        data: {
          amount: data.amount,
          currency: data.currency.label,
        },
      })
      .afterClosed()
      .subscribe((result) => {
        if (result) {
          this.expenseRequestsService.refundExpense(data.id, result).subscribe({
            next: () => {
              this.notificationService.notifySuccess(
                'Expense Refunded Successfully!'
              );
              this.loadData();
            },
            error: (error) => {
              this.notificationService.notifyError('Failed to refund expense');
            },
          });
        }
      });
  }

  onBeneficiaryTypeChange(value: any) {
    if (!value) {
      this.form.get('supplierId')?.setValue(null);
      this.form.get('maidId')?.setValue(null);
      this.form.get('taxiDriverId')?.setValue(null);
      this.form.get('officeStaffId')?.setValue(null);
    }
  }

  onRelatedToTypeChange(value: any) {
    if (!value) {
      this.form.get('applicantId')?.setValue(null);
      this.form.get('relatedMaidId')?.setValue(null);
      this.form.get('relatedOfficeStaffId')?.setValue(null);
    }
  }

  onDateOperatorChange(value: any): void {
    if (value !== 'between') {
      this.form.get('date2')?.reset();
    }
  }

  onStatusChange(value: any): void {
    if (value !== 'PENDING') {
      this.form.get('pendingForApproval')?.reset();
    }
  }

  supplierOptions: SelectOption[] = [];
  housemaidOptions: SelectOption[] = [];
  officeStaffOptions: SelectOption[] = [];
  teamOptions: SelectOption[] = [];
  applicantOptions: SelectOption[] = [];

  downloadAttachment(attachment: any) {
    this.mediaService
      .getFile(`public/download/${attachment.uuid}`)
      .subscribe((res: any) => {
        let blob = new Blob([res], { type: res.type });
        const blobUrl = URL.createObjectURL(blob);
        let file_type = res.type.split('/')[1];
        this.dialog.originalOpen(CCPreviewAttachmentComponent, {
          panelClass: ['col-md-10'],
          data: {
            url: blobUrl,
            blob: blob,
            type: file_type,
            filename: attachment.name,
          },
        });
      });
  }

  openExpenseDetails(record: any) {
    this.dialog.originalOpen(ExpenseDetailsComponent, {
      panelClass: ['col-md-6'],
      data: {
        expense: record,
      },
    });
  }

  formatNumber(value: number): string {
    return value.toLocaleString('en-US', {
      maximumFractionDigits: 2,
    });
  }
  exportCSV() {
    let searchModel = { ...this.form.value };
    if (searchModel.dateOperator !== 'between' || !searchModel.dateOperator) {
      delete searchModel.date2;
    }
    if (!!searchModel.date1) {
      searchModel.date1 = searchModel.date1 + ' 00:00:00';
    }
    if (!!searchModel.date2) {
      searchModel.date2 = searchModel.date2 + ' 00:00:00';
    }
    if (searchModel.requesterId) {
      searchModel.requesterId = +searchModel.requesterId;
    }
    if (searchModel.status != 'PENDING') {
      searchModel.pendingForApproval = '';
    }
    if (this.records.totalElements < 2000) {
      this.mediaService.downloadFile('accounting/expenseRequestTodo/search/csv','',{method:'POST',body:searchModel});
    } else {
      this.expenseRequestsService.exportCSVExpenseRequestsMail(searchModel).subscribe((res)=>{
        this.notifications.notifySuccess(res);
      })
    }
  }
}
