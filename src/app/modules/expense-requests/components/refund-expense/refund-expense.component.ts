import { Component, Inject } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CCDialog, CCDialogRef, CC_DIALOG_DATA } from '@maids/cc-lib/dialog';
import { CCNotificationService } from '@maids/cc-lib/services';

@Component({
  selector: 'app-refund-expense',
  templateUrl: './refund-expense.component.html',
  styleUrls: ['./refund-expense.component.scss']
})
export class RefundExpenseComponent {
  refundForm: FormGroup;

  constructor(
    private fb: FormBuilder,
    private dialogRef: CCDialogRef<RefundExpenseComponent>,
    @Inject(CC_DIALOG_DATA) public data: { amount: number; currency: string },
    private notificationService: CCNotificationService
  ) {
    this.refundForm = this.fb.group({
      refundAmount: [null, [Validators.required]]
    });
  }

  onSubmit() {
    if (this.refundForm.valid) {
      const maxRefundAllowed = 1.02 * this.data.amount;
      const refundAmount = this.refundForm.get('refundAmount')?.value;
      
      if (refundAmount >= maxRefundAllowed) {
        this.notificationService.notifyError(`Refund amount should be less than ${maxRefundAllowed}`);
        return;
      }

      if (refundAmount <= 0) {
        this.notificationService.notifyError('Refund amount should be greater than 0');
        return;
      }

      this.dialogRef.close(refundAmount);
    }
  }

  onCancel() {
    this.dialogRef.close();
  }
} 