import { HttpClient, HttpContext, HttpParams } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import {
  CCBackendEndpoint,
  REQ_SHOW_LOADING_ICON,
} from '@maids/cc-erp-services';
import { SearchModel, PageableResponseModel } from '@maids/cc-lib/common';
import { BehaviorSubject, map, Observable } from 'rxjs';
import { API } from 'src/environments/api';

interface ExpenseSearchParams {
  page: number;
  size: number;
  sort: string;
}

@Injectable({
  providedIn: 'root',
})
export class ExpenseRequestsService {
  private readonly PAGE_SIZE = 20;
  private readonly DEFAULT_PAGE = 0;

  public readonly initialSearch: SearchModel<any> = {
    search: {},
    params: {
      page: this.DEFAULT_PAGE,
      size: this.PAGE_SIZE,
      sort: 'id,DESC'
    }
  };

  searchSubject = new BehaviorSubject<SearchModel<any>>(this.initialSearch);

  constructor(
    private http: HttpClient,
    @Inject(CCBackendEndpoint) private _api: string
  ) {}

  public searchExpenseRequests(searchModel: SearchModel<any>): Observable<any> {
    const params = this.buildSearchParams(searchModel);
    
    return this.http.post<any>(
      API.searchExpenseRequests,
      searchModel?.search ?? {},
      { params }
    ).pipe(
      map(this.transformResponse)
    );
  }

  private buildSearchParams(searchModel: SearchModel<any>): HttpParams {
    let params = new HttpParams()
      .set('expensesRequestPage', 'expense_requests')
      .set('page', (searchModel.params?.page || this.DEFAULT_PAGE).toString())
      .set('size', (searchModel.params?.size || this.PAGE_SIZE).toString());

    if (searchModel.params?.sort) {
      params = params.set('sort', searchModel.params.sort);
    }

    return params;
  }

  private transformResponse(response: any): any {
    return {
      todos: response.todos || [],
      paymentData: {
        pending: response.pendingAmount || '0.00',
        paid: response.paidAmount || '0.00',
        pendingPayment: response.pendingPaymentAmount || '0.00'
      }
    };
  }

  searchExpenses(searchModel: SearchModel<string>): Observable<any> {
    let params = new HttpParams()
      .set('page', (searchModel.params?.page || 0).toString())
      .set('size', (searchModel.params?.size || 50).toString());

    if (searchModel.search) {
      params = params.set('search', searchModel.search);
    }

    return this.http.get(API.searchExpensesRequests, { params });
  }

  searchBuckets(searchModel: SearchModel<string>): Observable<any> {
    let params = new HttpParams()
      .set('page', (searchModel.params?.page || 0).toString())
      .set('size', (searchModel.params?.size || 50).toString());

    if (searchModel.search) {
      params = params.set('search', searchModel.search);
    }

    return this.http.get(API.searchBucketsRequests, { params });
  }

  getUsers(searchModel: SearchModel<string>): Observable<any> {
    let params = new HttpParams()
      .set('page', (searchModel.params?.page || 0).toString())
      .set('size', (searchModel.params?.size || 50).toString())
      .set('search', searchModel.search || '');

    return this.http.get(API.searchRequestors, { params });
  }

  getSuppliers(searchModel: SearchModel<string>): Observable<any> {
    let params = new HttpParams()
      .set('page', (searchModel.params?.page || 0).toString())
      .set('size', (searchModel.params?.size || 50).toString())
      .set('_type', 'query')
      .set('term', searchModel.search || '') // redundant
      .set('q', searchModel.search || ''); // redundant

    return this.http.get((API.searchSuppliers+'/'+searchModel.search), { params });
  }

  getOfficeStaff(searchModel: SearchModel<string>): Observable<any> {
    let params = new HttpParams()
      .set('page', (searchModel.params?.page || 0).toString())
      .set('size', (searchModel.params?.size || 50).toString())
      .set('sort', searchModel.params?.sort || 'name,ASC')
      .set('status', 'ACTIVE');

    if (searchModel.search) {
      params = params.set('search', searchModel.search);
    }

    return this.http.get(API.searchOfficeStaff, { params });
  }

  getPendingForApproval(): Observable<string[]> {
    return this.http.post<any>(`${API.searchExpenseRequests}?expensesRequestPage=expense_requests`, {}).pipe(
      map(response => response.pendingForApproval || [])
    );
  }

  getApplicants(searchModel: SearchModel<any>): Observable<any> {
    const params = new HttpParams()
      .set('page', (searchModel.params?.page || 0).toString())
      .set('size', (searchModel.params?.size || 100).toString())
      .set('name', searchModel.search || '');

    return this.http.get(API.getCandidateByName, { params });
  }

  searchHousemaid(searchModel: SearchModel<any>): Observable<any> {
    const params = new HttpParams()
      .set('page', (searchModel.params?.page || 0).toString())
      .set('size', (searchModel.params?.size || 100).toString())
      .set('search', searchModel.search || '');

    return this.http.get(API.searchHousemaid, { params });
  }

  refundExpense(id: number, refundAmount: number) {
    return this.http.get(`${API.refundExpense}?id=${id}&amount=${refundAmount}`);
  }

  deleteExpense(id: number, notes: string) {
    return this.http.post(`${API.deleteExpense}/${id}`, notes, {
      context: new HttpContext().set(REQ_SHOW_LOADING_ICON, true)
    });
  }

  getExpensePayment(id: number): Observable<any> {
    return this.http.get(`${API.getExpensePayment}/${id}`, {
      context: new HttpContext().set(REQ_SHOW_LOADING_ICON, true)
    });
  }
  exportCSVExpenseRequestsMail(searchModel: any): Observable<any> {
    return this.http.post(API.exportCSVExpenseRequestsMail, searchModel);
  }
  // TODO: Implement API calls similar to payment-report service
} 