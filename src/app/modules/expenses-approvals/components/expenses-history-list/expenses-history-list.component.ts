import { Component, Input, OnInit } from '@angular/core';
import { CCGridColumn } from '@maids/cc-lib/datagrid';

@Component({
  selector: 'app-expenses-history-list',
  templateUrl: './expenses-history-list.component.html',
  styleUrls: ['./expenses-history-list.component.scss'],
})
export class ExpensesHistoryListComponent implements OnInit {
  @Input() historyRecords: any | null = null;
  constructor() {}
  gridCols: CCGridColumn[] = [
    {
      field: 'creationDate',
      header: 'Date',
      formatter(rowData, colDef) {
        return rowData.creationDate.split(' ')[0];
      },
    },
    {
      field: 'requestedBy',
      header: 'Requestor',
      formatter(rowData, colDef) {
        return rowData.requestedBy ? rowData.requestedBy.name : '';
      },
    },
    {
      field: 'expense.name',
      header: 'Expense',
    },
    { field: 'relatedToInfo', header: 'Related To' },
    { field: 'benefeciaryInfo', header: 'Beneficiary' },
    {
      field: 'amount',
      header: 'Amount',
      formatter(rowData, colDef) {
        return rowData.amount
          ? rowData.amount.toLocaleString('en-US', {
              minimumFractionDigits: 0,
              maximumFractionDigits: 0,
            })
          : '';
      },
    },
    { field: 'paymentMethod.label', header: 'Payment Method' },
    {
      field: 'loanAmount',
      header: 'Loan Amount',
      formatter(rowData, colDef) {
        return rowData.loanAmount
          ? rowData.loanAmount.toLocaleString('en-US', {
              minimumFractionDigits: 0,
              maximumFractionDigits: 0,
            })
          : '';
      },
    },
    { field: 'notes', header: 'Notes' },
    { field: 'status.label', header: 'Status' },
  ];
  ngOnInit(): void {}
}
