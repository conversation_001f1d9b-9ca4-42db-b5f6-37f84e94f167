import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ExpensesApprovalsRoutingModule } from './expenses-approvals-routing.module';
import { ExpensesApprovalsListComponent } from './components/expenses-approvals-list/expenses-approvals-list.component';
import { ExpensesHistoryListComponent } from './components/expenses-history-list/expenses-history-list.component';
import { CCDatagridModule } from '@maids/cc-lib/datagrid';
import { CCButtonModule } from '@maids/cc-lib/button';
import { CCDialogModule } from '@maids/cc-lib/dialog';
import { CCTextareaModule } from '@maids/cc-lib/textarea';
import { RejectReasonComponent } from './components/reject-reason/reject-reason.component';
import { FormsModule } from '@angular/forms';
import { MatDialogModule} from '@angular/material/dialog'

@NgModule({
  declarations: [
    ExpensesApprovalsListComponent,
    ExpensesHistoryListComponent,
    RejectReasonComponent,
  ],
  imports: [
    CommonModule,
    ExpensesApprovalsRoutingModule,
    CCDatagridModule,
    CCButtonModule,
    CCTextareaModule,
    CCDialogModule,
    FormsModule,
    MatDialogModule
  ],
})
export class ExpensesApprovalsModule {}
