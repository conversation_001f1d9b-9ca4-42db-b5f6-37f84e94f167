<div class="acc-7735">
  <cc-dialog-header
    ><h1 cc-dialog-title>Edit refund amount</h1>
    <a
      role="button"
      type="button"
      cc-icon-button
      cc-dialog-close-button
      cc-dialog-close
    ></a
  ></cc-dialog-header>
  <cc-dialog-content>
    <form [formGroup]="form" class="row align-items-center">
      <div class="col">
        <cc-amount-input
          formControlName="amount"
          label="New refund amount"
          symbol=" "
        ></cc-amount-input>
        <span
          class="error-msg"
          *ngIf="form.controls['amount'].hasError('required')"
          >This field is required</span
        >
        <span class="error-msg" *ngIf="form.controls['amount'].hasError('min')"
          >Value must be greater than 0</span
        >
        <span class="error-msg" *ngIf="form.controls['amount'].hasError('max')"
          >Refund amount should be less than
          {{ 1.02 * data.expenseRefund }}</span
        >
      </div>
    </form>
  </cc-dialog-content>
  <cc-dialog-actions>
    <div class="d-flex justify-content-end">
      <div class="col-md-auto">
        <button cc-button cc-dialog-close type="submit">Cancel</button>
      </div>
      <div class="col-md-auto">
        <button
          cc-button
          color="accent"
          type="submit"
          [disabled]="form.invalid"
          (click)="edit()"
        >
          Edit
        </button>
      </div>
    </div>
  </cc-dialog-actions>
</div>
