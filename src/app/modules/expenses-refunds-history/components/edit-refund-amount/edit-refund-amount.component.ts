import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder, FormControl, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { CCDialogRef } from '@maids/cc-lib/dialog';
import { ExpensesRefundsHistoryService } from '../../services/expenses-refunds-history.service';
import { CCNotificationService } from '@maids/cc-lib/services';
@Component({
  selector: 'app-edit-refund-amount',
  templateUrl: './edit-refund-amount.component.html',
  styleUrls: ['./edit-refund-amount.component.scss'],
})
export class EditRefundAmountComponent implements OnInit {
  form = this.formBuilder.group({
    amount: [
      0,
      [
        Validators.required,
        Validators.max(1.02 * this.data.expenseRefund),
        Validators.min(0),
      ],
    ],
  });
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private ccDialogRef: CCDialogRef<EditRefundAmountComponent>,
    private formBuilder: FormBuilder,
    private expensesRefundsHistoryService: ExpensesRefundsHistoryService,
    public readonly notifications: CCNotificationService
  ) {}

  ngOnInit(): void {
    this.form.controls['amount'].setValue(this.data.amount);
    console.log(this.data.expenseRefund);
  }
  edit() {
    if (this.form.valid) {
      this.expensesRefundsHistoryService
        .updateRefund({
          id: this.data.id,
          refundAmount: this.form.controls['amount'].value,
        })
        .subscribe({
          next: () => {
            this.ccDialogRef.close(true);
            this.notifications.notifySuccess('The Refund has Edited successfully');
          },
          error: (err) => {
            this.notifications.notifyError(err.error.message);
          },
        });
    }
  }
}
