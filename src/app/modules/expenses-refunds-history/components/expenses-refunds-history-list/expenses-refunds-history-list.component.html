<div class="acc-7735">
  <cc-accordion>
    <cc-panel>
      <cc-panel-title>
        <cc-icon style="top: 0.7em; position: absolute; left: 1em"
          >filter_alt</cc-icon
        ><span style="margin-left: 20px">Search</span></cc-panel-title
      >
      <cc-panel-body>
        <form [formGroup]="searchForm" class="d-flex justify-content-between">
          <div class="col-5">
            <div class="row align-items-center">
              <div class="col-10">
                <cc-input
                  class="w-100"
                  type="text"
                  label="Expense ID"
                  formControlName="expenseId"
                ></cc-input>
              </div>
            </div>
            <div class="row align-items-center">
              <div class="col-10">
                <cc-input
                  class="w-100"
                  label="Refund ID"
                  type="text"
                  formControlName="refundId"
                ></cc-input>
              </div>
            </div>
            <div class="row align-items-center">
              <div class="col-10">
                <cc-select
                  label="Refund status"
                  class="w-100"
                  [data]="refundConfirmedOptions"
                  formControlName="refundConfirmed"
                ></cc-select>
              </div>
            </div>
            <div class="row align-items-center">
              <div class="col-4">
                <cc-select
                  label="Amount Operator"
                  [data]="expenseAmountOperatorsOptions"
                  formControlName="expenseAmountOperator"
                ></cc-select>
              </div>
              <div class="col-6">
                <cc-input
                  label="Expense amount"
                  class="w-100"
                  formControlName="expenseAmount"
                ></cc-input>
              </div>
            </div>
            <div class="row align-items-center">
              <div class="col-4">
                <cc-select
                  label="Amount Operator"
                  [data]="refundAmountOperatorsOptions"
                  formControlName="refundAmountOperator"
                ></cc-select>
              </div>
              <div class="col-6">
                <cc-input
                  label="Refund amount"
                  class="w-100"
                  formControlName="refundAmount"
                ></cc-input>
              </div>
            </div>
          </div>
          <div class="col-5">
            <div class="row align-items-center">
              <div class="col-10">
                <cc-select
                  label="Supplier"
                  class="w-100"
                  [lazyPageFetcher]="suppliersOptions"
                  formControlName="supplierId"
                ></cc-select>
              </div>
            </div>
            <div class="row align-items-center">
              <div class="col-4">
                <cc-select
                  label="Related to"
                  [data]="relatedToTypesOptions"
                  formControlName="relatedToType"
                ></cc-select>
              </div>
              <div class="col-6">
                <cc-select
                  [lazyPageFetcher]="applicantsOptions"
                  *ngIf="
                    searchForm.controls['relatedToType'].value == 'APPLICANT'
                  "
                  formControlName="relatedToId"
                ></cc-select>
                <cc-select
                  [lazyPageFetcher]="housemaidsOptions"
                  *ngIf="searchForm.controls['relatedToType'].value == 'MAID'"
                  formControlName="relatedToId"
                ></cc-select>
                <cc-select
                  [lazyPageFetcher]="officeStaffOptions"
                  *ngIf="
                    searchForm.controls['relatedToType'].value == 'OFFICE_STAFF'
                  "
                  formControlName="relatedToId"
                ></cc-select>
              </div>
            </div>
            <div class="row align-items-center">
              <div class="col-4">
                <cc-select
                  label="Date Operator"
                  [data]="expenseDateOperatorsOptions"
                  formControlName="expenseDateOperator"
                ></cc-select>
              </div>
              <div
                [ngClass]="{
                  'col-8':
                    searchForm.controls['expenseDateOperator'].value !==
                    'between',
                  'col-4':
                    searchForm.controls['expenseDateOperator'].value ==
                    'between'
                }"
              >
                <cc-datepicker [label]="searchForm.controls['expenseDateOperator'].value === 'between' ? 'Expense From' : 'Expense Date'" formControlName="expenseDate1"></cc-datepicker>
              </div>
              <div
                class="col-4"
                [ngClass]="{
                  'd-none':
                    searchForm.controls['expenseDateOperator'].value !==
                    'between'
                }"
              >
                <cc-datepicker [label]="searchForm.controls['expenseDateOperator'].value === 'between' ? 'Expense To' : 'Expense Date'" formControlName="expenseDate2"></cc-datepicker>
              </div>
            </div>
            <div class="row align-items-center">
              <div class="col-4">
                <cc-select
                  label="Date Operator"
                  [data]="refundDateOperatorsOptions"
                  formControlName="refundDateOperator"
                ></cc-select>
              </div>
              <div
                [ngClass]="{
                  'col-8':
                    searchForm.controls['refundDateOperator'].value !==
                    'between',
                  'col-4':
                    searchForm.controls['refundDateOperator'].value == 'between'
                }"
              >
                <cc-datepicker [label]="searchForm.controls['refundDateOperator'].value === 'between' ? 'Refund From' : 'Refund Date'" formControlName="refundDate1"></cc-datepicker>
              </div>
              <div
                class="col-4"
                [ngClass]="{
                  'd-none':
                    searchForm.controls['refundDateOperator'].value !==
                    'between'
                }"
              >
                <cc-datepicker [label]="searchForm.controls['refundDateOperator'].value === 'between' ? 'Refund To' : 'Refund Date'" formControlName="refundDate2"></cc-datepicker>
              </div>
            </div>
          </div>
        </form>
        <hr />
        <div class="d-flex justify-content-end">
          <div class="col-md-auto px-0 mx-2">
            <button
              cc-raised-button
              style="padding-left: 30px"
              (click)="getCSVFile()"
              [disabled]="searchForm.invalid"
            >
              <cc-icon class="icon">download</cc-icon> Export To CSV
            </button>
          </div>
          <div class="col-md-auto px-0">
            <button
              cc-raised-button
              style="padding-left: 30px"
              (click)="getExpensesList(0, 20)"
              [disabled]="searchForm.invalid"
              color="primary"
            >
              <cc-icon class="icon">search</cc-icon> Search
            </button>
          </div>
        </div>
      </cc-panel-body>
    </cc-panel>
  </cc-accordion>
  <cc-datagrid
    class="my-2"
    [data]="expenses?.content ?? []"
    [columns]="gridCols"
    [length]="expenses?.totalElements ?? 0"
    [pageOnFront]="false"
    [pageIndex]="expenses?.number ?? 0"
    [pageSize]="expenses?.size ?? 0"
    [pageSizeOptions]="[20]"
    (page)="getNextPage($event)"
    [stickyHeader]="true"
    [columnMovable]="true"
    [columnHideable]="true"
    [showColumnMenuButton]="true"
    [showColumnMenuHeader]="false"
    [columnMenuButtonIcon]="'settings'"
    [cellTemplate]="{
      expenseTransaction: expenseTransaction,
      amount: amount,
      refundAmount: refundAmount,
      refundConfirmed: refundConfirmed
    }"
  ></cc-datagrid>
  <ng-template #expenseTransaction let-row let-index="index" let-col="colDef">
    <a
      *ngIf="row.expenseTransaction"
      [href]="'#!/accounting/add-edit-transactions/' + row.expenseTransaction"
      class="cc-secondary"
      >{{ row.expenseTransaction }}</a
    ></ng-template
  >
  <ng-template #amount let-row let-index="index" let-col="colDef">
    <span>{{ row.amount ? row.amount : "0" }} {{ row.currency.label }}</span>
  </ng-template>
  <ng-template #refundAmount let-row let-index="index" let-col="colDef">
    <span
      >{{ row.refundAmount ? row.refundAmount : "0" }}
      {{ row.currency.label }}</span
    >
  </ng-template>
  <ng-template #refundConfirmed let-row let-index="index" let-col="colDef">
    <span>{{ row.refundConfirmed ? "Confirmed" : "Pending" }}</span>
  </ng-template>
</div>
