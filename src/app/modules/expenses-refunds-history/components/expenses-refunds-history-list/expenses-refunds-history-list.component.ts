import { Component, OnInit } from '@angular/core';
import { ExpensesRefundsHistoryService } from '../../services/expenses-refunds-history.service';
import { map, Observable } from 'rxjs';
import { FormBuilder, Validators } from '@angular/forms';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { PageEvent } from '@angular/material/paginator';
import { CCDialog } from '@maids/cc-lib/dialog';
import { EditRefundAmountComponent } from '../edit-refund-amount/edit-refund-amount.component';
import { PaginationRequest } from '@maids/cc-lib/common';
import { SelectOption } from '@maids/cc-lib/select-input';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';

@Component({
  selector: 'app-expenses-refunds-history-list',
  templateUrl: './expenses-refunds-history-list.component.html',
  styleUrls: ['./expenses-refunds-history-list.component.scss'],
})
export class ExpensesRefundsHistoryListComponent implements OnInit {
  searchForm = this.formBuilder.group({
    expenseId: [null],
    refundId: [null],
    refundConfirmed: [null],
    expenseAmountOperator: [null],
    expenseAmount: [null],
    refundAmountOperator: [null],
    refundAmount: [null],
    supplierId: [null],
    relatedToType: [null],
    relatedToId: [null],
    expenseDateOperator: [null],
    expenseDate1: [null],
    expenseDate2: [null],
    refundDateOperator: [null],
    refundDate1: [null],
    refundDate2: [null],
  });
  expenses: any | null = null;
  refundConfirmedOptions = [
    { id: 'true', text: 'Confirmed' },
    { id: 'false', text: 'Pending' },
  ];
  expenseAmountOperatorsOptions = [
    { id: '=', text: 'Equals' },
    { id: '!=', text: 'Not Equals' },
    { id: '>', text: 'More' },
    { id: '<', text: 'Less' },
  ];
  refundAmountOperatorsOptions = [
    { id: '=', text: 'Equals' },
    { id: '!=', text: 'Not Equals' },
    { id: '>', text: 'More' },
    { id: '<', text: 'Less' },
  ];
  relatedToTypesOptions = [
    { id: 'APPLICANT', text: 'Applicant' },
    { id: 'MAID', text: 'Housemaid' },
    { id: 'OFFICE_STAFF', text: 'Office Staff' },
    { id: 'NOT_DETERMINED', text: 'Not Determined' },
  ];
  expenseDateOperatorsOptions = [
    { id: '=', text: 'Equal' },
    { id: '<', text: 'Before' },
    { id: '>', text: 'After' },
    { id: 'between', text: 'Between' },
  ];
  refundDateOperatorsOptions = [
    { id: '=', text: 'Equal' },
    { id: '<', text: 'Before' },
    { id: '>', text: 'After' },
    { id: 'between', text: 'Between' },
  ];
  suppliersOptions = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.expensesRefundsHistoryService
      .suppliersOptions(pageReq.page, pageReq.size, pageReq.searchString)
      .pipe(
        map((res: any) => {
          return res.map((opt: any) => {
            return { id: opt.id, text: opt.label } as SelectOption;
          });
        })
      );
  };
  officeStaffOptions = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.expensesRefundsHistoryService
      .officeStaffOptions(pageReq.page, pageReq.size, pageReq.searchString)
      .pipe(
        map((res: any) => {
          return res.content.map((opt: any) => {
            return { id: opt.id, text: opt.name } as SelectOption;
          });
        })
      );
  };
  housemaidsOptions = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.expensesRefundsHistoryService
      .housemaidsOptions(pageReq.page, pageReq.size, pageReq.searchString)
      .pipe(
        map((res: any) => {
          return res.content.map((opt: any) => {
            return { id: opt.id, text: opt.label } as SelectOption;
          });
        })
      );
  };
  applicantsOptions = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.expensesRefundsHistoryService
      .applicantsOptions(pageReq.page, pageReq.size, pageReq.searchString)
      .pipe(
        map((res: any) => {
          return res.content.map((opt: any) => {
            return { id: opt.id, text: opt.name } as SelectOption;
          });
        })
      );
  };
  constructor(
    private expensesRefundsHistoryService: ExpensesRefundsHistoryService,
    private formBuilder: FormBuilder,
    private ccDialog: CCDialog,
    public readonly notifications: CCNotificationService,
    private mediaService: MediaService
  ) {}
  gridCols: CCGridColumn[] = [
    {
      field: 'actions',
      header: '',
      sortable: false,
      type: 'button',
      buttonConfig: {
        mode: 'single',
        type: 'raised',
        color: 'primary',
        text: 'Edit',
        disabled: false,
        hidden: (row: any) => {
          return row.refundConfirmed;
        },
        callback: (row: any) => this.edit(row),
      },
    },
    { field: 'expenseId', header: 'Expense ID' },
    { field: 'expenseName', header: 'Expense name' },
    { field: 'expenseTransaction', header: 'Expense transaction' },
    { field: 'supplier', header: 'Supplier' },
    { field: 'relatedToInfo', header: 'Related to' },
    { field: 'id', header: 'Refund ID' },
    { field: 'amount', header: 'Expense amount' },
    { field: 'refundAmount', header: 'Refund amount' },
    { field: 'creationDate', header: 'Expense date', type: 'date' },
    { field: 'refundDate', header: 'Refund date' },
    { field: 'refundConfirmed', header: 'Refund status' },
  ];
  ngOnInit(): void {
    this.getExpensesList();
    this.formValidation();
  }
  getExpensesList(page: number = 0, size: number = 20) {
    let payload: any;
    payload = {
      ...this.searchForm.value,
      expenseDate1: this.searchForm.controls['expenseDate1'].value
        ? this.searchForm.controls['expenseDate1'].value + ' 00:00:00'
        : '',
      expenseDate2: this.searchForm.controls['expenseDate2'].value
        ? this.searchForm.controls['expenseDate2'].value + ' 00:00:00'
        : '',
      refundDate1: this.searchForm.controls['refundDate1'].value
        ? this.searchForm.controls['refundDate1'].value + ' 00:00:00'
        : '',
      refundDate2: this.searchForm.controls['refundDate2'].value
        ? this.searchForm.controls['refundDate2'].value + ' 00:00:00'
        : '',
    };
    this.expensesRefundsHistoryService
      .getSearchRefund({ page, size }, payload)
      .pipe(
        map((res: any) => {
          this.expenses = res;
        })
      )
      .subscribe({
        next: () => {},
        error: (err) => {
          this.notifications.notifyError(err.error.message);
        },
      });
  }

  getNextPage(event: PageEvent) {
    this.getExpensesList(event.pageIndex, event.pageSize);
  }
  edit(element: any) {
    this.ccDialog
      .originalOpen(EditRefundAmountComponent, {
        data: {
          amount: element.refundAmount,
          id: element.id,
          expenseRefund: element.amount,
        },
      })
      .afterClosed()
      .subscribe((res: boolean) => {
        if (res) {
          this.getExpensesList();
        }
      });
  }
  getCSVFile() {
    let payload: any = this.searchForm.value;
    if (payload.expenseDate1) {
      payload.expenseDate1 = payload.expenseDate1 + ' 00:00:00';
    }
    if (payload.expenseDate2) {
      payload.expenseDate2 = payload.expenseDate2 + ' 00:00:00';
    }
    if (payload.refundDate1) {
      payload.refundDate1 = payload.refundDate1 + ' 00:00:00';
    }
    if (payload.refundDate2) {
      payload.refundDate2 = payload.refundDate2 + ' 00:00:00';
    }
    this.mediaService.downloadFile(
      '/accounting/expenseRequestTodo/exportRefundsAsCsv',
      '',
      {
        method: 'POST',
        body: payload,
      }
    );
  }
  formValidation() {
    this.searchForm.controls['expenseAmountOperator'].valueChanges.subscribe(
      (val) => {
        if (val) {
          this.searchForm.controls['expenseAmount'].setValidators([
            Validators.required,
          ]);
          this.searchForm.controls['expenseAmount'].updateValueAndValidity({
            emitEvent: false,
          });
        } else {
          this.searchForm.controls['expenseAmount'].clearValidators();
          this.searchForm.controls['expenseAmount'].updateValueAndValidity({
            emitEvent: false,
          });
        }
      }
    );
    this.searchForm.controls['refundAmountOperator'].valueChanges.subscribe(
      (val) => {
        if (val) {
          this.searchForm.controls['refundAmount'].setValidators([
            Validators.required,
          ]);
          this.searchForm.controls['refundAmount'].updateValueAndValidity({
            emitEvent: false,
          });
        } else {
          this.searchForm.controls['refundAmount'].clearValidators();
          this.searchForm.controls['refundAmount'].updateValueAndValidity({
            emitEvent: false,
          });
        }
      }
    );
    this.searchForm.controls['expenseDateOperator'].valueChanges.subscribe(
      (val) => {
        if (val) {
          this.searchForm.controls['expenseDate1'].setValidators([
            Validators.required,
          ]);
          this.searchForm.controls['expenseDate1'].updateValueAndValidity({
            emitEvent: false,
          });
          if (val === 'between') {
            this.searchForm.controls['expenseDate2'].setValidators([
              Validators.required,
            ]);
            this.searchForm.controls['expenseDate2'].updateValueAndValidity({
              emitEvent: false,
            });
          } else {
            this.searchForm.controls['expenseDate2'].clearValidators();
            this.searchForm.controls['expenseDate2'].updateValueAndValidity({
              emitEvent: false,
            });
          }
        } else {
          this.searchForm.controls['expenseDate1'].clearValidators();
          this.searchForm.controls['expenseDate1'].updateValueAndValidity({
            emitEvent: false,
          });
          this.searchForm.controls['expenseDate2'].clearValidators();
          this.searchForm.controls['expenseDate2'].updateValueAndValidity({
            emitEvent: false,
          });
        }
      }
    );
    this.searchForm.controls['refundDateOperator'].valueChanges.subscribe(
      (val) => {
        if (val) {
          this.searchForm.controls['refundDate1'].setValidators([
            Validators.required,
          ]);
          this.searchForm.controls['refundDate1'].updateValueAndValidity({
            emitEvent: false,
          });
          if (val === 'between') {
            this.searchForm.controls['refundDate2'].setValidators([
              Validators.required,
            ]);
            this.searchForm.controls['refundDate2'].updateValueAndValidity({
              emitEvent: false,
            });
          } else {
            this.searchForm.controls['refundDate2'].clearValidators();
            this.searchForm.controls['refundDate2'].updateValueAndValidity({
              emitEvent: false,
            });
          }
        } else {
          this.searchForm.controls['refundDate1'].clearValidators();
          this.searchForm.controls['refundDate1'].updateValueAndValidity({
            emitEvent: false,
          });
          this.searchForm.controls['refundDate2'].clearValidators();
          this.searchForm.controls['refundDate2'].updateValueAndValidity({
            emitEvent: false,
          });
        }
      }
    );
    this.searchForm.controls['expenseAmount'].valueChanges.subscribe((val) => {
      if (val) {
        this.searchForm.controls['expenseAmountOperator'].setValidators([
          Validators.required,
        ]);
        this.searchForm.controls[
          'expenseAmountOperator'
        ].updateValueAndValidity({ emitEvent: false });
      } else {
        this.searchForm.controls['expenseAmountOperator'].clearValidators();
        this.searchForm.controls[
          'expenseAmountOperator'
        ].updateValueAndValidity({ emitEvent: false });
      }
    });
    this.searchForm.controls['refundAmount'].valueChanges.subscribe((val) => {
      if (val) {
        this.searchForm.controls['refundAmountOperator'].setValidators([
          Validators.required,
        ]);
        this.searchForm.controls['refundAmountOperator'].updateValueAndValidity(
          { emitEvent: false }
        );
      } else {
        this.searchForm.controls['refundAmountOperator'].clearValidators();
        this.searchForm.controls['refundAmountOperator'].updateValueAndValidity(
          { emitEvent: false }
        );
      }
    });
    this.searchForm.controls['expenseDate1'].valueChanges.subscribe((val) => {
      if (val) {
        this.searchForm.controls['expenseDateOperator'].setValidators([
          Validators.required,
        ]);
        this.searchForm.controls['expenseDateOperator'].updateValueAndValidity({
          emitEvent: false,
        });
      } else {
        this.searchForm.controls['expenseDateOperator'].clearValidators();
        this.searchForm.controls['expenseDateOperator'].updateValueAndValidity({
          emitEvent: false,
        });
      }
    });
    this.searchForm.controls['refundDate1'].valueChanges.subscribe((val) => {
      if (val) {
        this.searchForm.controls['refundDateOperator'].setValidators([
          Validators.required,
        ]);
        this.searchForm.controls['refundDateOperator'].updateValueAndValidity({
          emitEvent: false,
        });
      } else {
        this.searchForm.controls['refundDateOperator'].clearValidators();
        this.searchForm.controls['refundDateOperator'].updateValueAndValidity({
          emitEvent: false,
        });
      }
    });
  }
}
