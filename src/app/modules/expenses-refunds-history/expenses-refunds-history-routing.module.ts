import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ExpensesRefundsHistoryListComponent } from './components/expenses-refunds-history-list/expenses-refunds-history-list.component';

const routes: Routes = [
  {
    path: '',
    component: ExpensesRefundsHistoryListComponent,
    data: { label: '' },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ExpensesRefundsHistoryRoutingModule {}
