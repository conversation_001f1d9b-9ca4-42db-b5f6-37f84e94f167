import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ExpensesRefundsHistoryRoutingModule } from './expenses-refunds-history-routing.module';
import { ExpensesRefundsHistoryListComponent } from './components/expenses-refunds-history-list/expenses-refunds-history-list.component';
import { EditRefundAmountComponent } from './components/edit-refund-amount/edit-refund-amount.component';
import { CCDatagridModule } from '@maids/cc-lib/datagrid';
import { CCButtonModule } from '@maids/cc-lib/button';
import { CCInputModule } from '@maids/cc-lib/input';
import { CCSelectInputModule } from '@maids/cc-lib/select-input';
import { CCDatepickerModule } from '@maids/cc-lib/date';
import { CCAccordionModule } from '@maids/cc-lib/accordion';
import { CCIconModule } from '@maids/cc-lib/icon';
import { CCDialogModule } from '@maids/cc-lib/dialog';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CCAmountInputModule } from '@maids/cc-lib/masked-input';

@NgModule({
  declarations: [
    ExpensesRefundsHistoryListComponent,
    EditRefundAmountComponent,
  ],
  imports: [
    CommonModule,
    ExpensesRefundsHistoryRoutingModule,
    CCDatagridModule,
    CCButtonModule,
    CCInputModule,
    CCSelectInputModule,
    CCDatepickerModule,
    CCAccordionModule,
    CCAmountInputModule,
    CCIconModule,
    CCDialogModule,
    ReactiveFormsModule,
    FormsModule,
  ],
})
export class ExpensesRefundsHistoryModule {}
