import { HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { CCBackendEndpoint } from '@maids/cc-erp-services';
import { Observable } from 'rxjs';
import { API } from 'src/environments/api';

@Injectable({
  providedIn: 'root',
})
export class ExpensesRefundsHistoryService {
  constructor(
    private _http: HttpClient,
    @Inject(CCBackendEndpoint) private _api: CCBackendEndpoint
  ) {}
  getSearchRefund(params: any, payload: any): Observable<any> {
    return this._http.post(`${this._api}/${API.getSearchRefund}`, payload, {
      params,
    });
  }
  officeStaffOptions(
    page: number = 0,
    size: number = 20,
    search: string = ''
  ): Observable<any> {
    return this._http.get(`${this._api}/${API.officeStaffOptions}`, {
      params: { page, size, search, status: 'ACTIVE' },
    });
  }
  housemaidsOptions(
    page: number = 0,
    size: number = 20,
    search: string = ''
  ): Observable<any> {
    return this._http.get(`${this._api}/${API.housemaidsOptions}`, {
      params: { page, size, search },
    });
  }
  applicantsOptions(
    page: number = 0,
    size: number = 20,
    search: string = ''
  ): Observable<any> {
    return this._http.get(`${this._api}/${API.applicantsOptions}`, {
      params: { page, size, name: search },
    });
  }
  suppliersOptions(
    page: number = 0,
    size: number = 20,
    search: string = ''
  ): Observable<any> {
    return this._http.get(`${this._api}/${API.suppliersOptions}`, {
      params: { page, size, search },
    });
  }
  updateRefund(payload: any): Observable<any> {
    return this._http.post(`${this._api}/${API.updateRefund}`, payload);
  }
}
