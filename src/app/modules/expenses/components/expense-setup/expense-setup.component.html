<div class="container acc-6302 w-75">
  <div class="row">
    <div [formGroup]="form" *ngIf="form">
      <div class="col-12 bordered-bottom-line">
        <h5>Expense Details</h5>
      </div>
      <div class="col-12 mt-2">
        <cc-input
          label="Expense Name"
          [required]="true"
          formControlName="name"
        ></cc-input>
      </div>
      <div class="col-12">
        <cc-select
          label="Parent Expense"
          formControlName="parent"
          [lazyPageFetcher]="searchExpensesOptionsWithParent"
          [emitFullSelectOption]="true"
        ></cc-select>
      </div>
      <div class="col-12">
        <cc-input
          label="Expense Code"
          [disabled]="disableCode"
          formControlName="code"
          [required]="true"
        ></cc-input>
      </div>
      <div class="col-12">
        <cc-input
          label="Expense Caption"
          formControlName="caption"
          [required]="true"
        ></cc-input>
      </div>
      <div class="col-12">
        <cc-select
          label="Expense Manager"
          formControlName="manager"
          [lazyPageFetcher]="expenseManagerOptions"
          [emitFullSelectOption]="true"
          [required]="true"
        ></cc-select>
      </div>
      <div class="col-12">
        <cc-select
          label="Requested From"
          formControlName="requestedFrom"
          [data]="requestedFromOptions"
          [emitFullSelectOption]="true"
        ></cc-select>
      </div>
      <div class="col-12 row justify-content-between">
        <div class="col-6">
          <cc-checkbox formControlName="isSecure">Is Secure </cc-checkbox>
        </div>
        <div class="col-6">
          <cc-checkbox formControlName="requireAttachment"
            >Require Attachment</cc-checkbox
          >
        </div>
        <div class="col-6">
          <cc-checkbox formControlName="requireInvoice"
            >Require Invoice</cc-checkbox
          >
        </div>
        <div class="col-6">
          <cc-checkbox formControlName="allowToAddSupplierToExpenseRequest"
            >Allow To Add Supplier To Expense Request</cc-checkbox
          >
        </div>
        <div class="col-6">
          <cc-checkbox formControlName="hideRequestRefundButton"
            >Hide Request Refund Button</cc-checkbox
          >
        </div>
      </div>
      <div class="col-12">
        <cc-textarea
          label="Description"
          formControlName="description"
        ></cc-textarea>
      </div>
      <div class="col-12 bordered-bottom-line">
        <h5>Approval Flow</h5>
      </div>
      <cc-select
        label="Requesters"
        formControlName="requestors"
        [lazyPageFetcher]="usersOptions"
        [emitFullSelectOption]="true"
        [multiple]="true"
      ></cc-select>
      <div class="col-12">
        <cc-radio-group class="row" formControlName="approvalMethod">
          <div class="col-3">Approval Method</div>
          <div class="col-3">
            <cc-radio-button value="AUTO_APPROVED"
              >Auto-approved</cc-radio-button
            >
          </div>
          <div class="col-3">
            <cc-radio-button value="APPROVAL_REQUIRED"
              >Approval required</cc-radio-button
            >
          </div>
          <div class="col-3">
            <cc-radio-button value="APPROVAL_REQUIRED_ON_LIMIT"
              >Approval required on a limit</cc-radio-button
            >
          </div>
        </cc-radio-group>
      </div>
      <div
        class="col-12"
        [hidden]="form.controls['approvalMethod'].value == 'AUTO_APPROVED'"
      >
        <cc-radio-group class="row" formControlName="approveHolderType">
          <div class="col-3">Approved by type</div>
          <div class="col-3">
            <cc-radio-button value="FINAL_MANAGER"
              >Final Manager</cc-radio-button
            >
          </div>
          <div class="col-3">
            <cc-radio-button value="USER">User</cc-radio-button>
          </div>
          <div class="col-3">
            <cc-radio-button value="EMAIL">Email</cc-radio-button>
          </div>
        </cc-radio-group>
      </div>
      <div
        class="col-12"
        [hidden]="
          form.controls['approvalMethod'].value === 'AUTO_APPROVED' ||
          form.controls['approveHolderType'].value === 'FINAL_MANAGER' ||
          !form.controls['approvalMethod'].value
        "
      >
        <cc-select
          label="Approved By"
          formControlName="approveHolder"
          *ngIf="form.controls['approveHolderType'].value === 'USER'"
          [lazyPageFetcher]="usersOptions"
          [emitFullSelectOption]="true"
        ></cc-select>
        <cc-input
          label="Approved By"
          formControlName="approveHolderEmail"
          *ngIf="form.controls['approveHolderType'].value === 'EMAIL'"
        ></cc-input>
      </div>
      <div
        class="col-12"
        [hidden]="
          form.controls['approvalMethod'].value === 'APPROVAL_REQUIRED' ||
          form.controls['approvalMethod'].value === 'AUTO_APPROVED'
        "
      >
        <cc-amount-input
          symbol="AED"
          label="Limit for approval (AED)"
          formControlName="limitForApproval"
        ></cc-amount-input>
      </div>
      <cc-checkbox formControlName="isLimitedCOO"
        >Enable CEO approval on a limit</cc-checkbox
      >
      <cc-amount-input
        symbol="AED"
        label="Limit for CEO Approval (AED)"
        formControlName="limitCOO"
        [disabled]="!form.controls['isLimitedCOO'].value"
      ></cc-amount-input>
      <div class="col-12 bordered-bottom-line">
        <h5>Payment Details</h5>
      </div>
      <div class="col-12">Expense Payment Method :</div>
      <div class="col-12 row" formArrayName="paymentMethods">
        <div
          class="col-12 row"
          *ngFor="let control of paymentMethods.controls; let i = index"
          [formGroupName]="i"
        >
          <div class="col-3">
            <cc-checkbox formControlName="value">
              {{ paymentMethods.at(i).get("label")?.value | paymentMethod }}
            </cc-checkbox>
          </div>
          <div class="col-3">
            <cc-checkbox
              *ngIf="paymentMethods.at(i).get('label')?.value == 'CASH'"
              formControlName="paidOnTheSpotCash"
              [disabled]="!isCashSelected"
            >
              Paid on the spot
            </cc-checkbox>
            <cc-checkbox
              *ngIf="paymentMethods.at(i).get('label')?.value == 'CREDIT_CARD'"
              formControlName="paidOnTheSpotCreditCard"
              [disabled]="!isCreditCardSelected"
            >
              Paid on the spot
            </cc-checkbox>
          </div>
          <div class="col-6">
            <div
              *ngIf="
                paymentMethods.at(i).get('label')?.value == 'CASH' &&
                paymentMethods.at(i).get('paidOnTheSpotCash')?.value == true
              "
              class="row"
            >
              <div class="col-12">
                <cc-select
                  label="Bucket"
                  formControlName="fromCashBuckets"
                  [lazyPageFetcher]="bucketOptions"
                  [emitFullSelectOption]="true"
                  [multiple]="true"
                ></cc-select>
              </div>
              <div class="col-12">
                <cc-select
                  label="Default Bucket"
                  formControlName="fromCashBucket"
                  [lazyPageFetcher]="bucketOptions"
                  [emitFullSelectOption]="true"
                ></cc-select>
              </div>
            </div>
            <div
              *ngIf="
                paymentMethods.at(i).get('label')?.value == 'CREDIT_CARD' &&
                paymentMethods.at(i).get('paidOnTheSpotCreditCard')?.value ==
                  true
              "
              class="row"
            >
              <div class="col-12">
                <cc-select
                  label="Bucket"
                  formControlName="fromCreditCardBuckets"
                  [lazyPageFetcher]="bucketOptions"
                  [emitFullSelectOption]="true"
                  [multiple]="true"
                >
                </cc-select>
              </div>
              <div class="col-12">
                <cc-select
                  label="Default Bucket"
                  formControlName="fromCreditCardBucket"
                  [lazyPageFetcher]="bucketOptions"
                  [emitFullSelectOption]="true"
                ></cc-select>
              </div>
            </div>
            <div
              *ngIf="
                paymentMethods.at(i).get('label')?.value == 'SALARY' &&
                paymentMethods.at(i).get('value')?.value == true
              "
              class="row"
            >
              <div class="col-12">
                <cc-select
                  label="Salary Addition Type"
                  formControlName="salaryAdditionType"
                  [lazyPageFetcher]="additionTypeOptions"
                  [emitFullSelectOption]="true"
                ></cc-select>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col-12">
        <cc-amount-input
          symbol="AED"
          formControlName="defaultAmount"
          label="Default amount for expense (AED)"
        ></cc-amount-input>
      </div>
      <div class="col-12">
        <cc-radio-group formControlName="beneficiaryType" class="row">
          <div class="col-2">Beneficiary</div>
          <div class="col-10 row">
            <div class="col">
              <cc-radio-button value="SUPPLIER">Supplier</cc-radio-button>
            </div>
            <div class="col">
              <cc-radio-button value="MAID">Maid</cc-radio-button>
            </div>
            <div class="col">
              <cc-radio-button value="TAXI_DRIVER">Taxi Driver</cc-radio-button>
            </div>
            <div class="col">
              <cc-radio-button value="OFFICE_STAFF"
                >Office Staff</cc-radio-button
              >
            </div>
            <div class="col">
              <cc-radio-button value="NOT_DETERMINED"
                >Not Determined</cc-radio-button
              >
            </div>
          </div>
        </cc-radio-group>
      </div>
      <div
        class="col-12"
        *ngIf="form.controls['beneficiaryType'].value === 'SUPPLIER'"
      >
        <cc-select
          label="Suppliers"
          formControlName="suppliers"
          [lazyPageFetcher]="suppliersOptions"
          [emitFullSelectOption]="true"
          [multiple]="true"
        ></cc-select>
      </div>
      <div class="col-12 bordered-bottom-line">
        <h5>Posting Details</h5>
      </div>
      <div class="col-12 row">
        <div class="col-12">Related To</div>
        <div class="col-2">
          <cc-checkbox formControlName="relatedToMaid">Maid</cc-checkbox>
        </div>
        <div class="col-10">
          <cc-datagrid
            [data]="maidOptions"
            [columns]="gridCols"
            [showPaginator]="false"
            [cellTemplate]="{ selectedExpenses: selectedExpenses }"
          ></cc-datagrid>
          <ng-template #selectedExpenses let-row>
            <cc-select
              *ngIf="row.type == 'Maids.cc'"
              label="Select Expense"
              formControlName="relatedToMaidCC"
              [lazyPageFetcher]="searchExpensesOptions"
              [emitFullSelectOption]="true"
              [disabled]="form.controls['relatedToMaid'].value == false"
            ></cc-select>
            <cc-select
              *ngIf="row.type == 'MaidVisa'"
              label="Select Expense"
              formControlName="relatedToMaidVisa"
              [lazyPageFetcher]="searchExpensesOptions"
              [emitFullSelectOption]="true"
              [disabled]="form.controls['relatedToMaid'].value === false"
            ></cc-select>
          </ng-template>
        </div>
        <div class="col-2">
          <cc-checkbox formControlName="relatedToApplicant"
            >Applicant</cc-checkbox
          >
        </div>
        <div class="col-10">
          <cc-select
            label="Select Expense"
            formControlName="relatedToApplicantExpense"
            [lazyPageFetcher]="searchExpensesOptions"
            [emitFullSelectOption]="true"
            [disabled]="form.controls['relatedToApplicant'].value === false"
          ></cc-select>
        </div>
        <div class="col-2">
          <cc-checkbox formControlName="relatedToOfficeStaff"
            >Office Staff</cc-checkbox
          >
        </div>
        <div class="col-10">
          <cc-select
            label="Select Expense"
            formControlName="relatedToOfficeStaffExpense"
            [lazyPageFetcher]="searchExpensesOptions"
            [emitFullSelectOption]="true"
            [disabled]="form.controls['relatedToOfficeStaff'].value === false"
          ></cc-select>
        </div>
        <div class="col-2">
          <cc-checkbox formControlName="relatedToTeam">Team</cc-checkbox>
        </div>
        <div class="col-10 grid-container">
          <cc-datagrid
            [data]="teams"
            [columns]="temsGridCols"
            [showPaginator]="false"
            [cellTemplate]="{ selectedExpenses: selectedExpenses1 }"
            [stickyHeader]="true"
          ></cc-datagrid>
          <ng-template #selectedExpenses1 let-row let-index="index">
            <cc-select
              label="Select Expense"
              [lazyPageFetcher]="searchExpensesOptions"
              [emitFullSelectOption]="true"
              [(ngModel)]="teamsRelatedExpense[row.id]"
              [ngModelOptions]="{ standalone: true }"
              [disabled]="form.controls['relatedToTeam'].value === false"
            ></cc-select>
          </ng-template>
        </div>
        <div class="col-12 bordered-bottom-line"></div>
        <div
          class="col-12 row align-items-center"
          *ngIf="form.controls['relatedToMaid'].value"
        >
          <div class="col-6">
            <cc-checkbox formControlName="allowToAddLoan"
              >Allow to Add an Outstanding Balance</cc-checkbox
            >
          </div>
          <div class="col-6">
            <cc-select
              label="Loan Type"
              formControlName="loanType"
              [data]="loanOptions"
              [required]="form.controls['allowToAddLoan'].value"
            ></cc-select>
          </div>
        </div>
        <div class="col-12 row align-items-center">
          <div class="col-auto">
            <cc-checkbox formControlName="allowSubExpense"
              >Allow to create a sub-expense in expense request screen with
              label</cc-checkbox
            >
          </div>
          <div class="col-auto" *ngIf="form.controls['allowSubExpense'].value">
            <cc-input
              label="Sub Expense Label"
              formControlName="subExpenseLabel"
            ></cc-input>
          </div>
        </div>
        <div class="col-12 bordered-bottom-line">
          <h5>Notification Service</h5>
        </div>
        <div class="col-12">
          <cc-select
            label="Notify"
            formControlName="notifyUsers"
            [lazyPageFetcher]="usersOptions"
            [emitFullSelectOption]="true"
            [multiple]="true"
          ></cc-select>
        </div>
        <div class="col-12 row align-items-center">
          <div class="col-2">
            <cc-checkbox formControlName="amountEnabled">If amount</cc-checkbox>
          </div>
          <div class="col-2">
            <cc-select
              label="Operation"
              [disabled]="!form.controls['amountEnabled'].value"
              formControlName="amountOperation"
              [data]="operatorsOptions"
            ></cc-select>
          </div>
          <div class="col-1">
            <span>than</span>
          </div>
          <div class="col-auto">
            <cc-amount-input
              symbol="AED"
              [disabled]="!form.controls['amountEnabled'].value"
              formControlName="amount"
            ></cc-amount-input>
          </div>
        </div>
        <div class="col-12 row align-items-center">
          <div class="col-2">
            <cc-checkbox formControlName="percentageEnabled"
              >If amount</cc-checkbox
            >
          </div>
          <div class="col-auto" style="width: 15%">
            <cc-select
              label="Operation"
              [disabled]="!form.controls['percentageEnabled'].value"
              formControlName="percentageOperation"
              [data]="operatorsOptions"
            ></cc-select>
          </div>
          <div class="col-auto" style="width: 5%">
            <span>than</span>
          </div>
          <div class="col-auto" style="width: 13%">
            <cc-amount-input
              symbol="AED"
              [disabled]="!form.controls['percentageEnabled'].value"
              formControlName="percentage"
            ></cc-amount-input>
          </div>
          <div class="col-auto" style="width: 20%">
            of the average expenses of the last
          </div>
          <div class="col-auto" style="width: 13%">
            <cc-amount-input
              symbol="AED"
              [disabled]="!form.controls['percentageEnabled'].value"
              formControlName="percentageNOCompared"
            ></cc-amount-input>
          </div>
          <div class="col-auto" style="width: 17%">
            <cc-select
              label="Compared To"
              [disabled]="!form.controls['percentageEnabled'].value"
              formControlName="percentageComparedTo"
              [data]="periodTypeOptions"
            ></cc-select>
          </div>
        </div>
        <div class="col-12 row align-items-center">
          <div class="col-6">
            <cc-checkbox formControlName="numberOfRequestsEnabled"
              >If expense request was requested more than
              {{ numberOfRequestsThreshold }} time in</cc-checkbox
            >
          </div>
          <div class="col-6">
            <cc-select
              label="Period"
              formControlName="numberOfRquestComparedTo"
              [data]="periodOptions"
              [disabled]="!form.controls['numberOfRequestsEnabled'].value"
            ></cc-select>
          </div>
        </div>
        <div class="col-12 bordered-bottom-line">
          <h5>Statement Parser</h5>
        </div>
        <div class="col-12">
          <cc-checkbox formControlName="autoDeducted"
            >Auto-deducted expense
          </cc-checkbox>
        </div>
        <div class="col-12">
          <cc-textarea
            label="Name in financial statement"
            formControlName="namesInFinancialStatements"
          ></cc-textarea>
        </div>
        <div class="col-12 px-0 row justify-content-end">
          <div class="col-md-auto px-0">
            <button cc-raised-button (click)="cancel()">Cancel</button>
          </div>
          <div class="col-md-auto">
            <button
              cc-raised-button
              color="primary"
              [disabled]="form.invalid"
              (click)="save()"
            >
              Save
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
