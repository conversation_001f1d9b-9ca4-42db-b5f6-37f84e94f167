import { Component, OnInit } from '@angular/core';
import { ExpensesService } from '../../services/expenses.service';
import { map, Observable } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { CCNotificationService } from '@maids/cc-lib/services';
import { PaginationRequest } from '@maids/cc-lib/common';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
@Component({
  selector: 'app-expense-setup',
  templateUrl: './expense-setup.component.html',
  styleUrls: ['./expense-setup.component.scss'],
})
export class ExpenseSetupComponent implements OnInit {
  requestedFromOptions: any[] = [
    {
      id: 'SHOW_EXPENSE_IN_REQUEST_EXPENSES_PAGE',
      text: 'Show expense in Request Expenses page',
    },
    {
      id: 'SHOW_EXPENSE_IN_MEDICAL_EXPENSES_PAGE',
      text: 'Show expense in Medical Expenses page',
    },
    {
      id: 'SHOW_EXPENSE_IN_ADD_PAYMENTS_TO_MAIDS_PAGE',
      text: 'Show expense in Add Payments to Maids page',
    },
    {
      id: 'SHOW_IN_MAINTENANCE_REQUEST_IN_REQUEST_EXPENSE_PAGE',
      text: 'Show in Maintenance Request in Request Expense Page',
    },
    {
      id: 'SHOW_IN_ADD_NEW_PAYMENT_IN_CASHIER_SCREEN',
      text: 'Show in Add New Payment in Cashier Screen',
    },
    {
      id: 'SHOW_IN_TICKETING_EXPENSE_SCREEN',
      text: 'Show in Ticketing Expense Screen',
    },
    { id: 'SHOW_IN_VIP_EXPENSE_SCREEN', text: 'Show in VIP Expense Screen' },
    { id: 'SHOW_IN_ONE_TIME_REQUEST', text: 'Show in One-Time Request' },
  ];
  loanOptions: any[] = [];
  numberOfRequestsThreshold: any;
  teams: any[] = [];
  id: any;
  isLoaded: boolean = false;
  disableCode: boolean = false;
  form!: FormGroup;
  currentCashDefaultBucket: any;
  currentCreditDefaultBucket: any;
  expenseManagerOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.expensesService
      .getUsers(pageReq.page, pageReq.size, pageReq.searchString || '')
      .pipe(
        map((res: any) => {
          return res && res.content ? res.content.map((item: any) => ({
            text: item.label,
            id: item.id,
          })) : [];
        })
      );
  };
  approvedByOptions: any[] = [];
  readonly bucketOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.expensesService.EsearchBuckets(
      pageReq.page,
      pageReq.size,
      pageReq.searchString || ''
    );
  };
  maidOptions: any[] = [
    { type: 'Maids.cc', selectedExpenses: null },
    { type: 'MaidVisa', selectedExpenses: null },
  ];
  gridCols: CCGridColumn[] = [
    { field: 'type', header: 'Type' },
    { field: 'selectedExpenses', header: 'Select Expense' },
  ];
  temsGridCols: CCGridColumn[] = [
    { field: 'name', header: 'Type' },
    { field: 'selectedExpenses', header: 'Select Expense' },
  ];
  additionTypeOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.expensesService.AdditionReasons(
      pageReq.page,
      pageReq.size,
      pageReq.searchString || ''
    );
  };
  expense: any;
  paymentMethodLoaded: boolean = false;
  paymentMethodsArray: any[] = [
    'CASH',
    'CREDIT_CARD',
    'BANK_TRANSFER',
    'MONEY_TRANSFER',
    'CHEQUE',
    'SALARY',
    'INVOICED',
  ];

  searchExpensesOptionsWithParent = (
    pageReq: PaginationRequest
  ): Observable<any> => {
    return this.expensesService.searchExpensesOptionsWithParent(
      pageReq.page,
      pageReq.size,
      pageReq.searchString || ''
    );
  };
  searchExpensesOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.expensesService.searchExpensesOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString || ''
    );
  };
  usersOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.expensesService
      .getUsers(pageReq.page, pageReq.size, pageReq.searchString || '')
      .pipe(
        map((res: any) => {
          return res && res.content ? res.content.map((item: any) => ({
            text: item.label,
            id: item.id,
          })) : [];
        })
      );
  };
  suppliersOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.expensesService.getActiveSuppliers(
      pageReq.page,
      pageReq.size,
      pageReq.searchString || ''
    );
  };
  teamsRelatedExpense: any[] = [];
  constructor(
    private expensesService: ExpensesService,
    private route: ActivatedRoute,
    private fb: FormBuilder,
    public readonly notifications: CCNotificationService,
    private router: Router
  ) {
    this.route.params.subscribe((params) => {
      this.id = params['id'];
    });
  }
  operatorsOptions: any[] = [
    { id: 'GREATER_THAN', text: '>' },
    { id: 'LESS_THAN', text: '<' },
  ];
  periodTypeOptions: any[] = [
    { id: 'PAYMENT', text: 'Payments' },
    { id: 'MONTH', text: 'Months' },
  ];
  periodOptions: any[] = [
    { id: 'DAY', text: 'a day' },
    { id: 'WEEK', text: 'a week' },
    { id: 'MONTH', text: 'a month' },
    { id: 'YEAR', text: 'a year' },
  ];
  // Flags to prevent infinite recursion in valueChanges subscriptions
  private isUpdatingCashBuckets = false;
  private isUpdatingCreditCardBuckets = false;

  ngOnInit(): void {
    this.form = this.fb.group({
      name: [null],
      parent: [null],
      code: [null],
      caption: [null],
      manager: [null],
      requestedFrom: [null],
      isSecure: [false],
      disabled: [null],
      requireAttachment: [false],
      requireInvoice: [false],
      allowToAddSupplierToExpenseRequest: [false],
      hideRequestRefundButton: [false],
      description: [null],
      requestors: [null],
      approvalMethod: [null],
      approveHolderType: [null],
      approveHolder: [null],
      approveHolderEmail: [null],
      limitForApproval: [null],
      isLimitedCOO: [false],
      limitCOO: [null],
      paymentMethods: this.fb.array(
        this.paymentMethodsArray.map((method) => {
          return this.fb.group({
            value: [false],
            label: [method],
            paidOnTheSpotCash: [null],
            fromCashBuckets: [null],
            fromCashBucket: [null],
            paidOnTheSpotCreditCard: [null],
            fromCreditCardBuckets: [null],
            fromCreditCardBucket: [null],
            salaryAdditionType: [null],
          });
        })
      ),
      defaultAmount: [null],
      beneficiaryType: [null],
      suppliers: [null],
      relatedToMaid: [false],
      relatedToMaidCC: [null],
      relatedToMaidVisa: [null],
      relatedToApplicant: [false],
      relatedToApplicantExpense: [null],
      relatedToOfficeStaff: [false],
      relatedToOfficeStaffExpense: [null],
      relatedToTeam: [false],
      allowToAddLoan: [null],
      loanType: [null],
      allowSubExpense: [null],
      subExpenseLabel: [null],
      notifyUsers: [null],
      amountEnabled: [null],
      amountOperation: [null],
      amount: [null],
      percentageEnabled: [null],
      percentageOperation: [null],
      percentage: [null],
      percentageNOCompared: [null],
      percentageComparedTo: [null],
      numberOfRequestsEnabled: [null],
      numberOfRquestComparedTo: [null],
      autoDeducted: [null],
      namesInFinancialStatements: [null],
      notificationId: [null],
    });

    this.expensesService.getRequestedFromOpts().subscribe((res) => {
      this.requestedFromOptions = res;
    });

    this.expensesService.EgetLoanTypes().subscribe((res) => {
      this.loanOptions = res;
    });
    this.expensesService.EgetParameter().subscribe((res) => {
      this.numberOfRequestsThreshold = res && res[0] ? res[0].value : null;
    });
    this.expensesService.EgetOfficeStaffTeam().subscribe({
      next: (res) => {
        this.teams = res;
        res.forEach((item: any) => {
          this.teamsRelatedExpense.push(null);
        });
        if (this.id) {
          this.expensesService.EgetExpenses(this.id).subscribe({
            next: (response) => {
              this.isLoaded = true;
              this.form.patchValue({
                name: response.name,
                code: response.code,
                caption: response.caption,
                parent: response.parent
                  ? {
                      id: response.parent.id,
                      text: response.parent.label,
                    }
                  : null,
                manager: response.manager
                  ? {
                      id: response.manager.id,
                      text: response.manager.label,
                    }
                  : null,
                requestedFrom: response.requestedFrom
                  ? {
                      id: response.requestedFrom.id,
                      text: response.requestedFrom.label,
                    }
                  : null,
                approveHolder: response.approveHolder
                  ? {
                      id: response.approveHolder.id,
                      text: response.approveHolder.label,
                    }
                  : null,
                isSecure: !!response.isSecure,
                disabled: !!response.disabled,
                hideRequestRefundButton: !!response.hideRequestRefundButton,
                requireAttachment: response.requireAttachment,
                requireInvoice: !!response.requireInvoice,
                allowToAddSupplierToExpenseRequest:
                  !!response.allowToAddSupplierToExpenseRequest,
                requestors:
                  response?.requestors?.length > 0
                    ? response.requestors.map((item: any) => {
                        return { id: item.id, text: item.label };
                      })
                    : [],
                approvalMethod: response.approvalMethod ? response.approvalMethod.value : null,
                approveHolderType: response.approveHolderType ? response.approveHolderType.value : null,
                approveHolderEmail: response.approveHolderEmail,
                limitForApproval: response.limitForApproval,
                isLimitedCOO: !!response.isLimitedCOO,
                limitCOO: response.limitCOO,
                defaultAmount: response.defaultAmount,
                beneficiaryType: response.beneficiaryType ? response.beneficiaryType.value : null,
                allowToAddLoan: !!response.loanType,
                loanType: response.loanType ? response.loanType.value : null,
                notificationId: response.notification ? response.notification.id : null,
                notifyUsers: response.notification && response.notification.notifyUsers
                  ? response.notification.notifyUsers.map((item: any) => {
                      return { id: item.id, text: item.label };
                    })
                  : [],
                amountOperation: response?.notification?.amountOperation?.value,
                percentageComparedTo:
                  response?.notification?.percentageComparedTo,
                percentageEnabled: response?.notification?.percentageEnabled,
                percentageOperation:
                  response?.notification?.percentageOperation?.value,
                amountEnabled: response?.notification?.amountEnabled,
                amount: response?.notification?.amount,
                percentage: response?.notification?.percentage,
                percentageNOCompared:
                  response?.notification?.percentageNOCompared,
                numberOfRequestsEnabled:
                  response?.notification?.numberOfRequestsEnabled,
                numberOfRquestComparedTo:
                  response?.notification?.numberOfRquestComparedTo,
                allowSubExpense: !!response.subExpenseLabel,
                subExpenseLabel: response.subExpenseLabel,
                autoDeducted: response.autoDeducted,
                suppliers:
                  response?.suppliers?.length > 0
                    ? response.suppliers.map((item: any) => {
                        return { id: item.id, text: item.label };
                      })
                    : [],
                namesInFinancialStatements:
                  response.namesInFinancialStatements.join('\n'),
                description: response.description,
              });
              response.relatedTos?.forEach((item: any) => {
                if (item.relatedToType.label === 'MAID') {
                  this.form.controls['relatedToMaid'].setValue(true);
                  if (item.expenseCC && item.expenseCC.id) {
                    this.form.controls['relatedToMaidCC'].setValue({
                      id: item.expenseCC.id,
                      text: item.expenseCC.label,
                    });
                  }
                  if (item.expenseVisa && item.expenseVisa.id) {
                    this.form.controls['relatedToMaidVisa'].setValue({
                      id: item.expenseVisa.id,
                      text: item.expenseVisa.label,
                    });
                  }
                }
                if (item.relatedToType.label === 'OFFICE_STAFF') {
                  this.form.controls['relatedToOfficeStaff'].setValue(true);
                  if (item.relatedExpense && item.relatedExpense.id) {
                    this.form.controls['relatedToOfficeStaffExpense'].setValue({
                      id: item.relatedExpense.id,
                      text: item.relatedExpense.label,
                    });
                  }
                }
                if (item.relatedToType.label === 'APPLICANT') {
                  this.form.controls['relatedToApplicant'].setValue(true);
                  if (item.relatedExpense && item.relatedExpense.id) {
                    this.form.controls['relatedToApplicantExpense'].setValue({
                      id: item.relatedExpense.id,
                      text: item.relatedExpense.label,
                    });
                  }
                }
                if (item.relatedToType.label === 'TEAM') {
                  this.form.controls['relatedToTeam'].setValue(true);
                  item.teams.forEach((team: any) => {
                    if (team.relatedExpense && team.relatedExpense.id) {
                      this.teamsRelatedExpense[team.team.id] = {
                        id: team.relatedExpense.id,
                        text: team.relatedExpense.label,
                      };
                    }
                  });
                }
              });
              response.paymentMethods?.forEach((method: any) => {
                this.paymentMethods
                  .at(this.paymentMethodsArray.indexOf(method.value))
                  .patchValue({
                    value: true,
                    label: method.value,
                    fromCashBuckets:
                      response.fromCashBuckets &&
                      Array.isArray(response.fromCashBuckets)
                        ? response.fromCashBuckets.map((item: any) => {
                            return {
                              id: item.id,
                              text: item.label,
                            };
                          })
                        : [],
                    fromCreditCardBuckets:
                      response.fromCreditCardBuckets &&
                      Array.isArray(response.fromCreditCardBuckets)
                        ? response.fromCreditCardBuckets.map((item: any) => {
                            return {
                              id: item.id,
                              text: item.label,
                            };
                          })
                        : [],
                    fromCashBucket:
                      method.value === 'CASH' && response.fromCashBucket
                        ? {
                            id: response.fromCashBucket.id,
                            text: response.fromCashBucket.label,
                          }
                        : null,
                    fromCreditCardBucket:
                      method.value === 'CREDIT_CARD' && response.fromCreditCardBucket
                        ? {
                            id: response.fromCreditCardBucket.id,
                            text: response.fromCreditCardBucket.label,
                          }
                        : null,
                    paidOnTheSpotCash:
                      method.value === 'CASH'
                        ? response.paidOnTheSpotCash
                        : false,
                    paidOnTheSpotCreditCard:
                      method.value === 'CREDIT_CARD'
                        ? response.paidOnTheSpotCreditCard
                        : false,
                    salaryAdditionType:
                      method.value === 'SALARY' && response.salaryAdditionType
                        ? {
                            id: response.salaryAdditionType.id,
                            text: response.salaryAdditionType.label,
                          }
                        : null,
                  });
              });
              this.expense = Object.assign({}, this.form.value);
              // this.paymentMethodLoaded = true;
            },
            error: (err) => {
              this.notifications.notifyError(err?.error?.message);
            },
          });
        }
      },
      error: (err) => {
        this.notifications.notifyError(err?.error?.message);
      },
    });
    this.form.controls['parent'].valueChanges.subscribe((value) => {
      if (value && value.code) {
        this.isLoaded = false;
        this.expensesService.getSubExpenseCode(value.id).subscribe((res) => {
          this.form.controls['code'].setValue(res);
        });
        this.disableCode = true;
      } else if (!value && !this.isLoaded) {
        this.form.controls['code'].setValue(null);
        this.disableCode = false;
      }
    });
    this.paymentMethods.at(0).valueChanges.subscribe((value) => {
      if (this.isUpdatingCashBuckets) {
        return;
      }

      if (value && value.value == false) {
        // Set flag to prevent recursion
        this.isUpdatingCashBuckets = true;
        this.paymentMethods.at(0).patchValue(
          {
            value: false,
            label: 'CASH',
            paidOnTheSpotCash: false,
            fromCashBuckets: [],
            fromCashBucket: null,
          },
          { emitEvent: false }
        );

        // Clear validators when cash is not selected
        // Use setTimeout to break the potential infinite loop
        setTimeout(() => {
          const cashFormGroup = this.paymentMethods.at(0) as FormGroup;
          cashFormGroup.get('fromCashBuckets')?.clearValidators();
          cashFormGroup.get('fromCashBucket')?.clearValidators();
          cashFormGroup
            .get('fromCashBuckets')
            ?.updateValueAndValidity({ emitEvent: false });
          cashFormGroup
            .get('fromCashBucket')
            ?.updateValueAndValidity({ emitEvent: false });

          // Reset flag after validation
          this.isUpdatingCashBuckets = false;
        }, 0);
      } else if (
        value &&
        value.value == true &&
        value.paidOnTheSpotCash == true
      ) {
        this.isUpdatingCashBuckets = true;
        setTimeout(() => {
          const cashFormGroup = this.paymentMethods.at(0) as FormGroup;
          cashFormGroup
            .get('fromCashBuckets')
            ?.setValidators([Validators.required]);
          cashFormGroup
            .get('fromCashBucket')
            ?.setValidators([Validators.required]);
          cashFormGroup
            .get('fromCashBuckets')
            ?.updateValueAndValidity({ emitEvent: false });
          cashFormGroup
            .get('fromCashBucket')
            ?.updateValueAndValidity({ emitEvent: false });
          this.isUpdatingCashBuckets = false;
        }, 0);

        if (value.fromCashBucket?.id) {
          const isNewDefaultBucket =
            this.currentCashDefaultBucket !== value.fromCashBucket.id;
          this.currentCashDefaultBucket = value.fromCashBucket.id;
          if (
            isNewDefaultBucket &&
            !value.fromCashBuckets?.some(
              (bucket: any) => bucket.id === value.fromCashBucket.id
            )
          ) {
            const buckets = value.fromCashBuckets || [];
            this.isUpdatingCashBuckets = true;
            this.paymentMethods.at(0).patchValue(
              {
                ...value,
                fromCashBuckets: [...buckets, value.fromCashBucket],
              },
              { emitEvent: false }
            );
            setTimeout(() => (this.isUpdatingCashBuckets = false), 0);
            return;
          }
        }
        if (
          value.fromCashBucket?.id &&
          !value.fromCashBuckets?.some(
            (bucket: any) => bucket.id === value.fromCashBucket.id
          )
        ) {
          const buckets = value.fromCashBuckets || [];
          this.isUpdatingCashBuckets = true;
          this.paymentMethods.at(0).patchValue(
            {
              ...value,
              fromCashBuckets: [...buckets, value.fromCashBucket],
            },
            { emitEvent: false }
          );
          setTimeout(() => {
            this.isUpdatingCashBuckets = false;
            this.notifications.notifyError('You cannot delete default bucket');
          }, 0);
        }
      } else if (
        value &&
        value.value == true &&
        value.paidOnTheSpotCash == false
      ) {
        this.isUpdatingCashBuckets = true;
        this.paymentMethods.at(0).patchValue(
          {
            fromCashBuckets: [],
            fromCashBucket: null,
          },
          { emitEvent: false }
        );
        const cashFormGroup = this.paymentMethods.at(0) as FormGroup;
        cashFormGroup.get('fromCashBuckets')?.clearValidators();
        cashFormGroup.get('fromCashBucket')?.clearValidators();
        cashFormGroup.get('fromCashBuckets')?.updateValueAndValidity();
        cashFormGroup.get('fromCashBucket')?.updateValueAndValidity();
        setTimeout(() => (this.isUpdatingCashBuckets = false), 0);
      }
    });
    this.paymentMethods.at(1).valueChanges.subscribe((value) => {
      if (this.isUpdatingCreditCardBuckets) {
        return;
      }

      if (value && value.value == false) {
        this.isUpdatingCreditCardBuckets = true;
        this.paymentMethods.at(1).patchValue(
          {
            value: false,
            label: 'CREDIT_CARD',
            paidOnTheSpotCreditCard: false,
            fromCreditCardBuckets: [],
            fromCreditCardBucket: null,
          },
          { emitEvent: false }
        );
        setTimeout(() => {
          const creditCardFormGroup = this.paymentMethods.at(1) as FormGroup;
          creditCardFormGroup.get('fromCreditCardBuckets')?.clearValidators();
          creditCardFormGroup.get('fromCreditCardBucket')?.clearValidators();
          creditCardFormGroup
            .get('fromCreditCardBuckets')
            ?.updateValueAndValidity({ emitEvent: false });
          creditCardFormGroup
            .get('fromCreditCardBucket')
            ?.updateValueAndValidity({ emitEvent: false });
          this.isUpdatingCreditCardBuckets = false;
        }, 0);
      } else if (
        value &&
        value.value == true &&
        value.paidOnTheSpotCreditCard == true
      ) {
        this.isUpdatingCreditCardBuckets = true;
        setTimeout(() => {
          const creditCardFormGroup = this.paymentMethods.at(1) as FormGroup;
          creditCardFormGroup
            .get('fromCreditCardBuckets')
            ?.setValidators([Validators.required]);
          creditCardFormGroup
            .get('fromCreditCardBucket')
            ?.setValidators([Validators.required]);
          creditCardFormGroup
            .get('fromCreditCardBuckets')
            ?.updateValueAndValidity({ emitEvent: false });
          creditCardFormGroup
            .get('fromCreditCardBucket')
            ?.updateValueAndValidity({ emitEvent: false });
          this.isUpdatingCreditCardBuckets = false;
        }, 0);

        if (value.fromCreditCardBucket?.id) {
          const isNewDefaultBucket =
            this.currentCreditDefaultBucket !== value.fromCreditCardBucket.id;
          this.currentCreditDefaultBucket = value.fromCreditCardBucket.id;
          if (
            isNewDefaultBucket &&
            !value.fromCreditCardBuckets?.some(
              (bucket: any) => bucket.id === value.fromCreditCardBucket.id
            )
          ) {
            const buckets = value.fromCreditCardBuckets || [];
            this.isUpdatingCreditCardBuckets = true;
            this.paymentMethods.at(1).patchValue(
              {
                ...value,
                fromCreditCardBuckets: [...buckets, value.fromCreditCardBucket],
              },
              { emitEvent: false }
            );
            setTimeout(() => (this.isUpdatingCreditCardBuckets = false), 0);
            return;
          }
        }
        if (
          value.fromCreditCardBucket?.id &&
          !value.fromCreditCardBuckets?.some(
            (bucket: any) => bucket.id === value.fromCreditCardBucket.id
          )
        ) {
          const buckets = value.fromCreditCardBuckets || [];
          this.isUpdatingCreditCardBuckets = true;
          this.paymentMethods.at(1).patchValue(
            {
              ...value,
              fromCreditCardBuckets: [...buckets, value.fromCreditCardBucket],
            },
            { emitEvent: false }
          );
          setTimeout(() => {
            this.isUpdatingCreditCardBuckets = false;
            this.notifications.notifyError('You cannot delete default bucket');
          }, 0);
        }
      } else if (
        value &&
        value.value == true &&
        value.paidOnTheSpotCreditCard == false
      ) {
        this.isUpdatingCreditCardBuckets = true;
        this.paymentMethods.at(1).patchValue(
          {
            fromCreditCardBuckets: [],
            fromCreditCardBucket: null,
          },
          { emitEvent: false }
        );
        const creditCardFormGroup = this.paymentMethods.at(1) as FormGroup;
        creditCardFormGroup.get('fromCreditCardBuckets')?.clearValidators();
        creditCardFormGroup.get('fromCreditCardBucket')?.clearValidators();
        creditCardFormGroup
          .get('fromCreditCardBuckets')
          ?.updateValueAndValidity();
        creditCardFormGroup
          .get('fromCreditCardBucket')
          ?.updateValueAndValidity();
        setTimeout(() => (this.isUpdatingCreditCardBuckets = false), 0);
      }
    });
  }
  get paymentMethods(): FormArray {
    return this.form.get('paymentMethods') as FormArray;
  }

  get isCashSelected(): boolean {
    return this.paymentMethods?.value.some(
      (pm: any) => pm.value && pm.label === 'CASH'
    );
  }
  get isCreditCardSelected(): boolean {
    return this.paymentMethods?.value.some(
      (pm: any) => pm.value && pm.label === 'CREDIT_CARD'
    );
  }
  save() {
    if (this.form.value.autoDeducted) {
      if (this.form.value.beneficiaryType !== 'SUPPLIER') {
        this.notifications.notifyError(
          'Beneficiary type should be SUPPLIER and should select one at least.'
        );
        return;
      }
      if (
        this.form.value.beneficiaryType === 'SUPPLIER' &&
        this.form.value.suppliers && this.form.value.suppliers.length === 0
      ) {
        this.notifications.notifyError('Please select one supplier at least.');
        return;
      }
    }
    if (this.form.valid) {
      let payload: any = {};
      const salaryPM = this.paymentMethods.value.find(
        (pm: any) => pm.label === 'SALARY' && pm.value
      );

      payload = {
        name: this.form.value.name,
        code: this.form.value.code,
        caption: this.form.value.caption,
        parent: this.form.value.parent
          ? { id: +this.form.value.parent.id }
          : null,
        manager: this.form.value.manager
          ? { id: this.form.value.manager.id }
          : null,

        requestedFrom: this.form.value.requestedFrom
          ? {
              id: +this.form.value.requestedFrom.id,
            }
          : null,
        isSecure: this.form.value.isSecure,
        autoDeducted: this.form.value.autoDeducted,
        description: this.form.value.description,
        hideRequestRefundButton: this.form.value.hideRequestRefundButton,
        disabled: this.form.value.disabled,
        requireAttachment: this.form.value.requireAttachment,
        requireInvoice: this.form.value.requireInvoice,
        allowToAddSupplierToExpenseRequest:
          this.form.value.allowToAddSupplierToExpenseRequest,
        approvalMethod: this.form.value.approvalMethod,
        approveHolderType: this.form.value.approveHolderType,
        limitForApproval: this.form.value.limitForApproval,
        approveHolder:
          (this.form.value.approveHolderType !== 'EMAIL' ||
            this.form.value.approveHolderType !== 'FINAL_MANAGER') &&
          this.form.value.approveHolderType == 'USER' &&
          this.form.value.approveHolder &&
          this.form.value.approveHolder.id
            ? {
                id: this.form.value.approveHolder.id,
              }
            : null,
        approveHolderEmail:
          (this.form.value.approveHolderType !== 'USER' ||
            this.form.value.approveHolderType !== 'FINAL_MANAGER') &&
          this.form.value.approveHolderType == 'EMAIL'
            ? this.form.value.approveHolderEmail
            : null,
        isLimitedCOO: this.form.value.isLimitedCOO,
        limitCOO: this.form.value.isLimitedCOO
          ? this.form.value.limitCOO
          : null,

        allowSubExpense: this.form.value.allowSubExpense,
        allowToAddLoan: this.form.value.allowToAddLoan,
        subExpenseLabel: this.form.value.allowSubExpense
          ? this.form.value.subExpenseLabel
          : null,
        loanType: this.form.value.allowToAddLoan
          ? this.form.value.loanType
          : null,
        namesInFinancialStatements: this.form.value.namesInFinancialStatements
          ? this.form.value.namesInFinancialStatements.split('\n')
          : [],
        notification: {
          id: this.form.value.notificationId,
          amountEnabled: this.form.value.amountEnabled,
          amountOperation: this.form.value.amountOperation,
          amount: this.form.value.amount,
          percentageEnabled: this.form.value.percentageEnabled,
          percentageOperation: this.form.value.percentageOperation,
          percentage: this.form.value.percentage,
          percentageNOCompared: this.form.value.percentageNOCompared,
          percentageComparedTo: this.form.value.percentageComparedTo,
          numberOfRequestsEnabled: this.form.value.numberOfRequestsEnabled,
          numberOfRquestComparedTo: this.form.value.numberOfRquestComparedTo,
          notifyUsers: this.form.value.notifyUsers && this.form.value.notifyUsers.length > 0
            ? this.form.value.notifyUsers.map((item: any) => {
                return { id: item.id };
              })
            : [],
        },
        requestors: this.form.value.requestors
          ? this.form.value.requestors.map((item: any) => {
              return { id: item.id };
            })
          : [],
        salaryAdditionType:
          salaryPM &&
          salaryPM.salaryAdditionType &&
          salaryPM.salaryAdditionType.id
            ? { id: +salaryPM.salaryAdditionType.id }
            : null,
        defaultAmount: this.form.value.defaultAmount
          ? this.form.value.defaultAmount
          : null,
        beneficiaryType: this.form.value.beneficiaryType
          ? this.form.value.beneficiaryType
          : null,
        suppliers:
          this.form.value.beneficiaryType == 'SUPPLIER' &&
          this.form.value.suppliers &&
          this.form.value.suppliers.length > 0
            ? this.form.value.suppliers.map((supplier: any) => {
                return { id: supplier.id };
              })
            : [],
      };
      payload.relatedTos = [];
      if (this.form.value.relatedToApplicant) {
        payload.relatedTos.push({
          relatedToType: 'APPLICANT',
          relatedExpense: this.form.value.relatedToApplicantExpense
            ? {
                id: +this.form.value.relatedToApplicantExpense.id,
              }
            : null,
        });
      }
      if (this.form.value.relatedToOfficeStaff) {
        payload.relatedTos.push({
          relatedToType: 'OFFICE_STAFF',
          relatedExpense: this.form.value.relatedToOfficeStaffExpense
            ? {
                id: +this.form.value.relatedToOfficeStaffExpense.id,
              }
            : null,
        });
      }
      if (this.form.value.relatedToMaid) {
        let maidsExpenses: any = {};
        if (this.form.value.relatedToMaidCC) {
          maidsExpenses.expenseCC = {
            id: +this.form.value.relatedToMaidCC.id,
          };
        }
        if (this.form.value.relatedToMaidVisa) {
          maidsExpenses.expenseVisa = {
            id: +this.form.value.relatedToMaidVisa.id,
          };
        }
        payload.relatedTos.push({
          relatedToType: 'MAID',
          ...maidsExpenses,
        });
      }
      if (this.form.value.relatedToTeam) {
        let teams: any[] = [];
        this.teamsRelatedExpense.forEach((item: any, index: number) => {
          if (!!item) {
            let team: any = {
              team: { id: index },
              relatedExpense: { id: item.id },
            };
            teams.push(team);
          }
        });
        payload.relatedTos.push({
          relatedToType: 'TEAM',
          teams,
        });
      }
      payload.paymentMethods = [];
      this.paymentMethods?.value.forEach((pm: any) => {
        if (pm.value) {
          payload.paymentMethods.push(pm.label);
        }
      });
      let pm0 = this.paymentMethods.at(0).value;
      let pm1 = this.paymentMethods.at(1).value;
      if (pm0.value) {
        if (pm0.paidOnTheSpotCash) {
          payload.paidOnTheSpotCash = true;
          payload.fromCashBucket = pm0.fromCashBucket ? { id: pm0.fromCashBucket.id } : null;
          payload.fromCashBuckets = pm0.fromCashBuckets ? pm0.fromCashBuckets.map((bucket: any) => {
            return { id: +bucket.id };
          }) : [];
        } else {
          payload.paidOnTheSpotCash = false;
          payload.fromCashBuckets = [];
          payload.fromCashBucket = null;
        }
      } else {
        payload.paidOnTheSpotCash = false;
        payload.fromCashBuckets = [];
        payload.fromCashBucket = null;
      }
      if (pm1.value) {
        if (pm1.paidOnTheSpotCreditCard) {
          payload.paidOnTheSpotCreditCard = true;
          payload.fromCreditCardBucket = pm1.fromCreditCardBucket ? { id: pm1.fromCreditCardBucket.id } : null;
          payload.fromCreditCardBuckets = pm1.fromCreditCardBuckets ? pm1.fromCreditCardBuckets.map(
            (bucket: any) => {
              return { id: +bucket.id };
            }
          ) : [];
        } else {
          payload.paidOnTheSpotCreditCard = false;
          payload.fromCreditCardBuckets = [];
          payload.fromCreditCardBucket = null;
        }
      } else {
        payload.paidOnTheSpotCreditCard = false;
        payload.fromCreditCardBuckets = [];
        payload.fromCreditCardBucket = null;
      }
      if (this.id) {
        payload.id = this.id;
        this.expensesService.updateExpense(payload).subscribe(
          (res) => {
            this.notifications.notifySuccess('Expense updated successfully');
            this.router.navigateByUrl('/accounting/v2/expenses');
          },
          (err) => {
            this.notifications.notifyError(err?.error?.message);
          }
        );
      } else {
        this.expensesService.createExpense(payload).subscribe(
          (res) => {
            this.notifications.notifySuccess('Expense created successfully');
            this.router.navigateByUrl('/accounting/v2/expenses');
          },
          (err) => {
            this.notifications.notifyError(err?.error?.message);
          }
        );
      }
    }
  }
  cancel() {
    this.router.navigateByUrl('/accounting/v2/expenses');
  }
}
