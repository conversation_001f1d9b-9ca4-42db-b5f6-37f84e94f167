<div class="acc-6302">
  <cc-accordion>
    <cc-panel>
      <cc-panel-title>Basic Search</cc-panel-title>
      <cc-panel-body>
        <form [formGroup]="form" (ngSubmit)="search()" class="row">
          <div class="col-4">
            <cc-input
              label="Expense Name / Code"
              formControlName="searchName"
            ></cc-input>
          </div>
          <div class="col-4">
            <cc-select
              label="Approved By Type"
              [data]="approvedByTypeOptions"
              formControlName="approveHolderType"
            ></cc-select>
          </div>
          <div
            class="col-4"
            *ngIf="form.controls['approveHolderType'].value == 'EMAIL'"
          >
            <cc-input
              label="Approved By"
              formControlName="approveHolderEmail"
              [required]="form.controls['approveHolderType'].value == 'EMAIL'"
            ></cc-input>
          </div>
          <div
            class="col-4"
            *ngIf="form.controls['approveHolderType'].value == 'USER'"
          >
            <cc-select
              label="Approved By"
              [lazyPageFetcher]="approvedByUserOptions"
              formControlName="approveHolder"
              [required]="form.controls['approveHolderType'].value == 'USER'"
            ></cc-select>
          </div>
          <div class="col-4">
            <cc-select
              label="CEO Approval"
              [data]="isLimitedCooOptions"
              formControlName="isLimitedCOO"
            ></cc-select>
          </div>
          <div class="col-4">
            <cc-select
              label="Requested From"
              [data]="requestedFromOptions"
              formControlName="requestedFrom"
              [emitFullSelectOption]="true"
            ></cc-select>
          </div>
          <div class="col-4">
            <cc-select
              label="Disabled Status"
              [data]="disabledStatusOptions"
              formControlName="disabledStatus"
            ></cc-select>
          </div>
        </form>
        <div class="row justify-content-center">
          <div class="col-md-auto">
            <button
              cc-raised-button
              color="primary"
              type="submit"
              [disabled]="form.invalid"
              (click)="search()"
            >
              Search
            </button>
          </div>
        </div>
      </cc-panel-body>
    </cc-panel>
  </cc-accordion>
  <div class="row justify-content-end my-2">
    <div class="col-md-auto">
      <button cc-raised-button (click)="create()">Add Expense</button>
    </div>
  </div>
  <cc-datagrid
    class="my-2"
    [data]="records?.content ?? []"
    [columns]="gridCols"
    [length]="records?.totalElements ?? 0"
    [pageOnFront]="false"
    [pageIndex]="records?.number ?? 0"
    [pageSize]="records?.size ?? 20"
    [pageSizeOptions]="[20]"
    (page)="getNextPage($event)"
    (sortChange)="onSortChange($event)"
    [stickyHeader]="true"
    [columnMovable]="true"
    [columnHideable]="true"
    [showColumnMenuButton]="true"
    [showColumnMenuHeader]="false"
    [columnMenuButtonIcon]="'settings'"
    [expandable]="true"
    [expansionTemplate]="expansionTemplate"
    collapseIcon="add"
    expandeIcon="minimize"
    [expansionCondition]="mycondition"
    [rowClassFormatter]="rowClassFormatter"
    [rowStriped]="false"
    [rowHover]="false"
    [cellTemplate]="{ name: name }"
    [noResultTemplate]="noResult1"
  ></cc-datagrid>
  <ng-template #name let-row>
    <a class="cc-secondary" style="cursor: pointer" (click)="update(row.id)">{{
      row.name
    }}</a>
  </ng-template>
  <ng-template #noResult1> </ng-template>
  <ng-template #expansionTemplate let-row>
    <cc-datagrid
      class="w-100"
      [data]="row?.children ?? []"
      [columns]="childgridCols"
      [showPaginator]="false"
      [rowStriped]="false"
      [rowClassFormatter]="childClassFormatter"
      [rowHover]="false"
      [cellTemplate]="{ name: name1 }"
      [noResultTemplate]="noResult2"
    ></cc-datagrid>
  </ng-template>
  <ng-template #name1 let-row>
    <a class="cc-secondary" style="cursor: pointer" (click)="update(row.id)">{{
      row.name
    }}</a>
  </ng-template>
  <ng-template #noResult2> </ng-template>
</div>
