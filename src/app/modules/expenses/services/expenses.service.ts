import { HttpClient, HttpContext } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import {
  CCBackendEndpoint,
  REQ_SHOW_LOADING_ICON,
} from '@maids/cc-erp-services';
import { BehaviorSubject, map, Observable } from 'rxjs';
import { API } from 'src/environments/api';

@Injectable({
  providedIn: 'root',
})
export class ExpensesService {
  initialSearch: any = {
    params: {
      page: 0,
      size: 20,
      sort: '',
      search: '',
      activeFilter: '',
    },
  };
  searchSubject = new BehaviorSubject<any>(this.initialSearch);
  constructor(
    private _http: HttpClient,
    @Inject(CCBackendEndpoint) private _api: CCBackendEndpoint
  ) {}
  getUsers(
    page: number = 0,
    size: number = 50,
    search: string
  ): Observable<any> {
    const context = new HttpContext().set(REQ_SHOW_LOADING_ICON, false);
    return this._http.get(`${this._api}/${API.EgetUsers}`, {
      context,
      params: {
        page,
        size,
        search,
      },
    });
  }
  getRequestedFromOpts(): Observable<any> {
    return this._http.get(`${this._api}/${API.getRequestedFromOpts}`, {}).pipe(
      map((res: any) => {
        return res.map((item: any) => ({
          text: item.name,
          id: item.id,
          code: item.code,
        }));
      })
    );
  }
  searchExpensesOptionsWithParent(
    page: number = 0,
    size: number = 50,
    search: string
  ): Observable<any> {
    const context = new HttpContext().set(REQ_SHOW_LOADING_ICON, false);
    return this._http
      .get(`${this._api}/${API.EsearchExpensesNew}`, {
        params: {
          page,
          size,
          search,
          onlyParent: true,
        },
        context,
      })
      .pipe(
        map((res: any) => {
          return res.content.map((item: any) => ({
            text: item.name,
            id: item.id,
            code: item.code,
            childs: item.children,
          }));
        })
      );
  }
  searchExpensesOptions(
    page: number = 0,
    size: number = 50,
    search: string
  ): Observable<any> {
    const context = new HttpContext().set(REQ_SHOW_LOADING_ICON, false);
    return this._http
      .get(`${this._api}/${API.EsearchExpensesNew}`, {
        params: {
          page,
          size,
          search,
        },
        context,
      })
      .pipe(
        map((res: any) => {
          return res.content.map((item: any) => ({
            text: item.name,
            id: item.id,
            code: item.code,
            childs: item.children,
          }));
        })
      );
  }
  getExpensesList(data: any): Observable<any> {
    return this._http.post(`${this._api}/${API.EsearchExpensesNew}`, data, {
      params: {
        ...this.searchSubject.getValue().params,
        onlyParent: true,
        withStats: true,
      },
    });
  }
  deleteExpense(id: number): Observable<any> {
    return this._http.post(`${this._api}/${API.EdeleteExpense}/${id}`, {});
  }
  enableDisableExpense(
    id: any,
    enable: boolean,
    userInformed: boolean
  ): Observable<any> {
    return this._http.post(
      `${this._api}/${API.enableDisableExpense}/${id}`,
      {},
      { params: { enable, userInformed } }
    );
  }
  EgetLoanTypes(): Observable<any> {
    return this._http.get(`${this._api}/${API.EgetLoanTypes}`).pipe(
      map((res: any) => {
        return res.map((item: any) => ({
          text: item.text,
          id: item.id,
        }));
      })
    );
  }
  EgetParameter(): Observable<any> {
    return this._http.get(`${this._api}/${API.EgetParameter}`, {
      params: { code: 'expense_notification_number_of_requests_threshold' },
    });
  }
  EgetExpenses(id: string): Observable<any> {
    return this._http.get(`${this._api}/${API.EgetExpenses(id)}`);
  }
  EgetOfficeStaffTeam(): Observable<any> {
    return this._http.get(`${this._api}/${API.EgetOfficeStaffTeam}`);
  }
  EsearchBuckets(
    page: number = 0,
    size: number = 50,
    search: string
  ): Observable<any> {
    const context = new HttpContext().set(REQ_SHOW_LOADING_ICON, false);
    return this._http
      .get(`${this._api}/${API.EsearchBuckets}`, {
        params: {
          page,
          size,
          search,
        },
        context,
      })
      .pipe(
        map((res: any) => {
          return res.content.map((item: any) => ({
            text: item.name,
            id: item.id,
          }));
        })
      );
  }
  getActiveSuppliers(
    page: number = 0,
    size: number = 50,
    search: string
  ): Observable<any> {
    const context = new HttpContext().set(REQ_SHOW_LOADING_ICON, false);
    return this._http
      .get(`${this._api}/${API.getActiveSuppliers}`, {
        params: {
          page,
          size,
          search,
        },
        context,
      })
      .pipe(
        map((res: any) => {
          return res.content.map((item: any) => ({
            text: item.name,
            id: item.id,
          }));
        })
      );
  }
  AdditionReasons(
    page: number = 0,
    size: number = 50,
    search: string
  ): Observable<any> {
    return this._http
      .get(`${this._api}/${API.AdditionReasons}`, {
        params: {
          page,
          size,
          search,
        },
      })
      .pipe(
        map((res: any) => {
          return res.map((item: any) => ({
            text: item.label,
            id: item.id,
          }));
        })
      );
  }
  getSubExpenseCode(id: string): Observable<any> {
    return this._http.get(`${this._api}/${API.getSubExpenseCode(id)}`);
  }
  createExpense(data: any): Observable<any> {
    return this._http.post(`${this._api}/${API.createExpense}`, data);
  }
  updateExpense(data: any): Observable<any> {
    return this._http.post(`${this._api}/${API.updateExpense}`, data);
  }
}
