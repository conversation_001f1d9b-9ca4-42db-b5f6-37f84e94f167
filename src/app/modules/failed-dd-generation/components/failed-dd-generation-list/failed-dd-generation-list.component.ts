import { Component, OnInit } from '@angular/core';
import { PageableResponseModel } from '@maids/cc-lib/common';
import { map, Observable } from 'rxjs';
import { FailedDdGeneration } from '../../model/failed-dd-generation';
import { FailedDdGenerationService } from '../../services/failed-dd-generation-service.service';
import { PageEvent } from '@angular/material/paginator';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { CCNotificationService } from '@maids/cc-lib/services';
import { CCDialog } from '@maids/cc-lib/dialog';

@Component({
  selector: 'app-failed-dd-generation-list',
  templateUrl: './failed-dd-generation-list.component.html',
  styleUrls: ['./failed-dd-generation-list.component.scss'],
})
export class FailedDdGenerationListComponent implements OnInit {
  generation: any;
  gridCols: CCGridColumn[] = [
    { field: 'check', header: '' },
    { field: 'client', header: 'Family Name' },
    { field: 'amount', header: 'DD Amount' },
    { field: 'ddsendData', header: 'DDStart Date' },
    { field: 'ddexpiryDate', header: 'DDEnd Date' },
    { field: 'ddgenerationDate', header: 'Scheduled Date' },
  ];
  selectedGeneration: any[] = [];
  isAllChecked: boolean = false;
  constructor(
    private failedDDGenerationService: FailedDdGenerationService,
    public readonly notification: CCNotificationService,
    private ccDilaog: CCDialog
  ) {}

  ngOnInit(): void {
    this.getGenerations();
  }
  getGenerations(page: number = 0, size: number = 20) {
    this.failedDDGenerationService
      .getFailedDdGeneration({ page, size })
      .subscribe((res: any) => {
        this.generation = res;
      });
  }
  getNextPage(event: PageEvent) {
    this.getGenerations(event.pageIndex, event.pageSize);
  }
  onCheckboxChange(isChecked: any, generationId: number) {
    if (isChecked.checked) {
      this.selectedGeneration.push(generationId);
    } else {
      const index = this.selectedGeneration.indexOf(generationId);
      if (index > -1) {
        this.selectedGeneration.splice(index, 1);
      }
    }
  }
  generate() {
    if (this.getSelected().length > 0) {
      this.ccDilaog.confirm(
        '',
        'Are you sure you want to generate the selected DDs?',
        () => {
          this.failedDDGenerationService
            .generatePostPoneDDs(this.getSelected())
            .subscribe({
              next: (res: any) => {
                this.notification.notifySuccess(
                  'Successfully generated DDs',
                  2000
                );
                this.getGenerations(0, 20);
                this.unSelectAll();
              },
              error: (err: any) => {
                this.notification.notifyError('Failed to generate DDs', 2000);
              },
            });
        }
      );
    } else {
      this.notification.notifyError('Please select at least one generation');
    }
  }
  generateDD(id: number) {
    this.ccDilaog.confirm(
      'Confirm',
      'Are you sure you want to generate this DD?',
      () => {
        this.failedDDGenerationService.generatePostPoneDDs([id]).subscribe({
          next: (res: any) => {
            this.notification.notifySuccess('Successfully generated DD', 2000);
            this.getGenerations(0, 20);
            this.unSelectAll();
          },
          error: (err: any) => {
            this.notification.notifyError(err.error.message, 2000);
          },
        });
      }
    );
  }
  dismiss(id: number) {
    this.ccDilaog.confirm(
      'Confirm',
      'Are you sure you want to dismiss this DD?',
      () => {
        this.failedDDGenerationService.dismissDD(id).subscribe({
          next: (res: any) => {
            this.notification.notifySuccess('Successfully dismissed DD', 2000);
            this.getGenerations(0, 20);
            this.unSelectAll();
          },
          error: (err: any) => {
            this.notification.notifyError(err.error.message, 2000);
          },
        });
      }
    );
  }
  checkIfAllSelected() {
    let select = true;
    this.generation.content.forEach((item: any) => {
      if (!this.selectedGeneration[item.id]) {
        select = false;
      }
    });
    return this.generation.content.length > 0 ? select : false;
  }
  selectAll() {
    if (this.isAllChecked) {
      this.unSelectAll();
      return;
    }
    this.generation.content.forEach((item: any) => {
      this.selectedGeneration[item.id] = true;
    });
    console.log(this.selectedGeneration);
  }
  unSelectAll() {
    this.selectedGeneration = [];
    this.isAllChecked = false;
  }
  getSelected() {
    let selected: any[] = [];
    let keys = Object.keys(this.selectedGeneration);
    keys.forEach((element: any) => {
      if (this.selectedGeneration[element]) {
        selected.push(+element);
      }
    });
    return selected;
  }
}
