<div cc-std-dialog>
  <cc-dialog-header>
    <h1 cc-dialog-title class="text-center">{{data?.message}}</h1>
    <a role="button" type="button" cc-dialog-close-button cc-dialog-close icon="close"></a>
  </cc-dialog-header>
  <cc-dialog-content>
    <form [formGroup]="form" class="pt-0 pl-4 pr-4">
      <cc-input label="Tran Ref Number*" formControlName="reference"></cc-input>
    </form>
    <div class="d-flex justify-content-end gap-2">
      <button cc-raised-button cc-dialog-close type="button">Cancel</button>
      <button cc-raised-button color="primary" [disabled]="form.invalid" (click)="addTransferForPayment()">Ok</button>
    </div>
  </cc-dialog-content>
</div>
