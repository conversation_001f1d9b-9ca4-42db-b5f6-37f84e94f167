import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FamilyRefundApprovalsComponent } from './components/family-refund-approvals/family-refund-approvals.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CCInputModule } from '@maids/cc-lib/input';
import { CCButtonModule } from '@maids/cc-lib/button';
import { CCAdvancedSearchModule } from '@maids/cc-lib/advanced-search';
import { CCDatagridModule } from '@maids/cc-lib/datagrid';
import { CCTabsModule } from '@maids/cc-lib/tabs';
import { FamilyRefundApprovalsRoutingModule } from './family-refund-approvals-routing.module';
import {CCWorkflowSingleTaskModule } from '@maids/cc-shared-components/workflow-single-task';
import { FamilyRefundApprovalDetailsComponent } from './components/family-refund-approval-details/family-refund-approval-details.component'
import { CCIconModule } from "@maids/cc-lib/icon";
import { CCSelectInputModule } from "@maids/cc-lib/select-input";
import { CCCheckboxModule } from "@maids/cc-lib/checkbox";
import { CCRadioButtonModule } from "@maids/cc-lib/radio-button";
import { CCAmountInputModule } from "@maids/cc-lib/masked-input";
import { CCTextareaModule } from '@maids/cc-lib/textarea';
import { CCValidateByModule } from '@maids/cc-lib/validation';
import { TransferMissingPaymentDialogComponent } from './components/dialog/transfer-missing-payment-dialog/transfer-missing-payment-dialog.component';
import { CCDialogModule } from '@maids/cc-lib/dialog';



@NgModule({
  declarations: [FamilyRefundApprovalsComponent, FamilyRefundApprovalDetailsComponent, TransferMissingPaymentDialogComponent],
  imports: [
    FamilyRefundApprovalsRoutingModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    CCInputModule,
    CCButtonModule,
    CCAdvancedSearchModule,
    CCDatagridModule,
    CCTabsModule,
    CCWorkflowSingleTaskModule,
    CCIconModule,
    CCSelectInputModule,
    CCCheckboxModule,
    CCRadioButtonModule,
    CCAmountInputModule,
    CCTextareaModule,
    CCValidateByModule,
    CCDialogModule
],
})
export class FamilyRefundApprovalsModule {}
