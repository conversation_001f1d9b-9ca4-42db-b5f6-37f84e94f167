import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { CCDialog } from '@maids/cc-lib/dialog';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { InsuranceAuditingService } from '../../services/insurance-auditing.service';
import { UploadNewAuditFileComponent } from '../upload-new-audit-file/upload-new-audit-file.component';
import { PageEvent } from '@angular/material/paginator';
import { CCGridColumn } from '@maids/cc-lib/datagrid';

@Component({
  selector: 'app-audit-active-employees',
  templateUrl: './audit-active-employees.component.html',
  styleUrls: ['./audit-active-employees.component.scss'],
})
export class AuditActiveEmployeesComponent implements OnInit {
  records: any;
  currentPage: number = 0;
  gridCols: CCGridColumn[] = [
    {
      field: 'creationDate',
      header: 'Upload Date',
      formatter(rowData, colDef) {
        return rowData.creationDate.split(' ')[0];
      },
    },
    {
      field: 'asOfDate',
      header: 'As of date',
      formatter(rowData, colDef) {
        return rowData.asOfDate.split(' ')[0];
      },
    },
    { field: 'file', header: 'File' },
  ];
  constructor(
    private insuranceAuditingService: InsuranceAuditingService,
    public readonly notifications: CCNotificationService,
    private ccDialog: CCDialog,
    private mediaService: MediaService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.getAllAuditFiles();
  }
  getAllAuditFiles(page: number = 0, size: number = 20) {
    this.insuranceAuditingService
      .allAuditFiles(page, size)
      .subscribe((res: any) => {
        this.records = res;
      });
  }
  getNextPage(event: PageEvent) {
    this.getAllAuditFiles(event.pageIndex, event.pageSize);
    this.currentPage = event.pageIndex;
  }
  downloadFile(uuid: string) {
    this.mediaService.downloadFile('public/download/' + uuid);
  }
  uploadAuditFile() {
    this.ccDialog.originalOpen(UploadNewAuditFileComponent, {}).afterClosed().subscribe((res:any)=>{
      if(res){
        this.getAllAuditFiles()
      }
    })
  }
  deleteAuditFile(id: any) {
    this.ccDialog.confirm(
      '',
      'Are you sure you want to delete this record?',
      () => {
        this.insuranceAuditingService
          .deleteAuditFile(id)
          .subscribe((res: any) => {
            this.getAllAuditFiles(this.currentPage, 20);
          });
      }
    );
  }
  view(id: any) {
    this.router.navigateByUrl(`/accounting/v2/insurance-auditing/audit-active-employees-result/${id}`);
  }
}
