import { Component, OnInit, OnDestroy } from '@angular/core';
import { InsuranceAuditingService } from '../../services/insurance-auditing.service';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { CCDialog } from '@maids/cc-lib/dialog';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { PageEvent } from '@angular/material/paginator';
import { Router } from '@angular/router';
import { Subscription } from 'rxjs';
@Component({
  selector: 'app-insurance-auditing',
  templateUrl: './insurance-auditing.component.html',
  styleUrls: ['./insurance-auditing.component.scss'],
})
export class InsuranceAuditingComponent implements OnInit, OnDestroy {
  gridCols: CCGridColumn[] = [
    {
      field: 'fromDate',
      header: 'From Date',
      formatter(rowData, colDef) {
        return rowData.fromDate.split(' ')[0];
      },
    },
    {
      field: 'toDate',
      header: 'To Date',
      formatter(rowData, colDef) {
        return rowData.toDate.split(' ')[0];
      },
    },
    { field: 'amount', header: 'Amount (AED)' },
    { field: 'balance', header: 'Balance' },
    { field: 'file', header: 'File' },
  ];
  records: any;
  private statementSub?: Subscription;
  constructor(
    private insuranceAuditingService: InsuranceAuditingService,
    public readonly notifications: CCNotificationService,
    private ccDialog: CCDialog,
    private mediaService: MediaService,
    private router: Router
  ) {}
  ngOnInit(): void {
    this.getInsuranceAuditingStatement();
  }
  ngOnDestroy(): void {
    this.statementSub?.unsubscribe();
  }
  getInsuranceAuditingStatement(page: number = 0, size: number = 20) {
    this.statementSub = this.insuranceAuditingService
      .getInsuranceAuditingStatement(page, size)
      .subscribe((res: any) => {
        this.records = res;
      });
  }
  deleteInsuranceAuditingStatement(id: any) {
    this.ccDialog.confirm('', 'Delete Record?', () => {
      this.insuranceAuditingService
        .deleteInsuranceAuditingStatement(id)
        .subscribe((res: any) => {
          this.getInsuranceAuditingStatement(0, 20);
          this.notifications.notifySuccess('Removed');
        });
    });
  }
  getNextPage(event: PageEvent) {
    this.getInsuranceAuditingStatement(event.pageIndex, event.pageSize);
  }
  downloadFile(uuid: string) {
    this.mediaService.downloadFile('public/download/' + uuid);
  }
  goto(type: string) {
    if (type == 'upload') {
      this.router.navigateByUrl('/accounting/v2/insurance-auditing/insurance-auditing-statement');
    } else if (type == 'setup') {
      this.router.navigateByUrl('/accounting/v2/insurance-auditing/insurance-auditing-setup');
    } else if (type == 'active') {
      this.router.navigateByUrl('/accounting/v2/insurance-auditing/audit-active-employees');
    }
  }
}
