import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { InsuranceAuditingComponent } from './components/insurance-auditing/insurance-auditing.component';
import { AuditActiveEmployeesComponent } from './components/audit-active-employees/audit-active-employees.component';
import { InsuranceAuditingStatementComponent } from './components/insurance-auditing-statement/insurance-auditing-statement.component';
import { AuditActiveEmployeesResultComponent } from './components/audit-active-employees-result/audit-active-employees-result.component';
import { SetupComponent } from './components/setup/setup.component';

const routes: Routes = [
  {
    path: '',
    component: InsuranceAuditingComponent,
    data: { label: '', pageCode: 'ACCOUNTING__InsuranceAuditing' },
  },
  {
    path: 'audit-active-employees',
    component: AuditActiveEmployeesComponent,
    data: {
      label: 'Audit Active Employees',
      pageCode: 'ACCOUNTING__AuditActiveEmployees',
    },
  },
  {
    path: 'audit-active-employees-result/:id',
    component: AuditActiveEmployeesResultComponent,
    data: {
      label: 'Audit Active Employees Result',
      pageCode: 'ACCOUNTING__AuditActiveEmployeesResult',
    },
  },
  {
    path: 'insurance-auditing-statement',
    component: InsuranceAuditingStatementComponent,
    data: {
      label: 'Statement',
      pageCode: 'ACCOUNTING__InsuranceStatementAuditing',
    },
  },
  {
    path: 'insurance-auditing-setup',
    component: SetupComponent,
    data: {
      label: 'Setup',
      pageCode: 'ACCOUNTING__InsuranceAuditingSetup',
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class InsuranceAuditingRoutingModule {}
