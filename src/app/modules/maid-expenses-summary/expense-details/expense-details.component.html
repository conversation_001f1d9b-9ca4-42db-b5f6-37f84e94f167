<cc-dialog-header>
  <h1 cc-dialog-title>Expense Details</h1>
  <a
    role="button"
    type="button"
    cc-icon-button
    cc-dialog-close-button
    cc-dialog-close
  ></a>
</cc-dialog-header>
<cc-dialog-content *ngIf="expenseDetails$ | async as expense">
  <div class="row mt-1 px-2">
    <div class="col-12">
      <cc-amount-input
        label="Amount"
        [ngModel]="expense.amount"
        [disabled]="true"
        [symbol]="expense.currency?.label"
      ></cc-amount-input>
    </div>
  </div>
  <div class="row mt-1 px-2">
    <div class="col-12">
      <cc-input
        label="Payment Method"
        [ngModel]="expense.paymentMethod?.value || expense.paymentMethod?.label"
        [disabled]="true"
      ></cc-input>
    </div>
  </div>
  <div class="row mt-1 px-2">
    <div class="col-12">
      <cc-input
        label="Date"
        [ngModel]="expense.creationDate?.split(' ')[0]"
        [disabled]="true"
      ></cc-input>
    </div>
  </div>
  <div class="row mt-1 px-2">
    <div class="col-12">
      <cc-input
        label="Requested by"
        [ngModel]="expense.requestedBy?.name"
        [disabled]="true"
      ></cc-input>
    </div>
  </div>
  <div class="row mt-1 px-2">
    <div class="col-12">
      <cc-input
        label="Status"
        [ngModel]="expense.status?.label"
        [disabled]="true"
      ></cc-input>
    </div>
  </div>
  <div class="row mt-1 px-2">
    <div class="col-12">
      <cc-input
        label="Refund Rejection's Note"
        [ngModel]="expense.rejectionNotes"
        [disabled]="true"
      ></cc-input>
    </div>
  </div>
  <div class="row mt-1 px-2">
    <div class="col-12">
      <cc-input
        label="Notes"
        [ngModel]="expense.notes"
        [disabled]="true"
      ></cc-input>
    </div>
  </div>
  <div class="row mt-1 px-2">
    <div class="col-12">
      <cc-textarea
        label="Description"
        [ngModel]="expense.description"
        [disabled]="true"
      ></cc-textarea>
    </div>
  </div>
  <div class="row mt-1 px-2">
    <div class="col-12">
      <cc-input
        label="Approved by"
        [ngModel]="expense.approvedBy?.name"
        [disabled]="true"
      ></cc-input>
    </div>
  </div>
  <div class="row mt-1 px-2">
    <div class="col-12">
      <cc-input
        label="Loan amount"
        [ngModel]="expense.loanAmount"
        [disabled]="true"
      ></cc-input>
    </div>
  </div>
  <div class="row mt-1 px-2">
    <div class="col-12">
      <cc-input
        label="Related to"
        [ngModel]="expense.relatedToInfo"
        [disabled]="true"
      ></cc-input>
    </div>
  </div>
  <div class="row mt-1 px-2">
    <div class="col-12">
      <cc-input
        label="Beneficiary"
        [ngModel]="expense.benefeciaryInfo"
        [disabled]="true"
      ></cc-input>
    </div>
  </div>
</cc-dialog-content>
