<!-- for add preference popup -->
<form>
  <div class="d-flex justify-content-start">
    <h5 mat-dialog-title class="">Revenue Data</h5>
  </div>
  <div mat-dialog-content class="my-2">
    <cc-input
      [(ngModel)]="code"
      name="code"
      #input="ngModel"
      [label]="'Code'"
      required
    >
    </cc-input>
    <cc-input
      [(ngModel)]="name"
      name="name"
      #input="ngModel"
      [label]="'Name'"
      required
    >
    </cc-input>
  </div>
  <div class="d-flex justify-content-end gap-2" mat-dialog-actions>
    <button type="button" cc-flat-button (click)="onNoClick()">Cancel</button>
    <button
      [disabled]="!(name && code)"
      type="button"
      cc-flat-button
      color="accent"
      (click)="onSubmit()"
    >
      Ok
    </button>
  </div>
</form>
