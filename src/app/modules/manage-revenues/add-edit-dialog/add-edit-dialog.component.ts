import {Component, Inject, OnInit} from '@angular/core';
import {ManageRevenuesService} from "../services/manage-revenues.service";
import {Subscription} from "rxjs";
import {CCNotificationService} from "@maids/cc-lib/services";
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

@Component({
  selector: 'app-add-edit-dialog',
  templateUrl: './add-edit-dialog.component.html',
  styleUrls: ['./add-edit-dialog.component.scss']
})
export class AddEditDialogComponent implements OnInit {
  private subscriptions = new Subscription();

  code: string = '';
  name: string = '';

  constructor(
    public dialogRef: MatDialogRef<AddEditDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private service: ManageRevenuesService,
    private notificaitonService: CCNotificationService
  ) {
  }

  ngOnDestroy(): void {
    this.subscriptions.unsubscribe()
  }

  ngOnInit(): void {
    if (this.data) {
      this.name = this.data.name;
      this.code = this.data.code;
    }
  }

  onNoClick(): void {
    this.dialogRef.close('normalClose');
  }

  onSubmit(): void {
    if (!!this.data?.id)
      this.onEditSubmit()
    else {
      const sub = this.service.addRevenue({name: this.name, code: this.code}).subscribe(result => {
          this.dialogRef.close();
          this.notificaitonService.notifySuccess('Success');
        },
        error => {
          this.notificaitonService.notifyError(error?.error.message);

        })
      this.subscriptions.add(sub)
    }

  }

  onEditSubmit() {
    const sub = this.service.updateRevenue(this.data.id, {name: this.name, code: this.code}).subscribe(() => {
        this.dialogRef.close();
        this.notificaitonService.notifySuccess('Updated');
      },
      error => {
        this.notificaitonService.notifyError(error?.error.message);

      })
    this.subscriptions.add(sub)
  }
}
