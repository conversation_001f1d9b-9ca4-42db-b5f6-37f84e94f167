<div class="acc-6301">
  <div class="my-2">
    <div class="row justify-content-center" [formGroup]="form">
      <cc-input
        class="col-4"
        label="Search by Revenue Name / Code"
        formControlName="nameOrCode"
        (keydown.enter)="submitFilter()"
      >
        <button
          cc-icon-button
          color="primary"
          class="input-icon-suffix"
          (click)="submitFilter()"
          type="submit"
        >
          <cc-icon class="search-icon">search</cc-icon>
        </button>
      </cc-input>
    </div>
    <div class="d-flex justify-content-between">
      <div
        *ngIf="manageRevenuesList$ | async; let manageRevenuesList"
        class="col-md-4 px-0"
      >
        <label class="cc-primary" style="font-weight: 500"
          >Record Count:
          {{ (manageRevenuesList$ | async)?.totalElements }}</label
        >
      </div>
      <div class="col-md-auto px-0">
        <button
          class="iconed-btn ml-2"
          cc-raised-button
          color="primary"
          (click)="addRevenue()"
        >
          <cc-icon class="icon panel-icon">add </cc-icon>
          <span>Add New Revenue</span>
        </button>
      </div>
    </div>
  </div>

  <cc-datagrid
    *ngIf="manageRevenuesList$ | async; let manageRevenuesList"
    class="my-2"
    [data]="manageRevenuesList.content"
    [columns]="gridCols"
    [length]="manageRevenuesList.totalElements"
    [pageOnFront]="false"
    [pageIndex]="manageRevenuesList.number"
    [pageSize]="manageRevenuesList.size"
    [pageSizeOptions]="[10, 20, 30, 40, 50]"
    (page)="getNextPage($event)"
    [stickyHeader]="true"
    [columnMovable]="true"
    [columnHideable]="true"
    [showColumnMenuButton]="true"
    [showColumnMenuHeader]="false"
    [columnMenuButtonIcon]="'settings'"
    [cellTemplate]="{ name: name }"
  >
    <cc-grid-actions-list
      *ccActionData="let ctx of manageRevenuesList?.content; row as row"
      [renderedActionsCount]="2"
      style="width: fit-content; gap: 8px"
    >
      <button
        cc-raised-button
        *cc-action
        (click)="showEditModal(row)"
        color="accent"
      >
        Edit
      </button>
      <button cc-raised-button *cc-action (click)="goToPL(row)">P & L</button>
    </cc-grid-actions-list>
  </cc-datagrid>
  <ng-template #name let-row>
    <a
      href="#!/accounting/statement-of-account/revenues/{{ row.code }}/{{
        row.name
      }}"
      target="_blank"
      class="cc-secondary"
      >{{ row.name }}</a
    >
  </ng-template>
</div>
