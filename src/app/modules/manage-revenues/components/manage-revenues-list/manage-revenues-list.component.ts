import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
} from '@angular/core';

import { PageEvent } from '@angular/material/paginator';
import { HttpParams } from '@angular/common/http';
import { ManageRevenuesModel } from '../../models/manage-revenues.model';
import { Subscription, take } from 'rxjs';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { API } from 'src/environments/api';
import { ManageRevenuesStoreService } from '../../services/manage-revenues-store.service';
import { FormBuilder } from '@angular/forms';
import { AutoUnsubscribe, SearchModel } from '@maids/cc-lib/common';
import { Sort } from '@angular/material/sort';
import { CCDialog } from '@maids/cc-lib/dialog';
import { AddEditDialogComponent } from '../../add-edit-dialog/add-edit-dialog.component';
import { Router } from '@angular/router';

@Component({
  selector: 'app-manage-revenues-list',
  templateUrl: './manage-revenues-list.component.html',
  styleUrls: ['./manage-revenues-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ManageRevenuesStoreService],
})
@AutoUnsubscribe
export class ManageRevenuesListComponent implements OnInit {
  private readonly subscriptions = new Subscription();
  panelFilter: { title: string } = {
    title: 'Filters',
  };
  form = this.formBuilder.group({
    nameOrCode: [''],
  });
  constructor(
    private formBuilder: FormBuilder,
    private store: ManageRevenuesStoreService,
    private cdr: ChangeDetectorRef, // Inject ChangeDetectorRef
    private ccDialog: CCDialog,
    private router: Router
  ) {}

  readonly manageRevenuesList$ = this.store.manageRevenuesList$;
  readonly search$ = this.store.search$;

  gridCols: CCGridColumn[] = [
    {
      field: 'name',
      header: 'Name',
    },
    { field: 'code', header: 'Code' },
  ];

  ngOnInit(): void {
    this.search$.pipe(take(1)).subscribe((e: SearchModel<any>) => {
      this.store.loadData(e);
    });
  }

  getNextPage(e: PageEvent) {
    this.store.updateSearchQueryParams({
      pageIndex: e.pageIndex,
      pageSize: e.pageSize,
    });
    // Load data after updating pagination params
    this.search$.pipe(take(1)).subscribe((searchParams: SearchModel<any>) => {
      this.store.loadData(searchParams);
    });
  }

  onSortChange(event: Sort) {
    this.store.updateSearchSortQueryParams({
      active: event.active,
      direction: event.direction,
    });
    // Load data after updating sort params
    this.search$.pipe(take(1)).subscribe((e: SearchModel<any>) => {
      this.store.loadData(e);
    });
  }

  submitFilter() {
    this.store.updateSearch(this.form.value.nameOrCode);
    // Use take(1) to automatically complete the subscription after one emission
    this.search$.pipe(take(1)).subscribe((e: any) => {
      this.store.loadData(e);
    });
  }

  goToPL(row: any) {
    this.router.navigateByUrl('/accounting/v2/revenues/pnl-bucket/revenue/' + row.id);
  }
  showEditModal(event: any) {
    console.log(event);
    const dialogRef = this.ccDialog.originalOpen(AddEditDialogComponent, {
      width: '550px',
      data: { id: event.id, code: event.code, name: event.name },
    });

    const sub = dialogRef.afterClosed().subscribe((result) => {
      if (!result)
        this.search$.pipe(take(1)).subscribe((e: SearchModel<any>) => {
          this.store.loadData(e);
        });
    });
    this.subscriptions.add(sub);
  }

  addRevenue() {
    const dialogRef = this.ccDialog.originalOpen(AddEditDialogComponent, {
      width: '550px',
    });

    const sub = dialogRef.afterClosed().subscribe((result) => {
      if (!result)
        this.search$.pipe(take(1)).subscribe((e: SearchModel<any>) => {
          this.store.loadData(e);
        });
    });
    this.subscriptions.add(sub);
  }
}
