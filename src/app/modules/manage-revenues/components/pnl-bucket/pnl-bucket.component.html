<h5 class="p-3">Assign Bucket To P & L</h5>
<cc-datagrid
  class="my-2"
  *ngIf="bucketVariablesData"
  [data]="bucketVariablesData"
  [columns]="gridCols"
  [length]="bucketVariablesData.length"
  [pageOnFront]="true"
  [stickyHeader]="true"
  [columnMovable]="true"
  [columnHideable]="true"
  [showColumnMenuButton]="true"
  [showColumnMenuHeader]="false"
  [columnMenuButtonIcon]="'settings'"
  [noResultTemplate]="noResultTpl"
  [showPaginator]="false"
>
  <ng-template #noResultTpl> No Results Found </ng-template>
</cc-datagrid>
<div class="row my-2 p-2">
  <div class="col-md-6">
    <cc-select
      #companyPageSelector
      [(ngModel)]="company"
      [data]="companies"
      [modelOptions]="modelData"
      label="Company"
      [search]="true"
      (valueChange)="changeCompany($event)"
    ></cc-select>
    <div class="row">
      <div class="col-md-6">
        <div class="main-navigation w-100">
          <div class="w-100 navigation-components">
            <cc-tree
              [data]="tree"
              [config]="config"
              [leafTemplate]="leafTpl"
              [isNested]="true"
              [expandAll]="false"
              [matTreeNodePaddingIndent]="'10'"
              [branchTemplate]="branchTpl"
              [selectable]="true"
              [multiple]="false"
              (selectionChange)="onSelectionChange($event)"
            ></cc-tree>
            <ng-template #branchTpl let-node>
              <cc-icon style="margin-right: 4px; margin-top: 8px" color="accent"
                >folder_open</cc-icon
              >
              <span style="margin-top: 8px">
                {{ node.name }}
              </span>
            </ng-template>
            <ng-template #leafTpl let-node>
              <strong style="margin-left: 5px"></strong> {{ node.name }}
            </ng-template>
          </div>
        </div>
      </div>
      <div class="col-md-6">
        <div *ngIf="selectedNode && selectedVariable" class="saveWeightForm">
          <h6 class="text-center">Node Weight</h6>
          <div class="form-group">
            <cc-input
              label="Weight"
              type="text"
              [(ngModel)]="weight"
            ></cc-input>
          </div>
          <div class="d-flex justify-content-center">
            <button
              cc-button
              cc-raised-button
              color="primary"
              type="button"
              class="px-1"
              (click)="saveWeight()"
            >
              Save
            </button>
            <button
              cc-button
              cc-raised-button
              type="button"
              (click)="cancelOperation()"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="col-md-6 p-0">
    <cc-datagrid
      class="my-2"
      *ngIf="bucketWeightData.length > 0"
      [data]="bucketWeightData"
      [columns]="pAndLCols"
      [length]="bucketWeightData.length"
      [showPaginator]="false"
      [stickyHeader]="true"
      [columnMovable]="true"
      [columnHideable]="true"
      [showColumnMenuButton]="true"
      [showColumnMenuHeader]="false"
      [columnMenuButtonIcon]="'settings'"
    >
    </cc-datagrid>
  </div>
</div>
