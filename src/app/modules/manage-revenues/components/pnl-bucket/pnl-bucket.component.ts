import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  TemplateRef,
  ViewChild,
} from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { ManageRevenuesStoreService } from '../../services/manage-revenues-store.service';
import { CCDialog } from '@maids/cc-lib/dialog';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { Router, ActivatedRoute } from '@angular/router';
import { ManageRevenuesService } from '../../services/manage-revenues.service';
import { CCTreeNodeConfig } from '@maids/cc-lib/tree';
import { CCNotificationService } from '@maids/cc-lib/services';

@Component({
  selector: 'app-pnl-bucket',
  templateUrl: './pnl-bucket.component.html',
  styleUrls: ['./pnl-bucket.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ManageRevenuesStoreService],
})
export class PnlBucketComponent implements OnInit {
  @ViewChild('treeTemplate') treeTemplate?: TemplateRef<any>;

  readonly api = '/accounting/plcompanies/list';

  bucketVariablesData: any = [];
  bucketWeightData: any = [];
  tree: any;
  addNodeEnable: boolean = false;
  config!: CCTreeNodeConfig;
  selectedNode: any;
  company: any;
  modelData: any = [];
  companies: any;
  weight: any = '';
  selectedVariable: boolean = false;
  id = this.route.snapshot.paramMap.get('id');
  type = this.route.snapshot.paramMap.get('type');
  editMode: boolean = false;
  rowId: any;
  variableId: any;
  constructor(
    private formBuilder: FormBuilder,
    private store: ManageRevenuesStoreService,
    private service: ManageRevenuesService,
    private cdr: ChangeDetectorRef, // Inject ChangeDetectorRef
    private ccDialog: CCDialog,
    private router: Router,
    private route: ActivatedRoute,
    private notification: CCNotificationService
  ) {}

  ngOnInit(): void {
    this.bucketWeightData = [];
    this.getBucketVariables();
    this.getCompanies();
  }
  getCompanies() {
    this.service.fetchCompanies().subscribe((response: any) => {
      this.companies = response.map((e: any) => ({ id: e.id, text: e.name }));
    });
  }
  getBucketVariables() {
    if (this.type == 'revenue') {
      this.service
        .fetchPlVariableBucketsListRevenue$(this.id)
        .subscribe((dt: any) => {
          this.bucketVariablesData = dt;
          this.cdr.markForCheck();
        });
    } else if (this.type == 'expense') {
      this.service
        .fetchPlVariableBucketsListExpense$(this.id)
        .subscribe((dt: any) => {
          this.bucketVariablesData = dt;
          this.cdr.markForCheck();
        });
    }
  }

  pAndLCols: CCGridColumn[] = [
    {
      field: 'companyName',
      header: 'Bucket',
      formatter: (row: any) => {
        return row.revenue ? row.revenue.label : row.expense.label;
      },
    },
    {
      field: 'wieght',
      header: 'Weight',
    },
  ];

  gridCols: CCGridColumn[] = [
    {
      field: 'operations',
      header: 'operations',
      sortable: false,
      type: 'button',
      buttonConfig: {
        mode: 'multiple',
        disabled: false,
        buttons: [
          {
            type: 'raised',
            icon: '',
            text: 'Edit Weight',
            color: 'light',
            mode: 'single',
            disabled: false,
            hidden: (row: any) => {
              return false;
            },
            callback: (row: any) => this.showTree(row),
          },
          {
            type: 'raised',
            text: 'Delete',
            tooltip: 'Delete',
            color: 'accent',
            pop: true,
            disabled: (row: any) => {
              return false;
            },
            hidden: (row: any) => {
              return false;
            },
            popTitle: 'Confirm',
            popDescription: 'Are you Sure you want to delete this variable?',
            popOkColor: 'accent',
            popCloseColor: 'primary',
            popCloseText: 'No Thanks',
            popOkText: 'Sure',
            callback: (record: any) => this.deleteItem(record),
          },
        ],
      },
    },
    {
      field: 'companyName',
      header: 'Company Name',
    },
    {
      field: 'mainLevel',
      header: 'Main Level',
    },
    {
      field: 'subLevel',
      header: 'Sub Level',
    },
    {
      field: 'variable',
      header: 'Variable',
    },
    {
      field: 'weight',
      header: 'Weight',
    },
  ];

  saveWeight() {
    let payload: any;
    if (this.type == 'revenue') {
      payload = {
        wieght: this.weight,
        id: this.rowId,
        revenue: this.id ? { id: parseInt(this.id) } : null,
      };
    } else if (this.type == 'expense') {
      payload = {
        wieght: this.weight,
        id: this.rowId,
        expense: this.id ? { id: parseInt(this.id) } : null,
      };
    }
    if (!this.editMode) {
      payload.PLVariable = {
        id: this.selectedNode.id,
        type: 'PLVariableNode',
      };
      this.service.createWeight(payload).subscribe({
        next: (res: any) => {
          this.notification.notifySuccess('Created');
          this.getBucketVariables();
          this.changeCompany(this.company);
        },
      });
    } else {
      this.service.updateWeight(payload).subscribe({
        next: (res: any) => {
          this.notification.notifySuccess('Updated');
          this.getBucketVariables();
          this.changeCompany(this.company);
        },
      });
    }
  }

  cancelOperation() {
    this.weight = '';
  }

  deleteItem(row: any) {
    this.service.delete(row.id).subscribe(
      (response: any) => {
        this.notification.notifySuccess('Bucket Is Deleted');
        window.location.reload();
      },
      (error) => this.notification.notifyError(error?.error?.message)
    );
  }

  showTree(row: any) {
    this.editMode = true;
    this.variableId = row.variableId;
    this.service.fetchPLCompanies(row?.comapnyId).subscribe((response: any) => {
      this.buildTreeData(response, this.variableId);
      this.cdr.markForCheck();
    });
  }
  selectNodeRecursively(nodes: any[], selectedVariable: any) {
    for (const node of nodes) {
      if (node.id === selectedVariable) {
        this.onSelectionChange([node]);
        return;
      }
      if (node.sortedChildren && node.sortedChildren.length > 0) {
        this.selectNodeRecursively(node.sortedChildren, selectedVariable);
      }
    }
  }
  buildTreeData = (response: any, selectedVariable?: any) => {
    this.tree = [];
    this.tree = response.sortedChildren;
    if (selectedVariable) {
      this.selectNodeRecursively(this.tree, selectedVariable);
    }
    this.config = {
      field: 'name',
      isleaf: (data: any) => data.sortedChildren.length == 0,
      children: [
        {
          field: 'sortedChildren',
          isleaf: (data: any) => data.sortedChildren.length == 0,
        },
      ],
    };
    this.modelData = [
      {
        id: response.company?.id,
        text: response.company?.label,
      },
    ];
    this.company = response.company.id;
  };
  changeCompany = (event: any) => {
    if(event) {
    this.service.fetchPLCompanies(event).subscribe((response: any) => {
      this.buildTreeData(response, this.variableId);
      this.cdr.markForCheck();
    });
  }else{
    this.tree = [];
  }
  };

  onSelectionChange = (event: any) => {
    this.weight=''
    this.selectedNode = event[0];
    console.log(this.selectedNode);
    this.type =
      this.selectedNode.pLNodeType.value == 'REVENUES' ? 'revenue' : 'expense';
    this.selectedNode.pLVariableBuckets.forEach((element: any) => {
      if (this.type == 'revenue') {
        if (element.revenue.id == this.id) {
          this.weight = element.wieght;
          this.rowId = element.id;
        }
      } else if (this.type == 'expense') {
        if (element.expense.id == this.id) {
          this.weight = element.wieght;
          this.rowId = element.id;
        }
      }
    });
    this.bucketWeightData = this.selectedNode.pLVariableBuckets;    
    if (this.selectedNode.type == 'PLVariableNode')
      this.selectedVariable = true;
    else this.selectedVariable = false;
    this.cdr.markForCheck();
  };
}
