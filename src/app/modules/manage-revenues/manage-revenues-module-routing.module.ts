import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CCRoutes } from '@maids/cc-lib/common';

import { ManageRevenuesListComponent } from './components/manage-revenues-list/manage-revenues-list.component';
import { PnlBucketComponent } from './components/pnl-bucket/pnl-bucket.component';

const routes: CCRoutes = [
  { path: '', component: ManageRevenuesListComponent },
  {
    path: 'pnl-bucket/:type/:id',
    component: PnlBucketComponent,
    data: { label: 'Bucket Assignment To P & L', pageCode: 'pnl_bucket' },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ManageRevenuesModuleRoutingModule {}
