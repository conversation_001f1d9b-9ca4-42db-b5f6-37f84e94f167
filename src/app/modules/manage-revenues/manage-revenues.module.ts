import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';

import {CCButtonModule} from '@maids/cc-lib/button';
import {CCBreadcrumbsModule} from '@maids/cc-lib/layout';
import {CCDatagridModule} from '@maids/cc-lib/datagrid';
import {CCIconModule} from '@maids/cc-lib/icon';
import {CCAdvancedSearchModule} from '@maids/cc-lib/advanced-search';
import {CCCardModule} from '@maids/cc-lib/card';
import {CCConnectFormModule, CCPaginationModule} from '@maids/cc-lib/common';

import {ManageRevenuesModuleRoutingModule} from './manage-revenues-module-routing.module';

import {ManageRevenuesListComponent} from './components/manage-revenues-list/manage-revenues-list.component';

import {ManageRevenuesStoreModule} from './store/manage-revenues-store.module';
import {CCDividerModule} from '@maids/cc-lib/divider';
import {CCAccordionModule} from '@maids/cc-lib/accordion';
import {CCInputModule} from '@maids/cc-lib/input';
import {CCDialog, CCDialogModule} from '@maids/cc-lib/dialog';
import {MatDialogModule} from '@angular/material/dialog';
import {AddEditDialogComponent} from './add-edit-dialog/add-edit-dialog.component';
import {PnlBucketComponent} from './components/pnl-bucket/pnl-bucket.component';
import {CCTreeModule} from "@maids/cc-lib/tree";
import {CCDeepSelectModule} from "@maids/cc-lib/deep-select";
import {CCSelectInputModule} from "@maids/cc-lib/select-input";

@NgModule({
  declarations: [ManageRevenuesListComponent, AddEditDialogComponent, PnlBucketComponent],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    // cc lib modules
    CCSelectInputModule,
    CCDividerModule,
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    CCInputModule,
    // cc lib modules
    CCDialogModule,
    CCBreadcrumbsModule,
    CCButtonModule,
    CCDatagridModule,
    CCIconModule,
    CCAdvancedSearchModule,
    CCCardModule,
    CCConnectFormModule,
    //routing module
    ManageRevenuesModuleRoutingModule,
    // store module
    ManageRevenuesStoreModule,
    CCAccordionModule,
    MatDialogModule,
    CCTreeModule,
    CCPaginationModule,
  ],
})
export class ManageRevenuesModule {
}
