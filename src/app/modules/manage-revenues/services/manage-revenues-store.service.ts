import { Inject, Injectable } from '@angular/core';
import { Observable, BehaviorSubject } from 'rxjs';
import { map } from 'rxjs/operators';
import { Store } from '@ngrx/store';
import { BaseStoreService, SearchModel } from '@maids/cc-lib/common';
import * as ManageRevenuesActions from '../store/manage-revenues.actions';
import * as ManageRevenuesSelectors from '../store/manage-revenues.selectors';
import { initialState } from '../store/manage-revenues.reducer';
import { SortDirection } from '@angular/material/sort';

@Injectable()
export class ManageRevenuesStoreService extends BaseStoreService {
  searchSubject = new BehaviorSubject<SearchModel<any>>(initialState.search);
  //SELECTORS
  readonly search$ = this.store.select(ManageRevenuesSelectors.selectSearch);
  readonly manageRevenuesList$ = this.store.select(
    ManageRevenuesSelectors.selectManageRevenuesList
  );

  constructor(public store: Store) {
    super(store);
  }

  //UPDATERS
  readonly updateSearchQueryParams = (event: {
    pageIndex: number;
    pageSize: number;
  }) => {
    this.store.dispatch(
      ManageRevenuesActions.updateSearchQueryParams({ event })
    );
    // Remove automatic loadData call - let the component decide when to load
    // this.loadData(this.searchSubject.getValue());
  };
  readonly updateSearch = (event: any) => {
    this.store.dispatch(ManageRevenuesActions.updateSearch({ event }));
  };
  readonly updateSearchSortQueryParams = (event: {
    active: string;
    direction: SortDirection;
  }) => {
    this.store.dispatch(
      ManageRevenuesActions.updateSearchSortQueryParams({ event })
    );
  };
  //ACTIONS

  readonly loadData = (search: SearchModel<any>) => {
    this.store.dispatch(
      ManageRevenuesActions.fetchManageRevenuesList({
        params: {
          ...search.params,
          search: search.search,
        },
      })
    );
  };

  pickListsCodes(): string[] {
    //return array of picklist codes you need in this feature
    return [];
  }

  resetState(): void {
    //dispatch the action that resets the state
    this.store.dispatch(ManageRevenuesActions.resetManageRevenuesState());
  }
}
