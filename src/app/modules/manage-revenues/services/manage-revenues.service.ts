import { HttpClient, HttpContext, HttpParams } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import {
  ManageRevenuesModel,
  ManageRevenuesFormDataModel,
} from '../models/manage-revenues.model';
import {
  PageableResponseModel,
  PaginatedEntity,
  PaginationRequest,
} from '@maids/cc-lib/common';
import { API } from 'src/environments/api';
import { Store } from '@ngrx/store';
import {
  CCBackendEndpoint,
  REQ_SHOW_LOADING_ICON,
} from '@maids/cc-erp-services';
import { SelectOption } from '@maids/cc-lib/select-input';

@Injectable({ providedIn: 'root' })
export class ManageRevenuesService {
  constructor(
    private _http: HttpClient,
    @Inject(CCBackendEndpoint) private _api: CCBackendEndpoint,
    private _store: Store
  ) {}

  // get all ManageRevenues
  fetchManageRevenuesList$(
    params?: any
  ): Observable<PageableResponseModel<ManageRevenuesModel>> {
    if (params.search) {
      params.search = params.search.trim();
    }

    return this._http.get<PageableResponseModel<ManageRevenuesModel>>(
      [`${this._api}/${API.getManageRevenuesList}`].join('/'),
      { params: params }
    );
  }

  // get all pl variable buckets
  fetchPlVariableBucketsListRevenue$(id: any): Observable<any> {
    return this._http.get<any>(
      [`${this._api}/${API.getBucketRevenueVariables}/${id}`].join('/')
    );
  }
  fetchPlVariableBucketsListExpense$(id: any): Observable<any> {
    return this._http.get<any>(
      [`${this._api}/${API.getBucketExpenseVariables}/${id}`].join('/')
    );
  }

  // get all pl companies
  fetchPLCompanies(id: any): Observable<any> {
    return this._http.get<any>(
      [`${this._api}/${API.getPnLCompanies}/${id}`].join('/')
    );
  }

  // get all companies
  fetchCompanies(): Observable<any> {
    return this._http.get<any>(`${this._api}/${API.searchCompanies}`).pipe();
  }

  addRevenue(object: { code: string; name: string }): Observable<any> {
    return this._http
      .post<any>(`${this._api}/${API.createRevenue}`, {
        name: object.name,
        code: object.code,
      })
      .pipe(map((response) => true));
  }

  createWeight(payload: any): Observable<any> {
    return this._http.post<any>(`${this._api}/${API.storeWeight}`, payload);
  }
  updateWeight(payload: any): Observable<any> {
    return this._http.post<any>(`${this._api}/${API.updateeWeight}`, payload);
  }

  updateRevenue(
    id: string,
    object: { code: string; name: string }
  ): Observable<any> {
    let payload = {
      id: id,
      name: object.name,
      code: object.code,
    };
    return this._http.post<any>(`${this._api}/${API.updateRevenue}`, payload);
  }

  delete(id: any): Observable<any> {
    return this._http.delete(`${this._api}/${API.deleteRevenue}/${id}`);
  }
}
