import { NgModule } from "@angular/core";
import { EffectsModule } from "@ngrx/effects";
import { StoreModule } from "@ngrx/store";
import { ManageRevenuesEffects } from "./manage-revenues.effects";
import * as fromManageRevenues from "./manage-revenues.reducer";

@NgModule({
  imports:[
    StoreModule.forFeature(fromManageRevenues.ManageRevenuesFeatureKey, fromManageRevenues.reducer),
    EffectsModule.forFeature([ManageRevenuesEffects])
  ],
  exports:[
    StoreModule,
    EffectsModule,
  ]
})
export class ManageRevenuesStoreModule{}

