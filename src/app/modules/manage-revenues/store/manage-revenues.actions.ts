import { createAction, props } from "@ngrx/store";
import {PageableResponseModel} from "@maids/cc-lib/common";
import { ManageRevenuesFormDataModel,ManageRevenuesModel } from "../models/manage-revenues.model";
import { SortDirection } from "@angular/material/sort";

export const fetchManageRevenuesList = createAction(
  "[ManageRevenues | INIT] fetch manage-revenues list",
  props<{params?:  { page?: number, size?: number, search?: string, sort?: string } }>()
)

export const fetchManageRevenuesListSuccess = createAction(
  "[ManageRevenues | EFFECT] fetch manage-revenues success",
  props<{payload: PageableResponseModel<ManageRevenuesModel>}>()
);


export const createManageRevenues = createAction(
  "[ManageRevenues | UI] create new ManageRevenues",
  props<{manage_revenues:ManageRevenuesFormDataModel}>()
)

export const createManageRevenuesSuccess = createAction(
  "[ManageRevenues | EFFECT] create new  ManageRevenues success",
  props<{success:boolean}>()
);


export const updateManageRevenues = createAction(
  "[ManageRevenues | UI] update  ManageRevenues",
  props<{manage_revenues:ManageRevenuesFormDataModel}>()
)

export const updateManageRevenuesSuccess = createAction(
  "[ManageRevenues | EFFECT] update  ManageRevenues success",
  props<{success:boolean}>()
);


export const deleteManageRevenues = createAction(
  "[ManageRevenues | INIT] delete ManageRevenues",
  props<{Id:number}>()
)
export const deleteManageRevenuesSuccess = createAction(
  "[ManageRevenues | EFFECT] delete ManageRevenues success",
  props<{success:boolean}>()
)


export const getManageRevenues = createAction(
  "[ManageRevenues | INIT] get ManageRevenues",
  props<{Id:number}>()
)
export const getManageRevenuesSuccess = createAction(
  "[ManageRevenues | EFFECT] get ManageRevenues success",
  props<{manage_revenues:ManageRevenuesModel}>()
)
export const updateSearchQueryParams = createAction(
  "[ManageRevenues | STORE SERVICE | Update state] update query params Configs",
  props<{event:{pageIndex:number,pageSize:number,search?:string}}>()
);
export const updateSearch = createAction(
  "[ManageRevenues | STORE SERVICE | Update state] update Search Field Config",
  props<{event:any}>()
);

export const updateSearchSortQueryParams = createAction(
  "[ManageRevenues | STORE SERVICE | Update state] update Search sortConfigs",
  props<{event:{active:string,direction:SortDirection}}>()
);


export const resetManageRevenuesState = createAction(
  "[ManageRevenues | RESET] reset ManageRevenues state"
)
