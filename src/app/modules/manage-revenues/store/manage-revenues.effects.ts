import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import { map, switchMap, withLatestFrom } from 'rxjs/operators';
import { ManageRevenuesService } from '../services/manage-revenues.service';
import * as ManageRevenuesActions from './manage-revenues.actions';
import { CCNotificationService } from '@maids/cc-lib/services';
import { PageableResponseModel, SearchModel } from '@maids/cc-lib/common';
import * as ManageRevenuesSelectors from './manage-revenues.selectors';

@Injectable()
export class ManageRevenuesEffects {
  constructor(
    private _actions: Actions,
    private _service: ManageRevenuesService,
    private _store: Store,
    private notifications: CCNotificationService
  ) {}

  readonly fetchManageRevenuesList$ = createEffect(() =>
    this._actions.pipe(
      ofType(ManageRevenuesActions.fetchManageRevenuesList),
      withLatestFrom(this._store.select(ManageRevenuesSelectors.selectSearch)),
      switchMap(([action, filter]: [any, SearchModel<any>]) => {
        let _params = {
          ...action.params,
          page: filter?.params?.page,
          size: filter?.params?.size,
          search: !!action.params.search ? action.params.search : '',
        };
        return this._service.fetchManageRevenuesList$(_params);
      }),
      map((list) =>
        ManageRevenuesActions.fetchManageRevenuesListSuccess({ payload: list })
      )
    )
  );
}
