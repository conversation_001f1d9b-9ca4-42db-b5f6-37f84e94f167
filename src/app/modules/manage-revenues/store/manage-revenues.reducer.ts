import { createReducer, on } from "@ngrx/store";
import { ManageRevenuesStateModel } from "./manage-revenues-state.model";
import * as ManageRevenuesActions from "./manage-revenues.actions";
import { PageableResponseModel } from "@maids/cc-lib/common";

export const ManageRevenuesFeatureKey: "manage_revenues" = "manage_revenues";

export const initialState: ManageRevenuesStateModel = {
  manageRevenuesList:{
    content : [],
    number: 0,
    size: 20,
    totalElements: 0,
    totalPages: 0,
  },
  search:{

    params:{page: 0, size: 20, sort: ''}
  },
  manageRevenuesFormData:{
  },

};

export const reducer = createReducer(
  initialState,
  on(
    ManageRevenuesActions.fetchManageRevenuesList,
    (state, { params }) => {
      return {
        ...state,
        defaultParams:{

          params:params,
        },
      };
    }
  ),
  on(
    ManageRevenuesActions.fetchManageRevenuesListSuccess,
    (state, { payload }) => {
      return {
        ...state,
         manageRevenuesList:{
          content: payload.content ?? [],
          number: payload.number,
          size: payload.size,
          totalElements: payload.totalElements,
          totalPages: payload.totalPages,
        }
      };
    }
  ),
  on(
    ManageRevenuesActions.updateSearchQueryParams,
    (state,{event}) => {
      return {
        ...state,
        search: {
          ...state.search,
          params:{
            ...state.search.params,
            page:event.pageIndex,
            size:event.pageSize,
            search:event.search
          }
        }
      };
    }
  ),
  on(
    ManageRevenuesActions.updateSearch,
    (state,{event}) => {
      return {
        ...state,
        search: {
          ...state.search,
          search:event
        }
      };
    }
  ),
  on(
    ManageRevenuesActions.updateSearchSortQueryParams,
    (state,{event}) => {
      return {
        ...state,
        search: {
          ...state.search,
          params:{
            ...state.search.params,
            sort:`${event.active},${event.direction}`
          }
        }
      };
    }
  ),
on(
    ManageRevenuesActions.resetManageRevenuesState,
    (state) => {
      return {
        ...state,
        ...initialState
      };
    }
  ),

)
