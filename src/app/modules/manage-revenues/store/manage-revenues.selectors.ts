import { createFeatureSelector, createSelector } from "@ngrx/store";
import { ManageRevenuesStateModel } from "./manage-revenues-state.model";
import { ManageRevenuesFeatureKey } from "./manage-revenues.reducer";

export const selectManageRevenuesFeature = createFeatureSelector<ManageRevenuesStateModel>(ManageRevenuesFeatureKey);

export const selectManageRevenuesList = createSelector(
  selectManageRevenuesFeature,
  state => state.manageRevenuesList
)

export const selectSearch = createSelector(
  selectManageRevenuesFeature,
  state=>state.search
)


export const selectManageRevenuesFormData = createSelector(
  selectManageRevenuesFeature,
  state => state.manageRevenuesFormData
)
