<div class="acc-7798 container">
  <div
    class="d-flex align-items-center justify-content-between p-0 mt-2"
    *ngIf="updateMode"
  >
    <div class="col-md-auto px-0">
      <h3>
        <b>Transactions Name:{{ formGroup.controls["id"].value }}</b>
      </h3>
    </div>
    <div
      class="col-md-auto row align-items-center"
      *ngIf="infoList && infoList.clientId"
    >
      <span>Payment ID:</span>
      <a
        href="#!/client/payments/edit/{{ infoList.clientId }}/{{
          infoList.contractId
        }}/{{ infoList.paymentId }}"
        class="cc-primary"
        >{{ infoList.paymentId }}</a
      >
    </div>
  </div>
  <form [formGroup]="formGroup" class="row justify-content-between">
    <div class="col-6">
      <cc-amount-input
        label="Amount"
        formControlName="amount"
        symbol="AED"
        [allowNegative]="true"
        [required]="formGroup.controls['vatNum'].value == 0"
        [disabled]="
          !canEdit || isAccessedFromReconciliatorPage || fromTransferData
        "
      ></cc-amount-input>
      <cc-datepicker
        label="Transaction Date"
        formControlName="date"
        [required]="true"
        [disabled]="isAccessedFromReconciliatorPage"
      ></cc-datepicker>
      <cc-select
        label="From Bucket"
        [lazyPageFetcher]="getBuckets"
        formControlName="fromBucket"
        [emitFullSelectOption]="true"
        [disabled]="!canEdit || isAccessedFromReconciliatorPage"
      ></cc-select>
      <cc-select
        label="Revenue"
        formControlName="revenue"
        [lazyPageFetcher]="revenueValueOptions"
        [emitFullSelectOption]="true"
        [disabled]="!canEdit || isAccessedFromReconciliatorPage"
      ></cc-select>
      <cc-datepicker
        label="Pnl Date"
        formControlName="pnlValueDate"
        [disabled]="isAccessedFromReconciliatorPage"
      ></cc-datepicker>
      <div class="section">
        <cc-select
          [data]="referenceForOptions"
          formControlName="selectedReferenceFor"
          label="For"
          [disabled]="isAccessedFromReconciliatorPage"
        ></cc-select>
        <div
          *ngIf="
            formGroup.controls['selectedReferenceFor'].value == 'HOUSEMAID'
          "
        >
          <div formArrayName="housemaids">
            <div class="d-flex justify-content-end mb-2">
              <button
                cc-raised-button
                color="accent"
                (click)="addHousemaid()"
                style="padding-left: 30px"
              >
                <cc-icon class="icon">add</cc-icon>
                Add Housemaid
              </button>
            </div>
            <div
              *ngFor="let housemaidGroup of housemaids.controls; let i = index"
              [formGroupName]="i"
              class="row align-items-center mb-2"
            >
              <div class="col-5">
                <cc-select
                  [emitFullSelectOption]="true"
                  [lazyPageFetcher]="TMrefHousemaidOptions"
                  label="Select Housemaid"
                  formControlName="housemaid"
                ></cc-select>
              </div>
              <div class="col-5">
                <cc-amount-input
                  label="Amount"
                  formControlName="amount"
                  [allowNegative]="true"
                  symbol="AED"
                ></cc-amount-input>
              </div>
              <div class="col-2">
                <button
                  cc-icon-button
                  color="warn"
                  (click)="deleteHousemaid(i)"
                >
                  <cc-icon>delete</cc-icon>
                </button>
              </div>
            </div>
          </div>
        </div>
        <cc-select
          *ngIf="
            formGroup.controls['selectedReferenceFor'].value == 'OFFICE_STAFF'
          "
          [multiple]="true"
          [emitFullSelectOption]="true"
          [lazyPageFetcher]="TMrefOfficeStaffOptions"
          label="Office Staff"
          formControlName="selectedReferencevalue"
        ></cc-select>
        <cc-select
          *ngIf="formGroup.controls['selectedReferenceFor'].value == 'CLIENT'"
          [multiple]="true"
          [emitFullSelectOption]="true"
          [lazyPageFetcher]="TMrefClientOptions"
          label="Maid cc Family"
          formControlName="selectedReferencevalue"
        ></cc-select>
        <cc-select
          *ngIf="
            formGroup.controls['selectedReferenceFor'].value == 'APPLICANT'
          "
          [multiple]="true"
          [emitFullSelectOption]="true"
          [lazyPageFetcher]="TMrefApplicantOptions"
          label="Applicant"
          formControlName="selectedReferencevalue"
        ></cc-select>
        <cc-select
          *ngIf="formGroup.controls['selectedReferenceFor'].value == 'PROSPECT'"
          [multiple]="true"
          [emitFullSelectOption]="true"
          [lazyPageFetcher]="TMrefProspectOptions"
          label="Maid cc Prospect"
          formControlName="selectedReferencevalue"
        ></cc-select>
        <cc-select
          *ngIf="formGroup.controls['selectedReferenceFor'].value == 'CONTRACT'"
          [multiple]="true"
          [emitFullSelectOption]="true"
          [lazyPageFetcher]="TMrefContractOptions"
          label="Contract"
          formControlName="selectedReferencevalue"
        ></cc-select>
        <cc-select
          *ngIf="
            formGroup.controls['selectedReferenceFor'].value ==
            'FREEDOM_OPERATOR'
          "
          [multiple]="true"
          [emitFullSelectOption]="true"
          [lazyPageFetcher]="TMrefFreedomOptions"
          label="Operator"
          formControlName="selectedReferencevalue"
        ></cc-select>
        <cc-datagrid
          class="my-2 w-100"
          *ngIf="
            formGroup.controls['selectedReferenceFor'].value == 'CLEANER' &&
            ((selectedCleanerList$ | async)?.length ?? 0 > 0)
          "
          [columns]="gridCols"
          [data]="selectedCleanerList$ | async"
          [showPaginator]="false"
          [showColumnMenuButton]="true"
          [showColumnMenuHeader]="false"
          [columnMenuButtonIcon]="'settings'"
          [columnMovable]="true"
          [pageOnFront]="false"
        ></cc-datagrid>
        <cc-datagrid
          class="my-2"
          *ngIf="
            formGroup.controls['selectedReferenceFor'].value == 'CONTACT' &&
            ((selectedContactList$ | async)?.length ?? 0 > 0)
          "
          [columns]="gridCols"
          [data]="selectedContactList$ | async"
          [showPaginator]="false"
          [showColumnMenuButton]="true"
          [showColumnMenuHeader]="false"
          [columnMenuButtonIcon]="'settings'"
          [columnMovable]="true"
          [pageOnFront]="false"
        ></cc-datagrid>
        <div class="input-wrapper">
          <cc-input-mask
            *ngIf="
              formGroup.controls['selectedReferenceFor'].value == 'CLEANER'
            "
            label="Sales Force Id"
            formControlName="selectedReferencevalue"
          ></cc-input-mask>
          <cc-input-mask
            *ngIf="
              formGroup.controls['selectedReferenceFor'].value == 'CONTACT'
            "
            label="Sales Force Id"
            formControlName="selectedReferencevalue"
          ></cc-input-mask>
          <div
            *ngIf="
              formGroup.controls['selectedReferencevalue'].invalid &&
              formGroup.controls['selectedReferencevalue'].touched &&
              (formGroup.controls['selectedReferenceFor'].value == 'CONTACT' ||
                formGroup.controls['selectedReferenceFor'].value == 'CLEANER')
            "
          >
            <span
              class="error-msg"
              *ngIf="formGroup.controls['selectedReferencevalue'].errors?.['minlength']"
            >
              This field is required
            </span>
          </div>
        </div>
        <cc-input
          *ngIf="
            formGroup.controls['selectedReferenceFor'].value == 'CONTACT' ||
            formGroup.controls['selectedReferenceFor'].value == 'CLEANER'
          "
          label="Name"
          formControlName="salesforceName"
        ></cc-input>
        <div
          class="d-flex justify-content-end"
          *ngIf="
            formGroup.controls['selectedReferenceFor'].value == 'CONTACT' ||
            formGroup.controls['selectedReferenceFor'].value == 'CLEANER'
          "
        >
          <button cc-button (click)="add()" style="padding-left: 30px">
            <cc-icon class="icon">add</cc-icon>
            Add
          </button>
        </div>
      </div>
    </div>
    <div class="col-6">
      <cc-textarea
        label="Description"
        formControlName="description"
        [required]="true"
        [disabled]="formGroup.controls['isDescriptionSecured'].value == true"
      ></cc-textarea>
      <cc-select
        label="Type of payment"
        formControlName="paymentType"
        [required]="true"
        [data]="paymentTypeOptions"
        [disabled]="isAccessedFromReconciliatorPage"
      ></cc-select>
      <cc-amount-input
        [allowNegative]="true"
        symbol="AED"
        label="VAT Amount"
        formControlName="vatNum"
        [disabled]="!canEdit"
      ></cc-amount-input>
      <cc-select
        label="VAT Type"
        [data]="vatTypeOptions"
        formControlName="vatType"
        [required]="formGroup.controls['vatNum'].value != 0"
      ></cc-select>
      <cc-select
        label="To Bucket"
        formControlName="toBucket"
        [lazyPageFetcher]="getBuckets"
        [emitFullSelectOption]="true"
        [disabled]="!canEdit || isAccessedFromReconciliatorPage"
      ></cc-select>
      <cc-select
        label="Expense"
        formControlName="expense"
        [lazyPageFetcher]="expenseValueOptions"
        [emitFullSelectOption]="true"
        [disabled]="
          (!canEdit || isAccessedFromReconciliatorPage) &&
          fromTransferDataWithExpense
        "
      ></cc-select>
      <cc-select
        [data]="licenseOptions"
        label="License"
        formControlName="license"
        [required]="true"
        [disabled]="!canEdit"
        [emitFullSelectOption]="true"
      ></cc-select>
      <cc-checkbox
        *ngIf="updateMode"
        color="accent"
        formControlName="missingTaxInvoice"
        [disabled]="true"
      >
        Missing Tax Invoice
      </cc-checkbox>
      <br />
      <div
        class="row mx-0"
        *ngIf="updateMode && formGroup.controls['visaExpenseName'].value"
      >
        <label class="col-4">Visa Expense Name</label>
        <label>{{ formGroup.controls["visaExpenseName"].value }}</label>
      </div>
      <cc-checkbox
        formControlName="accrual"
        [disabled]="isAccessedFromReconciliatorPage"
        >Accrual
      </cc-checkbox>
      <cc-datepicker
        *ngIf="formGroup.controls['accrual'].value == true"
        name="date2"
        [format]="'monthYear'"
        label="Accrual From Date"
        [monthPicker]="true"
        formControlName="accrualFromDate"
      ></cc-datepicker>
      <cc-datepicker
        *ngIf="formGroup.controls['accrual'].value == true"
        name="date2"
        [format]="'monthYear'"
        label="Accrual To Date"
        [monthPicker]="true"
        formControlName="accrualToDate"
      ></cc-datepicker>
    </div>
    <div class="col-12">
      <div class="col-12 background">
        <span class="cc-secondary">Add Attachments</span>
      </div>
      <div class="row col-12 justify-content-between mx-0">
        <div class="col-6">
          <cc-file-uploader
            [disabled]="isAccessedFromReconciliatorPage"
            class="w-100"
            label="Non-VAT Attachments"
            formControlName="nonvat_files"
            tag="NO_VAT"
            [dropzoneConfig]="config"
            [appendNonceToTag]="true"
          ></cc-file-uploader>
        </div>
        <div class="col-6">
          <cc-file-uploader
            class="w-100"
            label="VAT Attachments"
            formControlName="vat_files"
            tag="VAT"
            [dropzoneConfig]="config"
            [appendNonceToTag]="true"
          ></cc-file-uploader>
        </div>
      </div>
    </div>
    <div class="col-12" *ngIf="infoList && infoList.attachmentsCount > 0">
      <div class="col-12 background">
        <span class="cc-secondary">Files For this transaction</span>
      </div>
      <div class="row col-12 px-0 justify-content-center mx-0">
        <cc-datagrid
          class="w-100"
          [data]="infoList.attachments"
          [columns]="gridColsAttachments"
          [showPaginator]="false"
          [noResultTemplate]="noResult"
        ></cc-datagrid>
        <ng-template #noResult> No Files Found ... </ng-template>
      </div>
    </div>
    <div class="col-12 my-2" *ngIf="!hideScreenShots">
      <button cc-raised-button color="accent" (click)="showScreenshots()">
        DD Bank Info Screenshots
      </button>
    </div>
    <hr />
  </form>
  <hr />
  <div class="d-flex justify-content-end my-2 pb-2">
    <div class="col-md-auto">
      <button cc-raised-button (click)="goBack()">Cancel</button>
    </div>
    <div class="col-md-auto">
      <button
        cc-raised-button
        *ngIf="canEdit || !fromTransferData"
        color="primary"
        [disabled]="formGroup.invalid"
        (click)="!updateMode ? saveOperation('one') : saveOperation('edit')"
      >
        Save
      </button>
    </div>
    <div class="col-md-auto">
      <button
        cc-raised-button
        *ngIf="!updateMode"
        color="primary"
        [disabled]="formGroup.invalid"
        (click)="saveOperation('new')"
      >
        Save and New
      </button>
    </div>
  </div>
</div>
