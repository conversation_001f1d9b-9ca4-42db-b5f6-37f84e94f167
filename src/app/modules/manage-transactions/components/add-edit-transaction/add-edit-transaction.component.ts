import {
  ChangeDetectionStrategy,
  Component,
  On<PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ManageTransactionService } from '../../services/manage-transaction.service';
import { CCAuthService } from '@maids/cc-erp-services';
import { PaginationRequest } from '@maids/cc-lib/common';
import { SelectOption } from '@maids/cc-lib/select-input';
import { BehaviorSubject, forkJoin, map, Observable, tap } from 'rxjs';
import {
  FormArray,
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { CCValidatorFn } from '@maids/cc-lib/validation';
import { CCDialog } from '@maids/cc-lib/dialog';
import { CCPreviewAttachmentComponent } from '@maids/cc-lib/preview-attachment';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { ScreenshotsComponent } from '../screenshots/screenshots.component';
import * as moment from 'moment';
import {
  MomentDateAdapter,
  MAT_MOMENT_DATE_ADAPTER_OPTIONS,
} from '@angular/material-moment-adapter';
import {
  DateAdapter,
  MAT_DATE_FORMATS,
  MAT_DATE_LOCALE,
} from '@angular/material/core';
// tslint:disable-next-line:no-duplicate-imports
import { default as _rollupMoment, Moment } from 'moment';
import { CCFileUploaderConfig } from '@maids/cc-lib/file-uploader';
export const MY_FORMATS = {
  parse: {
    dateInput: 'MMM-YYYY',
  },
  display: {
    dateInput: 'MMM-YYYY',
    monthYearLabel: 'MMM YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'MMMM YYYY',
  },
};
@Component({
  selector: 'app-add-edit-transaction',
  templateUrl: './add-edit-transaction.component.html',
  styleUrls: ['./add-edit-transaction.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE, MAT_MOMENT_DATE_ADAPTER_OPTIONS],
    },
    { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS },
  ],
})
export class AddEditTransactionComponent implements OnInit, OnDestroy {
  formGroup = this.formBuilder.group({
    id: [''],
    amount: [''],
    date: [''],
    fromBucket: [''],
    revenue: [''],
    pnlValueDate: [''],
    description: [''],
    isDescriptionSecured: [false],
    paymentType: [''],
    vatNum: [''],
    vatType: [''],
    toBucket: [''],
    expense: [''],
    license: [''],
    missingTaxInvoice: [false],
    vat_files: [[]],
    nonvat_files: [[]],
    ////////////////////
    accrual: [false],
    accrualFromDate: [],
    accrualToDate: [],
    ////////////////////
    selectedReferenceFor: [''],
    selectedReferencevalue: [''],
    salesforceName: [''],
    visaExpenseName: [''],
    housemaids: this.formBuilder.array([]),
  });

  constructor(
    private route: ActivatedRoute,
    private manageTransactionService: ManageTransactionService,
    private authService: CCAuthService,
    private formBuilder: FormBuilder,
    private router: Router,
    private ccDialog: CCDialog,
    private mediaService: MediaService,
    private notifications: CCNotificationService
  ) {}

  addFromOperator: boolean = !!this.route.snapshot.queryParams['freedomOpId'];
  isAccessedFromReconciliatorPage =
    this.route.snapshot.queryParams['accessedFrom'] === 'reconciliator';
  canEdit: boolean = false;
  updateMode: boolean = false;
  refFreedomObj: any | null = null;
  infoList: any | null = null;
  hideScreenShots: boolean = true;
  accountNameFile: any | null = null;
  eidFile: any | null = null;
  ibanFile: any | null = null;
  config: CCFileUploaderConfig = {
    maxFiles: 5,
    maxFilesize: 10,
  };
  private selectedCleanerSubject = new BehaviorSubject<any[]>([]);
  selectedCleanerList$: Observable<any[]> =
    this.selectedCleanerSubject.asObservable();

  private selectedContactSubject = new BehaviorSubject<any[]>([]);
  selectedContactList$: Observable<any[]> =
    this.selectedContactSubject.asObservable();
  private selectedHousemaidSubject = new BehaviorSubject<any[]>([]);
  selectedHousemaidList$: Observable<any[]> =
    this.selectedHousemaidSubject.asObservable();
  editMode: boolean = false;
  editingIndex: number | null = null;

  gridCols: CCGridColumn[] = [
    { field: 'salesforceId', header: 'Sales Force Id' },
    { field: 'salesforceName', header: 'Sales Force Name' },
    {
      field: 'action',
      header: '#',
      type: 'button',
      buttonConfig: {
        mode: 'multiple',
        disabled: false,
        buttons: [
          {
            disabled: false,
            type: 'icon',
            icon: 'delete',
            color: 'accent',
            hidden(record) {
              return false;
            },
            mode: 'single',
            callback: (row: any, index: any) => {
              this.delete(row, index);
            },
          },
          {
            disabled: false,
            type: 'icon',
            icon: 'edit',
            color: 'primary',
            hidden(record) {
              return false;
            },
            mode: 'single',
            callback: (row: any, index: any) => {
              this.edit(row, index);
            },
          },
        ],
      },
    },
  ];
  gridColsAttachments: CCGridColumn[] = [
    { field: 'name', header: 'File' },
    { field: 'creationDate', header: 'Date' },
    { field: 'tag', header: 'Type' },
    {
      field: 'actions',
      header: 'Actions',
      type: 'button',
      buttonConfig: {
        disabled: false,
        mode: 'multiple',
        hidden(record) {
          return false;
        },
        buttons: [
          {
            type: 'stroked',
            color: 'primary',
            disabled: false,
            hidden(record) {
              return false;
            },
            icon: 'folder_open',
            text: 'Open',
            callback: (record) => {
              this.openFile(record);
            },
          },
          {
            type: 'stroked',
            color: 'accent',
            disabled: false,
            hidden(record) {
              return false;
            },
            icon: 'delete',
            text: 'Delete',
            callback: (record) => {
              this.showConfirmDeleteFile(record);
            },
          },
        ],
      },
    },
  ];
  housemaidGridCols: CCGridColumn[] = [
    { field: 'text', header: 'Housemaid' },
    { field: 'amount', header: 'Amount' },
    {
      field: 'action',
      header: '#',
      type: 'button',
      buttonConfig: {
        mode: 'multiple',
        disabled: false,
        buttons: [
          {
            disabled: false,
            type: 'icon',
            icon: 'delete',
            color: 'accent',
            hidden(record) {
              return false;
            },
            mode: 'single',
            callback: (row: any, index: any) => {
              this.deleteHousemaid(index);
            },
          },
          {
            disabled: false,
            type: 'icon',
            icon: 'edit',
            color: 'primary',
            hidden(record) {
              return false;
            },
            mode: 'single',
            callback: (row: any, index: any) => {
              this.editHousemaid(row, index);
            },
          },
        ],
      },
    },
  ];
  readonly getBuckets = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.manageTransactionService.TMgetBuckets(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly revenueValueOptions = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.manageTransactionService.TMrevenueValueOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly expenseValueOptions = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.manageTransactionService.TMexpenseValueOptionsAddEdit(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  /////////////////////////////////////
  readonly TMrefOfficeStaffOptions = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.manageTransactionService.TMrefOfficeStaffOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly TMrefHousemaidOptions = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.manageTransactionService.TMrefHousemaidOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly TMrefClientOptions = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.manageTransactionService.TMrefClientOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly TMrefApplicantOptions = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.manageTransactionService.TMrefApplicantOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly TMrefProspectOptions = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.manageTransactionService.TMrefProspectOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly TMrefContractOptions = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.manageTransactionService.TMrefContractOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly TMrefFreedomOptions = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.manageTransactionService.TMrefFreedomOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  fromTransferData: boolean = false;
  fromTransferDataWithExpense: boolean = false;
  bankStatmentId: any;
  bankStatmentFileId: any;
  paymentTypeOptions: any[] = [];
  licenseOptions: any[] = [];
  vatTypeOptions: any[] = [
    { id: 'IN', text: ' Input VAT' },
    { id: 'OUT', text: ' Output VAT' },
  ];
  referenceForOptions: any[] = [
    { id: 'HOUSEMAID', text: 'Housemaid/Amount' },
    { id: 'CLEANER', text: 'Cleaner' },
    { id: 'OFFICE_STAFF', text: 'Office Staff' },
    { id: 'CLIENT', text: 'Maids cc Family' },
    { id: 'APPLICANT', text: 'Applicant' },
    { id: 'PROSPECT', text: 'Maids cc Prospect' },
    { id: 'CONTACT', text: 'Contact' },
    { id: 'CONTRACT', text: 'Contract' },
    { id: 'FREEDOM_OPERATOR', text: 'Operator' },
  ];

  ngOnInit(): void {
    this.authService.loggedUser$.subscribe((user: any) => {
      this.canEdit =
        user.positions.filter((p: any) => {
          return p.code == 'accounting_edit_transaction';
        }).length > 0;
    });
    this.formGroup.controls['date'].setValue(moment().format('YYYY-MM-DD'));
    if (this.route.snapshot.params['id']) {
      this.updateMode = true;
    }
    if (this.addFromOperator) {
      this.setValuesForFreedomOperator();
    }
    this.loadInitialData();

    this.formGroup.controls['vat_files'].valueChanges.subscribe((val) => {
      let isLicenseMustaqemOrStorage = this.licenseOptions.find((item: any) => {
        item.code == this.formGroup.controls['license'].value?.code;
      });
      if (this.route.snapshot.params['id']) {
        if (this.formGroup.controls['vat_files'].value.length) {
          this.formGroup.controls['missingTaxInvoice'].setValue(
            !['mustaqeem', 'storage'].includes(isLicenseMustaqemOrStorage)
          );
        }
      }
    });
    this.formGroup.controls['selectedReferencevalue'].valueChanges.subscribe(
      (res: any) => {
        if (
          this.formGroup.controls['selectedReferenceFor'].value == 'CLEANER'
        ) {
          if (
            !this.formGroup.controls['selectedReferencevalue'].value.startsWith(
              'a00'
            )
          ) {
            this.formGroup.controls['selectedReferencevalue'].setValue('a00');
          }
        }
        if (
          this.formGroup.controls['selectedReferenceFor'].value == 'CONTACT'
        ) {
          if (
            !this.formGroup.controls['selectedReferencevalue'].value.startsWith(
              '003'
            )
          ) {
            this.formGroup.controls['selectedReferencevalue'].setValue('003');
          }
        }
      }
    );
    this.formGroup.controls['selectedReferenceFor'].valueChanges.subscribe(
      (val) => {
        if (
          val === 'CLEANER' &&
          this.selectedCleanerSubject.getValue().length == 0
        ) {
          this.formGroup.controls['selectedReferencevalue'].setValidators([
            Validators.minLength(4),
          ]);
          this.formGroup.controls[
            'selectedReferencevalue'
          ].updateValueAndValidity();
        } else {
          this.formGroup.controls['selectedReferencevalue'].clearValidators();
          this.formGroup.controls[
            'selectedReferencevalue'
          ].updateValueAndValidity();
        }
        if (
          val === 'CONTACT' &&
          this.selectedContactSubject.getValue().length == 0
        ) {
          this.formGroup.controls['selectedReferencevalue'].setValidators([
            Validators.minLength(4),
          ]);
          this.formGroup.controls[
            'selectedReferencevalue'
          ].updateValueAndValidity();
        } else {
          this.formGroup.controls['selectedReferencevalue'].clearValidators();
          this.formGroup.controls[
            'selectedReferencevalue'
          ].updateValueAndValidity();
        }
        if (val == 'HOUSEMAID') {
          this.housemaids.addValidators([Validators.required]);
          this.housemaids.updateValueAndValidity();
        } else {
          this.housemaids.removeValidators([Validators.required]);
          this.housemaids.updateValueAndValidity();
        }
        if (val === 'CLEANER' || val === 'CONTACT') {
          this.formGroup.controls['selectedReferencevalue'].setValue('');
        } else if (val !== 'HOUSEMAID') {
          this.formGroup.controls['selectedReferencevalue'].setValue([]);
        }
        this.formGroup.controls['salesforceName'].setValue('');
      }
    );
    this.formGroup.controls['accrual'].valueChanges.subscribe((val) => {
      if (val == true) {
        this.formGroup.controls['accrualFromDate'].addValidators([
          Validators.required,
        ]);
        this.formGroup.controls['accrualToDate'].addValidators([
          Validators.required,
        ]);
        this.formGroup.controls['accrualFromDate'].updateValueAndValidity();
        this.formGroup.controls['accrualToDate'].updateValueAndValidity();
      } else {
        this.formGroup.controls['accrualFromDate'].removeValidators([
          Validators.required,
        ]);
        this.formGroup.controls['accrualToDate'].removeValidators([
          Validators.required,
        ]);
        this.formGroup.controls['accrualFromDate'].updateValueAndValidity();
        this.formGroup.controls['accrualToDate'].updateValueAndValidity();
      }
    });
  }
  ngOnDestroy(): void {
    localStorage.removeItem('payroll-transfer');
  }
  loadInitialData() {
    forkJoin({
      paymentTypes: this.getPaymentTypes(),
      licenseOptions: this.getLicenseOptions(),
    }).subscribe({
      next: (results) => {
        this.paymentTypeOptions = results.paymentTypes;
        this.licenseOptions = results.licenseOptions;

        if (this.updateMode) {
          this.getDDBankInfoAttachments();
          this.getTransactionDetails();
        }
        this.loadPayrollTransferData();
      },
    });
  }

  getPaymentTypes(): Observable<any> {
    return this.manageTransactionService.getPaymentTypes();
  }

  getLicenseOptions(): Observable<any> {
    return this.manageTransactionService.TMlicenseOptions().pipe(
      map((res: any) => {
        return res.map((item: any) => {
          return { text: item.name, id: item.id, code: item.code };
        });
      })
    );
  }
  loadPayrollTransferData() {
    let payrollTransferData = localStorage.getItem('payroll-transfer');
    if (payrollTransferData) {
      let transferData = JSON.parse(payrollTransferData);
      this.fromTransferData = true;
      this.canEdit = false;
      this.bankStatmentId = transferData.id;
      this.bankStatmentFileId = transferData.fileId;
      this.formGroup.controls['amount'].setValue(
        transferData.transactionAmount
      );
      this.formGroup.controls['description'].setValue(
        transferData.description || ''
      );
      this.formGroup.controls['date'].setValue(moment().format('YYYY-MM-DD'));
      this.formGroup.controls['paymentType'].setValue('BANK_TRANSFER');
      let mustaqeemLicense = this.licenseOptions.find((item: any) => {
        return item.code == 'mustaqeem';
      });
      if (mustaqeemLicense) {
        this.formGroup.controls['license'].setValue({
          id: mustaqeemLicense.id,
          text: mustaqeemLicense.text,
          code: mustaqeemLicense.code,
        });
      }
      if (transferData.paymentDetail) {
        let expenseCode = transferData.paymentDetail.split('-').pop();
        this.manageTransactionService
          .TM_getExpenseByCode(expenseCode)
          .subscribe((res: any) => {
            if (res && res.length > 0) {
              this.fromTransferDataWithExpense = true;
              this.formGroup.controls['expense'].setValue({
                id: res[0].id,
                text: res[0].name,
              });
            } else {
              this.fromTransferDataWithExpense = false;
            }
          });
      }
      this.manageTransactionService
        .TMgetBuckets(0, 100, 'BC 10')
        .subscribe((res: any) => {
          if (res && res.length > 0) {
            this.formGroup.controls['fromBucket'].setValue({
              id: res[0].id,
              text: res[0].text,
            });
          }
        });
    }
  }
  get housemaids(): FormArray {
    return this.formGroup.get('housemaids') as FormArray;
  }

  createHousemaidGroup(): FormGroup {
    return this.formBuilder.group({
      housemaid: [null],
      amount: [null],
    });
  }

  addHousemaid() {
    this.housemaids.push(this.createHousemaidGroup());
  }

  deleteHousemaid(index: number) {
    this.housemaids.removeAt(index);
  }

  getDDBankInfoAttachments() {
    this.manageTransactionService
      .TMgetDDBankInfoAttachments(this.route.snapshot.params['id'])
      .subscribe((res: any) => {
        if (res && res.length > 0) {
          this.hideScreenShots = false;
          this.accountNameFile = res.find((obj: any) => {
            return obj.tag === 'bank_info_account_name';
          });
          this.ibanFile = res.find((obj: any) => {
            return obj.tag === 'bank_info_iban';
          });
          this.eidFile = res.find((obj: any) => {
            return obj.tag === 'bank_info_eid';
          });
        } else {
          this.hideScreenShots = true;
        }
      });
  }

  getTransactionDetails() {
    this.manageTransactionService
      .TMgetTransactionDetails(this.route.snapshot.params['id'])
      .subscribe((res: any) => {
        if (res == '') {
          this.router.navigateByUrl('/404');
          return;
        } else {
          this.infoList = res;
          this.formGroup.controls['id'].setValue(res.id);
          this.formGroup.controls['accrual'].setValue(res.accrual);
          if (res.accrual) {
            this.formGroup.controls['accrualFromDate'].setValue(
              moment(res.accrualFromDate).format('MMM-YYYY')
            );
            this.formGroup.controls['accrualToDate'].setValue(
              moment(res.accrualToDate).format('MMM-YYYY')
            );
          }
          if (this.canEdit) {
            this.formGroup.controls['missingTaxInvoice'].setValue(
              res.missingTaxInvoice
            );
          }
          this.formGroup.controls['amount'].setValue(res.amount);
          this.formGroup.controls['date'].setValue(
            res.date ? res.date.split(' ')[0] : ''
          );
          this.formGroup.controls['description'].setValue(res.description);
          this.formGroup.controls['paymentType'].setValue(
            res.paymentType.value
          );
          this.formGroup.controls['isDescriptionSecured'].setValue(
            res.isDescriptionSecured
          );
          this.formGroup.controls['visaExpenseName'].setValue(
            res.visaExpenseName
          );
          this.formGroup.controls['pnlValueDate'].setValue(
            res.pnlValueDate ? res.pnlValueDate.split(' ')[0] : ''
          );
          if (res.fromBucket) {
            this.formGroup.controls['fromBucket'].setValue({
              id: res.fromBucket.id,
              text: res.fromBucket.nameWithCardNumber,
            });
          }
          if (res.revenue) {
            if (res.revenue.code == 'UWR 01') {
              this.canEdit = true;
            }
            this.formGroup.controls['revenue'].setValue({
              id: res.revenue.id,
              text: res.revenue.name,
            });
          }
          if (res.toBucket) {
            this.formGroup.controls['toBucket'].setValue({
              id: res.toBucket.id,
              text: res.toBucket.nameWithCardNumber,
            });
          }
          if (res.expense) {
            this.formGroup.controls['expense'].setValue({
              id: res.expense.id,
              text: res.expense.name,
            });
          }
          if (res.license) {
            this.formGroup.controls['license'].setValue({
              id: res.license.id,
              text: res.license.label,
            });
          }
          this.formGroup.controls['selectedReferenceFor'].setValue(
            res.transactionType != 'UNKNOWN' ? res.transactionType : ''
          );
          this.formGroup.controls['vatNum'].setValue(res.vatAmount);
          this.formGroup.controls['vatType'].setValue(res.vatType);
          let values: any[] = [];
          switch (res.transactionType) {
            case 'HOUSEMAID':
              this.housemaids.clear();
              res.housemaids.forEach((element: any) => {
                this.housemaids.push(
                  this.formBuilder.group({
                    housemaid: [
                      {
                        id: element.housemaid.id,
                        text: element.housemaid.label,
                      },
                    ],
                    amount: [element.amount],
                  })
                );
              });
              break;
            case 'CLEANER':
              res.sales.forEach((element: any) => {
                values.push({
                  salesforceId: element.salesforceId,
                  salesforceName: element.salesforceName,
                });
              });
              this.selectedCleanerSubject.next(values);
              break;
            case 'CONTACT':
              res.sales.forEach((element: any) => {
                values.push({
                  salesforceId: element.salesforceId,
                  salesforceName: element.salesforceName,
                });
              });
              this.selectedContactSubject.next(values);
              break;
            case 'OFFICE_STAFF':
              res.officeStaffs.forEach((element: any) => {
                values.push({
                  id: element.officeStaff.id,
                  text: element.officeStaff.label,
                });
              });
              this.formGroup.controls['selectedReferencevalue'].setValue(
                values
              );
              break;
            case 'CLIENT':
              res.clients.forEach((element: any) => {
                values.push({
                  id: element.client.id,
                  text: element.client.name,
                });
              });
              this.formGroup.controls['selectedReferencevalue'].setValue(
                values
              );
              break;
            case 'APPLICANT':
              res.applicants.forEach((element: any) => {
                values.push({
                  id: element.applicant.id,
                  text: element.applicant.label,
                });
              });
              this.formGroup.controls['selectedReferencevalue'].setValue(
                values
              );
              break;
            case 'PROSPECT':
              res.prospects.forEach((element: any) => {
                values.push({
                  id: element.client.id,
                  text: element.client.mobileNumber,
                });
              });
              this.formGroup.controls['selectedReferencevalue'].setValue(
                values
              );
              break;
            case 'CONTRACT':
              res.contracts.forEach((element: any) => {
                values.push({
                  id: element.contract.id,
                  text: 'CONT-' + element.contract.label,
                });
              });
              this.formGroup.controls['selectedReferencevalue'].setValue(
                values
              );
              break;
            case 'FREEDOM_OPERATOR':
              res.freedomOperators.forEach((element: any) => {
                values.push({
                  id: element.freedomOperator.id,
                  text: element.freedomOperator.label,
                });
              });
              this.formGroup.controls['selectedReferencevalue'].setValue(
                values
              );
              break;
          }
        }
      });
  }

  date = new FormControl(moment());
  setValuesForFreedomOperator() {
    this.formGroup.controls['selectedReferenceFor'].setValue(
      'FREEDOM_OPERATOR'
    );
    this.formGroup.controls['date'].setValue(
      this.route.snapshot.queryParams['date']
    );
    this.formGroup.controls['selectedReferencevalue'].setValue(
      this.route.snapshot.queryParams['freedomOpId']
    );
    this.refFreedomObj = {
      id: this.route.snapshot.queryParams['freedomOpId'],
      text: this.route.snapshot.queryParams['freedomOpName'],
    };
    this.formGroup.controls['amount'].setValue(
      this.route.snapshot.queryParams['amt']
    );
  }

  add() {
    if (
      this.formGroup.controls['selectedReferencevalue'].valid &&
      this.formGroup.controls['salesforceName'].valid
    ) {
      if (this.formGroup.controls['selectedReferenceFor'].value == 'CONTACT') {
        this.saveContact();
      } else if (
        this.formGroup.controls['selectedReferenceFor'].value == 'CLEANER'
      ) {
        this.saveCleaner();
      }
      this.formGroup.controls['selectedReferencevalue'].markAsUntouched();
      this.formGroup.controls['salesforceName'].markAsUntouched();
    }
  }

  saveContact() {
    const salesforceId =
      this.formGroup.controls['selectedReferencevalue'].value;
    const salesforceName = this.formGroup.controls['salesforceName'].value;
    if (
      this.formGroup.controls['selectedReferencevalue'].valid &&
      this.formGroup.controls['salesforceName'].valid
    ) {
      let currentContacts = this.selectedContactSubject.getValue();
      if (this.editMode && this.editingIndex !== null) {
        currentContacts[this.editingIndex] = { salesforceId, salesforceName };
      } else {
        const contact = { salesforceId, salesforceName };
        currentContacts.push(contact);
      }
      this.selectedContactSubject.next([...currentContacts]);
      this.editMode = false;
      this.editingIndex = null;
      this.formGroup.controls['selectedReferencevalue'].setValue('');
      this.formGroup.controls['salesforceName'].setValue('');
    }
  }

  saveCleaner() {
    const salesforceId =
      this.formGroup.controls['selectedReferencevalue'].value;
    const salesforceName = this.formGroup.controls['salesforceName'].value;
    if (
      this.formGroup.controls['selectedReferencevalue'].valid &&
      this.formGroup.controls['salesforceName'].valid
    ) {
      let currentCleaners = this.selectedCleanerSubject.getValue();
      if (this.editMode && this.editingIndex !== null) {
        currentCleaners[this.editingIndex] = { salesforceId, salesforceName };
      } else {
        const cleaner = { salesforceId, salesforceName };
        currentCleaners.push(cleaner);
      }
      this.selectedCleanerSubject.next([...currentCleaners]);
      this.editMode = false;
      this.editingIndex = null;
      this.formGroup.controls['selectedReferencevalue'].setValue('');
      this.formGroup.controls['salesforceName'].setValue('');
    }
  }

  edit(element: any, index: number) {
    if (this.formGroup.controls['selectedReferenceFor'].value == 'CONTACT') {
      this.editContact(element, index);
    } else if (
      this.formGroup.controls['selectedReferenceFor'].value == 'CLEANER'
    ) {
      this.editCleaner(element, index);
    }
  }

  editContact(contact: any, index: number) {
    this.editMode = true;
    this.editingIndex = index;
    let currentContacts = this.selectedContactSubject.getValue();
    currentContacts.splice(index, 1);
    this.selectedContactSubject.next([...currentContacts]);
    this.formGroup.controls['selectedReferencevalue'].setValue(
      contact.salesforceId
    );
    this.formGroup.controls['salesforceName'].setValue(contact.salesforceName);
  }

  editCleaner(cleaner: any, index: number) {
    this.editMode = true;
    this.editingIndex = index;
    let currentCleaners = this.selectedCleanerSubject.getValue();
    currentCleaners.splice(index, 1);
    this.selectedCleanerSubject.next([...currentCleaners]);

    this.formGroup.controls['selectedReferencevalue'].setValue(
      cleaner.salesforceId
    );
    this.formGroup.controls['salesforceName'].setValue(cleaner.salesforceName);
  }

  delete(element: any, index: number) {
    if (this.formGroup.controls['selectedReferenceFor'].value == 'CONTACT') {
      this.deleteContact(index);
    } else if (
      this.formGroup.controls['selectedReferenceFor'].value == 'CLEANER'
    ) {
      this.deleteCleaner(index);
    }
  }

  deleteContact(index: number) {
    const currentContacts = this.selectedContactSubject.value;
    currentContacts.splice(index, 1);
    this.selectedContactSubject.next([...currentContacts]);
  }

  deleteCleaner(index: number) {
    const currentCleaners = this.selectedCleanerSubject.value;
    currentCleaners.splice(index, 1);
    this.selectedCleanerSubject.next([...currentCleaners]);
  }

  goBack() {
    this.router.navigateByUrl('/accounting/v2/manage-transactions');
  }

  saveOperation(type: string) {
    let files: any[] = [];
    this.formGroup.controls['vat_files'].value.forEach((element: any) => {
      files.push({ id: element.id });
    });
    this.formGroup.controls['nonvat_files'].value.forEach((element: any) => {
      files.push({ id: element.id });
    });
    let payload: any;
    payload = {
      attachments: files,
      amount: this.formGroup.controls['amount'].value,
      accrual: this.formGroup.controls['accrual'].value == true ? true : false,
      accrualFromDate: this.formGroup.controls['accrualFromDate'].value
        ? moment(this.formGroup.controls['accrualFromDate'].value).format(
            'YYYY-MM-01'
          )
        : null,
      accrualToDate: this.formGroup.controls['accrualToDate'].value
        ? moment(this.formGroup.controls['accrualToDate'].value).format(
            'YYYY-MM-01'
          )
        : null,
      vatAmount: this.formGroup.controls['vatNum'].value,
      vatType: this.formGroup.controls['vatType'].value,
      date: this.formGroup.controls['date'].value
        ? this.formGroup.controls['date'].value + ' 00:00:00'
        : '',
      pnlValueDate: this.formGroup.controls['pnlValueDate'].value
        ? this.formGroup.controls['pnlValueDate'].value + ' 00:00:00'
        : '',
      paymentType: this.formGroup.controls['paymentType'].value,
      fromBucket:
        this.formGroup.controls['fromBucket'].value &&
        this.formGroup.controls['fromBucket'].value.id
          ? { id: this.formGroup.controls['fromBucket']?.value?.id.toString() }
          : null,
      toBucket:
        this.formGroup.controls['toBucket'].value &&
        this.formGroup.controls['toBucket'].value.id
          ? { id: this.formGroup.controls['toBucket']?.value?.id.toString() }
          : null,
      revenue:
        this.formGroup.controls['revenue'].value &&
        this.formGroup.controls['revenue'].value.id
          ? { id: this.formGroup.controls['revenue']?.value?.id.toString() }
          : null,
      expense:
        this.formGroup.controls['expense'].value &&
        this.formGroup.controls['expense'].value.id
          ? { id: this.formGroup.controls['expense']?.value?.id.toString() }
          : null,
      license:
        this.formGroup.controls['license'].value &&
        this.formGroup.controls['license'].value.id
          ? { id: this.formGroup.controls['license']?.value?.id.toString() }
          : '',
      creationTriggeredAutomatically: false,
      previouslyUnknown: false,
      housemaids: [],
      sales: [],
      officeStaffs: [],
      clients: [],
      applicants: [],
      contracts: [],
      freedomOperators: [],
    };
    if (
      this.route.snapshot.params['id'] ||
      !this.formGroup.controls['isDescriptionSecured'].value
    ) {
      payload.description = this.formGroup.controls['description'].value;
    }
    if (this.route.snapshot.params['id']) {
      payload.id = this.route.snapshot.params['id'];
    }
    switch (this.formGroup.controls['selectedReferenceFor'].value) {
      case 'HOUSEMAID':
        let housemaids: any[] = [];
        if (this.housemaids.controls.length == 0) {
          this.notifications.notifyError('Please add at least one housemaid');
          return;
        }
        this.housemaids.controls.forEach((control) => {
          const housemaidControl = control.get('housemaid');
          const amountControl = control.get('amount');
          if (
            housemaidControl &&
            amountControl &&
            housemaidControl.value &&
            amountControl.value
          ) {
            housemaids.push({
              housemaid: { id: housemaidControl.value.id.toString() },
              amount: amountControl.value,
            });
          }
        });
        payload.housemaids = housemaids;
        break;
      case 'OFFICE_STAFF':
        let officeStaffs: any[] = [];
        this.formGroup.controls['selectedReferencevalue'].value.forEach(
          (element: any) => {
            officeStaffs.push({ officeStaff: { id: element.id } });
          }
        );
        payload.officeStaffs = officeStaffs;
        break;
      case 'CLIENT':
      case 'PROSPECT':
        let clients: any[] = [];
        this.formGroup.controls['selectedReferencevalue'].value.forEach(
          (element: any) => {
            clients.push({ client: { id: element.id } });
          }
        );
        payload.clients = clients;
        break;
      case 'APPLICANT':
        let applicants: any[] = [];
        this.formGroup.controls['selectedReferencevalue'].value.forEach(
          (element: any) => {
            applicants.push({ applicant: { id: element.id } });
          }
        );
        payload.applicants = applicants;
        break;
      case 'CONTRACT':
        let contracts: any[] = [];
        this.formGroup.controls['selectedReferencevalue'].value.forEach(
          (element: any) => {
            contracts.push({ contract: { id: element.id } });
          }
        );
        payload.contracts = contracts;
        break;
      case 'FREEDOM_OPERATOR':
        let freedomOperators: any[] = [];
        this.formGroup.controls['selectedReferencevalue'].value.forEach(
          (element: any) => {
            freedomOperators.push({ freedomOperator: { id: element.id } });
          }
        );
        payload.freedomOperators = freedomOperators;
        break;
      case 'CLEANER':
        if (this.selectedCleanerSubject.getValue().length == 0) {
          this.notifications.notifyError('Please add at least one cleaner');
          return;
        }
        payload.sales = this.selectedCleanerSubject.getValue();
        break;
      case 'CONTACT':
        if (this.selectedContactSubject.getValue().length == 0) {
          this.notifications.notifyError('Please add at least one contact');
          return;
        }
        payload.sales = this.selectedContactSubject.getValue();
        break;
    }
    payload.transactionType =
      this.formGroup.controls['selectedReferenceFor'].value == ''
        ? 'UNKNOWN'
        : this.formGroup.controls['selectedReferenceFor'].value;
    let isLicenseMustaqemOrStorage = this.licenseOptions.find(
      (item: any) => item.code == this.formGroup.controls['license'].value?.code
    );
    if (!this.route.snapshot.params['id']) {
      payload.missingTaxInvoice =
        this.formGroup.controls['vat_files'].value.length === 0 &&
        ['mustaqeem', 'storage'].includes(isLicenseMustaqemOrStorage);
    } else {
      payload.missingTaxInvoice =
        this.formGroup.controls['missingTaxInvoice'].value;
    }
    let attachments = this.infoList
      ? this.infoList.attachments.filter((item: any) => {
          return (
            item.tag.toUpperCase().startsWith('VAT') ||
            item.tag.toUpperCase().startsWith('PAYMENT_TAX_INVOICE') ||
            item.tag.toUpperCase().startsWith('PAYMENT_TAX_CREDIT_NOTE') ||
            item.tag.toUpperCase().startsWith('TR_PAYMENT_TAX_INVOICE') ||
            item.tag.toUpperCase().startsWith('TR_PAYMENT_TAX_CREDIT_NOTE')
          );
        })
      : [];
    let firstCond: boolean = false;
    if (!this.route.snapshot.params['id']) {
      firstCond =
        this.formGroup.controls['vat_files'].value.length == 0 &&
        ['mustaqeem', 'storage'].includes(isLicenseMustaqemOrStorage);
    } else {
      firstCond =
        this.formGroup.controls['vat_files'].value.length == 0 &&
        attachments.length == 0 &&
        ['mustaqeem', 'storage'].includes(isLicenseMustaqemOrStorage);
    }
    if (this.route.snapshot.queryParams['visaStatementTransactionId']) {
      payload.visaStatementTransactionId =
        this.route.snapshot.queryParams['visaStatementTransactionId'];
    }
    if (this.formGroup.valid) {
      if (this.bankStatmentId) {
        this.manageTransactionService
          .createTransaction(this.bankStatmentId, payload)
          .subscribe((res: any) => {
            this.notifications.notifySuccess(
              'Transaction has added successfully'
            );
            this.router.navigateByUrl(
              `/accounting/v2/upload-statements/bank-statement-transactions-details/payroll-transfers/${this.bankStatmentFileId}`
            );
          });
        return;
      }
      if (this.route.snapshot.params['id']) {
        this.manageTransactionService
          .updateTransaction(payload)
          .subscribe((res: any) => {
            this.notifications.notifySuccess(
              'Transaction has edited successfully'
            );
            if (this.isAccessedFromReconciliatorPage)
              this.router.navigateByUrl('/accounting/v2/reconciliator');
            else {
              window.location.reload();
            }
          });
      } else {
        this.manageTransactionService
          .addTransaction(payload)
          .subscribe((res: any) => {
            this.notifications.notifySuccess(
              'Transaction has added successfully'
            );
            if (type === 'new') {
              window.location.reload();
            }
            this.router.navigateByUrl('/accounting/v2/manage-transactions');
          });
      }
    }
  }

  openFile(element: any) {
    this.mediaService
      .getFile('accounting/transactions/file/' + element.id)
      .subscribe((res: any) => {
        let blob = new Blob([res], { type: res.type });
        const blobUrl = URL.createObjectURL(blob);
        let file_type = res.type.split('/')[1];
        this.ccDialog.originalOpen(CCPreviewAttachmentComponent, {
          data: {
            url: blobUrl,
            blob: blob,
            type: file_type,
            filename: element.name,
          },
        });
      });
  }

  showConfirmDeleteFile(element: any) {
    this.ccDialog.confirm(
      'Confirm Deletion',
      'Are you sure you want to delete this file?',
      () => {
        this.manageTransactionService
          .deleteTransactionFile(element.id)
          .subscribe((res: any) => {
            this.notifications.notifySuccess('Deleted Successfully');
            this.getTransactionDetails();
          });
      }
    );
  }

  showScreenshots() {
    this.ccDialog.originalOpen(ScreenshotsComponent, {
      data: {
        accountNameFile: this.accountNameFile,
        eidFile: this.eidFile,
        ibanFile: this.ibanFile,
      },
    });
  }

  editHousemaid(housemaid: any, index: number) {
    this.editMode = true;
    this.editingIndex = index;
    let currentHousemaids = this.selectedHousemaidSubject.getValue();
    currentHousemaids.splice(index, 1);
    this.selectedHousemaidSubject.next([...currentHousemaids]);
    (this.formGroup.controls['housemaids'] as FormArray).controls[index]
      .get('housemaid')!
      .setValue({
        id: housemaid.id,
        text: housemaid.text,
      });
    (this.formGroup.controls['housemaids'] as FormArray).controls[index]
      .get('amount')!
      .setValue(housemaid.amount);
  }
}
