import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ManageTransactionsRoutingModule } from './manage-transactions-routing.module';
import { AddEditTransactionComponent } from './components/add-edit-transaction/add-edit-transaction.component';
import { TransactionsListComponent } from './components/transactions-list/transactions-list.component';
import { CCButtonModule } from '@maids/cc-lib/button';
import { CCAccordionModule } from '@maids/cc-lib/accordion';
import { CCDatagridModule } from '@maids/cc-lib/datagrid';
import { CCInputModule } from '@maids/cc-lib/input';
import { CCSelectInputModule } from '@maids/cc-lib/select-input';
import {
  CCDatepickerModule,
  CCDaterangePickerModule,
} from '@maids/cc-lib/date';
import { CCIconModule } from '@maids/cc-lib/icon';
import { CCAdvancedSearchModule } from '@maids/cc-lib/advanced-search';
import { CCDialogModule } from '@maids/cc-lib/dialog';
import { CCTextareaModule } from '@maids/cc-lib/textarea';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  CCAmountInputModule,
  CCInputMaskModule,
} from '@maids/cc-lib/masked-input';
import { CCCheckboxModule } from '@maids/cc-lib/checkbox';
import { CCValidateByModule } from '@maids/cc-lib/validation';
import { CCFileUploaderModule } from '@maids/cc-lib/file-uploader';
import { DROPZONE_CONFIG, DropzoneConfigInterface } from 'ngx-dropzone-wrapper';
import { API } from 'src/environments/api';
import { ScreenshotsComponent } from './components/screenshots/screenshots.component';
export function dropzoneConfigFactory(): DropzoneConfigInterface {
  return {
    url: API.upload, // mandatory, where to upload files
    filesizeBase: 1000, // in bytes
    maxFiles: 5, // default maximum allowed file upload for a single field
    autoProcessQueue: true,
    dictRemoveFileConfirmation: 'Are you sure you want to delete this file?',
  };
}
@NgModule({
  declarations: [
    AddEditTransactionComponent,
    TransactionsListComponent,
    ScreenshotsComponent,
  ],
  imports: [
    CommonModule,
    ManageTransactionsRoutingModule,
    CCButtonModule,
    CCTextareaModule,
    CCAdvancedSearchModule,
    CCAccordionModule,
    CCDatagridModule,
    CCDialogModule,
    CCCheckboxModule,
    CCInputModule,
    CCAmountInputModule,
    CCSelectInputModule,
    CCDatepickerModule,
    CCIconModule,
    CCValidateByModule,
    CCDaterangePickerModule,
    CCFileUploaderModule.forChild({}),
    CCInputMaskModule,
    FormsModule,
    ReactiveFormsModule,
  ],
  providers: [
    { provide: DROPZONE_CONFIG, deps: [], useFactory: dropzoneConfigFactory },
  ],
})
export class ManageTransactionsModule {}
