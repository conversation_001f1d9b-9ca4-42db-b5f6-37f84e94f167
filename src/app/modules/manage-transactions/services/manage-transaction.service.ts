import { HttpClient, HttpContext } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import {
  CCBackendEndpoint,
  REQ_SHOW_LOADING_ICON,
} from '@maids/cc-erp-services';
import { ERPFilterExpression } from '@maids/cc-lib/advanced-search';
import { SearchModel } from '@maids/cc-lib/common';
import { BehaviorSubject, map, Observable } from 'rxjs';
import { API } from 'src/environments/api';

@Injectable({
  providedIn: 'root',
})
export class ManageTransactionService {
  initialSearch: SearchModel<any> = {
    params: {
      page: 0,
      size: 20,
      sort: '',
    },
    search: '',
  };
  searchSubject = new BehaviorSubject<SearchModel<any>>(this.initialSearch);
  constructor(
    @Inject(CCBackendEndpoint) private _api: string,
    private _http: HttpClient
  ) {}
  TMgetTransactionsBasic(searchObj: any): Observable<any> {
    return this._http.post(
      `${this._api}/${API.TMgetTransactionsBasic}`,
      searchObj,
      { params: this.searchSubject.getValue().params }
    );
  }
  TMgetTransactions(): Observable<any> {
    return this._http.post(
      `${this._api}/${API.TMgetTransactions}`,
      {},
      {
        params: {
          ...this.searchSubject.getValue().params,
          search: this.searchSubject.getValue().search,
        },
      }
    );
  }
  TMgetTransactionsAdvanced(searchFilter: any): Observable<any> {
    return this._http.get(`${this._api}/${API.TMgetTransactionsAdvanced}`, {
      headers: {
        searchFilter: searchFilter,
      },
      params: { ...this.searchSubject.getValue().params, withStats: true },
    });
  }
  TMlicenseOptions(): Observable<any> {
    const context = new HttpContext();
    return this._http.get(`${this._api}/${API.TMlicenseOptions}`, {
      context: context.set(REQ_SHOW_LOADING_ICON, false),
    });
  }
  TMgetlicenseOptions(
    page: number,
    size: number = 20,
    search: string = ''
  ): Observable<any> {
    const context = new HttpContext();
    return this._http
      .get(`${this._api}/${API.TMlicenseOptions}`, {
        context: context.set(REQ_SHOW_LOADING_ICON, false),
        params: { page, size, search },
      })
      .pipe(
        map((res: any) => {
          return res.map((item: any) => {
            return {
              text: item.label,
              id: item.id,
              code: item.code,
            };
          });
        })
      );
  }
  TMgetBuckets(
    page: number,
    size: number = 50,
    search: string = ''
  ): Observable<any> {
    const context = new HttpContext();
    return this._http
      .get(`${this._api}/${API.TMgetBuckets}`, {
        params: { page, size, search },
        context: context.set(REQ_SHOW_LOADING_ICON, false),
      })
      .pipe(
        map((res: any) => {
          return res.content.map((item: any) => {
            return {
              text: item.name,
              id: item.id,
            };
          });
        })
      );
  }
  TMrevenueValueOptions(
    page: number,
    size: number = 50,
    search: string = ''
  ): Observable<any> {
    const context = new HttpContext();
    return this._http
      .get(`${this._api}/${API.TMrevenueValueOptions}`, {
        params: { page, size, search },
        context: context.set(REQ_SHOW_LOADING_ICON, false),
      })
      .pipe(
        map((res: any) => {
          return res.content.map((item: any) => {
            return {
              text: item.name,
              id: item.id,
            };
          });
        })
      );
  }
  TMrefOfficeStaffOptions(
    page: number,
    size: number = 50,
    search: string = ''
  ): Observable<any> {
    const context = new HttpContext();
    return this._http
      .get(`${this._api}/${API.TMrefOfficeStaffOptions}/`, {
        params: { page, size, search },
        context: context.set(REQ_SHOW_LOADING_ICON, false),
      })
      .pipe(
        map((res: any) => {
          return res.content.map((item: any) => {
            return {
              text: item.name,
              id: item.id,
            };
          });
        })
      );
  }
  TMrefClientOptions(
    page: number,
    size: number = 50,
    search: string = ''
  ): Observable<any> {
    const context = new HttpContext();
    return this._http
      .get(`${this._api}/${API.TMrefClientOptions}`, {
        params: { page, size, search },
        context: context.set(REQ_SHOW_LOADING_ICON, false),
      })
      .pipe(
        map((res: any) => {
          return res.content.map((item: any) => {
            return {
              text: item.name,
              id: item.id,
            };
          });
        })
      );
  }
  TMrefApplicantOptions(
    page: number,
    size: number = 50,
    search: string = ''
  ): Observable<any> {
    const context = new HttpContext();
    return this._http
      .get(`${this._api}/${API.TMrefApplicantOptions}`, {
        params: { page, size, name: search },
        context: context.set(REQ_SHOW_LOADING_ICON, false),
      })
      .pipe(
        map((res: any) => {
          return res.content.map((item: any) => {
            return {
              text: item.name,
              id: item.id,
            };
          });
        })
      );
  }
  TMrefProspectOptions(
    page: number,
    size: number = 50,
    search: string = ''
  ): Observable<any> {
    const context = new HttpContext();
    return this._http
      .get(`${this._api}/${API.TMrefProspectOptions}`, {
        params: { page, size, search },
        context: context.set(REQ_SHOW_LOADING_ICON, false),
      })
      .pipe(
        map((res: any) => {
          return res.content.map((item: any) => {
            return {
              text: item.mobileNumber + (item.name ? ' - ' + item.name : ''),
              id: item.id,
            };
          });
        })
      );
  }
  TMrefHousemaidOptions(
    page: number,
    size: number = 50,
    search: string = ''
  ): Observable<any> {
    const context = new HttpContext();
    return this._http
      .get(`${this._api}/${API.TMrefHousemaidOptions}`, {
        params: { page, size, search },
        context: context.set(REQ_SHOW_LOADING_ICON, false),
      })
      .pipe(
        map((res: any) => {
          return res.content.map((item: any) => {
            return {
              text: item.label,
              id: item.id,
            };
          });
        })
      );
  }
  TMrefContractOptions(
    page: number,
    size: number = 50,
    search: string = ''
  ): Observable<any> {
    const context = new HttpContext();
    return this._http
      .get(`${this._api}/${API.TMrefContractOptions}`, {
        params: { page, size, search },
        context: context.set(REQ_SHOW_LOADING_ICON, false),
      })
      .pipe(
        map((res: any) => {
          return res.content.map((item: any) => {
            return {
              text: 'CONT-' + item.label,
              id: item.id,
            };
          });
        })
      );
  }
  TMrefFreedomOptions(
    page: number,
    size: number = 50,
    search: string = ''
  ): Observable<any> {
    const context = new HttpContext();
    return this._http
      .get(`${this._api}/${API.TMrefFreedomOptions}`, {
        params: { page, size, search },
        context: context.set(REQ_SHOW_LOADING_ICON, false),
      })
      .pipe(
        map((res: any) => {
          return res.content.map((item: any) => {
            return {
              text: item.name,
              id: item.id,
            };
          });
        })
      );
  }
  TMexpenseValueOptions(
    page: number,
    size: number = 50,
    search: string = ''
  ): Observable<any> {
    const context = new HttpContext();
    return this._http
      .get(`${this._api}/${API.TMexpenseValueOptions}`, {
        params: {
          search,
          page,
          size,
          withDeleted: true,
          activeFilter: 'all',
        },
        context: context.set(REQ_SHOW_LOADING_ICON, false),
      })
      .pipe(
        map((res: any) => {
          return res.content.map((item: any) => {
            return {
              text: item.name + (item.deleted ? ' (Deleted)' : ''),
              id: item.id,
            };
          });
        })
      );
  }
  TMexpenseValueOptionsAddEdit(
    page: number,
    size: number = 50,
    search: string = ''
  ): Observable<any> {
    const context = new HttpContext();
    return this._http
      .get(`${this._api}/${API.TMexpenseValueOptions}`, {
        params: {
          search,
          page,
          size,
        },
        context: context.set(REQ_SHOW_LOADING_ICON, false),
      })
      .pipe(
        map((res: any) => {
          return res.content.map((item: any) => {
            return {
              text: item.name,
              id: item.id,
            };
          });
        })
      );
  }
  deleteTransaction(id: any): Observable<any> {
    return this._http.delete(`${this._api}/${API.deleteTransaction}/${id}`);
  }
  TM_SearchGenerateCSVmail(params: any, data: any): Observable<any> {
    return this._http.post(
      `${this._api}/${API.TM_SearchGenerateCSVmail}`,
      data,
      { params }
    );
  }
  TM_BasicGenerateCSVmail(params: any, data: any): Observable<any> {
    return this._http.post(
      `${this._api}/${API.TM_BasicGenerateCSVmail}`,
      data,
      { params }
    );
  }
  TM_AdvGenerateCSVmail(
    params: any,
    data: any,
    searchFilter: any
  ): Observable<any> {
    return this._http.post(`${this._api}/${API.TM_AdvGenerateCSVmail}`, data, {
      params,
      headers: {
        searchFilter:
          typeof searchFilter === 'string'
            ? searchFilter
            : searchFilter
            ? JSON.stringify(searchFilter)
            : '',
      },
    });
  }
  TMgetDDBankInfoAttachments(id: any): Observable<any> {
    return this._http.get(
      `${this._api}/${API.TMgetDDBankInfoAttachments}/${id}`
    );
  }
  TMgetTransactionDetails(id: any): Observable<any> {
    return this._http.get(`${this._api}/${API.TMgetTransactionDetails}/${id}`);
  }
  getPaymentTypes(): Observable<any> {
    return this._http.get(`${this._api}/${API.TMgetPaymentTypes}`).pipe(
      map((res: any) => {
        return res.map((val: any) => {
          return {
            id: val.value,
            text: val.label,
          };
        });
      })
    );
  }
  addTransaction(data: any): Observable<any> {
    return this._http.post(`${this._api}/${API.addTransaction}`, data);
  }
  updateTransaction(data: any): Observable<any> {
    return this._http.post(`${this._api}/${API.updateTransaction}`, data);
  }
  deleteTransactionFile(id: any): Observable<any> {
    return this._http.delete(`${this._api}/${API.deleteTransactionFile}/${id}`);
  }
  TM_getExpenseByCode(code: any): Observable<any> {
    return this._http.get(`${this._api}/${API.TM_getExpenseByCode}`, {
      params: { code },
    });
  }
  createTransaction(bankStatementId: any, data: any): Observable<any> {
    return this._http.post(
      `${this._api}/${API.TM_createTransaction}/${bankStatementId}`,
      data
    );
  }
}
