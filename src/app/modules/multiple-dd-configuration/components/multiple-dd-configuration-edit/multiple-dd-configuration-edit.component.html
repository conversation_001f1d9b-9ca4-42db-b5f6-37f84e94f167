<cc-dialog-header>
  <h1 cc-dialog-title>Multiple DD Configuration Data</h1>
  <a
    role="button"
    type="button"
    cc-icon-button
    cc-dialog-close-button
    cc-dialog-close
  ></a>
</cc-dialog-header>

<cc-dialog-content>
  <form [formGroup]="formGroup">
    <div class="row">
      <div class="col-6">
        <div class="row align-items-center px-2">
          <cc-input
            label="OIC"
            type="text"
            class="w-100"
            id="oic"
            [required]="true"
            formControlName="oic"
            placeholder="OIC"
          ></cc-input>
        </div>
      </div>
      <div class="col-6">
        <div class="row align-items-center px-2">
          <cc-input
            label="Initial Seq Number"
            class="w-100"
            type="text"
            id="initialSeqNumber"
            [required]="true"
            formControlName="initialSeqNumber"
            placeholder="Initial Seq Number"
          ></cc-input>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-6">
        <div class="row align-items-center px-2">
          <cc-input
            label="File Index"
            class="w-100"
            type="text"
            id="fileIndex"
            [required]="true"
            formControlName="fileIndex"
            placeholder="File Index"
          ></cc-input>
        </div>
      </div>
      <div class="col-6">
        <div class="row align-items-center px-2">
          <cc-input
            label="Customer Type"
            class="w-100"
            type="text"
            id="customerType"
            [required]="true"
            formControlName="customerType"
            placeholder="Customer Type"
          ></cc-input>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-6">
        <div class="row align-items-center px-2">
          <cc-input
            label="Customer Id Type"
            class="w-100"
            type="text"
            id="customerIdType"
            [required]="true"
            formControlName="customerIdType"
            placeholder="Customer Id Type"
          ></cc-input>
        </div>
      </div>
      <div class="col-6">
        <div class="row align-items-center px-2">
          <cc-input
            label="DDA Issued For"
            class="w-100"
            type="text"
            id="ddaIssuedFor"
            [required]="true"
            formControlName="ddaIssuedFor"
            placeholder="DDA Issued For"
          ></cc-input>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-6">
        <div class="row align-items-center px-2">
          <cc-input
            label="Funding Account Type"
            class="w-100"
            type="text"
            id="fundingAccountType"
            [required]="true"
            formControlName="fundingAccountType"
            placeholder="Funding Account Type"
          ></cc-input>
        </div>
      </div>
      <div class="col-6">
        <div class="row align-items-center px-2">
          <cc-input
            label="DD Amount Type"
            class="w-100"
            type="text"
            id="ddAmountType"
            [required]="true"
            formControlName="ddAmountType"
            placeholder="DD Amount Type"
          ></cc-input>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-6">
        <div class="row align-items-center px-2">
          <cc-input
            class="w-100"
            label="Defined Days"
            type="text"
            id="definedDays"
            [required]="true"
            formControlName="definedDays"
            placeholder="Defined Days"
          ></cc-input>
        </div>
      </div>
      <div class="col-6">
        <div class="row align-items-center px-2">
          <cc-input
            class="w-100"
            label="Bulk File Format"
            type="text"
            id="bulkFileFormat"
            [required]="true"
            formControlName="bulkFileFormat"
            placeholder="Bulk File Format"
          ></cc-input>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-6">
        <div class="row align-items-center px-2">
          <cc-input
            label="Capture Mode"
            class="w-100"
            type="text"
            id="captureMode"
            [required]="true"
            formControlName="captureMode"
            placeholder="Capture Mode"
          ></cc-input>
        </div>
      </div>
    </div>
  </form>
</cc-dialog-content>
<cc-dialog-actions>
  <button cc-button cc-dialog-close>Cancel</button>
  <button
    cc-button
    color="accent"
    (click)="save()"
    [disabled]="formGroup.invalid"
  >
    OK
  </button>
</cc-dialog-actions>
