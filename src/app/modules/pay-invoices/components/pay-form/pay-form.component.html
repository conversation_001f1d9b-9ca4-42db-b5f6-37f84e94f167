<form [formGroup]="formGroup" class="row">
  <div class="col-4">
    <cc-input label="Beneficiary" formControlName="beneficiary"></cc-input>
  </div>
  <div class="col-4">
    <cc-datepicker label="From Date" formControlName="fromDate"></cc-datepicker>
  </div>
  <div class="col-4">
    <cc-input label="Maid's Name" formControlName="maidName"></cc-input>
  </div>
  <div class="col-4">
    <cc-input label="Balance" formControlName="balance"></cc-input>
  </div>
  <div class="col-4">
    <cc-datepicker label="To Date" formControlName="toDate"></cc-datepicker>
  </div>
</form>
<hr />
<div class="d-flex justify-content-end">
  <button cc-raised-button color="accent" (click)="exportCSV()">Export To CSV</button>
  <button cc-raised-button class="ml-2" color="primary" (click)="getInvoiceDetails()">
    Search
  </button>
  <button cc-raised-button class="ml-2" (click)="reset()">Reset</button>
</div>
<div class="d-flex align-items-center">
  <button
    cc-raised-button
    (click)="isSelectAll ? unSelectAll() : selectAll()"
    color="accent"
  >
    {{ isSelectAll ? "Unselect Page" : "Select Page" }}
  </button>
  <span class="text-danger ml-2"> {{ selectedRowsCount }} Seleted </span>
</div>
<cc-datagrid
  [data]="invoices?.expenseRequestPayInvoiceDtosPage?.content ?? []"
  [columns]="gridCols"
  [length]="invoices?.expenseRequestPayInvoiceDtosPage?.totalElements ?? 0"
  [pageIndex]="invoices?.expenseRequestPayInvoiceDtosPage?.number ?? 0"
  [pageSize]="invoices?.expenseRequestPayInvoiceDtosPage?.size ?? 0"
  [pageSizeOptions]="[20]"
  [pageOnFront]="false"
  (page)="getNextPage($event)"
  [showColumnMenuButton]="true"
  [showColumnMenuHeader]="false"
  [columnMenuButtonIcon]="'settings'"
  [columnMovable]="true"
  [noResultTemplate]="noResult"
  [cellTemplate]="{ checkbox: checkbox, amount: amount }"
>
</cc-datagrid>
<ng-template #checkbox let-row let-index="index" let-col="colDef">
  <cc-checkbox [(ngModel)]="selectedInvoices[row.id]"> </cc-checkbox>
</ng-template>
<ng-template #amount let-row let-index="index" let-col="colDef">
  <ng-container *ngIf="editingRow === index; else displayAmount">
    <div class="row align-items-center">
      <div class="col-md-auto px-0">
        <cc-input
          type="number"
          style="width: 80px"
          [(ngModel)]="row.amount"
        ></cc-input>
      </div>
      <div class="col-md-auto px-0">
        <button cc-icon-button (click)="saveAmount()">
          <cc-icon>check</cc-icon>
        </button>
      </div>
      <div class="col-md-auto px-0">
        <button cc-icon-button (click)="cancelEdit()">
          <cc-icon>close</cc-icon>
        </button>
      </div>
    </div>
  </ng-container>
  <ng-template #displayAmount>
    {{ row.amount }}
    <button cc-icon-button (click)="editAmount(index)">
      <cc-icon>edit</cc-icon>
    </button>
  </ng-template>
</ng-template>
<ng-template #noResult> </ng-template>
<div class="d-flex">
  <span>Total Amount To Pay : {{ selectedRowsAmount }} </span>
</div>
<div class="row my-1" [formGroup]="invoiceForm">
  <div class="col-8">
    <div class="col-12 px-0">
      <cc-file-uploader
        label="Attach Invoice"
        formControlName="paymentInvoiceAttachement"
        tag="EXPENSE_PAYMENT_INVOICE"
        [required]="true"
        [dropzoneConfig]="config"
      ></cc-file-uploader>
    </div>
    <div class="col-12 row px-0 mt-1">
      <div class="col-4">
        <label class="required">Does the invoice Contain Vat?</label>
      </div>
      <div class="col-8">
        <cc-radio-group
          formControlName="invoiceContainVat"
          [disabled]="invoices?.taxable !== true"
        >
          <cc-radio-button [value]="true">Yes</cc-radio-button>
          <cc-radio-button class="ml-4" [value]="false">No</cc-radio-button>
        </cc-radio-group>
      </div>
    </div>
    <div
      class="row align-items-center mt-1"
      *ngIf="invoiceForm.controls['invoiceContainVat'].value == true"
    >
      <div class="col-4"></div>
      <div class="col-8">
        <cc-input
          label="Vat Amount (AED)"
          formControlName="vatAmount"
          [required]="invoiceForm.controls['invoiceContainVat'].value == true"
        ></cc-input>
      </div>
    </div>
  </div>
</div>
<hr />
<div class="row justify-content-end px-3">
  <button cc-raised-button (click)="cancel()">Cancel</button>
  <button
    cc-raised-button
    class="ml-2"
    [disabled]="selectedInvoices.length == 0 || invoiceForm.invalid"
    color="primary"
    (click)="pay()"
  >
    Pay
  </button>
</div>
<ng-template #noData>
  <div class="row justify-content-center">
    <span class="text-danger">No Data Found</span>
  </div>
</ng-template>
