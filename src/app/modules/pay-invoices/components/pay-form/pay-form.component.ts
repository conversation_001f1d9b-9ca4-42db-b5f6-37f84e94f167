import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { map, Observable, of } from 'rxjs';
import { PayInvoicesService } from '../../services/pay-invoices.service';
import { FormBuilder, FormControl, Validators } from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { PageableResponseModel } from '@maids/cc-lib/common';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { CCValidatorFn } from '@maids/cc-lib/validation';
import { CCFileUploaderConfig } from '@maids/cc-lib/file-uploader';

@Component({
  selector: 'app-pay-form',
  templateUrl: './pay-form.component.html',
  styleUrls: ['./pay-form.component.scss'],
})
export class PayFormComponent implements OnInit {
  formGroup = this.formBuilder.group({
    beneficiary: [''],
    balance: [''],
    fromDate: [null],
    toDate: [null],
    maidName: [null],
  });
  config: CCFileUploaderConfig = {
    maxFilesize: 25,
  };
  isSelectAll: boolean = false;
  invoices: any;
  gridCols: CCGridColumn[] = [
    { field: 'checkbox', header: 'Select' },
    { field: 'expenseRequestDate', header: 'Date', type: 'date' },
    { field: 'expense', header: 'Expense' },
    { field: 'relatedTo', header: 'Related To' },
    { field: 'maidType', header: 'Maid Type' },
    { field: 'amount', header: 'Amount' },
    { field: 'reason', header: 'Reason' },
  ];
  selectedInvoices: boolean[] = []; // Explicitly declare the type of the array
  editingRow: any;
  currentRow: any;
  totalAmout: number = 0;
  invoiceForm = this.formBuilder.group({
    paymentInvoiceAttachement: [''],
    invoiceContainVat: [''],
    vatAmount: [''],
  });
  constructor(
    private route: ActivatedRoute,
    private payInvoicesService: PayInvoicesService,
    private formBuilder: FormBuilder,
    public readonly notification: CCNotificationService,
    private router: Router,
    private mediaService: MediaService
  ) {}

  ngOnInit(): void {
    this.getInvoiceDetails();
  }
  getInvoiceDetails(page: number = 0, size: number = 20) {
    this.payInvoicesService
      .getPayInvoice(
        this.route.snapshot.params['beneficiaryId'],
        this.route.snapshot.params['expenseId'],
        { page, size },
        {
          fromDate: this.formGroup.controls['fromDate'].value
            ? this.formGroup.controls['fromDate'].value + ' 00:00:00'
            : null,
          toDate: this.formGroup.controls['toDate'].value
            ? this.formGroup.controls['toDate'].value + ' 00:00:00'
            : null,
          maidName:
            this.formGroup.controls['maidName'].value !== '' ||
            this.formGroup.controls['maidName'].value !== null
              ? this.formGroup.controls['maidName'].value
              : null,
        }
      )
      .subscribe((res: any) => {
        this.invoices = res;
        this.formGroup.controls['beneficiary'].setValue(res.beneficiary);
        this.formGroup.controls['beneficiary'].disable();
        this.formGroup.controls['balance'].setValue(res.balance);
        this.formGroup.controls['balance'].disable();
        if (res.taxable == true) {
          this.invoiceForm.controls['invoiceContainVat'].setValue(true);
          this.invoiceForm.controls['invoiceContainVat'].disable();
        } else {
          if (res.taxable == false) {
            this.invoiceForm.controls['invoiceContainVat'].setValue(false);
            this.invoiceForm.controls['invoiceContainVat'].disable();
          } else {
            this.invoiceForm.controls['invoiceContainVat'].enable();
            this.invoiceForm.controls['invoiceContainVat'].setValue('');
          }
        }
        if (
          res.expenseRequestPayInvoiceDtos.length &&
          res.paymentMethod.value !== 'BANK_TRANSFER'
        ) {
          this.notification.notifyError(
            'Supplier payment method should be Bank Transfer'
          );
        }
      });
  }

  getNextPage(page: PageEvent) {
    this.getInvoiceDetails(page.pageIndex, page.pageSize);
  }

  exportCSV() {
    let data: any;
    data = {
      fromDate: !!this.formGroup.controls['fromDate'].value
        ? this.formGroup.controls['fromDate'].value + ' 00:00:00'
        : '',
      toDate: !!this.formGroup.controls['toDate'].value
        ? this.formGroup.controls['toDate'].value + ' 00:00:00'
        : '',
      maidName: !!this.formGroup.controls['maidName'].value
        ? this.formGroup.controls['maidName'].value
        : null,
    };
    this.mediaService.downloadFile(
      `accounting/expense-payment/pay-invoice-exportToCsv/${this.route.snapshot.params['beneficiaryId']}/${this.route.snapshot.params['expenseId']}`,
      '',
      { method: 'POST', body: data }
    );
  }
  editAmount(index: number) {
    this.editingRow = index;
    const content = this.invoices?.expenseRequestPayInvoiceDtosPage?.content;
    if (content && content[index]) {
      this.currentRow = content[index].amount;
    }
  }

  saveAmount() {
    this.editingRow = null;
  }
  cancelEdit() {
    if (this.editingRow !== null) {
      const content = this.invoices?.expenseRequestPayInvoiceDtosPage?.content;

      if (content && content[this.editingRow] && this.currentRow !== null) {
        content[this.editingRow].amount = this.currentRow;
      }
    }

    this.editingRow = null;
    this.currentRow = null;
  }
  selectAll() {
    if (this.isSelectAll) {
      this.unSelectAll();
      return;
    }
    this.isSelectAll = true;
    this.invoices?.expenseRequestPayInvoiceDtosPage?.content.forEach(
      (element: any) => {
        this.selectedInvoices[element.id] = true;
      }
    );
  }
  unSelectAll() {
    this.isSelectAll = false;
    this.selectedInvoices = [];
  }
  get selectedRowsCount() {
    return Object.values(this.selectedInvoices).filter(Boolean).length;
  }
  get selectedRowsAmount() {
    if (!this.invoices?.expenseRequestPayInvoiceDtosPage?.content) {
      return 0;
    }
    return this.invoices.expenseRequestPayInvoiceDtosPage.content
      .filter((element: any) => this.selectedInvoices[element.id])
      .reduce(
        (total: number, element: any) => total + (+element.amount || 0),
        0
      );
  }
  pay() {
    if (
      this.invoices.paymentMethod.value == null ||
      this.invoices.paymentMethod.value !== 'BANK_TRANSFER'
    ) {
      this.notification.notifyError(
        'Supplier payment method should be Bank Transfer',
        2000
      );
      return;
    }

    if (this.invoiceForm.valid) {
      let payload: any;
      payload = {
        taxable: this.invoices.taxable,
        attachments: [
          {
            id: this.invoiceForm.controls['paymentInvoiceAttachement'].value[0]
              .id,
          },
        ],
        expenseRequestPayInvoiceDtos:
          this.invoices.expenseRequestPayInvoiceDtosPage.content
            .filter((element: any) => this.selectedInvoices[element.id])
            .map((element: any) => {
              return {
                ...element,
              };
            }),
      };
      if (this.invoiceForm.controls['invoiceContainVat'].value == true) {
        payload.vatAmount = this.invoiceForm.controls['vatAmount'].value;
      }
      this.payInvoicesService.payInvoice(payload).subscribe({
        next: (res: any) => {
          this.notification.notifySuccess('Done Successfully', 2000);
          this.router.navigateByUrl('/accounting/v2/pay-invoices');
        },
        error: (err) => {
          this.notification.notifyError(err.error.message, 2000);
        },
      });
    }
  }
  reset() {
    this.formGroup.patchValue({
      fromDate: null,
      toDate: null,
      maidName: null,
    });
    this.getInvoiceDetails();
  }
  cancel() {
    this.router.navigateByUrl('/accounting/v2/pay-invoices');
  }
}
