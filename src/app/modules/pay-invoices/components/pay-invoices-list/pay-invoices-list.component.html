<cc-datagrid
  [data]="invoices?.content??[]"
  [columns]="gridCols"
  [length]="invoices?.totalElements??0"
  [pageOnFront]="false"
  [pageIndex]="invoices?.number??0"
  [pageSize]="invoices?.size??0"
  [pageSizeOptions]="[20]"
  (page)="getNextPage($event)"
  [stickyHeader]="true"
  [showColumnMenuButton]="true"
  [showColumnMenuHeader]="false"
  [columnMenuButtonIcon]="'settings'"
  [columnMovable]="true"
  [noResultTemplate]="noResult"
>
  <cc-grid-actions-list
    *ccActionData="let ctx of invoices?.content; row as row"
    style="width: fit-content;"
  >
    <button *cc-action cc-raised-button color="primary" (click)="pay(row)">
      Pay
    </button>
  </cc-grid-actions-list>
</cc-datagrid>
<ng-template #noResult> </ng-template>
