import { Component, OnInit } from '@angular/core';
import { Observable } from 'rxjs';
import { PayInvoicesService } from '../../services/pay-invoices.service';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { PageEvent } from '@angular/material/paginator';
import { Router } from '@angular/router';
@Component({
  selector: 'app-pay-invoices-list',
  templateUrl: './pay-invoices-list.component.html',
  styleUrls: ['./pay-invoices-list.component.scss'],
})
export class PayInvoicesListComponent implements OnInit {
  invoices: any;
  gridCols: CCGridColumn[] = [
    { field: 'beneficiary', header: 'Supplier' },
    { field: 'balance', header: 'Balance' },
  ];
  constructor(
    private payInvoicesService: PayInvoicesService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.getInvoices();
  }
  getInvoices(page: number = 0, size: number = 20) {
    this.payInvoicesService
      .getPayInvoicesList({ page, size })
      .subscribe((res: any) => {
        this.invoices = res;
      });
  }
  getNextPage(page: PageEvent) {
    this.getInvoices(page.pageIndex, page.pageSize);
  }
  pay(row: any) {
    if(row.isStatement){
      this.router.navigateByUrl(
        `/accounting/v2/pay-invoices/pay-statement/${row.beneficiaryId}`
      );
    }else{
      this.router.navigateByUrl(
        `/accounting/v2/pay-invoices/pay-form/${row.beneficiaryId}/${row.expenseId}`
      );
    }
    
  }
}
