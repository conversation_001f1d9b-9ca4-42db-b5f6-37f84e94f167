<!-- Search Form -->
<form [formGroup]="formGroup" class="row">
  <div class="col-4">
    <cc-input label="Beneficiary" formControlName="beneficiary"></cc-input>
  </div>
  <div class="col-4">
    <cc-datepicker label="From Date" formControlName="fromDate"></cc-datepicker>
  </div>
  <div class="col-4">
    <cc-input label="Maid's Name" formControlName="maidName"></cc-input>
  </div>
  <div class="col-4">
    <cc-input label="Balance" formControlName="balance"></cc-input>
  </div>
  <div class="col-4">
    <cc-datepicker label="To Date" formControlName="toDate"></cc-datepicker>
  </div>
  <div class="col-4">
    <cc-input
      label="Passport Number"
      formControlName="passportNumber"
    ></cc-input>
  </div>
</form>

<!-- Action Buttons -->
<hr />
<div class="d-flex justify-content-end">
  <button
    cc-raised-button
    color="primary"
    (click)="getTables()"
    style="padding-left: 30px"
  >
    <cc-icon style="position: absolute; top: 0.4em; left: 6px"
      >search</cc-icon
    >
    Search
  </button>
  <button cc-raised-button (click)="rematch()" class="ml-2" color="accent">
    Rematch
  </button>
</div>

<!-- Pending Matched Transactions -->
<div class="mt-4">
  <h4>Pending Matched Transactions</h4>
  <cc-datagrid
    [data]="pendingMatchedData"
    [columns]="pendingMatchedCols"
    [length]="pendingMatchedPagination?.totalElements ?? 0"
    [pageIndex]="pendingMatchedPagination?.number ?? 0"
    [pageSize]="pendingMatchedPagination?.size ?? 20"
    [pageSizeOptions]="[20]"
    [pageOnFront]="false"
    (page)="getPendingMatchedNextPage($event)"
    [showColumnMenuButton]="true"
    [columnMovable]="true"
    [cellTemplate]="{
      amount: pendingAmountTemplate,
      actions: pendingActionsTemplate
    }"
  >
  </cc-datagrid>
</div>

<!-- Mismatched Transactions -->
<div class="mt-4">
  <h4>Mismatched Transactions</h4>
  <cc-datagrid
    [data]="mismatchedData"
    [columns]="mismatchedCols"
    [length]="mismatchedPagination?.totalElements ?? 0"
    [pageIndex]="mismatchedPagination?.number ?? 0"
    [pageSize]="mismatchedPagination?.size ?? 20"
    [pageSizeOptions]="[20]"
    [pageOnFront]="false"
    (page)="getMismatchedNextPage($event)"
    [showColumnMenuButton]="true"
    [columnMovable]="true"
    [cellTemplate]="{
      amount: mismatchedAmountTemplate,
      actions: mismatchedActionsTemplate
    }"
  >
  </cc-datagrid>
</div>

<!-- Matched Transactions -->
<div class="mt-4">
  <div class="d-flex justify-content-between align-items-center">
    <h4>Matched Transactions</h4>
    <button cc-raised-button (click)="exportToCSV()" color="accent">
      Export to CSV
    </button>
  </div>

  <div class="mt-2 mb-2">
    <cc-checkbox
      [(ngModel)]="selectAllMatched"
      (ngModelChange)="selectAllMatchedItems()"
      [disabled]="matchedData.length === 0"
    >
      {{ selectAllMatched ? "Unselect All" : "Select All" }}
    </cc-checkbox>
  </div>

  <cc-datagrid
    [data]="matchedData"
    [columns]="matchedCols"
    [length]="matchedPagination?.totalElements ?? 0"
    [pageIndex]="matchedPagination?.number ?? 0"
    [pageSize]="matchedPagination?.size ?? 20"
    [pageSizeOptions]="[20]"
    [pageOnFront]="false"
    (page)="getMatchedNextPage($event)"
    [showColumnMenuButton]="true"
    [columnMovable]="true"
    [cellTemplate]="{ checkbox: checkboxTemplate }"
  >
  </cc-datagrid>

  <div class="mt-2">
    <strong
      >Total Amount to Pay: AED
      {{ info.totalAmountToPay | number : "1.2-2" }}</strong
    >
  </div>
</div>

<!-- Payment Form -->
<div class="row mt-4" [formGroup]="invoiceForm">
  <div class="col-12">
    <cc-file-uploader
      label="Attach Invoice"
      formControlName="annexureFile"
      tag="annexure_file"
      [required]="true"
      [dropzoneConfig]="config"
    ></cc-file-uploader>
  </div>

  <div class="col-12 mt-3">
    <div class="row align-items-center">
      <div class="col-4">
        <label class="control-label">Does the invoice contain VAT?</label>
      </div>
      <div class="col-8">
        <cc-radio-group formControlName="taxable" [disabled]="true">
          <cc-radio-button [value]="true">Yes</cc-radio-button>
          <cc-radio-button class="ml-4" [value]="false">No</cc-radio-button>
        </cc-radio-group>
      </div>
    </div>
  </div>
</div>

<!-- Action Buttons -->
<hr />
<div class="row justify-content-center mt-4 mb-4">
  <button
    cc-raised-button
    (click)="paySelectedItems()"
    color="primary"
    class="mr-3"
    style="min-width: 120px"
  >
    Pay
  </button>
  <button cc-raised-button (click)="goToReturnPage()" style="min-width: 120px">
    Do Nothing
  </button>
</div>

<!-- Templates for Pending Matched Amount Column -->
<ng-template #pendingAmountTemplate let-row let-index="index">
  <ng-container
    *ngIf="editingRowPending === index; else displayPendingAmount"
    class="d-flex align-items-center justify-content-center"
  >
    <cc-amount-input symbol="AED" [(ngModel)]="row.amount"></cc-amount-input>
  </ng-container>
  <ng-template #displayPendingAmount>
    <span>{{ row.amount }}</span>
  </ng-template>
</ng-template>

<!-- Templates for Pending Matched Actions Column -->
<ng-template #pendingActionsTemplate let-row let-index="index">
  <ng-container *ngIf="editingRowPending === index; else pendingDisplayActions">
    <button
      cc-raised-button
      (click)="savePendingAmount(index)"
      class="mr-1"
      color="primary"
    >
      Save
    </button>
    <button cc-raised-button (click)="cancelPendingEdit(index)">Cancel</button>
  </ng-container>
  <ng-template #pendingDisplayActions>
    <button
      cc-raised-button
      (click)="editPendingAmount(index)"
      class="mr-1"
      color="accent"
    >
      Edit
    </button>
    <button cc-raised-button (click)="deletePendingItem(index)" color="warn">
      Delete
    </button>
  </ng-template>
</ng-template>

<!-- Templates for Mismatched Amount Column -->
<ng-template #mismatchedAmountTemplate let-row let-index="index">
  <ng-container
    *ngIf="editingRowMismatched === index; else displayMismatchedAmount"
    class="d-flex align-items-center justify-content-center"
  >
    <cc-amount-input symbol="AED" [(ngModel)]="row.amount"></cc-amount-input>
  </ng-container>
  <ng-template #displayMismatchedAmount>
    <span>{{ row.amount }}</span>
  </ng-template>
</ng-template>

<!-- Templates for Mismatched Actions Column -->
<ng-template #mismatchedActionsTemplate let-row let-index="index">
  <ng-container
    *ngIf="editingRowMismatched === index; else mismatchedDisplayActions"
  >
    <button
      cc-raised-button
      (click)="saveMismatchedAmount(index)"
      class="mr-1"
      color="primary"
    >
      Save
    </button>
    <button cc-raised-button (click)="cancelMismatchedEdit(index)">
      Cancel
    </button>
  </ng-container>
  <ng-template #mismatchedDisplayActions>
    <ng-container *ngIf="row.reason === 'Not Existing in ERP'; else editAction">
      <button
        cc-raised-button
        (click)="deleteMismatchedItem(index)"
        color="warn"
      >
        Delete
      </button>
    </ng-container>
    <ng-template #editAction>
      <button
        cc-raised-button
        (click)="editMismatchedAmount(index)"
        color="accent"
      >
        Edit
      </button>
    </ng-template>
  </ng-template>
</ng-template>

<!-- Template for Matched Checkbox Column -->
<ng-template #checkboxTemplate let-row let-index="index">
  <cc-checkbox
    [(ngModel)]="selectedItems[row.id]"
    (ngModelChange)="onMatchedItemSelect()"
  >
  </cc-checkbox>
</ng-template>
