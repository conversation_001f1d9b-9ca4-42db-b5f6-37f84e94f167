import { Component, OnInit, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FormBuilder, FormGroup } from '@angular/forms';
import { interval, Subscription, timer } from 'rxjs';
import { switchMap, takeWhile } from 'rxjs/operators';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { PageEvent } from '@angular/material/paginator';
import { PayInvoicesService } from '../../services/pay-invoices.service';
import { CCDialog } from '@maids/cc-lib/dialog';
import { CCFileUploaderConfig } from '@maids/cc-lib/file-uploader';

interface InvoiceStatementInfo {
  balance: number;
  annexureFile?: any;
  supplierName: string;
  totalAmountToPay: number;
  taxable?: boolean;
}

interface TransactionItem {
  id: number;
  visitDate: string;
  expenseName: string;
  relatedTo: string;
  maidPassportNo: string;
  maidType: string;
  amount: number;
  reason?: string;
  expenseRequestTodoId?: number;
  isEditing?: boolean;
  oldAmount?: number;
}

@Component({
  selector: 'app-pay-statement',
  templateUrl: './pay-statement.component.html',
  styleUrls: ['./pay-statement.component.scss'],
})
export class PayStatementComponent implements OnInit, OnDestroy {
  formGroup: FormGroup;
  invoiceForm: FormGroup;
  config: CCFileUploaderConfig = {
    maxFilesize: 25,
  };
  statementId: string = '';
  info: InvoiceStatementInfo = {
    balance: 0,
    supplierName: '',
    totalAmountToPay: 0,
  };

  // Data for tables
  pendingMatchedData: TransactionItem[] = [];
  matchedData: TransactionItem[] = [];
  mismatchedData: TransactionItem[] = [];

  // Pagination info
  pendingMatchedPagination: any = {};
  matchedPagination: any = {};
  mismatchedPagination: any = {};

  // Current page numbers
  pendingMatchedPageNo = 0;
  matchedPageNo = 0;
  mismatchedPageNo = 0;

  // Selection for matched table
  selectAllMatched = false;
  selectedItems: { [key: number]: boolean } = {};
  isAllSelected = false;

  // Editing states
  editingRowPending: number | null = null;
  editingRowMismatched: number | null = null;

  // Auto-refresh
  private autoRefreshSubscription?: Subscription;
  private autoRefreshActive = false;

  // Grid configurations
  pendingMatchedCols: CCGridColumn[] = [
    { field: 'visitDate', header: 'Date', type: 'date' },
    { field: 'expenseName', header: 'Expense' },
    { field: 'relatedTo', header: 'Related To' },
    { field: 'maidPassportNo', header: 'Maid Passport Number' },
    { field: 'maidType', header: "Maid's Type" },
    { field: 'amount', header: 'Amount' },
    { field: 'actions', header: 'Actions' },
  ];

  matchedCols: CCGridColumn[] = [
    { field: 'checkbox', header: 'Select' },
    { field: 'visitDate', header: 'Date', type: 'date' },
    { field: 'expenseName', header: 'Expense' },
    { field: 'relatedTo', header: 'Related To' },
    { field: 'maidPassportNo', header: 'Maid Passport Number' },
    { field: 'maidType', header: "Maid's Type" },
    { field: 'amount', header: 'Amount' },
  ];

  mismatchedCols: CCGridColumn[] = [
    { field: 'visitDate', header: 'Date', type: 'date' },
    { field: 'expenseName', header: 'Expense' },
    { field: 'relatedTo', header: 'Related To' },
    { field: 'maidPassportNo', header: 'Maid Passport Number' },
    { field: 'maidType', header: "Maid's Type" },
    { field: 'amount', header: 'Amount' },
    { field: 'reason', header: 'Reason' },
    { field: 'actions', header: 'Actions' },
  ];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private formBuilder: FormBuilder,
    private payInvoicesService: PayInvoicesService,
    private notification: CCNotificationService,
    private mediaService: MediaService,
    private ccDialog: CCDialog
  ) {
    this.formGroup = this.formBuilder.group({
      beneficiary: [{ value: '', disabled: true }],
      balance: [{ value: '', disabled: true }],
      maidName: [''],
      passportNumber: [''],
      fromDate: [''],
      toDate: [''],
    });

    this.invoiceForm = this.formBuilder.group({
      annexureFile: [''],
      taxable: [false],
    });
  }

  ngOnInit(): void {
    this.statementId = this.route.snapshot.params['statementId'];
    if (this.statementId) {
      this.getCalculations();
      this.getTables();
    }
  }

  ngOnDestroy(): void {
    this.stopAutoRefresh();
  }

  getCalculations(showLoadingIcon = true): void {
    this.payInvoicesService
      .getInvoiceStatementCalculations(this.statementId)
      .subscribe({
        next: (response: InvoiceStatementInfo) => {
          this.info = response;
          // Update form with response data
          this.formGroup.patchValue({
            beneficiary: response.supplierName,
            balance: `AED ${response.balance}`,
          });
          if (response.annexureFile) {
            this.invoiceForm.patchValue({
              annexureFile: [response.annexureFile],
            });
          }
          if (response.taxable !== undefined) {
            this.invoiceForm.patchValue({
              taxable: response.taxable,
            });
          }
        },
      });
  }

  getTables(showLoadingIcon = true): void {
    this.searchInvoiceStatement('PENDING_MATCHED', 0, showLoadingIcon);
    this.searchInvoiceStatement('Matched', 0, showLoadingIcon);
    this.searchInvoiceStatement('MISMATCHED', 0, showLoadingIcon);
  }

  searchInvoiceStatement(
    type: string,
    pageNo: number,
    showLoadingIcon = true
  ): void {
    const pageSize = 20; // Following your pattern from other components
    const requestBody = {
      maidName: this.formGroup.get('maidName')?.value || '',
      passportNumber: this.formGroup.get('passportNumber')?.value || '',
      fromDate: this.formGroup.get('fromDate')?.value
        ? this.formGroup.get('fromDate')?.value + ' 00:00:00'
        : '',
      toDate: this.formGroup.get('toDate')?.value
        ? this.formGroup.get('toDate')?.value + ' 00:00:00'
        : '',
      type: type,
    };

    this.payInvoicesService
      .searchInvoiceStatement(
        this.statementId,
        { page: pageNo, size: pageSize },
        requestBody
      )
      .subscribe({
        next: (response: any) => {
          if (type === 'PENDING_MATCHED') {
            this.pendingMatchedData = response.content;
            this.pendingMatchedPagination = response;
          } else if (type === 'Matched') {
            this.matchedData = response.content;
            this.matchedPagination = response;
            // Initialize selection
            this.selectedItems = {};
            response.content.forEach((item: TransactionItem) => {
              this.selectedItems[item.id] = false;
            });
          } else if (type === 'MISMATCHED') {
            this.mismatchedData = response.content;
            this.mismatchedPagination = response;
          }
        },
      });
  }

  // Pagination handlers
  getPendingMatchedNextPage(page: PageEvent): void {
    this.pendingMatchedPageNo = page.pageIndex;
    this.searchInvoiceStatement('PENDING_MATCHED', page.pageIndex);
  }

  getMatchedNextPage(page: PageEvent): void {
    this.matchedPageNo = page.pageIndex;
    this.searchInvoiceStatement('Matched', page.pageIndex);
  }

  getMismatchedNextPage(page: PageEvent): void {
    this.mismatchedPageNo = page.pageIndex;
    this.searchInvoiceStatement('MISMATCHED', page.pageIndex);
  }

  // Auto-refresh functionality
  startAutoRefresh(): void {
    this.stopAutoRefresh();
    this.refreshData();

    this.autoRefreshActive = true;
    this.autoRefreshSubscription = timer(120000, 120000) // 2 minutes
      .pipe(takeWhile(() => this.autoRefreshActive))
      .subscribe(() => {
        this.refreshData();
      });
  }

  stopAutoRefresh(): void {
    this.autoRefreshActive = false;
    if (this.autoRefreshSubscription) {
      this.autoRefreshSubscription.unsubscribe();
    }
  }

  refreshData(): void {
    this.getCalculations(false);
    this.getTables(false);
  }

  // Edit amount functions for Pending Matched
  editPendingAmount(index: number): void {
    this.editingRowPending = index;
    const item = this.pendingMatchedData[index];
    if (item) {
      item.oldAmount = item.amount;
      item.isEditing = true;
    }
  }

  savePendingAmount(index: number): void {
    const item = this.pendingMatchedData[index];
    if (item && item.expenseRequestTodoId) {
      this.payInvoicesService
        .updatePendingMatchedTransactionAmount(
          item.expenseRequestTodoId,
          item.amount
        )
        .subscribe({
          next: () => {
            delete item.oldAmount;
            item.isEditing = false;
            this.editingRowPending = null;
            this.searchInvoiceStatement(
              'PENDING_MATCHED',
              this.pendingMatchedPageNo
            );
          },
        });
    }
  }

  cancelPendingEdit(index: number): void {
    const item = this.pendingMatchedData[index];
    if (item) {
      if (item.oldAmount !== undefined) {
        item.amount = item.oldAmount;
        delete item.oldAmount;
      }
      item.isEditing = false;
      this.editingRowPending = null;
    }
  }

  deletePendingItem(index: number): void {
    const item = this.pendingMatchedData[index];
    if (item && item.expenseRequestTodoId) {
      this.ccDialog.confirm(
        'Confirm Deletion',
        'Are you sure you want to delete this record? This action cannot be undone.',
        () => {
          this.payInvoicesService
            .updateExpenseRequestTodo({
              id: item.expenseRequestTodoId,
              status: 'CANCELED',
            })
            .subscribe({
              next: () => {
                this.searchInvoiceStatement(
                  'PENDING_MATCHED',
                  this.pendingMatchedPageNo
                );
              },
            });
        }
      );
    }
  }

  // Edit amount functions for Mismatched
  editMismatchedAmount(index: number): void {
    this.editingRowMismatched = index;
    const item = this.mismatchedData[index];
    if (item) {
      item.oldAmount = item.amount;
      item.isEditing = true;
    }
  }

  saveMismatchedAmount(index: number): void {
    const item = this.mismatchedData[index];
    if (item && item.expenseRequestTodoId) {
      this.payInvoicesService
        .updateTransactionAmount(item.expenseRequestTodoId, item.amount)
        .subscribe({
          next: () => {
            delete item.oldAmount;
            item.isEditing = false;
            this.editingRowMismatched = null;
            this.searchInvoiceStatement('MISMATCHED', this.mismatchedPageNo);
            this.startAutoRefresh();
          },
        });
    }
  }

  cancelMismatchedEdit(index: number): void {
    const item = this.mismatchedData[index];
    if (item) {
      if (item.oldAmount !== undefined) {
        item.amount = item.oldAmount;
        delete item.oldAmount;
      }
      item.isEditing = false;
      this.editingRowMismatched = null;
    }
  }

  deleteMismatchedItem(index: number): void {
    const item = this.mismatchedData[index];
    if (item) {
      this.ccDialog.confirm(
        'Confirm Deletion',
        'Are you sure you want to delete this record? This action cannot be undone.',
        () => {
          this.payInvoicesService.deleteTransaction(item.id).subscribe({
            next: () => {
              this.searchInvoiceStatement('MISMATCHED', this.mismatchedPageNo);
            },
          });
        }
      );
    }
  }

  // Selection functions for matched table
  onMatchedItemSelect(): void {
    this.isAllSelected = false;
    this.checkIfAllSelected();
  }

  checkIfAllSelected(): void {
    const selectedCount = Object.values(this.selectedItems).filter(
      Boolean
    ).length;
    this.selectAllMatched =
      selectedCount > 0 && selectedCount === this.matchedData.length;
  }

  selectAllMatchedItems(): void {
    this.isAllSelected = this.selectAllMatched;
    this.matchedData.forEach((item) => {
      this.selectedItems[item.id] = this.selectAllMatched;
    });
  }

  // Export and payment functions
  exportToCSV(): void {
    const requestData = {
      maidName: this.formGroup.get('maidName')?.value || '',
      passportNumber: this.formGroup.get('passportNumber')?.value || '',
      fromDate: this.formGroup.get('fromDate')?.value || '',
      toDate: this.formGroup.get('toDate')?.value || '',
    };
    this.mediaService.downloadFile(
      `accounting/invoiceStatement/exportToCSV/${this.statementId}`,
      '',
      {
        method: 'POST',
        body: requestData,
      }
    );
  }

  paySelectedItems(): void {
    const annexureFile = this.invoiceForm.get('annexureFile')?.value;
    if (!annexureFile || !annexureFile.id) {
      this.notification.notifyError(
        'Please attach an invoice file before proceeding with payment.'
      );
      return;
    }

    let requestData: any;
    let apiCall: any;

    if (this.isAllSelected) {
      requestData = {
        maidName: this.formGroup.get('maidName')?.value || '',
        passportNumber: this.formGroup.get('passportNumber')?.value || '',
        fromDate: this.formGroup.get('fromDate')?.value
          ? this.formGroup.get('fromDate')?.value + ' 00:00:00'
          : '',
        toDate: this.formGroup.get('toDate')?.value
          ? this.formGroup.get('toDate')?.value + ' 00:00:00'
          : '',
        attachments: [{ id: annexureFile.id }],
      };
      apiCall = this.payInvoicesService.payAllTransactions(
        this.statementId,
        requestData
      );
    } else {
      const selectedItems = this.matchedData.filter(
        (item) => this.selectedItems[item.id]
      );

      if (selectedItems.length === 0) {
        this.notification.notifyError(
          'Please select at least one transaction to pay.'
        );
        return;
      }

      requestData = {
        transactionItems: selectedItems.map((item) => ({ id: item.id })),
        attachments: [{ id: annexureFile.id }],
      };
      apiCall = this.payInvoicesService.payTransactions(requestData);
    }

    apiCall.subscribe({
      next: () => {
        this.notification.notifySuccess('Payment processed successfully!');
        this.startAutoRefresh();
      },
    });
  }

  rematch(): void {
    this.payInvoicesService
      .rematchInvoiceStatement(this.statementId)
      .subscribe({
        next: () => {
          this.notification.notifySuccess('Rematch successfully!');
          this.getTables();
        },
      });
  }

  goToReturnPage(): void {
    this.router.navigate(['/accounting/v2/pay-invoices']);
  }
}
