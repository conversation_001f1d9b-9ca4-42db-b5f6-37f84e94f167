import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PayInvoicesListComponent } from './components/pay-invoices-list/pay-invoices-list.component';
import { PayFormComponent } from './components/pay-form/pay-form.component';
import { PayStatementComponent } from './components/pay-statement/pay-statement.component';

const routes: Routes = [
  { path: '', component: PayInvoicesListComponent, data: { label: '' } },
  {
    path: 'pay-form/:beneficiaryId/:expenseId',
    component: PayFormComponent,
    data: { label: 'Pay Invoice', pageCode: 'accounting_PayInvoicesPayForm' },
  },
  // {
  //   path: 'pay-statement/:statementId',
  //   component: PayStatementComponent,
  //   data: { label: 'Pay Statement' },
  // },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class PayInvoicesRoutingModule {}
