import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { PayInvoicesRoutingModule } from './pay-invoices-routing.module';
import { CCDatagridModule } from '@maids/cc-lib/datagrid';
import { CCButtonModule } from '@maids/cc-lib/button';
import { PayInvoicesListComponent } from './components/pay-invoices-list/pay-invoices-list.component';
import { PayFormComponent } from './components/pay-form/pay-form.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CCInputModule } from '@maids/cc-lib/input';
import { CCDatepickerModule } from '@maids/cc-lib/date';
import { CCCheckboxModule } from '@maids/cc-lib/checkbox';
import { CCIconModule } from '@maids/cc-lib/icon';
import { CCFileUploaderModule } from '@maids/cc-lib/file-uploader';
import { CCRadioButtonModule } from '@maids/cc-lib/radio-button';
import { CCValidateByModule } from '@maids/cc-lib/validation';
import { PayStatementComponent } from './components/pay-statement/pay-statement.component';
import { CCAmountInputModule } from '@maids/cc-lib/masked-input';

@NgModule({
  declarations: [PayInvoicesListComponent, PayFormComponent, PayStatementComponent],
  imports: [
    CommonModule,
    PayInvoicesRoutingModule,
    CCDatagridModule,
    CCButtonModule,
    CCDatepickerModule,
    ReactiveFormsModule,
    CCInputModule,
    CCCheckboxModule,
    FormsModule,
    CCIconModule,
    CCFileUploaderModule.forChild({}),
    CCRadioButtonModule,
    CCValidateByModule,
    CCAmountInputModule,
  ],
})
export class PayInvoicesModule {}
