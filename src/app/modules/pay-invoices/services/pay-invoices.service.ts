import { HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { CCBackendEndpoint } from '@maids/cc-erp-services';
import { Observable } from 'rxjs';
import { API } from 'src/environments/api';

@Injectable({
  providedIn: 'root',
})
export class PayInvoicesService {
  constructor(
    private _http: HttpClient,
    @Inject(CCBackendEndpoint) private _api: CCBackendEndpoint
  ) {}
  getPayInvoicesList(params: any): Observable<any> {
    return this._http.get(`${this._api}/${API.getPayInvoicesList}`, {params});
  }
  getPayInvoice(
    beneficiaryId: number,
    expenseId: number,
    params: any,
    payload: any
  ): Observable<any> {
    return this._http.post(
      `${this._api}/${API.getPayInvoice}/${beneficiaryId}/${expenseId}`,
      payload,
      {params}
    );
  }
  payInvoice(payload: any): Observable<any> {
    return this._http.post(`${this._api}/${API.payInvoice}`, payload);
  }

  // Pay Statement methods
  getInvoiceStatementCalculations(statementId: string): Observable<any> {
    return this._http.get(`${this._api}/${API.invoiceStatementCalculations}/${statementId}`);
  }

  searchInvoiceStatement(statementId: string, params: any, payload: any): Observable<any> {
    return this._http.post(`${this._api}/${API.invoiceStatementSearch}/${statementId}`, payload, { params });
  }

  exportInvoiceStatementToCSV(statementId: string, payload: any): Observable<any> {
    return this._http.post(`${this._api}/${API.invoiceStatementExportToCSV}/${statementId}`, payload, { responseType: 'blob' });
  }

  payAllTransactions(statementId: string, payload: any): Observable<any> {
    return this._http.post(`${this._api}/${API.invoiceStatementPayAllTransactions}/${statementId}`, payload);
  }

  payTransactions(payload: any): Observable<any> {
    return this._http.post(`${this._api}/${API.invoiceStatementPayTransactions}`, payload);
  }

  rematchInvoiceStatement(statementId: string): Observable<any> {
    return this._http.post(`${this._api}/${API.invoiceStatementRematch}/${statementId}`, {});
  }

  updatePendingMatchedTransactionAmount(expenseRequestTodoId: number, amount: number): Observable<any> {
    return this._http.post(`${this._api}/${API.invoiceStatementUpdatePendingMatchedAmount}`, null, {
      params: { expenseRequestTodoId: expenseRequestTodoId.toString(), amount: amount.toString() }
    });
  }

  updateTransactionAmount(expenseRequestTodoId: number, amount: number): Observable<any> {
    return this._http.post(`${this._api}/${API.invoiceStatementUpdateTransactionAmount}`, null, {
      params: { expenseRequestTodoId: expenseRequestTodoId.toString(), amount: amount.toString() }
    });
  }

  deleteTransaction(transactionId: number): Observable<any> {
    return this._http.post(`${this._api}/${API.invoiceStatementDeleteTransaction}/${transactionId}`, {});
  }

  updateExpenseRequestTodo(payload: any): Observable<any> {
    return this._http.post(`${this._api}/${API.expenseRequestTodoUpdate}`, payload);
  }
}
