import { Component, OnInit } from '@angular/core';
import { PaymentReportService } from '../../services/payment-report.service';
import { FormBuilder, Validators } from '@angular/forms';
import { PaginationRequest } from '@maids/cc-lib/common';
import { Observable } from 'rxjs';
import {
  CCGridColumn,
  CCGridColumnSelectionItem,
} from '@maids/cc-lib/datagrid';
import { PageEvent } from '@angular/material/paginator';
import { Sort } from '@angular/material/sort';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
@Component({
  selector: 'app-payment-report-list',
  templateUrl: './payment-report-list.component.html',
  styleUrls: ['./payment-report-list.component.scss'],
})
export class PaymentReportListComponent implements OnInit {
  records: any;
  paymentSum: any;
  shownColumns: any[] = [];
  form = this.fb.group({
    paymentNameOperation: [''],
    paymentName: [''],
    clientNameOperation: [''],
    clientName: [''],
    contractNameOperation: [''],
    contractName: [''],
    contractType: [''],
    searchDDAppOperation: [''],
    DDApplication: [''],
    bankNameOperation: [''],
    bankName: [''],
    chequeNameOperation: [''],
    chequeName: [''],
    chequeNumberOperation: [''],
    chequeNumber: [''],
    initialOperation: [''],
    initial: [''],
    amountOperation: [''],
    amount: [''],
    notesOperation: [''],
    notes: [''],
    typeOfPaymentOperation: [''],
    typeOfPayment: [''],
    methodOfPaymentOperation: [''],
    methodOfPayment: [''],
    paymentDateOperation: [''],
    paymentDate: [''],
    paymentDateSecond: [''],
    createDateOperation: [''],
    createDate: [''],
    createDateSecond: [''],
    vatAmountOperation: [''],
    vatAmount: [''],
    status: [''],
    replacedFilter: [''],
    chequeWithTheBank: [''],
    vatPaidByClient: [''],
    isProRatedFilter: [''],
    contractStartDateOperation: [''],
    contractStartDate: [''],
    contractStartDateSecond: [''],
    changedToPDPDateOperation: [''],
    changedToPDPDate: [''],
    contractStatus: [''],
    changedToPDPDateSecond: [''],
    changedToReceivedDateOperation: [''],
    changedToReceivedDate: [''],
    nationality: [''],
    changedToReceivedDateSecond: [''],
  });
  searchTextOptions: any[] = [
    { id: '=', text: 'Equals' },
    { id: '!=', text: 'Not Equals' },
    { id: 'like', text: 'Contains' },
    { id: 'not like', text: 'Not Contains' },
    { id: 'starts with', text: 'Starts With' },
    { id: 'ends with', text: 'Ends With' },
  ];
  ddApplicationOperationOptions: any[] = [{ id: '=', text: 'Equal' }];
  searchDateOptions: any[] = [
    { id: '=', text: 'Equals' },
    { id: '<', text: 'Before' },
    { id: '>', text: 'After' },
    { id: 'between', text: 'Between' },
    { id: 'last_month', text: 'Last Month' },
    { id: 'this_month', text: 'This Month' },
  ];
  searchNumericOptions: any[] = [
    { id: '=', text: 'Equals' },
    { id: '>=', text: 'More or Equals' },
    { id: '<=', text: 'Less Or Equals' },
    { id: '!=', text: 'Not Equals' },
  ];
  initialOperationOptions: any[] = [
    { id: '=', text: 'Equals' },
    { id: '<>', text: 'Not Equals' },
    { id: 'IS NULL', text: 'Empty' },
    { id: 'IS NOT NULL', text: 'Not Empty' },
  ];
  searchListOptions: any[] = [
    { id: '=', text: 'Equals' },
    { id: '<>', text: 'Not Equals' },
  ];
  searchAmountOptions: any[] = [
    { id: '=', text: 'Equals' },
    { id: '!=', text: 'Not Equals' },
    { id: '>', text: 'Greater Than' },
    { id: '<', text: 'Less Than' },
  ];
  notesOptions: any[] = [
    { id: '=', text: 'Equals' },
    { id: '!=', text: 'Not Equals' },
    { id: 'like', text: 'Contains' },
    { id: 'not like', text: 'Not Contains' },
    { id: 'starts with', text: 'Starts With' },
    { id: 'ends with', text: 'Ends With' },
  ];
  replacedOptions: any[] = [
    { id: 'true', text: 'True' },
    { id: 'false', text: 'False' },
  ];
  isProRatedOptions: any[] = [
    { id: 'true', text: 'True' },
    { id: 'false', text: 'False' },
  ];
  chequeWithTheBankOptions: any[] = [
    { id: 'true', text: 'True' },
    { id: 'false', text: 'False' },
  ];
  booleanOptions: any[] = [
    { id: 'true', text: 'Yes' },
    { id: 'false', text: 'No' },
  ];
  initialOptions: any[] = [
    { id: 'true', text: 'True' },
    { id: 'false', text: 'False' },
  ];
  contractStatusOptions: any[] = [
    { id: 'ACTIVE', text: 'Active' },
    { id: 'CANCELLED', text: 'Cancelled' },
    { id: 'PLANNED_RENEWAL', text: 'Planned Renewal' },
    { id: 'POSTPONED', text: 'Postponed' },
    { id: 'EXPIRED', text: 'Expired' },
  ];
  statusOptions: any[] = [
    { id: 'PDC', text: 'PDP' },
    { id: 'PRE_PDP', text: 'PRE PDP' },
    { id: 'RECEIVED', text: 'Received' },
    { id: 'BOUNCED', text: 'Bounced' },
    { id: 'TEARED_UP', text: 'Teared up' },
    { id: 'RETURNED_TO_CLIENT', text: 'Returned to family' },
    { id: 'DELETED', text: 'Deleted' },
    { id: 'FROZEN', text: 'Frozen' },
    { id: 'REQUESTED', text: 'Requested' },
    { id: 'DEPOSIT', text: 'Deposit' },
    { id: 'UNCOLLECTED', text: 'Uncollected' },
    {
      id: 'CANCELLED_WAITING_CLIENT_PICKUP',
      text: 'Cancelled waiting family pickup',
    },
  ];
  contractTypeOptions: any[] = [];
  typeOfPaymentOptions: any[] = [];
  methodOfPaymentOptions: any[] = [];
  readonly nationalityOptions = (
    pageReq: PaginationRequest
  ): Observable<any> => {
    return this.paymentReportService.PRnationalityOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  constructor(
    private paymentReportService: PaymentReportService,
    private fb: FormBuilder,
    private mediaService: MediaService,
    public readonly notifications: CCNotificationService
  ) {}
  ngOnInit(): void {
    this.getcontractTypeOptions();
    this.gettypeOfPaymentOptions();
    this.getmethodOfPaymentOptions();
    this.setValueDates();
    this.form.controls['initialOperation'].valueChanges.subscribe((val) => {
      this.form.controls['initial'].setValue('');
    });
    this.shownColumns = this.gridCols
      .filter((col) => {
        return !col.hide;
      })
      .map((col) => {
        return { label: col.header, field: col.field };
      });
  }

  getTableData() {
    let filters = this.prepareFilters();
    this.paymentReportService
      .getPaymentReport(filters)
      .subscribe((res: any) => {
        this.records = res;
      });
  }
  getNextPage(event: PageEvent) {
    this.paymentReportService.searchSubject.next({
      ...this.paymentReportService.searchSubject.getValue(),
      params: {
        ...this.paymentReportService.searchSubject.getValue().params,
        page: event.pageIndex,
        size: event.pageSize,
      },
    });
    this.getTableData();
  }
  onSortChange(event: Sort) {
    this.paymentReportService.searchSubject.next({
      ...this.paymentReportService.searchSubject.getValue(),
      params: {
        ...this.paymentReportService.searchSubject.getValue().params,
        sort: event.active + ',' + event.direction,
      },
    });
    this.getTableData();
  }
  gridCols: CCGridColumn[] = [
    {
      field: 'contractName',
      header: 'Contract Name',
      formatter(rowData, colDef) {
        return 'Contr-' + rowData.contract.id;
      },
      hide: true,
    },
    {
      field: 'paymentName',
      header: 'Payment Name',
      formatter(rowData, colDef) {
        return `<a class="cc-secondary" href="#!/client/payments/edit/${rowData.contract.client.id}/${rowData.contract.id}/${rowData.id}" >Payment- ${rowData.id}</a>`;
      },
      sortable: true,
      sortProp: {
        arrowPosition: 'before',
        id: 'id',
        start: 'asc',
      },
    },
    {
      field: 'clientName',
      header: 'Full Time Family',
      formatter: (rowData: any) => {
        if (rowData.contract) {
          return `<a class="cc-secondary" href="#!/client/details/${rowData.contract.client.id}">${rowData.contract.client.name}</a>`;
        }
        return '';
      },
      sortable: true,
      width: '180px',
      sortProp: {
        arrowPosition: 'before',
        id: 'contract.client.name',
        start: 'asc',
      },
    },
    {
      field: 'nationality',
      header: 'Maid Nationality',
      formatter(rowData, colDef) {
        return rowData.contract.housemaid.nationality;
      },
      sortable: true,
      sortProp: {
        arrowPosition: 'before',
        id: 'contract.housemaid.nationality',
        start: 'asc',
      },
    },
    {
      field: 'contractStatus',
      header: 'Contract Status',
      formatter(rowData, colDef) {
        return rowData.contract.status;
      },
      width: '150px',
    },
    {
      field: 'contractStartDate',
      header: 'Contract Start Date',
      formatter(rowData, colDef) {
        return rowData.contract.startOfContract;
      },
      width: '150px',
      sortable: true,
      sortProp: {
        arrowPosition: 'before',
        id: 'contract.startOfContract',
        start: 'asc',
      },
    },
    {
      field: 'contractProspectType',
      header: 'Contract Type',
      formatter(rowData, colDef) {
        return rowData.contract.contractProspectType.name;
      },
    },
    {
      field: 'applicationId',
      header: 'DD Application',
      formatter(rowData, colDef) {
        return rowData.directDebitFile.applicationId;
      },
    },
    {
      field: 'typeOfPayment',
      header: 'Type of Payment',
      formatter(rowData, colDef) {
        return rowData.typeOfPayment.name;
      },
      sortable: true,
      sortProp: {
        arrowPosition: 'before',
        id: 'typeOfPayment',
        start: 'asc',
      },
    },
    {
      field: 'methodOfPayment',
      header: 'Method Of Payment',
      formatter(rowData, colDef) {
        return rowData.methodOfPayment.label;
      },
      width: '150px',
      sortable: true,
      sortProp: {
        arrowPosition: 'before',
        id: 'methodOfPayment',
        start: 'asc',
      },
    },
    { field: 'amountOfPayment', header: 'Amount Of Payment', width: '150px' },
    {
      field: 'dateOfPayment',
      header: 'Date Of Payment',
      formatter(rowData, colDef) {
        return rowData.dateOfPayment;
      },
      width: '150px',
      sortable: true,
      sortProp: {
        arrowPosition: 'before',
        id: 'dateOfPayment',
        start: 'asc',
      },
    },
    {
      field: 'dateChangedToPDP',
      header: 'Date Changed to PDP',
      formatter(rowData, colDef) {
        return rowData.dateChangedToPDP;
      },
      width: '200px',
      sortable: true,
      sortProp: {
        arrowPosition: 'before',
        id: 'dateChangedToPDP',
        start: 'asc',
      },
    },
    {
      field: 'dateChangedToReceived',
      header: 'Date Changed to Received',
      formatter(rowData, colDef) {
        return rowData.dateChangedToReceived;
      },
      width: '200px',
      sortable: true,
      sortProp: {
        arrowPosition: 'before',
        id: 'dateChangedToReceived',
        start: 'asc',
      },
    },
    {
      field: 'vatPaidByClient',
      header: 'VAT paid by family',
      formatter(rowData, colDef) {
        return rowData.vatPaidByClient;
      },
      width: '180px',
    },
    {
      field: 'bankName',
      header: 'Bank Name',
      formatter(rowData, colDef) {
        return rowData.bankName?.name;
      },
    },
    {
      field: 'chequeName',
      header: 'Cheque Name',
      formatter(rowData, colDef) {
        return rowData.chequeName;
      },
      hide: true,
    },
    {
      field: 'chequeNumber',
      header: 'Cheque No',
      formatter(rowData, colDef) {
        return rowData.chequeNumber;
      },
      hide: true,
    },
    {
      field: 'status',
      header: 'Status',
      formatter(rowData, colDef) {
        return rowData.status?.label;
      },
      hide: true,
      sortable: true,
      sortProp: {
        arrowPosition: 'before',
        id: 'status',
        start: 'asc',
      },
    },
    {
      field: 'creationDate',
      header: 'Create Date',
      formatter(rowData, colDef) {
        return rowData.creationDate;
      },
      sortable: true,
      sortProp: {
        arrowPosition: 'before',
        id: 'creationDate',
        start: 'asc',
      },
    },
    {
      field: 'chequeWithTheBank',
      header: 'Cheque Is In The Office',
      formatter(rowData, colDef) {
        return rowData.chequeWithTheBank;
      },
      width: '200px',
      hide: true,
      sortable: true,
      sortProp: {
        arrowPosition: 'before',
        id: 'creationDate',
        start: 'asc',
      },
    },
    {
      field: 'replaced',
      header: 'Replaced',
      formatter(rowData, colDef) {
        return rowData.replaced;
      },
    },
    {
      field: 'note',
      header: 'Notes',
      formatter(rowData, colDef) {
        return rowData.note;
      },
    },
    {
      field: 'isInitial',
      header: 'Is Initial',
      formatter(rowData, colDef) {
        return rowData.isInitial;
      },
    },
    {
      field: 'vat',
      header: 'Vat Amount',
      formatter(rowData, colDef) {
        return rowData.vat;
      },
    },
  ];
  formatDateToYYYYMMDD(date: Date): string {
    if (!date) return '';
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  setValueDates() {
    this.form.controls['paymentDateOperation'].valueChanges.subscribe((val) => {
      if (val == 'this_month') {
        const today = new Date();
        const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
        const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        this.form.controls['paymentDate'].setValue(
          this.formatDateToYYYYMMDD(firstDay)
        );
        this.form.controls['paymentDateSecond'].setValue(
          this.formatDateToYYYYMMDD(lastDay)
        );
      } else if (val == 'last_month') {
        const today = new Date();
        const firstDay = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        const lastDay = new Date(today.getFullYear(), today.getMonth(), 0);
        this.form.controls['paymentDate'].setValue(
          this.formatDateToYYYYMMDD(firstDay)
        );
        this.form.controls['paymentDateSecond'].setValue(
          this.formatDateToYYYYMMDD(lastDay)
        );
      } else {
        this.form.controls['paymentDate'].setValue('');
        this.form.controls['paymentDateSecond'].setValue('');
      }
    });
    this.form.controls['createDateOperation'].valueChanges.subscribe((val) => {
      if (val == 'this_month') {
        const today = new Date();
        const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
        const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        this.form.controls['createDate'].setValue(
          this.formatDateToYYYYMMDD(firstDay)
        );
        this.form.controls['createDateSecond'].setValue(
          this.formatDateToYYYYMMDD(lastDay)
        );
      } else if (val == 'last_month') {
        const today = new Date();
        const firstDay = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        const lastDay = new Date(today.getFullYear(), today.getMonth(), 0);
        this.form.controls['createDate'].setValue(
          this.formatDateToYYYYMMDD(firstDay)
        );
        this.form.controls['createDateSecond'].setValue(
          this.formatDateToYYYYMMDD(lastDay)
        );
      } else {
        this.form.controls['createDate'].setValue('');
        this.form.controls['createDateSecond'].setValue('');
      }
    });
    this.form.controls['contractStartDateOperation'].valueChanges.subscribe(
      (val) => {
        if (val == 'this_month') {
          const today = new Date();
          const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
          const lastDay = new Date(
            today.getFullYear(),
            today.getMonth() + 1,
            0
          );
          this.form.controls['contractStartDate'].setValue(
            this.formatDateToYYYYMMDD(firstDay)
          );
          this.form.controls['contractStartDateSecond'].setValue(
            this.formatDateToYYYYMMDD(lastDay)
          );
        } else if (val == 'last_month') {
          const today = new Date();
          const firstDay = new Date(
            today.getFullYear(),
            today.getMonth() - 1,
            1
          );
          const lastDay = new Date(today.getFullYear(), today.getMonth(), 0);
          this.form.controls['contractStartDate'].setValue(
            this.formatDateToYYYYMMDD(firstDay)
          );
          this.form.controls['contractStartDateSecond'].setValue(
            this.formatDateToYYYYMMDD(lastDay)
          );
        } else {
          this.form.controls['contractStartDate'].setValue('');
          this.form.controls['contractStartDateSecond'].setValue('');
        }
      }
    );
    this.form.controls['changedToPDPDateOperation'].valueChanges.subscribe(
      (val) => {
        if (val == 'this_month') {
          const today = new Date();
          const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
          const lastDay = new Date(
            today.getFullYear(),
            today.getMonth() + 1,
            0
          );
          this.form.controls['changedToPDPDate'].setValue(
            this.formatDateToYYYYMMDD(firstDay)
          );
          this.form.controls['changedToPDPDateSecond'].setValue(
            this.formatDateToYYYYMMDD(lastDay)
          );
        } else if (val == 'last_month') {
          const today = new Date();
          const firstDay = new Date(
            today.getFullYear(),
            today.getMonth() - 1,
            1
          );
          const lastDay = new Date(today.getFullYear(), today.getMonth(), 0);
          this.form.controls['changedToPDPDate'].setValue(
            this.formatDateToYYYYMMDD(firstDay)
          );
          this.form.controls['changedToPDPDateSecond'].setValue(
            this.formatDateToYYYYMMDD(lastDay)
          );
        } else {
          this.form.controls['changedToPDPDate'].setValue('');
          this.form.controls['changedToPDPDateSecond'].setValue('');
        }
      }
    );
    this.form.controls['changedToReceivedDateOperation'].valueChanges.subscribe(
      (val) => {
        if (val == 'this_month') {
          const today = new Date();
          const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
          const lastDay = new Date(
            today.getFullYear(),
            today.getMonth() + 1,
            0
          );
          this.form.controls['changedToReceivedDate'].setValue(
            this.formatDateToYYYYMMDD(firstDay)
          );
          this.form.controls['changedToReceivedDateSecond'].setValue(
            this.formatDateToYYYYMMDD(lastDay)
          );
        } else if (val == 'last_month') {
          const today = new Date();
          const firstDay = new Date(
            today.getFullYear(),
            today.getMonth() - 1,
            1
          );
          const lastDay = new Date(today.getFullYear(), today.getMonth(), 0);
          this.form.controls['changedToReceivedDate'].setValue(
            this.formatDateToYYYYMMDD(firstDay)
          );
          this.form.controls['changedToReceivedDateSecond'].setValue(
            this.formatDateToYYYYMMDD(lastDay)
          );
        } else {
          this.form.controls['changedToReceivedDate'].setValue('');
          this.form.controls['changedToReceivedDateSecond'].setValue('');
        }
      }
    );
  }

  prepareFilters() {
    const filters = [];

    if (
      this.form.get('paymentName')?.value &&
      this.form.get('paymentNameOperation')?.value
    ) {
      filters.push({
        property: 'id',
        operation: this.form.get('paymentNameOperation')?.value,
        value: this.form.get('paymentName')?.value
          ? parseFloat(this.form.get('paymentName')?.value)
          : '',
      });
    }

    if (this.form.get('vatPaidByClient')?.value) {
      filters.push({
        property: 'vatPaidByClient',
        operation: '=',
        value: this.form.get('vatPaidByClient')?.value === 'true',
      });
    }

    if (
      this.form.get('clientName')?.value &&
      this.form.get('clientNameOperation')?.value
    ) {
      filters.push({
        property: 'contract.client.name',
        operation: this.form.get('clientNameOperation')?.value,
        value: this.form.get('clientName')?.value,
      });
    }

    if (
      this.form.get('contractName')?.value &&
      this.form.get('contractNameOperation')?.value
    ) {
      filters.push({
        property: 'contract.id',
        operation: this.form.get('contractNameOperation')?.value,
        value: this.form.get('contractName')?.value
          ? parseFloat(this.form.get('contractName')?.value)
          : '',
      });
    }

    if (
      this.form.get('DDApplication')?.value &&
      this.form.get('searchDDAppOperation')?.value
    ) {
      filters.push({
        property: 'directDebitFile.applicationId',
        operation: this.form.get('searchDDAppOperation')?.value,
        value: this.form.get('DDApplication')?.value,
      });
    }

    if (this.form.get('contractType')?.value) {
      filters.push({
        property: 'contract.contractProspectType.id',
        operation: '=',
        value: this.form.get('contractType')?.value,
      });
    }

    if (
      this.form.get('bankName')?.value &&
      this.form.get('bankNameOperation')?.value
    ) {
      filters.push({
        property: 'bankName.name',
        operation: this.form.get('bankNameOperation')?.value,
        value: this.form.get('bankName')?.value,
      });
    }

    if (
      this.form.get('amount')?.value &&
      this.form.get('amountOperation')?.value
    ) {
      filters.push({
        property: 'amountOfPayment',
        operation: this.form.get('amountOperation')?.value,
        value: parseInt(this.form.get('amount')?.value),
      });
    }

    if (
      this.form.get('changedToPDPDate')?.value &&
      this.form.get('changedToPDPDateOperation')?.value
    ) {
      let op = this.form.get('changedToPDPDateOperation')?.value;
      if (op === 'last_month' || op === 'this_month') {
        op = 'between';
      }
      const filterObj: any = {
        property: 'dateChangedToPDP',
        operation: op,
        value: this.form.get('changedToPDPDate')?.value,
      };

      if (op === 'between') {
        filterObj.secondValue = this.form.get('changedToPDPDateSecond')?.value;
      }
      filters.push(filterObj);
    }

    if (
      this.form.get('changedToReceivedDate')?.value &&
      this.form.get('changedToReceivedDateOperation')?.value
    ) {
      let op = this.form.get('changedToReceivedDateOperation')?.value;
      if (op === 'last_month' || op === 'this_month') {
        op = 'between';
      }
      const filterObj: any = {
        property: 'dateChangedToReceived',
        operation: op,
        value: this.form.get('changedToReceivedDate')?.value,
      };

      if (op === 'between') {
        filterObj.secondValue = this.form.get(
          'changedToReceivedDateSecond'
        )?.value;
      }
      filters.push(filterObj);
    }

    if (
      this.form.get('notes')?.value &&
      this.form.get('notesOperation')?.value
    ) {
      filters.push({
        property: 'note',
        operation: this.form.get('notesOperation')?.value,
        value: this.form.get('notes')?.value,
      });
    }

    if (
      this.form.get('chequeName')?.value &&
      this.form.get('chequeNameOperation')?.value
    ) {
      filters.push({
        property: 'chequeName',
        operation: this.form.get('chequeNameOperation')?.value,
        value: this.form.get('chequeName')?.value,
      });
    }

    if (
      this.form.get('chequeNumber')?.value &&
      this.form.get('chequeNumberOperation')?.value
    ) {
      filters.push({
        property: 'chequeNumber',
        operation: this.form.get('chequeNumberOperation')?.value,
        value: this.form.get('chequeNumber')?.value,
      });
    }

    if (
      this.form.get('initial')?.value ||
      this.form.get('initialOperation')?.value
    ) {
      filters.push({
        property: 'isInitial',
        operation: this.form.get('initialOperation')?.value,
        value:
          this.form.get('initial')?.value === ''
            ? null
            : this.form.get('initial')?.value === 'true',
      });
    }

    if (
      this.form.get('typeOfPayment')?.value &&
      this.form.get('typeOfPaymentOperation')?.value
    ) {
      filters.push({
        property: 'typeOfPayment.id',
        operation: this.form.get('typeOfPaymentOperation')?.value,
        value: this.form.get('typeOfPayment')?.value,
      });
    }

    if (
      this.form.get('methodOfPayment')?.value &&
      this.form.get('methodOfPaymentOperation')?.value
    ) {
      filters.push({
        property: 'methodOfPayment',
        operation: this.form.get('methodOfPaymentOperation')?.value,
        value: this.form.get('methodOfPayment')?.value,
      });
    }

    if (
      this.form.get('paymentDate')?.value &&
      this.form.get('paymentDateOperation')?.value
    ) {
      let op = this.form.get('paymentDateOperation')?.value;
      if (op === 'last_month' || op === 'this_month') {
        op = 'between';
      }
      const filterObj: any = {
        property: 'dateOfPayment',
        operation: op,
        value: this.form.get('paymentDate')?.value,
      };

      if (op === 'between') {
        filterObj.secondValue = this.form.get('paymentDateSecond')?.value;
      }
      filters.push(filterObj);
    }

    if (
      this.form.get('createDate')?.value &&
      this.form.get('createDateOperation')?.value
    ) {
      let op = this.form.get('createDateOperation')?.value;
      if (op === 'last_month' || op === 'this_month') {
        op = 'between';
      }
      const filterObj: any = {
        property: 'creationDate',
        operation: op,
        value: this.form.get('createDate')?.value,
      };
      if (op === 'between') {
        filterObj.secondValue = this.form.get('createDateSecond')?.value;
      }
      filters.push(filterObj);
    }

    if (
      this.form.get('contractStartDate')?.value &&
      this.form.get('contractStartDateOperation')?.value
    ) {
      let op = this.form.get('contractStartDateOperation')?.value;
      if (op === 'last_month' || op === 'this_month') {
        op = 'between';
      }
      const filterObj: any = {
        property: 'contract.startOfContract',
        operation: op,
        value: this.form.get('contractStartDate')?.value,
      };
      if (op === 'between') {
        filterObj.secondValue = this.form.get('contractStartDateSecond')?.value;
      }
      filters.push(filterObj);
    }

    if (this.form.get('status')?.value?.length > 0) {
      filters.push({
        property: 'status',
        operation: 'in',
        value: this.form.get('status')?.value,
      });
    }

    if (
      this.form.get('chequeWithTheBank')?.value &&
      this.form.get('chequeWithTheBank')?.value !== 'all'
    ) {
      filters.push({
        property: 'chequeWithTheBank',
        operation: '=',
        value: this.form.get('chequeWithTheBank')?.value == 'true',
      });
    }

    if (this.form.get('isProRatedFilter')?.value) {
      filters.push({
        property: 'contract.isProRated',
        operation: '=',
        value: this.form.get('isProRatedFilter')?.value === 'true',
      });
    }

    if (
      this.form.get('replacedFilter')?.value &&
      this.form.get('replacedFilter')?.value !== 'all'
    ) {
      filters.push({
        property: 'replaced',
        operation: '=',
        value: this.form.get('replacedFilter')?.value === 'true',
      });
    }

    if (
      this.form.get('vatAmount')?.value &&
      this.form.get('vatAmountOperation')?.value
    ) {
      filters.push({
        property: 'vat',
        operation: this.form.get('vatAmountOperation')?.value,
        value: parseFloat(this.form.get('vatAmount')?.value),
      });
    }

    if (this.form.get('nationality')?.value) {
      filters.push({
        property: 'contract.housemaid.nationality.name',
        operation: '=',
        value: this.form.get('nationality')?.value?.text,
      });
    }

    if (this.form.get('contractStatus')?.value) {
      filters.push({
        property: 'contract.status',
        operation: '=',
        value: this.form.get('contractStatus')?.value,
      });
    }

    return filters;
  }
  getcontractTypeOptions() {
    this.paymentReportService.PRcontractTypeOptions().subscribe((res: any) => {
      this.contractTypeOptions = res;
    });
  }
  gettypeOfPaymentOptions() {
    this.paymentReportService.PRTypeOfPayment().subscribe((res: any) => {
      this.typeOfPaymentOptions = res;
    });
  }
  getmethodOfPaymentOptions() {
    this.paymentReportService.getallpaymentmethods().subscribe((res: any) => {
      this.methodOfPaymentOptions = res;
    });
  }
  filterDataGrid() {
    let filters = this.prepareFilters();
    if (!!filters && filters.length > 0) {
      this.getTableData();
      this.paymentSum = null;
    }
  }
  resetFilters() {
    this.form.reset();
    this.filterDataGrid();
    this.paymentSum = null;
  }
  calculatePaymentSum() {
    let filters = this.prepareFilters();
    if (!!filters && filters.length > 0) {
      this.paymentReportService
        .calculatePaymentsSumAndVatByFiltered(filters)
        .subscribe((res: any) => {
          this.paymentSum = res;
        });
    }
  }
  checkDisableCalcPaymentsSum() {
    let filters = this.prepareFilters();
    return !(filters.length > 0);
  }
  generateExcelFileAdvanced() {
    let headers = this.getNonHiddenHeaders();
    let cols = this.getNonHiddenCols();
    let orderBy = this.paymentReportService.searchSubject.getValue().params.sort
      ? '?pageName=Payments Records Report&sort=' +
        this.paymentReportService.searchSubject.getValue().params.sort
      : '';
    let filters = this.prepareFilters();
    this.mediaService.downloadFile(
      `accounting/payments/csv/advanceSearchNEW${orderBy}`,
      '',
      {
        method: 'POST',
        body: {
          headers: headers,
          columns: cols,
          filters: filters,
        },
      }
    );
  }
  getNonHiddenHeaders() {
    return this.shownColumns.map((col) => {
      return col.label;
    });
  }
  getNonHiddenCols() {
    return this.shownColumns.map((col) => {
      return col.field;
    });
  }
  onColumnChange(columns: CCGridColumnSelectionItem[]) {
    this.shownColumns = columns.filter((col) => {
      return col.show;
    });
  }

  generateExcelFileAdvancedViaEmail() {
    let headers = this.getNonHiddenHeaders();
    let cols = this.getNonHiddenCols();
    let params: any = {};
    if (this.paymentReportService.searchSubject.getValue().params.sort) {
      params.pageName = 'Payments Records Report';
      params.sort =
        this.paymentReportService.searchSubject.getValue().params.sort;
    }
    let filters = this.prepareFilters();
    this.paymentReportService
      .PRgenerateExcelFileAdvancedViaEmail(params, {
        headers: headers,
        columns: cols,
        filters: filters,
      })
      .subscribe((res: any) => {
        this.notifications.notifySuccess(res, 6000);
      });
  }
}
