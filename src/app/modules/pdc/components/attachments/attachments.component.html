<cc-dialog-header>
  <h1 cc-dialog-title>Attachments</h1>
  <a
    role="button"
    type="button"
    cc-icon-button
    cc-dialog-close-button
    cc-dialog-close
  ></a
></cc-dialog-header>
<cc-dialog-content>
  <cc-datagrid
    [data]="data"
    [columns]="gridCols"
    [showPaginator]="false"
    [cellTemplate]="{ action: action }"
  >
  </cc-datagrid>
  <ng-template #action let-row let-index="index" let-col="colDef">
    <button cc-button (click)="preview(row)">
      <cc-icon>folder_alt</cc-icon>Open
    </button>
  </ng-template>
</cc-dialog-content>
