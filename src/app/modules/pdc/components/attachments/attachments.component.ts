import { Component, Inject, OnInit } from '@angular/core';
import { CCDialogRef } from '@maids/cc-lib/dialog';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { API } from 'src/environments/api';
import { HttpClient } from '@angular/common/http';
import { CCPreviewAttachmentComponent } from '@maids/cc-lib/preview-attachment';
import { MatDialog } from '@angular/material/dialog';
@Component({
  selector: 'app-attachments',
  templateUrl: './attachments.component.html',
  styleUrls: ['./attachments.component.scss'],
})
export class AttachmentsComponent implements OnInit {
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private ccDialogRef: CCDialogRef<AttachmentsComponent>,
    private http: HttpClient,
    private dialog: MatDialog
  ) {}
  gridCols: CCGridColumn[] = [
    { field: 'name', header: 'File' },
    { field: 'creationDate', header: 'Date' },
    { field: 'action', header: 'Action' },
  ];
  ngOnInit(): void {
    console.log(this.data);
  }
  preview(row: any) {
    this.http
      .get(`${API.download}/${row.uuid}`, { responseType: 'blob' })
      .subscribe((res: any) => {
        this.previewFile(res, row.name);
      });
  }
  previewFile(data: any, fileName: string) {
    let blob = new Blob([data], { type: data.type });
    const blobUrl = URL.createObjectURL(blob);
    let file_type = data.type.split('/')[1];
    this.dialog.open(CCPreviewAttachmentComponent, {
      width: '100%',
      data: {
        url: blobUrl,
        blob: blob,
        type: file_type,
        filename: fileName,
      },
    });
  }
}
