<div class="container" [formGroup]="formGroup">
  <div class="row justify-content-between">
    <div class="col-5">
      <div class="row align-items-center">
        <cc-input
          class="w-100"
          label="Amount"
          type="number"
          formControlName="amount"
          [required]="true"
        ></cc-input>
      </div>
    </div>
    <div class="col-5">
      <div class="row align-items-center">
        <cc-datepicker
          label="Cheque Due Date"
          class="w-100"
          formControlName="chequeDueDate"
          [required]="true"
        ></cc-datepicker>
      </div>
    </div>
  </div>
  <div class="row justify-content-between">
    <div class="col-5">
      <div class="row align-items-center">
        <cc-input
          formControlName="leaseCompanyName"
          label="Beneficiary Name"
          class="w-100"
        ></cc-input>
      </div>
    </div>
    <div class="col-5">
      <div class="row align-items-center">
        <cc-select
          label="Tenancy Contract"
          [data]="tenancyContractsList$ | async"
          formControlName="tenancyContract"
          [emitFullSelectOption]="true"
          class="w-100"
          [required]="true"
        ></cc-select>
      </div>
    </div>
  </div>
  <div class="row justify-content-between">
    <div class="col-5">
      <div class="row align-items-center">
        <cc-input
          label="Name of the Issuer Written on the Cheque"
          class="w-100"
          formControlName="chequeIssuerName"
        ></cc-input>
      </div>
    </div>
    <div class="col-5">
      <div class="row align-items-center">
        <cc-select
          class="w-100"
          label="Bank bucket name"
          [lazyPageFetcher]="buckets$"
          formControlName="bankIssuerBucket"
          [(ngModel)]="bucketModel"
        ></cc-select>
      </div>
    </div>
  </div>
  <div class="row justify-content-between">
    <div class="col-5"></div>
    <div class="col-5">
      <div class="row align-items-center">
        <cc-input
          label="Description"
          class="w-100"
          formControlName="propertyDiscription"
        ></cc-input>
      </div>
    </div>
  </div>
  <div class="row justify-content-between">
    <div class="col-5">
      <div class="row align-items-center">
        <cc-input
          type="number"
          class="w-100"
          label="VAT Amount"
          formControlName="vatAmount"
        ></cc-input>
      </div>
    </div>
    <div class="col-5">
      <div class="row align-items-center">
        <cc-input
          formControlName="license"
          label="License"
          class="w-100"
        ></cc-input>
      </div>
    </div>
  </div>
  <div class="row justify-content-between">
    <div class="col-5">
      <div class="row align-items-center">
        <cc-select
          label="Expense Code"
          class="w-100"
          [lazyPageFetcher]="expenses$"
          formControlName="expense"
        ></cc-select>
      </div>
    </div>
  </div>
  <hr />
  <div class="row justify-content-end">
    <div class="col-md-auto">
      <button cc-raised-button (click)="cancel()">Cancel</button>
    </div>
    <div class="col-md-auto">
      <button
        cc-raised-button
        color="accent"
        [disabled]="formGroup.invalid"
        (click)="save()"
      >
        Save
      </button>
    </div>
  </div>
</div>
