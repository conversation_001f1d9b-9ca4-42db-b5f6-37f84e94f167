import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { CCNotificationService } from '@maids/cc-lib/services';
import { map, Observable } from 'rxjs';
import { SelectOption } from '@maids/cc-lib/select-input';
import { PaginationRequest } from '@maids/cc-lib/common';
import { PdcService } from '../../services/pdc.service';

@Component({
  selector: 'app-pdc-form',
  templateUrl: './pdc-form.component.html',
  styleUrls: ['./pdc-form.component.scss'],
})
export class PdcFormComponent implements OnInit {
  formGroup = this.formBuilder.group({
    amount: [null],
    chequeDueDate: [null],
    leaseCompanyName: [null],
    tenancyContract: [null],
    chequeIssuerName: [],
    bankIssuerBucket: [null],
    propertyDiscription: [''],
    vatAmount: [null],
    license: [''],
    expense: [null],
  });
  bucketModel: any | null = null;
  constructor(
    private formBuilder: FormBuilder,
    private http: HttpClient,
    private pdcService: PdcService,
    public readonly notifications: CCNotificationService,
    private router: Router,
    private route: ActivatedRoute
  ) {}
  tenancyContractsList$: Observable<any[]> = this.pdcService
    .tenancyContractsList()
    .pipe(
      map((res: any[]) => {
        return res.map((opt: any) => {
          return { id: opt.id, text: opt.name } as SelectOption;
        });
      })
    );
  buckets$ = (pageReq: PaginationRequest): Observable<SelectOption[]> => {
    return this.pdcService
      .searchBuckets(pageReq.page, pageReq.size, pageReq.searchString)
      .pipe(
        map((data) =>
          data.content.map(
            (opt: any) => ({ id: opt.id, text: opt.name } as SelectOption)
          )
        )
      );
  };
  readonly expenses$ = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.pdcService
      .getExpenses(pageReq.page, pageReq.size, pageReq.searchString)
      .pipe(
        map((val: any) => {
          return val.content.map(
            (opt: any) => ({ id: opt.id, text: opt.label } as SelectOption)
          );
        })
      );
  };
  ngOnInit(): void {
    if (this.route.snapshot.params['id']) {
      this.pdcService
        .nonclientpdcById(this.route.snapshot.params['id'])
        .subscribe((res: any) => {
          this.formGroup.patchValue(res);
          if (res.bankIssuerBucket) {
            this.bucketModel = {
              id: res.bankIssuerBucket.id,
              text: res.bankIssuerBucket.label,
            };
            this.formGroup.controls['bankIssuerBucket'].setValue(
              this.bucketModel
            );
          }
          if (res.expense) {
            this.formGroup.controls['expense'].setValue({
              id: res.expense.id,
              text: res.expense.label,
            });
          }
          if (res.tenancyContract) {
            this.formGroup.controls['tenancyContract'].setValue({
              id: res.tenancyContract.id,
              text: res.tenancyContract.label,
            });
          }
        });
    }
  }
  save() {
    let payload: any;
    payload = {
      amount: this.formGroup.controls['amount'].value,
      leaseCompanyName: this.formGroup.controls['leaseCompanyName'].value,
      chequeIssuerName: this.formGroup.controls['chequeIssuerName'].value,
      propertyDiscription: this.formGroup.controls['propertyDiscription'].value,
      chequeDueDate: this.formGroup.controls['chequeDueDate'].value,
      vatAmount: this.formGroup.controls['vatAmount'].value,
      license: this.formGroup.controls['license'].value,
    };
    if (this.formGroup.controls['expense'].value) {
      payload.expense = {
        id:
          this.formGroup.controls['expense'].value.id ??
          this.formGroup.controls['expense'].value,
      };
    } else {
      payload.expense = null;
    }
    if (this.formGroup.controls['bankIssuerBucket'].value) {
      payload.bankIssuerBucket = {
        id:
          this.formGroup.controls['bankIssuerBucket'].value.id ??
          this.formGroup.controls['bankIssuerBucket'].value,
      };
    } else {
      payload.bankIssuerBucket = null;
    }
    if (this.formGroup.controls['tenancyContract'].value) {
      payload.tenancyContract = {
        id: this.formGroup.controls['tenancyContract'].value.id,
      };
    }
    payload.id = +this.route.snapshot.params['id'];
    if (this.route.snapshot.params['id']) {
      this.pdcService.updateNonclientpdc(payload).subscribe({
        next: (res: any) => {
          this.notifications.notifySuccess('PDC Updated Successfully', 2000);
          this.router.navigateByUrl(`accounting/v2/pdc-list`);
        },
        error: (err: any) => {
          this.notifications.notifyError(err.error.message, 2000);
        },
      });
    } else {
      this.pdcService.createNonclientpdc(payload).subscribe({
        next: (res: any) => {
          this.notifications.notifySuccess('PDC Added Successfully', 2000);
          this.router.navigateByUrl(`accounting/v2/pdc-list`);
        },
        error: (err: any) => {
          this.notifications.notifyError(err.error.message, 2000);
        },
      });
    }
  }
  cancel() {
    if (this.route.snapshot.params['id']) {
      this.router.navigateByUrl(`accounting/v2/pdc-list`);
    } else {
      this.router.navigateByUrl(
        `accounting/v2/tenancy-contracts/tenancy-contracts-form/${this.route.snapshot.params['id']}`
      );
    }
  }
}
