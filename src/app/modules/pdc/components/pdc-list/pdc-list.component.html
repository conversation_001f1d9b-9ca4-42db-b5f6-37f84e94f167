<div class="acc-7830">
  <cc-accordion>
    <cc-panel>
      <cc-panel-title>
        <cc-icon class="icon_filter">filter_alt</cc-icon>
        <span style="padding-left: 30px"> Advanced Search</span></cc-panel-title
      >
      <cc-panel-body>
        <form [formGroup]="searchForm" class="row">
          <div class="col-6 row align-items-center">
            <cc-select
              label="Operator"
              class="col-4 w-100"
              [data]="searchNumericOptions"
              formControlName="amountOperation"
            ></cc-select>
            <div
              class="col-8"
              [ngClass]="{
                'd-none': !searchForm.controls['amountOperation'].value
              }"
            >
              <cc-input
                label="Amount"
                type="number"
                formControlName="amount"
              ></cc-input>
            </div>
          </div>
          <div class="col-6 row align-items-center">
            <cc-select
              class="col-4 w-100"
              label="Operator"
              [data]="searchTextOptions"
              formControlName="chequeIssuerNameOperation"
            ></cc-select>
            <div
              class="col-8"
              [ngClass]="{
                'd-none':
                  !searchForm.controls['chequeIssuerNameOperation'].value
              }"
            >
              <cc-input
                label="Name of the Issuer Written on the Cheque"
                type="text"
                formControlName="chequeIssuerName"
              ></cc-input>
            </div>
          </div>
          <div class="col-6 row align-items-center">
            <cc-select
              class="col-4 w-100"
              label="Operator"
              [data]="searchTextOptions"
              formControlName="leaseCompanyNameOperation"
            ></cc-select>
            <div
              class="col-8"
              [ngClass]="{
                'd-none':
                  !searchForm.controls['leaseCompanyNameOperation'].value
              }"
            >
              <cc-input
                label="Beneficiary Name"
                type="text"
                formControlName="leaseCompanyName"
              ></cc-input>
            </div>
          </div>
          <div class="col-6 row align-items-center">
            <cc-select
              label="Operator"
              class="col-4 w-100"
              [data]="searchTextOptions"
              formControlName="propertyDiscriptionOperation"
            ></cc-select>
            <div
              class="col-8"
              [ngClass]="{
                'd-none':
                  !searchForm.controls['propertyDiscriptionOperation'].value
              }"
            >
              <cc-input
                label="Description"
                type="text"
                formControlName="propertyDiscription"
              ></cc-input>
            </div>
          </div>
          <div class="col-6 row align-items-center">
            <cc-select
              class="col-4 w-100"
              label="Operator"
              [data]="searchDateOptions"
              formControlName="chequeDueDateOperation"
            ></cc-select>

            <cc-datepicker
              [ngClass]="{
                'col-8':
                  searchForm.controls['chequeDueDateOperation'].value &&
                  searchForm.controls['chequeDueDateOperation'].value !==
                    'between',
                'col-4':
                  searchForm.controls['chequeDueDateOperation'].value ===
                  'between',
                'd-none': !searchForm.controls['chequeDueDateOperation'].value
              }"
              [label]="
                searchForm.controls['chequeDueDateOperation'].value == 'between'
                  ? 'Cheque Due Date From'
                  : 'Cheque Due Date'
              "
              formControlName="chequeDueDate"
            ></cc-datepicker>
            <cc-datepicker
              [ngClass]="{
                'd-none':
                  searchForm.controls['chequeDueDateOperation'].value !==
                    'between' ||
                  !searchForm.controls['chequeDueDateOperation'].value,
                'col-4':
                  searchForm.controls['chequeDueDateOperation'].value ==
                  'between'
              }"
              formControlName="chequeDueDateSecond"
              label="Cheque Due Date To"
            ></cc-datepicker>
          </div>
          <div class="col-6 row align-items-center">
            <cc-select
              class="col-4"
              label="Operator"
              [data]="searchNumericOptions"
              formControlName="vatOperation"
            ></cc-select>
            <div
              class="col-8"
              [ngClass]="{
                'd-none': !searchForm.controls['vatOperation'].value
              }"
            >
              <cc-input
                label="VAT Amount"
                type="number"
                formControlName="vatAmount"
              ></cc-input>
            </div>
          </div>
          <div class="row col-6 align-items-center">
            <cc-select
              class="col-4"
              label="Operator"
              [data]="searchTextOptions"
              formControlName="licenseOperation"
            ></cc-select>
            <div
              class="col-8"
              [ngClass]="{
                'd-none': !searchForm.controls['licenseOperation'].value
              }"
            >
              <cc-input label="License" type="text" formControlName="license"></cc-input>
            </div>
          </div>
          <div class="col-6 row align-items-center">
            <cc-select
              class="col-4"
              label="Expense"
              [lazyPageFetcher]="expenses$"
              formControlName="expense"
            ></cc-select>
          </div>
          <div class="col-6 row align-items-center">
            <p class="pre">Pre-Defined Reports</p>
            <label class="control-label col-md-4">PDCs / Aldrin :</label>
            <div class="col-md-3">
              <cc-checkbox
                formControlName="predefined"
                color="accent"
                (change)="togglePredefined($event)"
              ></cc-checkbox>
            </div>
          </div>
          <div class="row col-6">
            <div class="col-md-auto">
              <button
                cc-raised-button
                (click)="getPdcs()"
                style="padding-left: 35px"
                color="primary"
              >
                <cc-icon class="icon">search</cc-icon>Apply Filter
              </button>
            </div>
            <div class="col-md-auto">
              <button
                cc-button
                color="accent"
                (click)="reset()"
                style="padding-left: 35px"
              >
                <cc-icon class="icon">refresh</cc-icon>
                Reset Filters
              </button>
            </div>
          </div>
        </form>
      </cc-panel-body>
    </cc-panel>
  </cc-accordion>
  <div class="d-flex justify-content-between align-items-center mt-5">
    <div class="col-md-auto">
      <p>
        PDCs Sum: <span class="cc-secondary">{{ sum | number }}</span>
      </p>
      <p>
        Record Count: <span class="cc-secondary">{{ pdcs?.totalElements }}</span>
      </p>
    </div>
    <div class="col-md-auto">
      <button cc-raised-button color="primary" (click)="addPdc()" style="padding-left: 35px">
        <cc-icon class="icon">add</cc-icon>Add New PDC
      </button>
    </div>
  </div>
  <cc-datagrid
    class="my-2"
    [data]="pdcs?.content"
    [columns]="gridCols"
    [length]="pdcs?.totalElements"
    [pageOnFront]="false"
    [pageIndex]="pdcs?.number"
    [pageSize]="pdcs?.size"
    [pageSizeOptions]="[20]"
    (page)="getNextPage($event)"
    (sortChange)="onSortChange($event)"
    [stickyHeader]="true"
    [columnMovable]="true"
    [columnHideable]="true"
    [showColumnMenuButton]="true"
    [showColumnMenuHeader]="false"
    [columnMenuButtonIcon]="'settings'"
    [cellTemplate]="{ attachments: attachments }"
  ></cc-datagrid>
  <ng-template #attachments let-row let-index="index" let-col="colDef">
    <button
      cc-icon-button
      *ngIf="row.attachments.length > 0"
      (click)="openAttachmentsDialog(row)"
    >
      <cc-icon>folder</cc-icon>
    </button>
    <span *ngIf="row.attachments.length === 0">No Attachments</span>
  </ng-template>
</div>

