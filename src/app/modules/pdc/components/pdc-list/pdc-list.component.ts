import { Component, OnInit } from '@angular/core';
import { PaginationRequest } from '@maids/cc-lib/common';
import { SelectOption } from '@maids/cc-lib/select-input';
import { Observable, map } from 'rxjs';
import { PdcService } from '../../services/pdc.service';
import { FormBuilder } from '@angular/forms';
import { Sort } from '@angular/material/sort';
import { PageEvent } from '@angular/material/paginator';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { Router } from '@angular/router';
import { CCDialog } from '@maids/cc-lib/dialog';
import { CCNotificationService } from '@maids/cc-lib/services';
import { AttachmentsComponent } from '../attachments/attachments.component';
import { UploadAttachmentsComponent } from '../upload-attachments/upload-attachments.component';
import * as moment from 'moment';
@Component({
  selector: 'app-pdc-list',
  templateUrl: './pdc-list.component.html',
  styleUrls: ['./pdc-list.component.scss'],
})
export class PdcListComponent implements OnInit {
  searchForm = this.formBuilder.group({
    amount: [null],
    amountOperation: ['='],
    chequeIssuerName: [null],
    chequeIssuerNameOperation: ['like'],
    leaseCompanyName: [null],
    leaseCompanyNameOperation: ['like'],
    propertyDiscription: [null],
    propertyDiscriptionOperation: ['like'],
    chequeDueDate: [null],
    chequeDueDateOperation: ['='],
    chequeDueDateSecond: [null],
    vatAmount: [null],
    vatOperation: ['='],
    license: [null],
    licenseOperation: ['='],
    expense: [null],
    predefined: [false],
  });
  searchTextOptions = [
    { id: '=', text: 'Equals' },
    { id: '!=', text: 'Not Equals' },
    { id: 'like', text: 'Contains' },
    { id: 'not like', text: 'Not Contains' },
    { id: 'starts with', text: 'Starts With' },
    { id: 'ends with', text: 'Ends With' },
  ];

  searchDateOptions = [
    { id: '=', text: 'Equals' },
    { id: '<', text: 'Before' },
    { id: '>', text: 'After' },
    { id: 'between', text: 'Between' },
  ];

  searchNumericOptions = [
    { id: '=', text: 'Equals' },
    { id: '>=', text: 'More or Equals' },
    { id: '<=', text: 'Less Or Equals' },
    { id: '!=', text: 'Not Equals' },
  ];
  pdcs: any | null = null;
  sum: number = 0;
  gridCols: CCGridColumn[] = [
    {
      field: 'actions',
      header: '',
      sortable: false,
      type: 'button',
      buttonConfig: {
        mode: 'menu',
        icon: 'more_vert',
        disabled: false,
        buttons: [
          {
            type: 'stroked',
            color: 'basic',
            text: 'Edit',
            icon: 'edit',
            mode: 'single',
            disabled: false,
            hidden: (row: any) => {
              return false;
            },
            callback: (row: any) => this.edit(row),
          },
          {
            type: 'stroked',
            text: 'Delete',
            icon: 'delete',
            color: 'warn',
            mode: 'single',
            disabled: false,
            hidden: (row: any) => {
              return false;
            },
            callback: (row: any) => this.delete(row),
          },
          {
            type: 'stroked',
            text: 'Upload Attachment',
            icon: 'cloud_upload',
            color: 'basic',
            mode: 'single',
            disabled: false,
            hidden: (row: any) => {
              return false;
            },
            callback: (row: any) => this.uploadAttachment(row),
          },
          {
            type: 'stroked',
            text: 'Withdraw',
            icon: 'paid',
            color: 'basic',
            mode: 'single',
            disabled: false,
            hidden: (row: any) => {
              return false;
            },
            callback: (row: any) => this.withdraw(row),
          },
          {
            type: 'stroked',
            text: 'Bounced',
            icon: 'attach_money',
            color: 'basic',
            mode: 'single',
            disabled: false,
            hidden: (row: any) => {
              return false;
            },
            callback: (row: any) => this.bounced(row),
          },
        ],
      },
    },
    { field: 'amount', header: 'Amount', sortable: true },
    {
      field: 'chequeDueDate',
      header: 'Cheque Due Date',
      type: 'date',
      sortable: true,
    },
    {
      field: 'tenancyContract.label',
      header: 'Tenancy Contract',
      sortable: true,
     formatter(rowData, colDef) {
      return rowData.tenancyContract
        ? `<a class="cc-secondary" href="#!/accounting/v2/tenancy-contracts/tenancy-contracts-form/${rowData.tenancyContract.id}">${rowData.tenancyContract.label}</a>`
        : '';
    },
    },
    { field: 'propertyDiscription', header: 'Description', sortable: true },
    {
      field: 'bankIssuerBucket.name',
      header: 'Bank bucket name',
      sortable: true,
    },
    { field: 'leaseCompanyName', header: 'Beneficiary Name', sortable: true },
    {
      field: 'chequeIssuerName',
      header: 'Name of the Issuer Written on the Cheque',
      sortable: true,
    },
    { field: 'vatAmount', header: 'Vat Amount', sortable: true },
    { field: 'license', header: 'License', sortable: true },
    { field: 'expense.label', header: 'Expense', sortable: true },
    { field: 'attachments', header: 'Attachments' },
  ];
  readonly expenses$ = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.pdcService
      .getExpenses(pageReq.page, pageReq.size, pageReq.searchString)
      .pipe(
        map((val: any) => {
          return val.content.map(
            (opt: any) => ({ id: opt.id, text: opt.label } as SelectOption)
          );
        })
      );
  };
  constructor(
    private pdcService: PdcService,
    private formBuilder: FormBuilder,
    private router: Router,
    private ccDialog: CCDialog,
    public readonly notifications: CCNotificationService
  ) {}
  ngOnInit(): void {
    this.getPdcs();
    this.formValidation();
  }
  getPdcs() {
    this.pdcService
      .getNonclientpdcs(this.applyFilter())
      .pipe(
        map((res: any) => {
          this.pdcs = res;
          return res;
        })
      )
      .subscribe();
    this.sumPdcs();
  }
  sumPdcs() {
    this.pdcService
      .getSumpdc(this.applyFilter())
      .pipe(
        map((res: any) => {
          this.sum = res;
          return res;
        })
      )
      .subscribe();
  }
  getNextPage(e: PageEvent) {
    this.pdcService.searchSubject.next({
      params: {
        page: e.pageIndex,
        size: e.pageSize,
        sort: this.pdcService.searchSubject.getValue().params.sort,
      },
    });
    this.getPdcs();
  }
  onSortChange(event: Sort) {
    if (event.active == 'tenancyContract.label') {
      this.pdcService.searchSubject.next({
        params: {
          sort: `tenancyContract,${event.direction}`,
          page: this.pdcService.searchSubject.getValue().params.page,
          size: this.pdcService.searchSubject.getValue().params.size,
        },
      });
    } else if (event.active == 'expense.label') {
      this.pdcService.searchSubject.next({
        params: {
          sort: `expense,${event.direction}`,
          page: this.pdcService.searchSubject.getValue().params.page,
          size: this.pdcService.searchSubject.getValue().params.size,
        },
      });
    } else {
      this.pdcService.searchSubject.next({
        params: {
          sort: `${event.active},${event.direction}`,
          page: this.pdcService.searchSubject.getValue().params.page,
          size: this.pdcService.searchSubject.getValue().params.size,
        },
      });
    }
    this.getPdcs();
  }
  applyFilter() {
    let payload: any[] = [];
    this.searchForm.controls['amount'].value &&
    this.searchForm.controls['amountOperation'].value
      ? payload.push({
          operation: this.searchForm.controls['amountOperation'].value,
          property: 'amount',
          value: +this.searchForm.controls['amount'].value,
        })
      : null;
    this.searchForm.controls['chequeIssuerName'].value &&
    this.searchForm.controls['chequeIssuerNameOperation'].value
      ? payload.push({
          operation:
            this.searchForm.controls['chequeIssuerNameOperation'].value,
          property: 'chequeIssuerName',
          value: this.searchForm.controls['chequeIssuerName'].value,
        })
      : null;
    this.searchForm.controls['leaseCompanyName'].value &&
    this.searchForm.controls['leaseCompanyNameOperation'].value
      ? payload.push({
          operation:
            this.searchForm.controls['leaseCompanyNameOperation'].value,
          property: 'leaseCompanyName',
          value: this.searchForm.controls['leaseCompanyName'].value,
        })
      : null;
    this.searchForm.controls['propertyDiscription'].value &&
    this.searchForm.controls['propertyDiscriptionOperation'].value
      ? payload.push({
          operation:
            this.searchForm.controls['propertyDiscriptionOperation'].value,
          property: 'propertyDiscription',
          value: this.searchForm.controls['propertyDiscription'].value,
        })
      : null;
    if (
      this.searchForm.controls['chequeDueDate'].value &&
      this.searchForm.controls['chequeDueDateOperation'].value
    ) {
      let filter: any;
      filter = {
        operation: this.searchForm.controls['chequeDueDateOperation'].value,
        property: 'chequeDueDate',
        value: this.searchForm.controls['chequeDueDate'].value,
      };
      if (
        this.searchForm.controls['chequeDueDateOperation'].value == 'between'
      ) {
        filter.secondValue =
          this.searchForm.controls['chequeDueDateSecond'].value;
      }
      payload.push(filter);
    }

    this.searchForm.controls['vatAmount'].value
      ? payload.push({
          operation: this.searchForm.controls['vatOperation'].value,
          property: 'vatAmount',
          value: parseFloat(this.searchForm.controls['vatAmount'].value),
        })
      : null;
    this.searchForm.controls['license'].value
      ? payload.push({
          operation: this.searchForm.controls['licenseOperation'].value,
          property: 'license',
          value: this.searchForm.controls['license'].value,
        })
      : null;
    this.searchForm.controls['expense'].value
      ? payload.push({
          operation: '=',
          property: 'expense.id',
          value: this.searchForm.controls['expense'].value,
        })
      : null;
    return payload;
  }
  togglePredefined(event: any) {
    if (event.checked) {
      this.searchForm.controls['chequeDueDateOperation'].setValue('between');
      this.searchForm.controls['chequeDueDate'].setValue(
        moment().startOf('month').format('YYYY-MM-DD')
      );
      this.searchForm.controls['chequeDueDateSecond'].setValue(
        moment().endOf('month').format('YYYY-MM-DD')
      );
      this.getPdcs();
    }
  }
  reset() {
    this.searchForm.reset();
    this.searchForm.controls['amountOperation'].setValue('=');
    this.searchForm.controls['chequeIssuerNameOperation'].setValue('like');
    this.searchForm.controls['leaseCompanyNameOperation'].setValue('like');
    this.searchForm.controls['propertyDiscriptionOperation'].setValue('like');
    this.searchForm.controls['chequeDueDateOperation'].setValue('=');
    this.searchForm.controls['vatOperation'].setValue('=');
    this.searchForm.controls['licenseOperation'].setValue('=');
    this.getPdcs();
  }
  edit(element: any) {
    this.router.navigateByUrl(`accounting/v2/pdc-list/pdc-form/${element.id}`);
  }
  uploadAttachment(element: any) {
    this.ccDialog
      .originalOpen(UploadAttachmentsComponent, { data: element })
      .afterClosed()
      .subscribe((res: any) => {
        if (res) {
          this.getPdcs();
        }
      });
  }
  delete(element: any) {
    this.ccDialog.confirm(
      'Warning',
      'Are you sure you want to Delete this PDC?',
      () => {
        this.pdcService.deleteNonclientpdc(element.id).subscribe({
          next: (res: any) => {
            this.notifications.notifySuccess('PDC Deleted Successfully');
            this.getPdcs();
          },
          error: (err: any) =>
            this.notifications.notifyError(err.error.message),
        });
      }
    );
  }
  withdraw(element: any) {
    this.ccDialog.confirm(
      'Warning',
      'Are you sure you want to make this PDC Withdraw?',
      () => {
        this.pdcService.withdrawNonclientpdc(element.id).subscribe({
          next: (res: any) => {
            this.notifications.notifySuccess('PDC Updated Successfully');
            this.getPdcs();
          },
          error: (err: any) =>
            this.notifications.notifyError(err.error.message),
        });
      }
    );
  }
  bounced(element: any) {
    this.ccDialog.confirm(
      'Warning',
      'Are you sure you want to make this PDC Bounced?',
      () => {
        this.pdcService.bounceNonclientpdc(element.id).subscribe({
          next: (res: any) => {
            this.notifications.notifySuccess('PDC Updated Successfully');
            this.getPdcs();
          },
          error: (err: any) =>
            this.notifications.notifyError(err.error.message),
        });
      }
    );
  }
  openAttachmentsDialog(element: any) {
    this.ccDialog.originalOpen(AttachmentsComponent, {
      data: element.attachments,
    });
  }
  addPdc() {
    this.router.navigateByUrl(`accounting/v2/pdc-list/pdc-form`);
  }
  formValidation() {
    this.searchForm.controls['amountOperation'].valueChanges.subscribe(
      (val) => {
        if (!val) {
          this.searchForm.controls['amount'].setValue(null);
        }
      }
    );
    this.searchForm.controls[
      'chequeIssuerNameOperation'
    ].valueChanges.subscribe((val) => {
      if (!val) {
        this.searchForm.controls['chequeIssuerName'].setValue(null);
      }
    });
    this.searchForm.controls[
      'leaseCompanyNameOperation'
    ].valueChanges.subscribe((val) => {
      if (!val) {
        this.searchForm.controls['leaseCompanyName'].setValue(null);
      }
    });
    this.searchForm.controls[
      'propertyDiscriptionOperation'
    ].valueChanges.subscribe((val) => {
      if (!val) {
        this.searchForm.controls['propertyDiscription'].setValue(null);
      }
    });
    this.searchForm.controls['chequeDueDateOperation'].valueChanges.subscribe(
      (val) => {
        if (!val) {
          this.searchForm.controls['chequeDueDate'].setValue(null);
          this.searchForm.controls['chequeDueDateSecond'].setValue(null);
        }
      }
    );
    this.searchForm.controls['vatOperation'].valueChanges.subscribe((val) => {
      if (!val) {
        this.searchForm.controls['vat'].setValue(null);
      }
    });
    this.searchForm.controls['licenseOperation'].valueChanges.subscribe(
      (val) => {
        if (!val) {
          this.searchForm.controls['license'].setValue(null);
        }
      }
    );
  }
}
