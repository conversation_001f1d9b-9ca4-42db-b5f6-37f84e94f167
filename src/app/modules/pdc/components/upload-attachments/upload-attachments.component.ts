import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { CCPicklistService } from '@maids/cc-erp-services';
import { PaginationRequest } from '@maids/cc-lib/common';
import { CCDialogRef } from '@maids/cc-lib/dialog';
import { SelectOption } from '@maids/cc-lib/select-input';
import { Observable, map } from 'rxjs';
import { CCNotificationService } from '@maids/cc-lib/services';
import { PdcService } from '../../services/pdc.service';
import { CCFileUploaderConfig } from '@maids/cc-lib/file-uploader';
@Component({
  selector: 'app-upload-attachments',
  templateUrl: './upload-attachments.component.html',
  styleUrls: ['./upload-attachments.component.scss'],
})
export class UploadAttachmentsComponent implements OnInit {
  formGroup = this.formBuilder.group({
    attachments: [null, [Validators.required]],
  });
  readonly typeOfDocument = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.picklistService
      .getPicklist({
        code: 'PICKLIST_TENANCY_TYPE_OF_DOCUMENT',
        page: pageReq.page,
        pageSize: pageReq.size,
        search: pageReq.searchString,
      })
      .pipe(
        map((val: any[]) => {
          return val.map(
            (opt) => ({ id: opt.id, text: opt.label } as SelectOption)
          );
        })
      );
  };
  config: CCFileUploaderConfig = {
    maxFilesize: 10,
  };
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private ccDialogRef: CCDialogRef<UploadAttachmentsComponent>,
    private formBuilder: FormBuilder,
    private picklistService: CCPicklistService,
    private pdcService: PdcService,
    public readonly notifications: CCNotificationService
  ) {}

  ngOnInit(): void {}
  add() {
    let payload: any;
    payload = {
      id: this.data.id,
      attachments: [
        ...this.data.attachments.map((data: any) => {
          return { id: data.id };
        }),
        { id: this.formGroup.value.attachments[0].id },
      ],
    };
    this.pdcService.addAttachment(payload).subscribe({
      next: () => {
        this.notifications.notifySuccess('Attachments Added Successfully');
        this.ccDialogRef.close(true);
      },
      error: (err: any) => {
        this.notifications.notifyError(err.error.message);
      },
    });
  }
}
