import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PdcListComponent } from './components/pdc-list/pdc-list.component';
import { PdcFormComponent } from './components/pdc-form/pdc-form.component';

const routes: Routes = [
  { path: '', component: PdcListComponent },
  {
    path: 'pdc-form',
    component: PdcFormComponent,
    data: { label: 'Add PDC', pageCode: 'pdc-form' },
  },
  {
    path: 'pdc-form/:id',
    component: PdcFormComponent,
    data: { label: 'Edit PDC', pageCode: 'pdc-form' },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class PdcRoutingModule {}
