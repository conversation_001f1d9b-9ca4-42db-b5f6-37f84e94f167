import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { PdcRoutingModule } from './pdc-routing.module';
import { PdcListComponent } from './components/pdc-list/pdc-list.component';
import { PdcFormComponent } from './components/pdc-form/pdc-form.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CCSelectInputModule } from '@maids/cc-lib/select-input';
import { CCInputModule } from '@maids/cc-lib/input';
import { CCCardModule } from '@maids/cc-lib/card';
import { CCButtonModule } from '@maids/cc-lib/button';
import { CCDatepickerModule } from '@maids/cc-lib/date';
import { CCAccordionModule } from '@maids/cc-lib/accordion';
import { CCCheckboxModule } from '@maids/cc-lib/checkbox';
import { CCDatagridModule } from '@maids/cc-lib/datagrid';
import { CCDialogModule } from '@maids/cc-lib/dialog';
import { CCIconModule } from '@maids/cc-lib/icon';
import { AttachmentsComponent } from './components/attachments/attachments.component';
import { UploadAttachmentsComponent } from './components/upload-attachments/upload-attachments.component';
import { CCFileUploaderModule } from '@maids/cc-lib/file-uploader';

@NgModule({
  declarations: [
    PdcListComponent,
    PdcFormComponent,
    AttachmentsComponent,
    UploadAttachmentsComponent,
  ],
  imports: [
    CommonModule,
    PdcRoutingModule,
    ReactiveFormsModule,
    CCSelectInputModule,
    CCInputModule,
    CCCardModule,
    CCButtonModule,
    CCDatepickerModule,
    CCAccordionModule,
    FormsModule,
    CCCheckboxModule,
    CCDatagridModule,
    CCDialogModule,
    CCIconModule,
    CCFileUploaderModule.forChild({}),
  ],
})
export class PdcModule {}
