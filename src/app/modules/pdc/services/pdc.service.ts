import { HttpClient, HttpContext } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import {
  CCBackendEndpoint,
  REQ_SHOW_LOADING_ICON,
} from '@maids/cc-erp-services';
import { SearchModel } from '@maids/cc-lib/common';
import { BehaviorSubject, Observable } from 'rxjs';
import { API } from 'src/environments/api';

@Injectable({
  providedIn: 'root',
})
export class PdcService {
  initialSearch: SearchModel<any> = {
    params: {
      page: 0,
      size: 20,
      sort: '',
    },
    search: '',
  };
  searchSubject = new BehaviorSubject<SearchModel<any>>(this.initialSearch);
  constructor(
    private _http: HttpClient,
    @Inject(CCBackendEndpoint) private _api: CCBackendEndpoint
  ) {}
  tenancyContractsList(): Observable<any[]> {
    return this._http.get<any[]>(`${this._api}/${API.tenancyContractsList}`);
  }
  createNonclientpdc(payload: any): Observable<any> {
    return this._http.post(`${this._api}/${API.createNonclientpdc}`, payload);
  }
  updateNonclientpdc(payload: any): Observable<any> {
    return this._http.post(`${this._api}/${API.updateNonclientpdc}`, payload);
  }
  searchBuckets(
    page: number,
    size: number = 20,
    search: string = ''
  ): Observable<any> {
    return this._http.get<any[]>(`${this._api}/${API.searchBuckets}`, {
      params: { page, size, search },
    });
  }
  getExpenses(
    page: number,
    size: number = 20,
    search: string = ''
  ): Observable<any> {
    const context = new HttpContext().set(REQ_SHOW_LOADING_ICON, false);
    return this._http.get(`${this._api}/${API.searchExpensesPageable}`, {
      params: { page, size, search },
      context,
    });
  }
  nonclientpdcById(id: string): Observable<any> {
    return this._http.get(`${this._api}/${API.nonclientpdcById}/${id}`);
  }
  getNonclientpdcs(payload: any): Observable<any> {
    return this._http.post(`${this._api}/${API.getNonclientpdc}`, payload, {
      params: this.searchSubject.getValue().params,
    });
  }
  getSumpdc(payload: any): Observable<any> {
    return this._http.post(`${this._api}/${API.getSumpdc}`, payload);
  }
  deleteNonclientpdc(id: string): Observable<any> {
    return this._http.delete(`${this._api}/${API.deleteNonclientpdc}/${id}`);
  }
  withdrawNonclientpdc(id: string): Observable<any> {
    return this._http.get(`${this._api}/${API.withdrawNonclientpdc}/${id}`);
  }
  bounceNonclientpdc(id: string): Observable<any> {
    return this._http.get(`${this._api}/${API.bounceNonclientpdc}/${id}`);
  }
  addAttachment(payload: any): Observable<any> {
    return this._http.post(`${this._api}/${API.updateNonclientpdc}`, payload);
  }
}
