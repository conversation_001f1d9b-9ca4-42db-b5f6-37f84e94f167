
<div class="ACC-7718">
  <div class="container-fluid">
    <div class="d-flex align-items-center">
      <cc-select
        class="col-2"
        label="Company"
        [(ngModel)]="company"
        [lazyPageFetcher]="fetchCompanyOptions$"
      ></cc-select>
      <cc-datepicker
        class="col-2"
        label="From Date"
        [(ngModel)]="fromDate"
        [disabled]="!company"
      >
      </cc-datepicker>
      <cc-datepicker
        class="col-2"
        label="To Date"
        [(ngModel)]="toDate"
        [disabled]="!company"
      >
      </cc-datepicker>

      <cc-label class="mb-2">Generate based on</cc-label>
      <div class="d-flex">
        <cc-radio-group
          [(ngModel)]="searchCriteria"
          name="searchCriteria"
          class="d-flex col-md-4"
        >
          <cc-radio-button value="PNL_DATE" class="mx-2">
            PnL date of transaction
          </cc-radio-button>
          <cc-radio-button value="ACTUAL_DATE">
            Actual date of transaction
          </cc-radio-button>
        </cc-radio-group>
      </div>
    </div>
    <div class="row justify-content-center">
      <div class="col-md-auto">
        <button
          [disabled]="!fromDate || !toDate || !company"
          (click)="generateReport('HTML')"
          cc-raised-button
          color="primary"
        >
          Gererate P&L Report
        </button>
      </div>
    </div>
    <div class="d-flex">
      <div class="col-md-auto">
        <h4>Export</h4>
      </div>
    </div>
    <div class="d-flex">
      <div class="col-md-auto">
        <button
          [disabled]="!generatedData"
          (click)="generateReport('PDF')"
          cc-raised-button
          color="accent"
        >
          PDF
        </button>
      </div>
      <div class="col-md-auto">
        <button
          [disabled]="!generatedData"
          (click)="generateReport('EXCEL')"
          cc-raised-button
          color="accent"
        >
          EXCEL
        </button>
      </div>
      <div class="col-md-auto">
        <button
          [disabled]="!generatedData"
          (click)="generateReport('EXCEL', true)"
          cc-raised-button
          color="accent"
        >
          EXCEL With Rounding
        </button>
      </div>
    </div>
  </div>

  <div [innerHTML]="html" class="pdf-page"></div>
</div>
