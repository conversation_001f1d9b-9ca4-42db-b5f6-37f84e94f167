// Modal content scrolling styles
.transaction-details-modal {
  .container {
    max-height: 80vh;
    overflow-y: auto;
    padding: 1rem;
    
    // Smooth scrolling behavior
    scroll-behavior: smooth;
    
    // Custom scrollbar styling
    &::-webkit-scrollbar {
      width: 8px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;
      
      &:hover {
        background: #a1a1a1;
      }
    }
  }
  
  // Ensure datagrid doesn't exceed modal height
  cc-datagrid {
    max-height: 60vh;
    overflow-y: auto;
    
    // Make table body scrollable while keeping header fixed
    ::ng-deep .mat-table-container {
      max-height: 60vh;
      overflow-y: auto;
    }
  }
}

// Alternative approach for global modal scrolling
::ng-deep .mat-dialog-container {
  &.transaction-details-modal-container {
    max-height: 90vh;
    overflow-y: auto;
    
    .mat-dialog-content {
      max-height: 70vh;
      overflow-y: auto;
      padding: 0;
    }
  }
}
