import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnInit,
  AfterViewInit,
  OnDestroy,
  Inject,
} from '@angular/core';
import { SelectOption } from '@maids/cc-lib/select-input';
import { PnlReportService } from '../../services/pnl-report.service';
import { PaginationRequest } from '@maids/cc-lib/common';
import { DomSanitizer } from '@angular/platform-browser';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { CCPreviewAttachmentComponent } from '@maids/cc-lib/preview-attachment';
import {
  MatDialog,
  MatDialogRef,
  MAT_DIALOG_DATA,
} from '@angular/material/dialog';
import { API } from '../../../../../environments/api';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { PageEvent } from '@angular/material/paginator';
import { Sort } from '@angular/material/sort';
@Component({
  selector: 'app-transaction-details-modal',
  template: ` <div class="transaction-details-modal">
    <div class="container">
      <div class="row">
        <div class="col-md-auto">
          <h4>Transactions Behind Row</h4>
        </div>
      </div>
      <div class="d-flex justify-content-between align-items-center my-2">
        <div class="col-md-auto px-0 row">
          <div class="col-md-auto">
            Total Amount:
            {{ totalAmount_api ? totalAmount_api : data.totalAmount }}
          </div>
          <div class="col-md-auto ">
            Total VAT: {{ totalVat_api ? totalVat_api : data.totalVat }}
          </div>
        </div>
        <div class="col-md-auto px-0">
          <button
            cc-raised-button
            color="primary"
            (click)="exportTransactionsBehindRow()"
          >
            Export
          </button>
        </div>
      </div>
      <cc-datagrid
        *ngIf="gridReady && (gridCols?.length ?? 0) > 0"
        [data]="transactionDetails.content ?? []"
        [columns]="gridCols"
        [length]="transactionDetails?.totalElements ?? 0"
        [pageIndex]="transactionDetails?.number ?? 0"
        [pageSize]="transactionDetails?.size ?? 0"
        (page)="getNextPage($event)"
        (sortChange)="onSortChange($event)"
        [cellTemplate]="{ index: indexTemplate }"
      ></cc-datagrid>
      <ng-template #indexTemplate let-index="index">
        {{ index + 1 }}
      </ng-template>
    </div>
  </div>`,
})
export class TransactionDetailsModalComponent implements OnInit {
  totalAmount_api: number = 0;
  transactionDetails: any = [];
  totalVat_api: number = 0;
  gridReady: boolean = false;
  columns: string[] = [
    'fromBucket',
    'revenue',
    'expense',
    'toBucket',
    'description',
    'amount',
    'vatAmount',
    'averageAmount',
    'profitAdjustment',
    'date',
    'creationDate',
  ];
  gridCols: CCGridColumn[] = [];

  constructor(
    public dialogRef: MatDialogRef<TransactionDetailsModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private _pnlReportService: PnlReportService,
    private _cdr: ChangeDetectorRef,
    private mediaService: MediaService
  ) {}
  ngOnInit(): void {
    this.getDetailsData();
  }
  private getBaseGridCols(): CCGridColumn[] {
    return [
      { field: 'index', header: '#' },
      {
        field: 'id',
        header: 'Transaction ID',
        sortable: true,
        sortProp: { arrowPosition: 'before', id: 'id', start: 'desc' },
      },
      {
        field: 'fromBucket.name',
        header: 'Bucket From',
        sortable: true,
        sortProp: { arrowPosition: 'before', id: 'fromBucket', start: 'desc' },
      },
      {
        field: 'revenue.name',
        header: 'Revenue Name',
        sortable: true,
        sortProp: { arrowPosition: 'before', id: 'revenue', start: 'desc' },
      },
      {
        field: 'expense.name',
        header: 'Expense Name',
        sortable: true,
        sortProp: { arrowPosition: 'before', id: 'expense', start: 'desc' },
      },
      {
        field: 'toBucket.name',
        header: 'Bucket To',
        sortable: true,
        sortProp: { arrowPosition: 'before', id: 'toBucket', start: 'desc' },
      },
      {
        field: 'description',
        header: 'Description',
        sortable: true,
        sortProp: { arrowPosition: 'before', id: 'description', start: 'desc' },
      },
      {
        field: 'amount',
        header: 'Amount (AED)',
        sortable: true,
        sortProp: { arrowPosition: 'before', id: 'amount', start: 'desc' },
      },
      {
        field: 'vatAmount',
        header: 'Vat',
        sortable: true,
        sortProp: { arrowPosition: 'before', id: 'vatAmount', start: 'desc' },
      },
      {
        field: 'averageAmount',
        header: 'Average',
        sortable: true,
        sortProp: {
          arrowPosition: 'before',
          id: 'averageAmount',
          start: 'desc',
        },
      },
      {
        field: 'profitAdjustment',
        header: 'Profit Adjustment',
        sortable: true,
        sortProp: {
          arrowPosition: 'before',
          id: 'profitAdjustment',
          start: 'desc',
        },
      },
      {
        field: 'date',
        header: 'Date of Transaction',
        sortable: true,
        sortProp: { arrowPosition: 'before', id: 'date', start: 'desc' },
      },
      {
        field: 'creationDate',
        header: 'Date of Creation',
        sortable: true,
        sortProp: { arrowPosition: 'before', id: 'creationDate', start: 'desc' },
      },
    ];
  }
  private buildGridColsWithVisibility(data: any[]): CCGridColumn[] {
    const baseCols = this.getBaseGridCols();
    if (!Array.isArray(data)) return baseCols;
    const keyToFieldMap: { [key: string]: string } = {
      fromBucket: 'fromBucket.name',
      revenue: 'revenue.name',
      expense: 'expense.name',
      toBucket: 'toBucket.name',
      description: 'description',
      amount: 'amount',
      vatAmount: 'vatAmount',
      averageAmount: 'averageAmount',
      profitAdjustment: 'profitAdjustment',
      date: 'date',
      creationDate: 'creationDate',
    };
    const nestedNameKeys = new Set(['fromBucket', 'revenue', 'expense', 'toBucket']);

    return baseCols.map((col) => {
      // keep index and id always visible
      if (col.field === 'index' || col.field === 'id') return col;
      // find corresponding key for this field
      const key = Object.keys(keyToFieldMap).find(
        (k) => keyToFieldMap[k] === col.field
      );
      if (!key) return col;
      let hasValues = false;
      if (nestedNameKeys.has(key)) {
        hasValues = data.some((row) => !!(row?.[key]?.name));
      } else {
        hasValues = data.some((row) => Boolean(row?.[key]));
      }
      return { ...col, hide: !hasValues } as CCGridColumn;
    });
  }
  getDetailsData() {
    this._pnlReportService
      .getTransactionDetails(this.data.detailsID, {
        fromDate: this.data.fromDate,
        toDate: this.data.toDate,
        searchCriteria: this.data.searchCriteria,
      })
      .subscribe((response: any) => {
        this.transactionDetails = response;
        this.totalAmount_api = response.totalSum;
        this.totalVat_api = response.totalVat;
        this.gridCols = this.buildGridColsWithVisibility(
          response?.content ?? []
        );
        this.gridReady = true;
        this._cdr.markForCheck();
      });
  }
  getNextPage(pageEvent: PageEvent) {
    this._pnlReportService.searchSubject.next({
      ...this._pnlReportService.searchSubject.value,
      params: {
        ...this._pnlReportService.searchSubject.value.params,
        page: pageEvent.pageIndex,
        size: pageEvent.pageSize,
      },
    });
    this.getDetailsData();
  }
  onSortChange(sort: Sort) {
    this._pnlReportService.searchSubject.next({
      ...this._pnlReportService.searchSubject.value,
      params: {
        ...this._pnlReportService.searchSubject.value.params,
        sort: sort.active + ',' + sort.direction,
      },
    });
    this.getDetailsData();
  }
  exportTransactionsBehindRow() {
    this.mediaService.downloadFile(
      [API.exporttransactionsbehindrow, this.data.detailsID].join('/'),
      '',
      {
        method: 'GET',
        params: {
          fromDate: this.data.fromDate,
          toDate: this.data.toDate,
          searchCriteria: this.data.searchCriteria,
        },
      }
    );
  }

  onClose(): void {
    this.dialogRef.close();
  }
}

@Component({
  selector: 'app-pnl-report',
  templateUrl: './pnl-report.component.html',
  styleUrls: ['./pnl-report.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PnlReportComponent implements OnInit, AfterViewInit, OnDestroy {
  companyOptions: SelectOption[] = [];
  company: string | number = 0;
  fromDate: any = '';
  toDate: any = '';
  searchCriteria: string = 'ACTUAL_DATE';
  html: any = '';
  generatedData: boolean = false;
  // Properties for transaction details modal
  detailsID: string = '';
  totalAmount: string = '';
  totalVat: string = '';

  private clickListener?: (event: Event) => void;

  constructor(
    private _pnlReportService: PnlReportService,
    private sanitized: DomSanitizer,
    private dialog: MatDialog,
    private _mediaService: MediaService,
    private _cdr: ChangeDetectorRef,
    private notificationService: CCNotificationService
  ) {}

  ngOnInit(): void {}

  ngAfterViewInit(): void {
    this.setupEventDelegation();
  }

  ngOnDestroy(): void {
    // Clean up event listener
    if (this.clickListener) {
      document.removeEventListener('click', this.clickListener);
    }
  }

  private setupEventDelegation(): void {
    // Create the click listener
    this.clickListener = (event: Event) => {
      const target = event.target as HTMLElement;

      // Check if the clicked element is an anchor with class 'showdetails'
      if (target.tagName === 'A' && target.classList.contains('showdetails')) {
        event.preventDefault(); // Prevent default anchor behavior

        // Extract data from the clicked element
        const detailsID = target.getAttribute('data-id') || '';
        const totalAmount = target.getAttribute('total-amount') || '';
        const totalVat = target.getAttribute('total-vat') || '';

        console.log('Clicked transaction details:', {
          detailsID,
          totalAmount,
          totalVat,
        });

        // Open the modal with the extracted data
        this.openTransactionDetailsModal(detailsID, totalAmount, totalVat);
      }
    };

    // Add event listener to document for event delegation
    document.addEventListener('click', this.clickListener);
  }

  private openTransactionDetailsModal(
    detailsID: string,
    totalAmount: string,
    totalVat: string
  ): void {
    // Store the values
    this.detailsID = detailsID;
    this.totalAmount = totalAmount;
    this.totalVat = totalVat;

    // Call your details data method
    // this.getDetailsData();

    // Open the modal dialog
    const dialogRef = this.dialog.open(TransactionDetailsModalComponent, {
      width: '90%',
      maxHeight: '90vh',
      panelClass: 'transaction-details-modal-container',
      data: {
        detailsID: detailsID,
        totalAmount: totalAmount,
        totalVat: totalVat,
        fromDate: this.fromDate,
        toDate: this.toDate,
        searchCriteria: this.searchCriteria,
      },
    });
  }

  readonly fetchCompanyOptions$ = (pageReq: PaginationRequest) => {
    return this._pnlReportService.getCompanyOptions(pageReq);
  };

  // Method to get transaction details data

  generateReport(type: string, withRounding = false) {
    let _filter: { [key: string]: string } = {};
    if (!!this.fromDate) _filter['fromDate'] = this.fromDate;
    if (!!this.toDate) _filter['toDate'] = this.toDate;
    if (!!this.searchCriteria) _filter['searchCriteria'] = this.searchCriteria;
    if (withRounding) _filter['withRounding'] = `${withRounding}`;
    if (type == 'HTML') {
      this._pnlReportService
        .getData(this.company, type, _filter)
        .subscribe((response: any) => {
          this.html = this.sanitized.bypassSecurityTrustHtml(response);
          this.generatedData = true;
          this._cdr.markForCheck();
        });
    }
    if (type == 'PDF') {
      this._pnlReportService
        .getData(this.company, type, _filter)
        .subscribe((response: any) => {
          this.previewFile(response);
        });
    }
    if (type == 'EXCEL') {
      this.notificationService.notifyInfo('Downloading the file...');
      this._mediaService.downloadFile(
        [API.getReportData, this.company, type].join('/'),
        'EXCEL',
        { method: 'GET', params: _filter }
      );
    }
  }

  previewFile(data: any) {
    let blob = new Blob([data], { type: data.type });
    const blobUrl = URL.createObjectURL(blob);
    let file_type = data.type.split('/')[1];
    this.dialog.open(CCPreviewAttachmentComponent, {
      width: '100%',
      data: {
        url: blobUrl,
        blob: blob,
        type: file_type,
      },
    });
  }
}
