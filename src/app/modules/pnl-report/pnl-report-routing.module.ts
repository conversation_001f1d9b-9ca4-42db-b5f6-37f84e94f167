import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LoginComponent } from '../core/components/login/login.component';
import { CCRoutes } from '@maids/cc-lib/common';
import { PnlReportComponent } from './components/pnl-report/pnl-report.component';

const routes: CCRoutes = [
  {
    path: '',
    component: PnlReportComponent,
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class PnlReportRoutingModule {}
