import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { PnlReportRoutingModule } from './pnl-report-routing.module';
import { PnlReportComponent, TransactionDetailsModalComponent } from './components/pnl-report/pnl-report.component';
import {CCSelectInputModule} from "@maids/cc-lib/select-input";
import {FormsModule} from "@angular/forms";
import {CCDatepickerModule} from "@maids/cc-lib/date";
import {CCRadioButtonModule} from "@maids/cc-lib/radio-button";
import {CCButtonModule} from "@maids/cc-lib/button";
import { MatDialogModule } from '@angular/material/dialog';
import { CCDatagridModule } from '@maids/cc-lib/datagrid';

@NgModule({
  declarations: [
    PnlReportComponent,
    TransactionDetailsModalComponent
  ],
  imports: [
    CommonModule,
    PnlReportRoutingModule,
    CCSelectInputModule,
    FormsModule,
    CCDatepickerModule,
    CCRadioButtonModule,
    CCButtonModule,
    MatDialogModule,
    CCDatagridModule
  ]
})
export class PnlReportModule { }
