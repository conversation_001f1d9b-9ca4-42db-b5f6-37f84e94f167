import {
  HttpClient,
  HttpContext,
  HttpHeaders,
  HttpParams,
} from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import {
  PageableResponseModel,
  PaginatedEntity,
  PaginationRequest,
  SearchModel,
} from '@maids/cc-lib/common';
import { API } from 'src/environments/api';
import { Store } from '@ngrx/store';
import {
  CCBackendEndpoint,
  REQ_SHOW_LOADING_ICON,
} from '@maids/cc-erp-services';
import { SelectOption } from '@maids/cc-lib/select-input';

@Injectable({
  providedIn: 'root',
})
export class PnlReportService {
  constructor(
    private _http: HttpClient,
    @Inject(CCBackendEndpoint) private _api: CCBackendEndpoint,
    private _store: Store
  ) {}
  initialSearch: SearchModel<any> = {
    params: {
      page: 0,
      size: 20,
      sort: 'amount,DESC',
    },
  };
  searchSubject = new BehaviorSubject<SearchModel<any>>(this.initialSearch);
  getCompanyOptions(pageReq: PaginationRequest) {
    const paramsObject = {
      isactive: true,
      sort: 'name,ASC',
      search: pageReq.searchString!,
    };
    const params = new HttpParams({ fromObject: paramsObject });
    const context = new HttpContext().set(REQ_SHOW_LOADING_ICON, false);
    return this._http
      .get<any>(`${this._api}/${API.getCompanyOptions}`, { params, context })
      .pipe(
        map((response) =>
          response.map(
            (data: any) => <SelectOption>{ id: data.id, text: data.name }
          )
        )
      );
  }

  getData(
    company: string | number,
    type: string,
    filter: { [key: string]: string }
  ) {
    console.log(filter);
    let paramsObject: {} = {
      ...filter,
    };
    if (filter['withRounding'])
      paramsObject = {
        ...paramsObject,
        withRounding: filter['withRounding'],
      };
    console.log(paramsObject);
    const params = new HttpParams({ fromObject: paramsObject });
    const context = new HttpContext().set(REQ_SHOW_LOADING_ICON, true);
    console.log(params);
    if (type == 'HTML')
      return this._http.get<any>(
        [this._api, API.getReportData, company, type].join('/'),
        {
          params: params,
          context: context,
        }
      );
    else {
      return this._http.get(
        [this._api, API.getReportData, company, type].join('/'),
        {
          params: params,
          context: new HttpContext().set(REQ_SHOW_LOADING_ICON, true),
          responseType: 'blob',
        }
      );
    }
  }
  getTransactionDetails(detailsID: string, params: any) {
    return this._http.get<any>(
      [this._api, API.getTransactionDetails, detailsID].join('/'),
      { params: { ...params, ...this.searchSubject.value.params } }
    );
  }
  exportTransactionsBehindRow(detailsID: string, params: any) {
    return this._http.get(
      [this._api, API.exporttransactionsbehindrow, detailsID].join('/'),
      { params }
    );
  }
}
