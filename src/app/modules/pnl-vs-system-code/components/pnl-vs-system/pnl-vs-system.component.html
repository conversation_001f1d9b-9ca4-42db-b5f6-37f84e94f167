<div class="container mx-2">
  <div class="d-flex align-items-center justify-content-between mb-4">
    <div class="col-md-auto d-flex align-items-center">
      <div class="col-md-auto mt-2">
        <cc-checkbox [(ngModel)]="indetail">In Detail</cc-checkbox>
      </div>
      <div class="col-md-auto">
        <button cc-raised-button color="primary" (click)="generateReport('HTML')">
          Generate Report
        </button>
      </div>
    </div>
    <div class="col-md-auto row align-items-center" *ngIf="generatedReport">
      <div class="col-md-auto">
        <span>Export</span>
      </div>
      <div class="col-md-auto">
        <button cc-raised-button color="accent" (click)="generateReport('PDF')">
          PDF
        </button>
      </div>
    </div>
  </div>
</div>

<div *ngIf="generatedReport">
  <div [innerHTML]="html"></div>
</div>
