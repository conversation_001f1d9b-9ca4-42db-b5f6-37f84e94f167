import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { PnlreportStructureService } from '../../services/pnlreport-structure.service';
import { Observable } from 'rxjs';
import { PaginationRequest } from '@maids/cc-lib/common';
import { CCValidatorFn } from '@maids/cc-lib/validation';
import { CCNotificationService } from '@maids/cc-lib/services';

@Component({
  selector: 'app-variable',
  templateUrl: './variable.component.html',
  styleUrls: ['./variable.component.scss'],
})
export class VariableComponent implements OnInit {
  @Input() editMode: boolean = false;
  @Input() bucketType: string = '';
  @Input() currentNode: any;
  @Input() bucketUsage: any[] = [];
  @Input() buckets: any[] = [];
  @Output() nodeData = new EventEmitter<any>();
  nodeBuckets: any[] = [];
  nodeBucketsWeights: any[] = [];
  nodeBucketsData: any[] = [];
  readonly bucketOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.pnlreportStructureService.getPlVariableBuckets(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  constructor(
    private pnlreportStructureService: PnlreportStructureService,
    public readonly notifications: CCNotificationService
  ) {}

  ngOnInit(): void {
    this.nodeBucketsData = this.buckets;
    this.nodeBuckets = this.nodeBucketsData.map((item: any) => {
      this.nodeBucketsWeights.push(item.wieght);
      return {
        text: item.expense?.label || item.revenue?.label,
        id: item.expense?.id || item.revenue?.id,
        type: item.expense ? 'Expense' : 'Revenue',
      };
    });
  }
  addNewBucket() {
    this.nodeBuckets.push({
      text: '',
      id: '',
      bucketType: '',
      wieght: 0,
    });
  }
  weightValidator: CCValidatorFn = (control: any) => {
    if (control.value > 1 || control.value < -1) {
      return {
        error: 'Weight must be between -1 and 1',
      };
    }
    return null;
  };
  save() {
    if (this.nodeBucketsWeights.some((wieght) => wieght > 1 || wieght < -1)) {
      this.notifications.notifyError('Weight must be between -1 and 1');
      return;
    }
    this.currentNode.addType = 'sub';
    this.nodeData.emit({ ...this.currentNode, buckets: this.nodeBuckets });
  }
}
