import { Component, OnInit } from '@angular/core';
import { PurchaseOrderHistoryService } from '../../services/purchase-order-history.service';
import { PaginationRequest } from '@maids/cc-lib/common';
import { Observable } from 'rxjs';
import { PageEvent } from '@angular/material/paginator';
import { FormBuilder } from '@angular/forms';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { CCDialog } from '@maids/cc-lib/dialog';
import { ItemsListComponent } from '../items-list/items-list.component';
@Component({
  selector: 'app-purchase-order-history',
  templateUrl: './purchase-order-history.component.html',
  styleUrls: ['./purchase-order-history.component.scss'],
})
export class PurchaseOrderHistoryComponent implements OnInit {
  formGroup = this.fb.group({
    supplier: [''],
    status: [''],
    category: [''],
    date: [''],
  });
  list: any;
  pendingList: any;
  readonly suppliersOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.purchaseOrderHistoryService.getSupplierList(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly categoryOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.purchaseOrderHistoryService.getCategoryOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  statusOptions: any[] = [
    { id: 'PENDING_PURCHASING', text: 'Pending Purchasing' },
    { id: 'PENDING_PAYMENT', text: 'Pending Payment' },
    { id: 'RECEIVED', text: 'Received' },
  ];
  constructor(
    private purchaseOrderHistoryService: PurchaseOrderHistoryService,
    private fb: FormBuilder,
    private ccDialog: CCDialog
  ) {}
  ngOnInit(): void {
    this.getPurchaseOrder();
    this.getPendingPurchaseRequestList();
  }
  getPurchaseOrder(page: number = 0, size: number = 20) {
    this.purchaseOrderHistoryService
      .getPurchaseOrder(page, size)
      .subscribe((res: any) => {
        this.list = res;
      });
  }
  getNextPage(event: PageEvent) {
    this.getPurchaseOrder(event.pageIndex, event.pageSize);
  }
  getPendingPurchaseRequestList() {
    this.purchaseOrderHistoryService
      .getPendingPurchaseRequestList()
      .subscribe((res: any) => {
        this.pendingList = res;
      });
  }
  searchData(page: number = 0, size: number = 20) {
    let params: any = {};
    if (this.formGroup.controls['supplier'].value) {
      params.supplierId = this.formGroup.controls['supplier'].value;
    }
    if (this.formGroup.controls['status'].value) {
      params.status = this.formGroup.controls['status'].value;
    }
    if (this.formGroup.controls['category'].value) {
      params.categoryId = this.formGroup.controls['category'].value;
    }
    if (this.formGroup.controls['date'].value) {
      params.date = this.formGroup.controls['date'].value;
    }
    params.page = page;
    params.size = size;
    this.purchaseOrderHistoryService
      .getPurshaseOrderPage(params)
      .subscribe((res: any) => {
        this.list = res;
      });
  }
  getNextPageData(event:PageEvent){
    this.searchData(event.pageIndex,event.pageSize)
  }
  gridColsList: CCGridColumn[] = [
    { field: 'id', header: 'ID' },
    { field: 'supplier.label', header: 'Supplier' },
    { field: 'purchasingToDo.category', header: 'Category' },
    { field: 'purchasingToDo.orderCycle', header: 'Cycle' },
    { field: 'creationDate', header: 'Date' },
    { field: 'status', header: 'Status' },
    { field: 'items', header: 'Items' },
  ];
  gridColsPendingList: CCGridColumn[] = [
    { field: 'category', header: 'Category' },
    { field: 'cycle', header: 'Cycle' },
    { field: 'date', header: 'Date' },
    {
      field: 'itemsNumber',
      header: 'Items',
      formatter(rowData, colDef) {
        return `${rowData.itemsNumber} Items pending`;
      },
    },
  ];
  getItems(id: any) {
    this.purchaseOrderHistoryService
      .getItemsOfOrder(id)
      .subscribe((res: any) => {
        this.ccDialog.originalOpen(ItemsListComponent, { data: res });
      });
  }
}
