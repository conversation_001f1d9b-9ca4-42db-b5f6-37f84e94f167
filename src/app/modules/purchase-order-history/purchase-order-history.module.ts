import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { PurchaseOrderHistoryRoutingModule } from './purchase-order-history-routing.module';
import { PurchaseOrderHistoryComponent } from './components/purchase-order-history/purchase-order-history.component';
import { CCDatagridModule } from '@maids/cc-lib/datagrid';
import { CCButtonModule } from '@maids/cc-lib/button';
import { CCInputModule } from '@maids/cc-lib/input';
import { CCSelectInputModule } from '@maids/cc-lib/select-input';
import { CCDialogModule } from '@maids/cc-lib/dialog';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CCDatepickerModule } from '@maids/cc-lib/date';
import { CCAccordionModule } from '@maids/cc-lib/accordion';
import { ItemsListComponent } from './components/items-list/items-list.component';
import { CCIconModule } from '@maids/cc-lib/icon';

@NgModule({
  declarations: [PurchaseOrderHistoryComponent, ItemsListComponent],
  imports: [
    CommonModule,
    CCDatagridModule,
    CCButtonModule,
    CCAccordionModule,
    CCInputModule,
    CCSelectInputModule,
    CCIconModule,
    CCDialogModule,
    ReactiveFormsModule,
    CCDatepickerModule,
    FormsModule,
    PurchaseOrderHistoryRoutingModule,
  ],
})
export class PurchaseOrderHistoryModule {}
