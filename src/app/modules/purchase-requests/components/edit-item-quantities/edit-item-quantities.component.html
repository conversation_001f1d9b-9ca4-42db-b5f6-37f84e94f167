<cc-dialog-header>
  <h1 cc-dialog-title>Edit item quantities</h1>
  <a
    role="button"
    type="button"
    cc-icon-button
    cc-dialog-close-button
    cc-dialog-close
  ></a>
</cc-dialog-header>
<cc-dialog-content>
  <div class="row justify-content-center">
    <div class="col-8 my-4">
      <span class="cc-secondary">{{ data.element.name }}</span>
    </div>
    <div class="col-8">
      <cc-amount-input
        [(ngModel)]="data.element.updatedConsumptionRate"
        label="Consumption Rate"
        [required]="true"
        symbol=""
      ></cc-amount-input>
    </div>
  </div>
</cc-dialog-content>
<cc-dialog-actions>
  <button cc-raised-button cc-dialog-close>Cancel</button>
  <button
    cc-raised-button
    color="accent"
    (click)="update()"
    [disabled]="!data.element.updatedConsumptionRate"
  >
    Update
  </button>
</cc-dialog-actions>
