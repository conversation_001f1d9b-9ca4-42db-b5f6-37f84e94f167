import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { CCDialogRef } from '@maids/cc-lib/dialog';
import { PurchaseRequestsService } from '../../services/purchase-requests.service';
import { CCNotificationService } from '@maids/cc-lib/services';
@Component({
  selector: 'app-edit-item-quantities',
  templateUrl: './edit-item-quantities.component.html',
  styleUrls: ['./edit-item-quantities.component.scss'],
})
export class EditItemQuantitiesComponent implements OnInit {
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private ccdialogRef: CCDialogRef<EditItemQuantitiesComponent>,
    private purchaseRequestsService: PurchaseRequestsService,
    public readonly notifications: CCNotificationService
  ) {}

  ngOnInit(): void {
    console.log(this.data);
    
  }
  update() {
    let payload: any;
    payload = {
      consumptionRate: this.data.element.updatedConsumptionRate,
    };
    this.purchaseRequestsService
      .updateConsumptionRate(this.data.element.id, payload)
      .subscribe((res: any) => {
        this.notifications.notifySuccess('Updated successfully!');
        this.ccdialogRef.close(true);
      });
  }
}
