import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { PurchasingManagerService } from '../../services/purchasing-manager.service';
import { CCDialogRef } from '@maids/cc-lib/dialog';
import { CCNotificationService } from '@maids/cc-lib/services';
import { CCFileUploaderConfig } from '@maids/cc-lib/file-uploader';
@Component({
  selector: 'app-purchase-in-one-bill-modal',
  templateUrl: './purchase-in-one-bill-modal.component.html',
  styleUrls: ['./purchase-in-one-bill-modal.component.scss'],
})
export class PurchaseInOneBillModalComponent implements OnInit {
  formGroup = this.fb.group({
    deliveryService: [''],
    taxable: [''],
    paymentMethods: [''],
    totalBillAmount: [''],
    totalItemsCost: [''],
    vatAmount: [''],
    receipt: [null],
    vatInvoice: [null],
  });
  paymentMethodsOptions: any[] = [];
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private fb: FormBuilder,
    private purchasingManagerService: PurchasingManagerService,
    private ccDialogRef: CCDialogRef<PurchaseInOneBillModalComponent>,
    public readonly notifications: CCNotificationService
  ) {}
  config: CCFileUploaderConfig = {
    maxFilesize: 10,
  };
  ngOnInit(): void {
    this.formGroup.controls['totalItemsCost'].valueChanges.subscribe((val) => {
      if (
        this.formGroup.controls['totalItemsCost'].value >= 0 &&
        this.formGroup.controls['deliveryService'].value >= 0
      ) {
        this.formGroup.controls['totalBillAmount'].setValue(
          +this.formGroup.controls['totalItemsCost'].value +
            +this.formGroup.controls['deliveryService'].value
        );
      }
    });
    this.formGroup.controls['deliveryService'].valueChanges.subscribe((val) => {
      if (
        this.formGroup.controls['totalItemsCost'].value >= 0 &&
        this.formGroup.controls['deliveryService'].value >= 0
      ) {
        this.formGroup.controls['totalBillAmount'].setValue(
          +this.formGroup.controls['totalItemsCost'].value +
            +this.formGroup.controls['deliveryService'].value
        );
      }
    });
    this.paymentMethodsOptions = this.data.paymentMethods.map((item: any) => {
      return { text: item.label, id: item.value };
    });
    this.formGroup.patchValue({
      deliveryService: this.data.deliveryService,
      taxable:
        this.data.taxable !== '' || this.data.taxable !== null
          ? this.data.taxable
          : '',
      paymentMethods:
        this.data.paymentMethods.length == 1
          ? this.data.paymentMethods[0].value
          : null,
      totalBillAmount: this.data.totalBillAmount,
      totalItemsCost: this.data.totalItemsCost,
      vatAmount: this.data.vatAmount,
    });
  }
  submitPurchaseInOneBill() {
    let payload: any;
    payload = {
      deliveryService: this.formGroup.controls['deliveryService'].value,
      id: this.data.id,
      paymentMethods: [this.formGroup.controls['paymentMethods'].value],
      taxable: this.data.taxable,
      totalBillAmount: this.formGroup.controls['totalBillAmount'].value,
      totalItemsCost: this.formGroup.controls['totalItemsCost'].value,
      vatAmount: this.formGroup.controls['vatAmount'].value
        ? this.formGroup.controls['vatAmount'].value
        : '',
      attachments: [],
    };
    if (this.formGroup.controls['receipt'].value) {
      payload.attachments.push(this.formGroup.controls['receipt'].value[0]);
    }
    if (this.formGroup.controls['vatInvoice'].value) {
      payload.attachments.push(this.formGroup.controls['vatInvoice'].value[0]);
    }
    this.purchasingManagerService
      .purchaseInOneBill(this.data.id, payload)
      .subscribe({
        next: (res: any) => {
          this.ccDialogRef.close(true);
        },
        error: (err: any) => {
          this.notifications.notifyError(err.error.message);
        },
      });
  }
}
