<div class="acc-7824">
  <cc-datagrid
    class="my-2"
    [data]="records ?? []"
    [columns]="gridCols"
    [pageOnFront]="false"
    [showPaginator]="false"
    [cellTemplate]="{
      todoName: todoName,
      todoType: todoType
    }"
  ></cc-datagrid>
  <ng-template #todoName let-row>
    <a class="cc-secondary" (click)="goTo(row.id, row.todoName)">
      <div *ngIf="row.categoryName && row.orderCycle; else elseBlock">
        {{
          row.todoName.includes("get better supplier")
            ? row.todoName.replace(
                "get better supplier",
                "Recheck for better prices"
              )
            : (row.todoName.replace("supplier", "prices") | titlecase)
        }}
        for {{ row.categoryName }} for {{ row.orderCycleName }}
        {{ row.orderCycle }} order
      </div>
      <ng-template #elseBlock>
        {{
          row.todoName.includes("get better supplier")
            ? row.todoName.replace(
                "get better supplier",
                "Recheck for better prices"
              )
            : (row.todoName.replace("supplier", "prices") | titlecase)
        }}
      </ng-template>
    </a>
  </ng-template>
  <ng-template #todoType let-row>
    {{
      row.todoName.includes("get better supplier")
        ? row.todoName.replace(
            "get better supplier",
            "Recheck for better prices"
          )
        : (row.todoName.replace("supplier", "prices") | titlecase)
    }}
  </ng-template>

</div>
