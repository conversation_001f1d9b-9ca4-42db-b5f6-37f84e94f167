<div class="acc-8902 my-2">
  <div class="row" *ngIf="!confirmed">
    <form class="w-100" [formGroup]="formGroup">
      <div class="col-8">
        <div class="row col-12">
          <div class="col-8">
            <cc-input
              label="Beneficiary"
              formControlName="beneficiaryName"
              [disabled]="true"
            ></cc-input>
          </div>
          <div
            class="col-4"
            *ngIf="
              payment &&
              payment.beneficiaryType &&
              payment.beneficiaryType.value === 'SUPPLIER'
            "
          >
            <a class="cc-secondary" (click)="showSupplierDetails()">
              Supplier Details
            </a>
          </div>
        </div>
        <div class="col-12">
          <cc-input
            label="Payment Method"
            formControlName="paymentMethodValue"
            [disabled]="true"
          ></cc-input>
        </div>
        <div class="col-12 row">
          <div class="col-8">
            <cc-amount-input
              type="number"
              label="Amount"
              [required]="true"
              formControlName="amount"
              [symbol]="''"
            ></cc-amount-input>
          </div>
          <div class="col-4 px-0">
            <cc-select
              class="w-100"
              [data]="currencyOptions"
              label="Currency"
              formControlName="currency"
            ></cc-select>
          </div>
        </div>
        <div class="col-12">
          <cc-input
            type="number"
            label="Loan Amount"
            formControlName="loanAmount"
          ></cc-input>
        </div>
        <div class="col-12" *ngIf="payment && !payment.replenishmentTodo">
          <cc-select
            label="Expense"
            [lazyPageFetcher]="expenseOptions"
            formControlName="expenseToPost"
            [emitFullSelectOption]="true"
          ></cc-select>
        </div>
        <div
          class="col-12"
          *ngIf="
            formGroup.controls['paymentMethodValue'].value &&
            formGroup.controls['paymentMethodValue'].value !== 'SALARY'
          "
        >
          <cc-select
            label="From Bucket"
            [lazyPageFetcher]="BucketOptions"
            formControlName="fromBucket"
            [emitFullSelectOption]="true"
            [required]="true"
          ></cc-select>
        </div>
        <div class="col-12" *ngIf="payment && payment.replenishmentTodo">
          <cc-select
            label="To Bucket"
            [lazyPageFetcher]="BucketOptions"
            formControlName="toBucket"
            [emitFullSelectOption]="true"
            [required]="true"
          ></cc-select>
        </div>
        <div
          class="col-12"
          *ngIf="
            (formGroup.controls['paymentMethodValue'].value && formGroup.controls['paymentMethodValue'].value == 'SALARY') &&
            (payment && payment.requiresInvoice)
          "
        >
          <cc-radio-group
            [required]=" (formGroup.controls['paymentMethodValue'].value &&
            formGroup.controls['paymentMethodValue'].value == 'SALARY') &&
            (payment && payment.requiresInvoice)"
            [disabled]="
              payment && (payment.taxable === true || payment.taxable === false)
            "
            formControlName="taxable"
            class="row" style="margin-bottom: 20px;"
          >
            <div class="col-6">Contains VAT</div>
            <div class="col-6 row">
              <div class="col-auto">
                <cc-radio-button [value]="true">Yes</cc-radio-button>
              </div>
              <div class="col-auto">
                <cc-radio-button [value]="false">No</cc-radio-button>
              </div>
            </div>
          </cc-radio-group>
        </div>
        <div
          class="col-12"
          *ngIf="
            formGroup.controls['taxable'].value == true &&
            formGroup.controls['paymentMethodValue'].value !== 'SALARY'
          "
        >
          <cc-input
            type="number"
            [required]="formGroup.controls['taxable'].value == true &&
            formGroup.controls['paymentMethodValue'].value !== 'SALARY'"
            label="VAT Amount (AED)"
            formControlName="vatAmount"
          ></cc-input>
        </div>
        <div class="col-12" *ngIf="showDescription">
          <cc-textarea
            label="Transaction Description"
            formControlName="transaction"
            [required]="true"
          ></cc-textarea>
        </div>
        <div class="col-12">
          <cc-input
            formControlName="relatedTo"
            label="Related To"
            [disabled]="true"
          ></cc-input>
        </div>
      </div>
      <div class="col-12 row mx-1">
        <div class="col-12 bg-secondary py-2 my-1">
          <h5 class="text-white"><b>Attachments</b></h5>
        </div>
        <div class="col-12 row">
          <div class="col-3 text-center" *ngIf="receipt && receipt.url">
            <span><b>Receipt</b></span>
            <p>
              <a
                class="cc-secondary"
                style="cursor: pointer;"
                (click)="downloadFile(receipt.file.uuid, true)"
                >{{ receipt?.file?.name }}</a
              >
            </p>
          </div>
          <div class="col-3 text-center" *ngIf="!receipt || !receipt.url">
            <span><b>Receipt</b></span>
            <p>No receipt</p>
          </div>
          <div class="col-3 row">
            <div class="col-12 text-center">
              <span><b>Attachment</b></span>
            </div>
            <div class="col-12">
              <cc-datagrid
                class="w-100"
                [data]="attachments"
                [columns]="attachmentsColumns"
                [showPaginator]="false"
                [cellTemplate]="{ name: name, action: action }"
                [noResultTemplate]="noResultTemplate"
              ></cc-datagrid>
              <ng-template #name let-row>
                <a class="cc-secondary" style="cursor: pointer;" (click)="downloadFile(row.uuid,true)">{{
                  row?.name
                }}</a>
              </ng-template>
              <ng-template #action let-row>
                <a
                  class="cc-secondary"
                  style="cursor: pointer;"
                  (click)="selectAsInvoice(row)"
                  *ngIf="payment && payment.requiresInvoice"
                  >Select as Invoice</a
                >
              </ng-template>
              <ng-template #noResultTemplate>
                <p>No attachments</p>
              </ng-template>
            </div>
          </div>
          <div
            class="col-3 text-center"
            *ngIf="invoiceControl.value && payment && payment.requiresInvoice"
          >
            <span><b>Invoice</b></span>
            <p>
              <a
                class="cc-secondary"
                style="cursor: pointer;"
                (click)="downloadFile(invoiceControl.value[0].uuid,true)"
                >{{ invoiceControl?.value[0]?.name }}</a
              >
            </p>
            <div *ngIf="showEditInvoice">
              <cc-file-uploader
              [dropzoneConfig]="{
                maxFiles:1,
                maxFilesize: 10
                }"
                [formControl]="invoiceControl"
                tag="EXPENSE_PAYMENT_INVOICE"
              ></cc-file-uploader>
            </div>
            <span>
              <a class="cc-secondary" style="cursor: pointer;" (click)="editInvoice()">{{
                showEditInvoice ? "Close" : "Edit"
              }}</a>
            </span>
            |
            <span>
              <a
                class="cc-secondary"
                style="cursor: pointer;"
                (click)="
                  deleteInvoice(invoiceControl.value[0].id, 'invoice', null)
                "
                >Delete</a
              > </span
            ><br />
            <a
              class="cc-secondary"
              style="cursor: pointer;"
              *ngIf="
                formGroup.controls['taxable'].value == true &&
                formGroup.controls['paymentMethodValue'].value !== 'SALARY' &&
                payment &&
                payment.requiresInvoice
              "
              (click)="selectAsVatInvoice()"
              >Select as VAT Invoice</a
            >
          </div>
          <div
            class="col-3 text-center"
            *ngIf="!invoiceControl.value && payment && payment.requiresInvoice"
          >
            <span><b>Invoice</b></span>
            <p *ngIf="!showEditInvoice">No invoice</p>
            <div [hidden]="!showEditInvoice">
              <cc-file-uploader
              [dropzoneConfig]="{
                maxFiles:1,
                maxFilesize: 10
                }"
                [formControl]="invoiceControl"
                tag="EXPENSE_PAYMENT_INVOICE"
              ></cc-file-uploader>
            </div>
            <span>
              <a class="cc-secondary" 
              style="cursor: pointer;"
               (click)="editInvoice()">{{
                showEditInvoice ? "Close" : "Add"
              }}</a>
            </span>
          </div>
          <div
            class="col-3 text-center"
            *ngIf="
              formGroup.controls['taxable'].value == true &&
              formGroup.controls['paymentMethodValue'].value !== 'SALARY' &&
              payment &&
              payment.requiresInvoice
            "
          >
            <span><b>VAT Invoice</b></span>
            <p *ngIf="vatInvoiceControl.value && !showEditVatInvoice">
              <a
                class="cc-secondary"
                style="cursor: pointer;"
                (click)="downloadFile(vatInvoiceControl.value[0].uuid)"
                >{{ vatInvoiceControl?.value[0]?.name }}</a
              >
            </p>
            <div [hidden]="!showEditVatInvoice">
              <cc-file-uploader
              [dropzoneConfig]="{
                maxFiles:1,
                maxFilesize: 10
                }"
                [formControl]="vatInvoiceControl"
                tag="EXPENSE_PAYMENT_VAT_INVOICE"
              ></cc-file-uploader>
            </div>
            <div *ngIf="vatInvoiceControl.value">
              <span>
                <a class="cc-secondary" 
                style="cursor: pointer;"
                 (click)="editVatInvoice()">{{
                  showEditVatInvoice ? "Close" : "Edit"
                }}</a> </span
              >|
              <span>
                <a
                  class="cc-secondary"
                  style="cursor: pointer;"
                  (click)="
                    deleteInvoice(
                      vatInvoiceControl.value[0].id,
                      'vatInvoice',
                      null
                    )
                  "
                  >Delete</a
                >
              </span>
            </div>
            <div *ngIf="!vatInvoiceControl.value">
              <p *ngIf="!showEditVatInvoice">No VAT invoice</p>
              <span>
                <a class="cc-secondary" style="cursor: pointer;" 
                  (click)="editVatInvoice()">{{
                  showEditVatInvoice ? "Close" : "Add"
                }}</a>
              </span>
            </div>
          </div>
        </div>
      </div>
    </form>
    <div class="col-12 row justify-content-end my-4">
      <div class="col-auto">
        <button cc-raised-button (click)="doNothing()">Do Nothing</button>
      </div>
      <div class="col-auto">
        <button cc-raised-button (click)="saveAndConfirm(true)">
          Confirm Payment and Go to Next
        </button>
      </div>
      <div class="col-auto">
        <button cc-raised-button (click)="saveAndConfirm()">
          Confirm Payment and Exit
        </button>
      </div>
      <div class="col-auto">
        <button cc-raised-button (click)="saveAndNotConfirm()">
          Save Only Do Not Confirm Yet
        </button>
      </div>
    </div>
  </div>
</div>
