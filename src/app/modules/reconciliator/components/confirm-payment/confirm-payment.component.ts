import { Component, OnInit } from '@angular/core';
import { ReconciliatorService } from '../../services/reconciliator.service';
import { FormBuilder, FormControl, Validators } from '@angular/forms';
import { PaginationRequest } from '@maids/cc-lib/common';
import { Observable } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { API } from 'src/environments/api';
import { CCDialog } from '@maids/cc-lib/dialog';
import { SupplierDetailsComponent } from '../supplier-details/supplier-details.component';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { CCPreviewAttachmentComponent } from '@maids/cc-lib/preview-attachment';
import { BreadcrumbLink } from '@maids/cc-lib/layout';

@Component({
  selector: 'app-confirm-payment',
  templateUrl: './confirm-payment.component.html',
  styleUrls: ['./confirm-payment.component.scss'],
})
export class ConfirmPaymentComponent implements OnInit {
  formGroup = this.fb.group({
    fromBucket: [null],
    toBucket: [null],
    expenseToPost: [null],
    currency: [null],
    taxable: [null],
    vatAmount: [null],
    amount: [null],
    loanAmount: [null],
    transaction: [null],
    relatedTo: [''],
    paymentMethodValue: [''],
    beneficiaryName: [''],
  });
  invoiceControl = new FormControl(null);
  vatInvoiceControl = new FormControl(null);
  invoice: { file?: any; url?: string } = {};
  vatInvoice: { file?: any; url?: string } = {};
  readonly BucketOptions = (pageReq: PaginationRequest): Observable<any> =>
    this._reconciliatorService.RgetBucketFromOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  readonly expenseOptions = (pageReq: PaginationRequest): Observable<any> =>
    this._reconciliatorService.RgetexpenseOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  currencyOptions: any[] = [];
  id: any;
  confirmed: boolean = false;
  attachments: any[] = [];
  payment: any;
  receipt: any;
  attachment: { file?: any; url?: string } = {};
  showDescription: boolean = false;
  taskName: string = '';
  showEditInvoice: boolean = false;
  showEditVatInvoice: boolean = false;
  ignoreNextInvoiceChange = false;
  ignoreNextVatInvoiceChange = false;
  attachmentsColumns: CCGridColumn[] = [
    {
      header: 'File',
      field: 'name',
    },
    {
      header: 'Action',
      field: 'action',
    },
  ];
  breadcrumbs: BreadcrumbLink[] = [
    { label: 'Reconciliator', url: '/accounting/v2/reconciliator' },
    { label: 'Reconciliator Details', disabled: true },
  ];
  constructor(
    private _reconciliatorService: ReconciliatorService,
    private fb: FormBuilder,
    private route: ActivatedRoute,
    public readonly notifications: CCNotificationService,
    private ccDialog: CCDialog,
    private router: Router,
    private mediaService: MediaService
  ) {}

  ngOnInit(): void {
    this.getCurrencyOptions();
    this.id = this.route.snapshot.params['id'];
    this.getExpensePayment();

    this.invoiceControl.valueChanges.subscribe((val: any) => {
      if (this.ignoreNextInvoiceChange) {
        this.ignoreNextInvoiceChange = false;
        return;
      }
      if (this.showEditInvoice && val && val[0]?.id) {
        if (this.invoice.file?.id) {
          this.deleteInvoice(this.invoice.file.id, 'invoice', val[0].id);
        } else {
          this.addInvoice(val[0].id);
        }
        setTimeout(() => {
          this.invoice = {
            file: val[0],
          };
          if (this.invoice.file) {
            this.invoice.url = `public/download/${val[0].uuid}`;
          }
          this.showEditInvoice = false;
        });
      }
    });

    this.vatInvoiceControl.valueChanges.subscribe((val: any) => {
      if (this.ignoreNextVatInvoiceChange) {
        this.ignoreNextVatInvoiceChange = false;
        return;
      }
      if (this.showEditVatInvoice && val && val[0]?.id) {
        if (this.vatInvoice.file?.id) {
          this.deleteInvoice(this.vatInvoice.file.id, 'vatInvoice', val[0].id);
        } else {
          this.addInvoice(val[0].id);
        }
        setTimeout(() => {
          this.vatInvoice = {
            file: val[0],
          };
          if (this.vatInvoice.file) {
            this.vatInvoice.url = `public/download/${val[0].uuid}`;
          }
          this.showEditVatInvoice = false;
        });
      }
    });
  }
  getCurrencyOptions() {
    this._reconciliatorService.RgetCurrencies().subscribe((res) => {
      this.currencyOptions = res;
    });
  }
  getExpensePayment() {
    this._reconciliatorService.RgetExpensePayment(this.id).subscribe((res) => {
      this.confirmed = res.confirmed;
      if (res.confirmed) {
        this.notifications.notifySuccess('Payment Already confirmed!');
        return;
      }
      this.payment = res;
      this.attachments = [...res.attachments].filter((attachment: any) => {
        return (
          attachment.tag !== 'EXPENSE_PAYMENT_INVOICE' &&
          attachment.tag !== 'EXPENSE_PAYMENT_VAT_INVOICE' &&
          attachment.tag !== 'RECEIPT_EXPENSE_PAYMENT' &&
          !attachment.tag.includes('SIGNATURE')
        );
      });
      this.receipt = {
        file: this.payment.attachments.find((attachment: any) => {
          return attachment.tag === 'RECEIPT_EXPENSE_PAYMENT';
        }),
      };
      if (this.receipt.file) {
        this.receipt.url = `public/download/${this.receipt.file.uuid}`;
      }

      this.attachment = {
        file: this.payment.attachments.find((attachment: any) => {
          return attachment.tag === 'EXPENSE_REQUEST_INVOICE';
        }),
      };
      if (this.attachment.file) {
        this.attachment.url = `public/download/${this.attachment.file.uuid}`;
      }
      if (
        this.payment.attachments.find((attachment: any) => {
          return attachment.tag === 'EXPENSE_PAYMENT_INVOICE';
        })
      ) {
        this.invoiceControl.setValue([
          this.payment.attachments.find((attachment: any) => {
            return attachment.tag === 'EXPENSE_PAYMENT_INVOICE';
          }),
        ]);
      }
      if (
        this.payment.attachments.find((attachment: any) => {
          return attachment.tag === 'EXPENSE_PAYMENT_VAT_INVOICE';
        })
      ) {
        this.vatInvoiceControl.setValue([
          this.payment.attachments.find((attachment: any) => {
            return attachment.tag === 'EXPENSE_PAYMENT_VAT_INVOICE';
          }),
        ]);
      }
      console.log(this.vatInvoiceControl.value);
      this.formGroup.patchValue({
        amount: this.payment.amount,
        loanAmount: this.payment.loanAmount,
        vatAmount: this.payment.vatAmount,
        transaction: this.payment.transaction.description,
        relatedTo:
          (this.payment.relatedToType
            ? this.payment.relatedToType + ' / '
            : '') + this.payment.relatedToName,
        beneficiaryName: res.beneficiaryName,
        paymentMethodValue: res.method.value,
      });

      this.showDescription = !!this.payment.transaction.description;

      if (!this.showDescription) {
        this.formGroup.patchValue({ transaction: null });
      }

      const taxableValue = this.payment.expenseToPost.hasOwnProperty('taxable')
        ? this.payment.expenseToPost.expense
        : this.payment.taxable;

      this.formGroup.patchValue({
        taxable: taxableValue !== '' ? taxableValue : '',
      });

      this.taskName = res.taskName;

      if (this.payment.expenseToPost) {
        this.formGroup.patchValue({
          expenseToPost: {
            id: this.payment.expenseToPost.id,
            text: this.payment.expenseToPost.label,
          },
        });
      }

      if (this.payment.fromBucket) {
        this.formGroup.patchValue({
          fromBucket: {
            id: this.payment.fromBucket.id,
            text: this.payment.fromBucket.label,
          },
        });
      }

      if (this.payment.replenishmentTodo && this.payment.toBucket) {
        this.formGroup.patchValue({
          toBucket: {
            id: this.payment.toBucket.id,
            text: this.payment.toBucket.label,
          },
        });
      }

      if (this.payment.currency) {
        this.formGroup.patchValue({
          currency: this.payment.currency.id,
        });
      }
    });
  }
  selectAsInvoice(attachment: any) {
    this._reconciliatorService
      .selectAsInvoice(this.payment.id, attachment.id)
      .subscribe({
        next: (res: any) => {
          this.notifications.notifySuccess(
            'Attachment selected as invoice successfully!'
          );
          this.invoice = Object.assign({}, attachment);
          this.invoiceControl.setValue(null);
          this.showEditInvoice = false;
          this.getExpensePayment();
        },
      });
  }
  selectAsVatInvoice() {
    this._reconciliatorService
      .selectAsVatInvoice(this.payment.id, this.invoiceControl.value[0].id)
      .subscribe({
        next: (res: any) => {
          this.notifications.notifySuccess(
            'Attachment selected as vat invoice successfully!'
          );
          this.vatInvoiceControl.setValue([this.invoiceControl.value[0]]);
          this.showEditVatInvoice = false;
          this.getExpensePayment();
        },
      });
  }
  addInvoice(id: any) {
    this._reconciliatorService
      .addInvoice(this.payment.id, id)
      .subscribe((res: any) => {
        this.showEditInvoice = false;
        this.showEditVatInvoice = false;
        this.invoice = {
          file: res,
        };
        if (res) {
          this.invoice.url = `public/download/${res.uuid}`;
        }
        this.vatInvoice = {
          file: res,
        };
        if (res) {
          this.vatInvoice.url = `public/download/${res.uuid}`;
        }
      });
  }
  deleteInvoice(id: any, type: string, newId: any) {
    this._reconciliatorService.deleteInvoice(id).subscribe((res: any) => {
      if (type == 'invoice') {
        this.invoiceControl.reset();
        this.showEditInvoice = false;
        this.invoice = {};
      } else {
        this.vatInvoiceControl.reset();
        this.showEditVatInvoice = false;
        this.vatInvoice = {};
      }
      if (newId) {
        this.addInvoice(newId);
      }
    });
  }
  editInvoice() {
    this.showEditInvoice = !this.showEditInvoice;
    if (this.showEditInvoice) {
      this.ignoreNextInvoiceChange = true;
    }
  }
  editVatInvoice() {
    this.showEditVatInvoice = !this.showEditVatInvoice;
    if (this.showEditVatInvoice) {
      this.ignoreNextVatInvoiceChange = true;
    }
  }
  showSupplierDetails() {
    this.ccDialog.originalOpen(SupplierDetailsComponent, {
      data: {
        supplierId: this.payment.beneficiaryId,
      },
    });
  }
  saveAndConfirm(next: boolean = false) {
    if(this.formGroup.invalid){
      this.formGroup.markAllAsTouched();
      this.formGroup.updateValueAndValidity();
      return;
    }
    let payload = {
      fromBucket: this.formGroup.value.fromBucket
        ? { id: this.formGroup.value.fromBucket.id }
        : '',
      expenseToPost: { id: this.formGroup.value.expenseToPost? this.formGroup.value.expenseToPost.id : '' },
      currency: this.formGroup.value.currency
        ? { id: this.formGroup.value.currency }
        : '',
      taxable: this.formGroup.value.taxable ? this.formGroup.value.taxable : '',
      toBucket: this.formGroup.value.toBucket
        ? { id: this.formGroup.value.toBucket.id }
        : '',
      vatAmount: this.formGroup.value.vatAmount
        ? this.formGroup.value.vatAmount
        : '',
      amount: this.formGroup.value.amount ? this.formGroup.value.amount : '',
      loanAmount: this.formGroup.value.loanAmount
        ? this.formGroup.value.loanAmount
        : '',
      transaction: this.formGroup.value.transaction
        ? { description: this.formGroup.value.transaction }
        : '',
      modifiedByReconciliator: true,
      confirmed: true,
    };
    if (
      this.payment.method &&
      this.payment.method.value == 'CASH' &&
      payload.taxable &&
      (!this.vatInvoiceControl.value || !this.vatInvoiceControl.value[0].id)
    ) {
      this.ccDialog.confirm(
        'Are you sure you want to dismiss this transaction',
        'VAT information is not entered yes, it will be posted to accounting as Missing VAT invoice',
        () => {
          this.submitConfirm(next, payload);
        }
      );
      return;
    }
     this.submitConfirm(next, payload);
  }
  submitConfirm(next: any, data: any) {
    this._reconciliatorService
      .completeExpensePayment(this.payment.id, this.taskName, data)
      .subscribe((res: any) => {
        if (next && res && res.id) {
          this.notifications.notifySuccess('Confirmed successfully!');
          this.router.navigateByUrl(
            '/accounting/v2/reconciliator/confirm-payment/' + res.id
          );
        } else {
          this.router.navigateByUrl('/accounting/v2/reconciliator');
        }
      });
  }
  doNothing() {
    this.router.navigateByUrl('/accounting/v2/reconciliator');
  }
  downloadFile(uuid: string, preview: boolean = false) {
    if (preview) {
      this.mediaService
        .getFile(`public/download/${uuid}`)
        .subscribe((file: any) => {
          this.previewFile(file);
        });
    } else {
      this.mediaService.downloadFile(`public/download/${uuid}`);
    }
  }
  previewFile(data: any) {
    let blob = new Blob([data], { type: data.type });
    const blobUrl = URL.createObjectURL(blob);
    let file_type = data.type.split('/')[1];
    this.ccDialog.originalOpen(CCPreviewAttachmentComponent, {
      width: '800px',
      data: {
        url: blobUrl,
        blob: blob,
        type: file_type,
        filename: 'attachment',
      },
      panelClass: 'attachment-preview',
      backdropClass: 'attachement-preview-backdrop',
      id: 'attachmentPreview',
    });
  }
  saveAndNotConfirm() {
    if(this.formGroup.invalid){
      this.formGroup.markAllAsTouched();
      this.formGroup.updateValueAndValidity();
      return;
    }
    let payload = {
      fromBucket: this.formGroup.value.fromBucket
        ? { id: this.formGroup.value.fromBucket.id }
        : '',
      expenseToPost: this.formGroup.value.expenseToPost? { id: this.formGroup.value.expenseToPost.id } : null,
      currency: this.formGroup.value.currency
        ? { id: this.formGroup.value.currency }
        : '',
      taxable: this.formGroup.value.taxable ? this.formGroup.value.taxable : '',
      toBucket: this.formGroup.value.toBucket
        ? { id: this.formGroup.value.toBucket.id }
        : '',
      vatAmount: this.formGroup.value.vatAmount
        ? this.formGroup.value.vatAmount
        : '',
      amount: this.formGroup.value.amount ? this.formGroup.value.amount : '',
      loanAmount: this.formGroup.value.loanAmount
        ? this.formGroup.value.loanAmount
        : '',
      transaction: this.formGroup.value.transaction
        ? { description: this.formGroup.value.transaction }
        : '',
      modifiedByReconciliator: true,
      confirmed: false,
    };
    this._reconciliatorService
      .saveExpensePaymentTask(this.payment.id, this.taskName, payload)
      .subscribe((res: any) => {
        this.notifications.notifySuccess('Saved successfully!');
        this.router.navigateByUrl('/accounting/v2/reconciliator');
      });
  }
}
