::ng-deep .mat-dialog-container {
  position: relative;
  overflow: visible;
}

.alert-background {
  position: relative;
  min-height: 200px;
  min-width: 200px;
  display: block;
}

.alert-background::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("./alert.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  opacity: 0.2;
  z-index: 0;
  pointer-events: none;
}

cc-dialog-header,
cc-dialog-content {
  position: relative;
  z-index: 1;
}
