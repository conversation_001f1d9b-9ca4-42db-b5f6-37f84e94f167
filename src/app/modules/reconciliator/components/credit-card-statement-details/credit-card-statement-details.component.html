<div class="acc-8902">
  <div class="row mx-0">
    <div class="col px-0">
      <div class="fw-500 pt-3">
        <div>
          Authorized transactions:
          <span class="fw-bold cc-secondary"
            >AED {{ getAmountNumberLabel(authorizedTransactions) }}</span
          >
        </div>
        <div>
          {{ matchedTransactions?.length ?? 0 }} Matched Card Deductions
        </div>
      </div>
    </div>
  </div>
  <div class="row mx-0">
    <cc-datagrid
      class="w-100"
      [data]="matchedTransactions ?? []"
      [columns]="gridColsMatchedTransactions"
      [showPaginator]="false"
      [noResultTemplate]="noResultTemplate"
      [stickyHeader]="true"
      [cellTemplate]="{ recordDescription: recordDescription }"
    >
      >
      <cc-grid-actions-list
        *ccActionData="
          let ctx of matchedTransactions?.content ?? [];
          row as row
        "
        [renderedActionsCount]="1"
        style="width: fit-content"
      >
        <ng-container *ngIf="!row.confirmed">
          <button
            *cc-action
            cc-raised-button
            color="primary"
            (click)="reviewToConfirm(row)"
          >
            Review to confirm
          </button>
        </ng-container>
      </cc-grid-actions-list>
    </cc-datagrid>
    <ng-template #noResultTemplate></ng-template>
    <ng-template #recordDescription let-row>
      <span class="desc-row">{{ row?.recordDescription }}</span>
    </ng-template>
  </div>
  <div class="row mx-0">
    <div class="col px-0">
      <div class="fw-500 pt-3">
        <div>
          {{ unMatchedTransactions?.length ?? 0 }} Unmatched Card Deductions
        </div>
      </div>
    </div>
  </div>
  <div class="row mx-0">
    <cc-datagrid
      class="w-100"
      [data]="unMatchedTransactions ?? []"
      [columns]="gridColsUnMatchedTransactions"
      [showPaginator]="false"
      [noResultTemplate]="noResultTemplate"
      [stickyHeader]="true"
      [cellTemplate]="{ recordDescription: recordDescription }"
    >
      <cc-grid-actions-list
        *ccActionData="
          let ctx of unMatchedTransactions?.content ?? [];
          row as row
        "
        [renderedActionsCount]="1"
        style="width: fit-content"
      >
        <ng-container *ngIf="!row.confirmed">
          <button
            *cc-action
            cc-raised-button
            color="primary"
            (click)="matchRow(row)"
          >
            Match
          </button>
        </ng-container>
        <button
          *cc-action
          cc-raised-button
          (click)="matchAlready(row)"
        >
          Matched Already
        </button>
      </cc-grid-actions-list> </cc-datagrid
    ><ng-template #noResultTemplate></ng-template>
    <ng-template #recordDescription let-row>
      <span class="desc-row">{{ row?.recordDescription }}</span>
    </ng-template>
  </div>
  <div *ngIf="match && expenseItem && search" #matchExpensePanel>
    <cc-accordion>
      <cc-panel [expanded]="true">
        <cc-panel-title> Match Expense </cc-panel-title>
        <cc-panel-body>
          <div class="row">
            <div class="col-12 row">
              <div class="col-2 bold" style="text-align: right">
                Description
              </div>
              <div class="col-6">{{ expenseItem?.recordDescription }}</div>
            </div>
            <div class="col-12 row">
              <div class="col-2 bold" style="text-align: right">Amount</div>
              <div class="col-6">AED {{ expenseItem?.recordAmount }}</div>
            </div>
            <div class="col-12 row">
              <div class="col-2 bold" style="text-align: right">Date</div>
              <div class="col-6">
                {{ expenseItem?.recordTransactionDate.split(" ")[0] }}
              </div>
            </div>
            <div class="col-12 row">
              <div class="col-2 bold" style="text-align: right">CD/DR</div>
              <div class="col-6">{{ expenseItem?.crdrAction }}</div>
            </div>
          </div>
          <div class="row mx-0">
            <div class="col-12 border pt-2 my-2">
              <h6><b>Unconfirmed Expenses</b></h6>
            </div>
            <div class="col-12 row align-items-center">
              <div class="col-md-auto">Search By</div>
              <div class="col-md-auto">
                <cc-select
                  label="Expense"
                  [lazyPageFetcher]="expenseOptions"
                  [(ngModel)]="search.expenseId"
                ></cc-select>
              </div>
              <div class="col-md-auto">
                <cc-input
                  label="Amount"
                  [(ngModel)]="search.amount"
                  type="number"
                ></cc-input>
              </div>
              <div class="col-md-auto">
                <button
                  cc-raised-button
                  color="accent"
                  (click)="searchExpenses()"
                >
                  Search
                </button>
              </div>
            </div>
            <div class="col-12 row justify-content-center">
              <div class="col-12">
                <cc-datagrid
                  [data]="unConfirmedExpenses?.content ?? []"
                  [columns]="gridColsUnConfirmedExpenses"
                  [length]="unConfirmedExpenses?.totalElements ?? 0"
                  [pageIndex]="unConfirmedExpenses?.number ?? 0"
                  [pageSize]="unConfirmedExpenses?.size ?? 0"
                  [pageSizeOptions]="[20]"
                  (page)="getNextPage($event)"
                  [noResultTemplate]="noResultTemplate"
                  [cellTemplate]="{ description: description }"
                >
                  <cc-grid-actions-list
                    *ccActionData="
                      let ctx of unConfirmedExpenses?.content ?? [];
                      row as row
                    "
                    [renderedActionsCount]="1"
                    style="width: fit-content"
                  >
                    <button
                      *cc-action
                      cc-raised-button
                      color="primary"
                      (click)="matchUnconfirmedExpense(row)"
                    >
                      Match
                    </button>
                  </cc-grid-actions-list> </cc-datagrid
                ><ng-template #noResultTemplate></ng-template>
                <ng-template #description let-row>
                  <span class="desc-row">{{ row?.description }}</span>
                </ng-template>
              </div>
              <div class="col-12 d-flex my-1 justify-content-center">
                <button
                  cc-raised-button
                  color="warn"
                  (click)="addTransaction()"
                >
                  No match found, create a manual transaction
                </button>
              </div>
            </div>
          </div>
        </cc-panel-body>
      </cc-panel>
    </cc-accordion>
  </div>
  <div *ngIf="add" #noMatchFoundCreateManual>
    <cc-accordion>
      <cc-panel [expanded]="true">
        <cc-panel-title> Add Transaction </cc-panel-title>
        <cc-panel-body>
          <div class="container" [formGroup]="form">
            <div class="row">
              <div class="col-6">
                <cc-input
                  label="From Bucket"
                  formControlName="fromBucket"
                ></cc-input>
                <cc-input
                  label="Amount"
                  formControlName="amount"
                  type="number"
                  [required]="true"
                ></cc-input>
                <cc-input
                  label="VAT Amount"
                  formControlName="vatAmount"
                  type="number"
                  [required]="
                    form.controls['license'].value &&
                    form.controls['license'].value.code !== 'no_vat'
                  "
                ></cc-input>
                <cc-select
                  label="VAT Type"
                  formControlName="vatType"
                  [data]="vatTypeOptions"
                  [required]="
                    form.controls['license'].value &&
                    form.controls['license'].value.code !== 'no_vat'
                  "
                ></cc-select>
                <cc-datepicker
                  label="Date"
                  formControlName="date"
                  [required]="true"
                ></cc-datepicker>
                <cc-textarea
                  label="Description"
                  formControlName="description"
                  [required]="true"
                ></cc-textarea>
              </div>
              <div class="col-6">
                <cc-input
                  label="Type of payment"
                  formControlName="paymentType"
                ></cc-input>
                <cc-select
                  label="Expense"
                  formControlName="expense"
                  [lazyPageFetcher]="expenseOptions"
                ></cc-select>
                <cc-select
                  label="To Bucket"
                  formControlName="toBucket"
                  [lazyPageFetcher]="bucketOptions"
                ></cc-select>
                <cc-select
                  label="License"
                  formControlName="license"
                  [data]="licenseOptions"
                  [required]="true"
                  [emitFullSelectOption]="true"
                ></cc-select>
                <div class="d-flex">
                  <cc-file-uploader
                    label="Non VAT Attachment"
                    formControlName="nonVatAttachment"
                    tag="NO_VAT"
                    [dropzoneConfig]="config"
                  ></cc-file-uploader>
                  <cc-file-uploader
                    label="VAT Attachment"
                    formControlName="vatAttachment"
                    tag="VAT"
                    [dropzoneConfig]="config"
                  ></cc-file-uploader>
                </div>
                <div class="col-12 row">
                  <div class="col-md-auto">
                    <cc-checkbox formControlName="missingTaxInvoice"
                      >Missing Tax Invoice</cc-checkbox
                    >
                  </div>
                </div>
              </div>
              <div class="col-12 row justify-content-end">
                <div class="col-md-auto">
                  <button
                    cc-raised-button
                    color="primary"
                    (click)="confirmAddTransaction()"
                  >
                    Save
                  </button>
                </div>
                <div class="col-md-auto">
                  <button cc-raised-button (click)="cancelTransaction()">
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        </cc-panel-body>
      </cc-panel>
    </cc-accordion>
  </div>
  <div *ngIf="confirmReplenishment && expense && transaction">
    <cc-accordion>
      <cc-panel [expanded]="true">
        <cc-panel-title>Confirm Replenishment</cc-panel-title>
        <cc-panel-body>
          <div class="container">
            <div class="row">
              <div class="col-12">
                <h4><b>Replenishment Details</b></h4>
                <hr />
                <div class="row">
                  <div class="col-lg-6">
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="expense.amount"
                          label="Amount"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="expense.creationDate"
                          label="Date"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                  </div>
                </div>
                <h4><b>Post to Accounting</b></h4>
                <hr />
                <div class="row">
                  <div class="col-lg-6">
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="transaction.fromBucket.label"
                          label="From Bucket"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="transaction.toBucket.label"
                          label="To Bucket"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="transaction.license.label"
                          label="License"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="transaction.amount"
                          label="Amount"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="transaction.creationDate"
                          label="Date"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-textarea
                          [(ngModel)]="transaction.description"
                          label="Description"
                        ></cc-textarea>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="transaction.paymentType.label"
                          label="Type of Payment"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="row justify-content-end">
                  <div class="col-md-auto">
                    <button
                      cc-raised-button
                      color="accent"
                      (click)="confirmReplenishmentAction()"
                    >
                      Confirm Replenishment
                    </button>
                  </div>
                  <div class="col-md-auto">
                    <button
                      cc-raised-button
                      color="basic"
                      (click)="doNothing()"
                    >
                      Do Nothing
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </cc-panel-body>
      </cc-panel>
    </cc-accordion>
  </div>
  <div *ngIf="confirmRefund && expense && transaction">
    <cc-accordion>
      <cc-panel [expanded]="true">
        <cc-panel-title>Confirm Refund</cc-panel-title>
        <cc-panel-body>
          <div class="container">
            <div class="row">
              <div class="col-12">
                <h4><b>Expense Details</b></h4>
                <hr />
                <div class="row">
                  <div class="col-lg-6">
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="expense.expense.label"
                          label="Expense"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [ngModel]="
                            expense.amountInLocalCurrency +
                            ' AED ' +
                            '(' +
                            expense.amount +
                            ' ' +
                            expense.currency?.label +
                            ')'
                          "
                          label="Amount"
                          [disabled]="true"
                        >
                          <span class="text-muted"
                            >({{ expense?.amount }}
                            {{ expense?.currency?.label }})</span
                          >
                        </cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="expense.expensePayment.vatAmount"
                          label="Vat Amount"
                          [disabled]="true"
                        >
                          <span class="text-muted">AED</span>
                        </cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="expense.expensePayment.creationDate"
                          label="Date"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="expense.requestedBy.label"
                          label="Requester"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-textarea
                          [(ngModel)]="expense.description"
                          label="Description"
                          [disabled]="true"
                        ></cc-textarea>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="expense.notes"
                          label="Notes"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-6">
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="expense.expensePayment.invoiceNumber"
                          label="Invoice Number"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-5">Invoice File</div>
                      <div class="col-7">
                        <a
                          class="cc-secondary"
                          (click)="downloadFile(expense?.invoiceFile?.uuid)"
                          *ngIf="expense?.invoiceFile?.uuid"
                        >
                          {{ expense?.invoiceFile?.name }}
                        </a>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-5">Vat Invoice File</div>
                      <div class="col-7">
                        <a
                          class="cc-secondary"
                          (click)="downloadFile(expense?.vatInvoiceFile?.uuid)"
                          *ngIf="expense?.vatInvoiceFile?.uuid"
                        >
                          {{ expense?.vatInvoiceFile?.name }}
                        </a>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="expense.beneficiaryName"
                          label="Beneficiary"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="expense.relatedToName"
                          label="Related To"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                  </div>
                </div>
                <h4><b>Post to Accounting</b></h4>
                <hr />
                <div class="row">
                  <div class="col-lg-6">
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="transaction.fromBucket.label"
                          label="From Bucket"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="transaction.expense.label"
                          label="Expense"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="transaction.license.label"
                          label="License"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="transaction.amount"
                          label="Amount"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="transaction.vatType"
                          label="Vat Type"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="transaction.vatAmount"
                          label="Vat Amount"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="transaction.creationDate"
                          label="Date"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-textarea
                          [(ngModel)]="transaction.description"
                          label="Description"
                        ></cc-textarea>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-6">
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="transaction.paymentType.label"
                          label="Type of Payment"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-5" style="margin-right: 3px">
                        No Vat Attachment
                      </div>
                      <div class="col-7">
                        <div *ngFor="let noVat of transaction?.noVatAttachment">
                          <a
                            class="cc-secondary"
                            (click)="downloadFile(noVat?.uuid)"
                            *ngIf="noVat?.uuid"
                          >
                            {{ noVat?.name }}
                          </a>
                        </div>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-5">VAT Attachment</div>
                      <div class="col-7">
                        <div *ngFor="let vat of transaction?.vatAttachment">
                          <a
                            class="cc-secondary"
                            (click)="downloadFile(vat?.uuid)"
                            *ngIf="vat?.uuid"
                          >
                            {{ vat?.name }}
                          </a>
                        </div>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="transaction.missingTaxInvoice"
                          label="Missing Tax Invoice"
                          [disabled]="true"
                        >
                        </cc-input>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="row justify-content-end">
                  <div class="col-md-auto">
                    <button
                      cc-raised-button
                      color="accent"
                      (click)="confirmRefundAction()"
                    >
                      Confirm Refund
                    </button>
                  </div>
                  <div class="col-md-auto">
                    <button
                      cc-raised-button
                      color="accent"
                      (click)="wrongMatch()"
                    >
                      Wrong Match
                    </button>
                  </div>
                  <div class="col-md-auto">
                    <button cc-raised-button (click)="doNothing()">
                      Do Nothing
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </cc-panel-body>
      </cc-panel>
    </cc-accordion>
  </div>
  <div *ngIf="confirmExpense && expense && transaction" #confirmExpensePanel>
    <cc-accordion>
      <cc-panel [expanded]="true">
        <cc-panel-title>Confirm Expense</cc-panel-title>
        <cc-panel-body>
          <div class="container">
            <div class="row">
              <div class="col-12">
                <h4><b>Expense Details</b></h4>
                <hr />
                <div class="row">
                  <div class="col-lg-6">
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="expense.expense.label"
                          label="Expense"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [ngModel]="
                            expense.amount + ' ' + expense.currency?.label
                          "
                          label="Amount"
                          [disabled]="true"
                        >
                          <span class="text-muted">{{
                            expense?.currency?.label
                          }}</span>
                        </cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="expense.expensePayment.vatAmount"
                          label="Vat Amount"
                          [disabled]="true"
                        >
                          <span class="text-muted">AED</span>
                        </cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="expense.expensePayment.date"
                          label="Date"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="expense.requestedBy.label"
                          label="Requestor"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-textarea
                          [(ngModel)]="expense.description"
                          label="Description"
                          [disabled]="true"
                        ></cc-textarea>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="expense.notes"
                          label="Notes"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-6">
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="expense.expensePayment.invoiceNumber"
                          label="Invoice Number"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-5">Invoice File</div>
                      <div class="col-7">
                        <a
                          class="cc-secondary"
                          (click)="downloadFile(expense?.invoiceFile?.uuid)"
                          *ngIf="expense?.invoiceFile?.uuid"
                        >
                          {{ expense?.invoiceFile?.name }}
                        </a>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-5">Vat Invoice File</div>
                      <div class="col-7">
                        <a
                          class="cc-secondary"
                          (click)="downloadFile(expense?.vatInvoiceFile?.uuid)"
                          *ngIf="expense?.vatInvoiceFile?.uuid"
                        >
                          {{ expense?.vatInvoiceFile?.name }}
                        </a>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="expense.beneficiaryName"
                          label="Beneficiary"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="expense.relatedToName"
                          label="Related To"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                  </div>
                </div>
                <h4><b>Transaction Details</b></h4>
                <hr />
                <div class="row">
                  <div class="col-lg-6">
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="transaction.fromBucket.label"
                          label="From Bucket"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="transaction.expense.label"
                          label="Expense"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="transaction.license.label"
                          label="License"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="transaction.amount"
                          label="Amount"
                          [disabled]="true"
                        >
                          <span class="text-muted">AED</span>
                        </cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="transaction.vatType"
                          label="Vat Type"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="transaction.vatAmount"
                          label="Vat Amount"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="transaction.creationDate"
                          label="Date"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-textarea
                          [(ngModel)]="transaction.description"
                          label="Description"
                        ></cc-textarea>
                      </div>
                    </div>
                    <div class="row mb-3" *ngIf="ableToEditVatInfo">
                      <div class="col-lg-9 offset-lg-3">
                        <a class="cc-secondary" (click)="openEditVat()">
                          Edit VAT Info
                        </a>
                      </div>
                    </div>
                  </div>
                  <div class="col-lg-6">
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="transaction.paymentType.label"
                          label="Type of Payment"
                          [disabled]="true"
                        ></cc-input>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-5">No Vat Attachment</div>
                      <div class="col-7">
                        <div *ngFor="let noVat of transaction?.noVatAttachment">
                          <a
                            class="cc-secondary"
                            (click)="downloadFile(noVat?.uuid)"
                            *ngIf="noVat?.uuid"
                          >
                            {{ noVat?.name }}
                          </a>
                        </div>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-5">VAT Attachment</div>
                      <div class="col-7">
                        <div *ngFor="let vat of transaction?.vatAttachment">
                          <a
                            class="cc-secondary"
                            (click)="downloadFile(vat?.uuid)"
                            *ngIf="vat?.uuid"
                          >
                            {{ vat?.name }}
                          </a>
                        </div>
                      </div>
                    </div>
                    <div class="row mb-3">
                      <div class="col-12">
                        <cc-input
                          [(ngModel)]="transaction.missingTaxInvoice"
                          label="Missing Tax Invoice"
                          [disabled]="true"
                        >
                        </cc-input>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="row justify-content-end">
                  <div class="col-md-auto">
                    <button
                      cc-raised-button
                      color="accent"
                      (click)="confirmExpenseAction()"
                    >
                      Confirm Expense
                    </button>
                  </div>
                  <div class="col-md-auto">
                    <button
                      cc-raised-button
                      color="accent"
                      (click)="wrongMatch()"
                    >
                      Wrong Match
                    </button>
                  </div>
                  <div class="col-md-auto">
                    <button
                      cc-raised-button
                      color="default"
                      (click)="doNothing()"
                    >
                      Do Nothing
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </cc-panel-body>
      </cc-panel>
    </cc-accordion>
  </div>
  <div class="row my-2 justify-content-center">
    <div class="col-md-auto">
      <button cc-raised-button color="primary" (click)="done()">Done</button>
    </div>
  </div>
</div>