import {
  Component,
  OnInit,
  ViewChild,
  ElementRef,
  AfterViewChecked,
} from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ReconciliatorService } from '../../services/reconciliator.service';
import { PaginationRequest } from '@maids/cc-lib/common';
import { map, Observable, combineLatest } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { PageEvent } from '@angular/material/paginator';
import { CCDialog } from '@maids/cc-lib/dialog';
import { EditVatComponent } from './edit-vat/edit-vat.component';
import { MatchedAlreadyConfirmComponent } from './matched-already-confirm/matched-already-confirm.component';
import { AlertComponent } from './alert/alert.component';
import { BreadcrumbLink } from '@maids/cc-lib/layout';
@Component({
  selector: 'app-credit-card-statement-details',
  templateUrl: './credit-card-statement-details.component.html',
  styleUrls: ['./credit-card-statement-details.component.scss'],
})
export class CreditCardStatementDetailsComponent
  implements OnInit, AfterViewChecked
{
  @ViewChild('confirmExpensePanel') confirmExpensePanel!: ElementRef;
  @ViewChild('matchExpensePanel') matchExpensePanel!: ElementRef;
  @ViewChild('noMatchFoundCreateManual') noMatchFoundCreateManual!: ElementRef;
  private shouldScrollToConfirmPanel = false;
  private shouldScrollToMatchPanel = false;
  private shouldScrollToCreateManualPanel = false;

  form: FormGroup = this.fb.group({
    amount: [null],
    vatAmount: [null],
    fromBucket: [{ value: null, disabled: true }],
    vatType: ['IN'],
    description: [null],
    missingTaxInvoice: [null],
    date: [null],
    paymentType: [{ value: 'Credit Card', disabled: true }],
    toBucket: [null],
    expense: [null],
    license: [null],
    licenseObj: [null],
    nonVatAttachment: [null],
    vatAttachment: [null],
  });
  breadcrumbs: BreadcrumbLink[] = [
    { label: 'Credit Card Statements', url: '/accounting/v2/reconciliator' },
    { label: 'Credit Card Statement Details', disabled: true },
  ];
  constructor(
    private _reconciliatorService: ReconciliatorService,
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private notifications: CCNotificationService,
    private mediaService: MediaService,
    private router: Router,
    private ccDialog: CCDialog
  ) {}
  readonly expenseOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this._reconciliatorService.RsearchExpenses(
      pageReq.page,
      pageReq.size,
      pageReq.searchString || ''
    );
  };
  readonly bucketOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this._reconciliatorService.RgetBucketFromOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString || ''
    );
  };

  vatTypeOptions: any[] = [
    { id: 'IN', text: ' Input VAT' },
    { id: 'OUT', text: ' Output VAT' },
  ];
  licenseOptions: any[] = [];
  currencies: any[] = [];
  localCurrency: any = null;
  matchedTransactions: any;
  unMatchedTransactions: any;
  unConfirmedExpenses: any;
  id: any;
  authorizedTransactions: any;
  fromBucket: any;
  confirmRefund: boolean = false;
  confirmExpense: boolean = false;
  confirmReplenishment: boolean = false;
  expense: any;
  add: boolean = false;
  search: any = {
    expenseId: '',
    amount: '',
    amountOperator: '=',
    paymentMethod: 'CREDIT_CARD',
  };
  expenseItem: any;
  selectedExpense: any;
  transaction: any;
  vatModel: any;
  match: boolean = false;
  matchType: any;
  ableToEditVatInfo: boolean = false;
  selectedId: any;
  idByType: any;
  ngOnInit(): void {
    this.id = this.route.snapshot.params['id'];
    this.getTransactionLicenseOptions();
    this.getCurrencyOptions();
    this.getStatementData();
  }

  ngAfterViewChecked(): void {
    if (
      this.shouldScrollToConfirmPanel &&
      this.confirmExpensePanel &&
      this.confirmExpensePanel.nativeElement
    ) {
      this.shouldScrollToConfirmPanel = false;
      setTimeout(() => {
        this.confirmExpensePanel.nativeElement.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        });
      }, 100);
    }

    if (
      this.shouldScrollToMatchPanel &&
      this.matchExpensePanel &&
      this.matchExpensePanel.nativeElement
    ) {
      this.shouldScrollToMatchPanel = false;
      setTimeout(() => {
        this.matchExpensePanel.nativeElement.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        });
      }, 100);
    }

    if (
      this.shouldScrollToCreateManualPanel &&
      this.noMatchFoundCreateManual &&
      this.noMatchFoundCreateManual.nativeElement
    ) {
      this.shouldScrollToCreateManualPanel = false;
      setTimeout(() => {
        this.noMatchFoundCreateManual.nativeElement.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
        });
      }, 100);
    }
  }
  getCurrencyOptions(): void {
    combineLatest([
      this._reconciliatorService.RgetCurrencies(),
      this._reconciliatorService.RgetParameter(),
    ]).subscribe(([currencies, parameters]) => {
      this.currencies = currencies;
      this.localCurrency = parameters[0].value;
      this.getTransactionData();
    });
  }
  getTransactionData(): void {
    this._reconciliatorService
      .getTransactions(this.id)
      .subscribe((res: any) => {
        this.matchedTransactions = res.filter((item: any) => {
          return item.matchType !== 'UNMATCHED';
        });
        this.unMatchedTransactions = res.filter((item: any) => {
          return item.matchType == 'UNMATCHED';
        });
      });
  }
  getTransactionLicenseOptions(): void {
    this._reconciliatorService.transaction_license().subscribe((res: any) => {
      this.licenseOptions = res.map((item: any) => ({
        id: item.id,
        text: item.name,
        code: item.code,
      }));
    });
  }
  getStatementData(): void {
    this._reconciliatorService
      .getStatementData(this.id)
      .subscribe((res: any) => {
        this.authorizedTransactions = res.authorizedTransactions;
        this.fromBucket = res.creditCard;
      });
  }
  wrongMatch() {
    this._reconciliatorService
      .wrongMatch(this.selectedId)
      .subscribe((res: any) => {
        this.getTransactionData();
        this.confirmRefund = false;
        this.confirmExpense = false;
      });
  }
  confirmRefundAction() {
    this._reconciliatorService
      .confirmRefundAction(
        (this.transaction.description ? JSON.stringify(this.transaction.description) : ''),
        this.selectedId
      )
      .subscribe((res: any) => {
        this.getTransactionData();
        this.confirmRefund = false;
        this.getStatementData();
      });
  }
  confirmExpenseAction() {
    this._reconciliatorService
      .confirmExpenseAction(
        (this.transaction.description ? JSON.stringify(this.transaction.description) : ''),
        this.selectedId
      )
      .subscribe((res: any) => {
        this.getTransactionData();
        this.confirmExpense = false;
        this.getStatementData();
      });
  }
  confirmReplenishmentAction() {
    this._reconciliatorService
      .confirmReplenishmentAction(
        (this.transaction.description ? JSON.stringify(this.transaction.description) : ''),
        this.selectedId
      )
      .subscribe((res: any) => {
        this.getTransactionData();
        this.confirmReplenishment = false;
        this.getStatementData();
      });
  }
  getReviewDetails(matchType: string, idByType: string) {
    if (matchType === 'REPLENISHMENT') {
      this._reconciliatorService
        .getReviewDetailsBucket(idByType)
        .subscribe((res: any) => {
          this.expense = res;
        });
    } else {
      this._reconciliatorService
        .getReviewDetailsExpense(idByType)
        .subscribe((res: any) => {
          this.expense = res;
          this.expense.vatInvoiceFile =
            this.expense.expensePayment.attachments.find((attachment: any) => {
              return attachment.tag === 'EXPENSE_PAYMENT_VAT_INVOICE';
            });
          if (!this.expense.vatInvoiceFile) {
            this.expense.vatInvoiceFile =
              this.expense.expensePayment.attachments.find(
                (attachment: any) => {
                  return attachment.tag === 'EXPENSE_REQUEST_VAT_INVOICE';
                }
              );
          }
          this.expense.invoiceFile =
            this.expense.expensePayment.attachments.find((attachment: any) => {
              return attachment.tag === 'EXPENSE_PAYMENT_INVOICE';
            });
          if (!this.expense.invoiceFile) {
            this.expense.invoiceFile =
              this.expense.expensePayment.attachments.find(
                (attachment: any) => {
                  return attachment.tag === 'EXPENSE_REQUEST_INVOICE';
                }
              );
          }
        });
    }
  }
  gridColsMatchedTransactions: CCGridColumn[] = [
    {
      field: 'recordTransactionDate',
      header: 'Date',
      formatter: (rowData) => {
        return rowData.recordTransactionDate?.split(' ')[0];
      },
      width: '150px',
    },
    {
      field: 'recordDescription',
      header: 'Description',
      width: '350px',
    },
    {
      field: 'recordAmount',
      header: 'Amount (AED)',
      formatter: (rowData) => {
        return this.getAmountLabel(
          Math.floor(parseFloat(rowData.recordAmount))
        );
      },
      width: '100px',
    },
    {
      field: 'crdrAction',
      header: 'CR/DR',
    },
    {
      field: 'transaction.expense.label',
      header: 'Expense',
    },
    {
      field: 'paymentDate',
      header: 'Payment Date',
      formatter: (rowData) => {
        if (rowData.matchType !== 'REPLENISHMENT') {
          return rowData.matchedExpenseRequest?.paymentDate?.split(' ')[0];
        }
        if (rowData.matchType === 'REPLENISHMENT') {
          return rowData.replenishmentTodo?.paymentDate?.split(' ')[0];
        }
        return '';
      },
    },
    {
      field: 'requestAmount',
      header: 'Amount',
      formatter: (rowData) => {
        let html = `${this.getAmountLabel(rowData.requestAmount)} ${
          rowData.requestCurrency?.label || ''
        }`;
        const localCurrency = this.currencies.find(
          (c) => c.name === this.localCurrency
        );

        if (
          rowData.requestCurrency &&
          localCurrency &&
          localCurrency.id !== rowData.requestCurrency.id
        ) {
          html = `${this.getAmountLabel(rowData.requestAmount)} ${
            rowData.requestCurrency.label
          } <br>
                 ${this.getAmountLabel(
                   rowData.requestAmountInLocalCurrency
                 )} AED`;
        }

        if (!rowData.requestAmount && !rowData.requestAmountInLocalCurrency) {
          html = '';
        }
        return html;
      },
    },
    {
      field: 'beneficiaryName',
      header: 'Beneficiary',
      formatter: (rowData) => {
        if (rowData.matchType !== 'REPLENISHMENT') {
          return rowData.matchedExpenseRequest?.beneficiaryName;
        }
        if (rowData.matchType === 'REPLENISHMENT') {
          return rowData.replenishmentTodo?.beneficiaryName;
        }
        return '';
      },
    },
    {
      field: 'status',
      header: 'Status',
    },
    {
      field: 'matchType',
      header: 'Match Type',
    },
  ];
  gridColsUnMatchedTransactions: CCGridColumn[] = [
    {
      field: 'recordTransactionDate',
      header: 'Date',
      formatter: (rowData) => {
        return rowData.recordTransactionDate?.split(' ')[0];
      },
      width: '150px',
    },
    {
      field: 'recordDescription',
      header: 'Description',
      width: '350px',
    },
    {
      field: 'recordAmount',
      header: 'Amount (AED)',
      formatter: (rowData) => {
        return this.getAmountLabel(
          Math.floor(parseFloat(rowData.recordAmount))
        );
      },
      width: '100px',
    },
    {
      field: 'crdrAction',
      header: 'CR/DR',
    },
  ];
  searchExpenses(page: number = 0, size: number = 20) {
    if (this.expenseItem.crdrAction == 'CR') {
      this.search.isRefunded = true;
    } else {
      delete this.search.isRefunded;
    }
    let searchObj: any = {};
    if (this.expenseItem.crdrAction == 'CR') {
      searchObj = Object.assign({ refundConfirmed: false }, this.search);
    } else {
      searchObj = Object.assign({ confirmed: false }, this.search);
    }
    this._reconciliatorService
      .searchExpenseTodo(page, size, searchObj)
      .subscribe((res: any) => {
        this.unConfirmedExpenses = res;
      });
  }
  getNextPage(event: PageEvent) {
    this.searchExpenses(event.pageIndex, event.pageSize);
  }
  gridColsUnConfirmedExpenses: CCGridColumn[] = [
    {
      field: 'expense.name',
      header: 'Expense',
    },
    {
      field: 'amount',
      header: 'Amount',
      formatter: (rowData) => {
        let amount = `${this.getAmountLabel(
          Math.floor(parseFloat(rowData.amount))
        )} (${rowData.currency.label})`;
        if (rowData.currency.code !== 'aed') {
          amount += `<br>${this.getAmountLabel(
            Math.floor(parseFloat(rowData.amountInLocalCurrency))
          )} (AED)`;
        }
        return amount;
      },
    },
    {
      field: 'creationDate',
      header: 'Date',
      formatter: (rowData) => {
        return rowData.creationDate?.split(' ')[0];
      },
    },
    {
      field: 'description',
      header: 'Description',
      width: '350px',
    },
    {
      field: 'notes',
      header: 'Notes',
    },
    {
      field: 'attachments',
      header: 'Invoice',
      formatter: (rowData) => {
        let html = '';
        if (rowData.attachments && rowData.attachments.length) {
          rowData.attachments.forEach((item: any) => {
            if (item.tag === 'EXPENSE_PAYMENT_INVOICE') {
              html += `<a class="cc-secondary" (click)="downloadFile('${item.uuid}')">${item.name}</a>`;
            }
          });
        }
        return html;
      },
    },
  ];
  private getAmountLabel(amount: number): string {
    return amount?.toLocaleString() || '';
  }
  downloadFile(uuid: string) {
    this.mediaService.downloadFile('public/download/' + uuid);
  }
  addTransaction() {
    this.add = true;
    this.confirmExpense = false;
    this.confirmRefund = false;
    this.confirmReplenishment = false;
    this.form.reset();
    this.form.patchValue({
      fromBucket: this.fromBucket.label,
      paymentType: 'Credit Card',
    });

    // Set flag to scroll to create manual panel
    this.shouldScrollToCreateManualPanel = true;
  }
  cancelTransaction() {
    this.add = false;
    this.shouldScrollToCreateManualPanel = false;
  }
  confirmAddTransaction() {
    let files: any[] = [];
    if (this.form.value.nonVatAttachment) {
      files.push({ id: this.form.value.nonVatAttachment[0].id });
    }
    if (this.form.value.vatAttachment) {
      files.push({ id: this.form.value.vatAttachment[0].id });
    }
    if (
      (this.form.value.toBucket && this.form.value.expense) ||
      (!this.form.value.toBucket && !this.form.value.expense)
    ) {
      this.notifications.notifyError(
        'Please select one of to bucket or expense.'
      );
      return;
    }
    if (this.form.valid) {
      let dataSave: any = {
        attachments: files,
        amount: +this.form.value.amount,
        vatAmount: +this.form.value.vatAmount,
        vatType: this.form.value.vatType,
        description: this.form.value.description,
        missingTaxInvoice: this.form.value.missingTaxInvoice,
        date: this.form.value.date + ' 00:00:00',
        paymentType: 'CREDIT_CARD',
        creationTriggeredAutomatically: false,
        toBucket: this.form.value.toBucket
          ? { id: this.form.value.toBucket.toString() }
          : null,
        fromBucket: this.fromBucket
          ? { id: this.fromBucket.id.toString() }
          : null,
        expense: this.form.value.expense
          ? { id: this.form.value.expense.toString() }
          : null,
        license: this.form.value.license
          ? { id: this.form.value.license.id.toString() }
          : null,
      };
      this._reconciliatorService
        .createManualTransaction(this.selectedId, dataSave)
        .subscribe((res: any) => {
          this.getTransactionData();
          this.add = false;
          this.form.reset();
          this.shouldScrollToCreateManualPanel = false;
        });
    }
  }
  done() {
    this._reconciliatorService
      .confirmStatement(this.id)
      .subscribe((res: any) => {
        this.router.navigateByUrl(
          '/accounting/v2/reconciliator?page=credit-card'
        );
      });
  }
  doNothing() {
    this.confirmRefund = false;
    this.confirmReplenishment = false;
    this.confirmExpense = false;
    this.add = false;
    this.match = false;
    this.shouldScrollToMatchPanel = false;
    this.shouldScrollToConfirmPanel = false;
    this.shouldScrollToCreateManualPanel = false;
  }
  openEditVat() {
    this.ccDialog
      .originalOpen(EditVatComponent, {
        data: {
          vatModel: this.vatModel,
          transaction: this.transaction,
        },
      })
      .afterClosed()
      .subscribe((res: any) => {
        this.transaction = res;
        this.transaction.missingTaxInvoice = res.missingTaxInvoice
          ? 'Yes'
          : 'No';
        this.transaction.creationDate = res?.creationDate?.split(' ')[0];
      });
  }
  matchExpense() {
    let data: any = this.selectedExpense;
    this._reconciliatorService
      .matchWithExpense(this.expenseItem.id, data.id)
      .subscribe((res: any) => {
        if (res.matchType == 'REFUND') {
          this.confirmRefund = true;
          this.confirmExpense = false;
          this.confirmReplenishment = false;
        } else {
          this.confirmExpense = true;
          this.confirmRefund = false;
          this.confirmReplenishment = false;
        }
        this.getReviewDetails(res.matchType, res.matchedExpenseRequest.id);
        this.transaction = res.transaction;
        this.transaction.missingTaxInvoice = res.transaction.missingTaxInvoice
          ? 'Yes'
          : 'No';
        this.transaction.creationDate =
          res?.transaction?.creationDate.split(' ')[0];
        this.transaction.vatAttachment = this.transaction.attachments.filter(
          (item: any) => {
            return item.tag.startsWith('VAT_');
          }
        );
        this.transaction.noVatAttachment = this.transaction.attachments.filter(
          (item: any) => {
            return !item.tag.startsWith('VAT_');
          }
        );

        // Hide match panel and scroll to confirm panel
        this.match = false;
        this.shouldScrollToMatchPanel = false;
        if (this.confirmExpense) {
          this.shouldScrollToConfirmPanel = true;
        }
      });
  }

  reviewToConfirm(row: any) {
    this.match = false;
    this.add = false;
    this.matchType = row.matchType;
    this.ableToEditVatInfo = row.ableToEditVatInfo;
    if (row.matchType === 'REFUND') {
      this.confirmRefund = true;
      this.confirmExpense = false;
      this.confirmReplenishment = false;
      this.idByType = row.matchedExpenseRequest.id;
    } else if (row.matchType === 'REPLENISHMENT') {
      this.confirmReplenishment = true;
      this.confirmRefund = false;
      this.confirmExpense = false;
      this.idByType = row.replenishmentTodo.id;
    } else if (row.matchType === 'AUTO_DEDUCTED') {
      this.confirmExpense = true;
      this.confirmRefund = false;
      this.confirmReplenishment = false;
      this.idByType = row.matchedExpenseRequest.id;
    } else if (row.matchType === 'EXISTING_EXPENSE') {
      this.confirmExpense = true;
      this.confirmRefund = false;
      this.confirmReplenishment = false;
      this.idByType = row.matchedExpenseRequest.id;
    } else if (row.matchType === 'ALREADY_MATCHED_TRANSACTION') {
      this.confirmExpense = false;
      this.confirmRefund = false;
      this.confirmReplenishment = false;
    }
    this.transaction = row.transaction;
    this.transaction.missingTaxInvoice = row.transaction.missingTaxInvoice
      ? 'Yes'
      : 'No';
    this.transaction.creationDate =
      row?.transaction?.creationDate?.split(' ')[0];
    this.vatModel = {
      vatAmount: this.transaction.vatAmount,
      vatType: this.transaction.vatType,
      attachment: '',
      missingTaxInvoice: this.transaction.missingTaxInvoice,
    };
    this.transaction.vatAttachment = this.transaction.attachments.filter(
      (item: any) => {
        return item.tag.startsWith('VAT_');
      }
    );
    this.vatModel.attachment = this.transaction.attachments.find(
      (item: any) => {
        return item.tag.startsWith('VAT_');
      }
    );
    this.vatModel.hasVatAttachment = !!this.vatModel.attachment;
    this.transaction.noVatAttachment = this.transaction.attachments.filter(
      (item: any) => {
        return !item.tag.startsWith('VAT_');
      }
    );
    this.selectedId = row.id;
    if (this.idByType) {
      this.getReviewDetails(this.matchType, this.idByType);
    }

    if (this.confirmExpense) {
      this.shouldScrollToConfirmPanel = true;
    }
  }
  matchRow(row: any) {
    this.selectedId = row.id;
    this.match = true;

    this.expenseItem = row;
    if (this.expenseItem.crdrAction == 'DR') {
      this.search.status = 'PAID';
    } else {
      this.search.status = null;
    }
    this.searchExpenses();

    // Set flag to scroll to match panel
    this.shouldScrollToMatchPanel = true;
  }
  matchAlready(row: any) {
    this.selectedId = row.id;
    this.expenseItem = row;
    this.ccDialog
      .originalOpen(MatchedAlreadyConfirmComponent, {
        data: { selectedId: this.selectedId, expenseItem: this.expenseItem },
        width: '50vw',
      })
      .afterClosed()
      .subscribe((res: any) => {
        if (res) {
          location.reload();
        }
      });
  }
  matchUnconfirmedExpense(row: any) {
    this.selectedExpense = row;
    if (this.selectedExpense.bucket.name !== this.fromBucket.name) {
      this.ccDialog
        .originalOpen(AlertComponent, {
          data: {
            expense: this.selectedExpense.bucket.name,
            name: this.fromBucket.name,
          },
          width: '500px',
        })
        .afterClosed()
        .subscribe((res: boolean) => {
          if (res) {
            this.matchExpense();
          }
        });
    } else {
      this.matchExpense();
    }
  }
  getAmountNumberLabel(amount: any) {
    if (
      amount === null ||
      amount == undefined ||
      isNaN(amount) ||
      amount === ''
    )
      return amount;
    var parts = amount.toString().split('.');
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    return parts.join('.');
  }
}
