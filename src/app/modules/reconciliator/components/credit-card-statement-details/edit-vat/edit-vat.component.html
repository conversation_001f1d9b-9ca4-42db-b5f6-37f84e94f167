<cc-dialog-header>
  <h1 cc-dialog-title>Recheck Item price</h1>
  <a
    role="button"
    type="button"
    cc-icon-button
    cc-dialog-close-button
    cc-dialog-close
  ></a>
</cc-dialog-header>
<cc-dialog-content>
  <form [formGroup]="form">
    <div class="form-group row w3-margin-0">
      <div class="col-lg-12">
        <cc-input
          label="Vat Amount"
          formControlName="vatAmount"
          [required]="true"
        ></cc-input>
      </div>
    </div>
    <div class="form-group row w3-margin-0">
      <div class="col-lg-12">
        <cc-select
          label="Vat Type"
          formControlName="vatType"
          [data]="vatTypeOptions"
          [required]="true"
        ></cc-select>
      </div>
    </div>
    <div class="form-group row w3-margin-0">
      <div class="col-lg-{{ data.vatModel.hasVatAttachment ? '11' : '12' }}">
        <cc-file-uploader
          label="Vat Attachment"
          formControlName="attachment"
          tag="VAT_EXPENSE_PAYMENT_VAT_INVOICE"
          [dropzoneConfig]="{
            maxFiles:1,
            maxFilesize: 10
            }"
        ></cc-file-uploader>
      </div>
      <div class="col-lg-1" *ngIf="data.vatModel.hasVatAttachment">
        <button
          class="btn btn-danger btn-raised"
          style="padding: 6px 11px"
          (click)="deleteVatAttachment()"
        >
          <span class="glyphicon glyphicon-trash"></span>
        </button>
      </div>
    </div>
    <div class="form-group">
      <div class="col-lg-12 row">
        <div class="col-md-auto">
          <cc-checkbox formControlName="missingTaxInvoice"></cc-checkbox>
        </div>
        <div class="col-md-auto">Missing Tax Invoice</div>
      </div>
    </div>
  </form>
</cc-dialog-content>
<cc-dialog-actions>
  <button cc-raised-button cc-dialog-close>Cancel</button>
  <button
    cc-raised-button
    color="primary"
    [disabled]="form.invalid"
    (click)="saveVatInfo()"
  >
    Save
  </button>
</cc-dialog-actions>
