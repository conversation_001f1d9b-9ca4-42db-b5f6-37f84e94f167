import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ReconciliatorService } from 'src/app/modules/reconciliator/services/reconciliator.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CCDialogRef } from '@maids/cc-lib/dialog';

interface VatData {
  id: any;
  vatAmount: number;
  vatType: string;
  attachments: any[];
  missingTaxInvoice: boolean;
  license?: {
    id: string;
    code: string;
  };
}

@Component({
  selector: 'app-edit-vat',
  templateUrl: './edit-vat.component.html',
  styleUrls: ['./edit-vat.component.scss'],
})
export class EditVatComponent implements OnInit {
  vatTypeOptions: any[] = [
    { id: 'IN', text: ' Input VAT' },
    { id: 'OUT', text: ' Output VAT' },
  ];
  form: FormGroup = this.fb.group({
    id: [null],
    vatAmount: [null, Validators.required],
    vatType: [null, Validators.required],
    attachment: [null],
    missingTaxInvoice: [null],
  });

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private _reconciliatorService: ReconciliatorService,
    private fb: FormBuilder,
    private dialogRef: CCDialogRef<EditVatComponent>
  ) {}

  ngOnInit(): void {
    this.form.patchValue({
      id: this.data.vatModel.id,
      vatAmount: this.data.vatModel.vatAmount,
      vatType: this.data.vatModel.vatType,
      attachment: [this.data.vatModel.attachment],
      missingTaxInvoice: this.data.vatModel.missingTaxInvoice,
    });
  }

  deleteVatAttachment() {
    this._reconciliatorService
      .reconciliationTransactionDelete(this.form.value.attachment[0].id)
      .subscribe();
  }

  saveVatInfo() {
    if (this.form.valid) {
      const vatModel = this.form.value;
      if (vatModel.vatAmount !== 0) {
        this._reconciliatorService
          .transaction_license()
          .subscribe((response: any[]) => {
            const license = response.find((item) => {
              return item.code === 'mustaqeem';
            });
            this.saveVatData(vatModel, license);
          });
      } else {
        this.saveVatData(vatModel);
      }
    }
  }

  private saveVatData(vatModel: any, license?: any) {
    const data: VatData = {
      id: this.data.transaction.id,
      vatAmount: vatModel.vatAmount,
      vatType: vatModel.vatType,
      attachments: vatModel.attachment,
      missingTaxInvoice: vatModel.missingTaxInvoice,
    };

    if (license) {
      data.license = {
        id: license.id,
        code: license.code,
      };
    }

    this._reconciliatorService
      .reconciliationTransactionUpdate(data)
      .subscribe((response) => {
        if (vatModel.attachment) {
          this.data.transaction.vatAttachment = [
            ...this.data.transaction.vatAttachment,
            vatModel.attachment,
          ];
          this.data.transaction.attachments = [
            ...this.data.transaction.attachments,
            vatModel.attachment,
          ];
        }
        this.form.reset({
          vatAmount: '',
          vatType: '',
          attachment: '',
          missingTaxInvoice: '',
        });
        let closedData: any = this.data.transaction;
        closedData.vatAmount = vatModel.vatAmount;
        closedData.vatType = vatModel.vatType;
        closedData.missingTaxInvoice = vatModel.missingTaxInvoice;
        this.dialogRef.close(closedData);
      });
  }
}
