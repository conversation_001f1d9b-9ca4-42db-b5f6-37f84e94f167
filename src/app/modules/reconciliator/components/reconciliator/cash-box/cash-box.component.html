<div class="acc-8902">
  <div class="row">
    <div class="col-12 px-0">
      <h4><b>Cash boxes summary</b></h4>
    </div>
    <div class="col-12 px-0">
      <cc-datagrid
        class="my-2"
        [data]="records ?? []"
        [columns]="gridCols"
        [pageOnFront]="false"
        [showPaginator]="false"
        [stickyHeader]="true"
        [columnMovable]="true"
        [columnHideable]="true"
        [showColumnMenuButton]="true"
        [showColumnMenuHeader]="false"
        [columnMenuButtonIcon]="'settings'"
        [noResultTemplate]="noResultTemplate"
      ></cc-datagrid>
    </div>
  </div>
  <div class="row my-2">
    <div class="col-12 px-0">
      <h4><b>Complete Expenses Pending Confirmation</b></h4>
    </div>
    <div class="col-12 px-0" [formGroup]="form">
      <cc-radio-group formControlName="expenseType" class="row">
        <div class="col-md-auto">
          <cc-radio-button value="ALL">All</cc-radio-button>
        </div>
        <div class="col-md-auto">
          <cc-radio-button value="CASH">Cash</cc-radio-button>
        </div>
        <div class="col-md-auto">
          <cc-radio-button value="SALARY">Salary</cc-radio-button>
        </div>
      </cc-radio-group>
    </div>
    <div class="col-12 px-0">
      <cc-datagrid
        class="my-2"
        [data]="completeExpensesRecords ?? []"
        [columns]="gridColsCEPTD"
        [pageOnFront]="false"
        [showPaginator]="false"
        [stickyHeader]="true"
        [columnMovable]="true"
        [columnHideable]="true"
        [showColumnMenuButton]="true"
        [showColumnMenuHeader]="false"
        [columnMenuButtonIcon]="'settings'"
        [cellTemplate]="{description:description}"
        [noResultTemplate]="noResultTemplate"
      >
        <cc-grid-actions-list
          *ccActionData="let ctx of completeExpensesRecords; row as row"
          style="width: fit-content"
        >
          <button
            *cc-action
            cc-raised-button
            color="accent"
            (click)="confirm(row.id)"
          >
            Confirm
          </button>
        </cc-grid-actions-list>
      </cc-datagrid>
      <ng-template #description let-row >
        <span class="desc-row">{{row?.description}}</span>
      </ng-template>
    </div>
  </div>
  <div class="row my-2">
    <div class="col-12 px-0">
      <h4><b>Expenses Pending Invoices</b></h4>
    </div>
    <div class="col-12 px-0">
      <cc-datagrid
        class="my-2"
        [data]="expensesInvoicesRecords ?? []"
        [columns]="gridColsEPITD"
        [pageOnFront]="false"
        [showPaginator]="false"
        [stickyHeader]="true"
        [columnMovable]="true"
        [columnHideable]="true"
        [showColumnMenuButton]="true"
        [showColumnMenuHeader]="false"
        [columnMenuButtonIcon]="'settings'"
        [noResultTemplate]="noResultTemplate"
        [cellTemplate]="{description:description}"
      >
        <cc-grid-actions-list
          *ccActionData="let ctx of expensesInvoicesRecords; row as row"
          style="width: fit-content"
        >
          <button
            *cc-action
            cc-raised-button
            color="accent"
            (click)="confirm(row.id)"
          >
            Collect Invoice
          </button>
        </cc-grid-actions-list>
      </cc-datagrid>
      <ng-template #description let-row>
        <span class="desc-row">{{row?.description}}</span>
      </ng-template>
      <ng-template #noResultTemplate></ng-template>
    </div>
  </div>
</div>
