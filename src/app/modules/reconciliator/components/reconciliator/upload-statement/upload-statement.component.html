<cc-dialog-header>
  <h1 cc-dialog-title>Add New Statement</h1>
  <a
    role="button"
    type="button"
    cc-icon-button
    cc-dialog-close-button
    cc-dialog-close
  ></a>
</cc-dialog-header>
<cc-dialog-content>
  <form [formGroup]="form">
    <div class="row">
      <div class="col-12">
        <cc-select
          label="Credit Card"
          formControlName="creditCardId"
          [lazyPageFetcher]="creditCardOptions"
          [required]="true"
        ></cc-select>
      </div>
      <div class="col-12">
        <cc-file-uploader
          label="Attach Statement"
          formControlName="attachment"
          [required]="true"
          [dropzoneConfig]="{
            maxFiles:1,
            maxFilesize: 10
            }"
        ></cc-file-uploader>
      </div>
    </div>
  </form>
</cc-dialog-content>
<cc-dialog-actions>
  <button cc-raised-button color="primary" (click)="parse()">
    Start Parsing
  </button>
</cc-dialog-actions>
