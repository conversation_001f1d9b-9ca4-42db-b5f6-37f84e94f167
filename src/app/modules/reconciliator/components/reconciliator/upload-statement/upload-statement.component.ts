import { Component, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { ReconciliatorService } from '../../../services/reconciliator.service';
import { CCDialogRef } from '@maids/cc-lib/dialog';
import { PaginationRequest } from '@maids/cc-lib/common';
import { Observable } from 'rxjs';
import { CCNotificationService } from '@maids/cc-lib/services';

@Component({
  selector: 'app-upload-statement',
  templateUrl: './upload-statement.component.html',
  styleUrls: ['./upload-statement.component.scss'],
})
export class UploadStatementComponent implements OnInit {
  form = this.fb.group({
    creditCardId: [''],
    attachment: [''],
  });
  creditCardOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.reconciliatorService.getcreditCardOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  constructor(
    private fb: FormBuilder,
    private reconciliatorService: ReconciliatorService,
    private ccDialogRef: CCDialogRef<UploadStatementComponent>,
    public readonly notifications: CCNotificationService
  ) {}

  ngOnInit(): void {}
  parse() {
    this.reconciliatorService
      .RuploadStatement(
        this.form.controls['attachment'].value[0].id,
        this.form.controls['creditCardId'].value
      )
      .subscribe({
        next: (res: any) => {
          this.ccDialogRef.close(res.id);
        },
        error: (err: any) => {
          this.notifications.notifyError(err.error.message);
        },
      });
  }
}
