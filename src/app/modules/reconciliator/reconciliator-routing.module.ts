import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ReconciliatorComponent } from './components/reconciliator/reconciliator.component';
import { ConfirmPaymentComponent } from './components/confirm-payment/confirm-payment.component';
import { CreditCardStatementDetailsComponent } from './components/credit-card-statement-details/credit-card-statement-details.component';

const routes: Routes = [
  { path: '', component: ReconciliatorComponent },
  {
    path: 'confirm-payment/:id',
    component: ConfirmPaymentComponent,
    data: {
      label: 'Confirm Payment',
      pageCode: 'ACCOUNTING__ReconciliatorConfirmPayment',
    },
  },
  {
    path: 'credit-card-statement-details/:id',
    component: CreditCardStatementDetailsComponent,
    data: {
      label: 'Credit Card Statement Details',
      pageCode: 'ACCOUNTING__ReconciliatorCreditCardStatementDetails',
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ReconciliatorRoutingModule {}
