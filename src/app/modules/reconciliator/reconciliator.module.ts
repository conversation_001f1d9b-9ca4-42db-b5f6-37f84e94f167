import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ReconciliatorRoutingModule } from './reconciliator-routing.module';
import { ReconciliatorComponent } from './components/reconciliator/reconciliator.component';
import { CashBoxComponent } from './components/reconciliator/cash-box/cash-box.component';
import { CreditCardStatementsComponent } from './components/reconciliator/credit-card-statements/credit-card-statements.component';
import { MissingTaxInvoicesComponent } from './components/reconciliator/missing-tax-invoices/missing-tax-invoices.component';
import { ConfirmPaymentComponent } from './components/confirm-payment/confirm-payment.component';
import { CreditCardStatementDetailsComponent } from './components/credit-card-statement-details/credit-card-statement-details.component';
import { CCButtonModule } from '@maids/cc-lib/button';
import { CCDatagridModule } from '@maids/cc-lib/datagrid';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CCInputModule } from '@maids/cc-lib/input';
import { CCSelectInputModule } from '@maids/cc-lib/select-input';
import { CCRadioButtonModule } from '@maids/cc-lib/radio-button';
import { UploadStatementComponent } from './components/reconciliator/upload-statement/upload-statement.component';
import { CCDialogModule } from '@maids/cc-lib/dialog';
import { CCFileUploaderModule } from '@maids/cc-lib/file-uploader';
import { CCAccordionModule } from '@maids/cc-lib/accordion';
import { CCDatepickerModule } from '@maids/cc-lib/date';
import { SupplierDetailsComponent } from './components/supplier-details/supplier-details.component';
import { CCTextareaModule } from '@maids/cc-lib/textarea';
import { EditVatComponent } from './components/credit-card-statement-details/edit-vat/edit-vat.component';
import { MatchedAlreadyConfirmComponent } from './components/credit-card-statement-details/matched-already-confirm/matched-already-confirm.component';
import { CCCheckboxModule } from '@maids/cc-lib/checkbox';
import { AlertComponent } from './components/credit-card-statement-details/alert/alert.component';
import { CCIconModule } from '@maids/cc-lib/icon';
import { CCBreadcrumbsModule } from '@maids/cc-lib/layout';
import { CCAmountInputModule } from '@maids/cc-lib/masked-input';
@NgModule({
  declarations: [
    ReconciliatorComponent,
    CashBoxComponent,
    CreditCardStatementsComponent,
    MissingTaxInvoicesComponent,
    ConfirmPaymentComponent,
    CreditCardStatementDetailsComponent,
    UploadStatementComponent,
    SupplierDetailsComponent,
    EditVatComponent,
    MatchedAlreadyConfirmComponent,
    AlertComponent,
  ],
  imports: [
    CommonModule,
    ReconciliatorRoutingModule,
    CCButtonModule,
    CCAccordionModule,
    CCTextareaModule,
    CCDatepickerModule,
    CCDialogModule,
    CCCheckboxModule,
    CCFileUploaderModule.forChild({}),
    CCDatagridModule,CCIconModule,
    ReactiveFormsModule,
    FormsModule,
    CCInputModule,
    CCSelectInputModule,
    CCRadioButtonModule,
    CCAmountInputModule,
    CCBreadcrumbsModule
  ],
})
export class ReconciliatorModule {}
