import { HttpClient, HttpContext, HttpHeaders } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import {
  CCBackendEndpoint,
  REQ_SHOW_LOADING_ICON,
} from '@maids/cc-erp-services';
import { map, Observable } from 'rxjs';
import { API } from 'src/environments/api';

@Injectable({
  providedIn: 'root',
})
export class ReconciliatorService {
  constructor(
    private _http: HttpClient,
    @Inject(CCBackendEndpoint) private _api: CCBackendEndpoint
  ) {}
  RgetBucketFromOptions(
    page: number,
    size: number = 50,
    search: string = ''
  ): Observable<any> {
    const context = new HttpContext().set(REQ_SHOW_LOADING_ICON, false);
    return this._http
      .get<any>(`${this._api}/${API.RgetBucketOptions}`, {
        params: { page, size, search },
        context,
      })
      .pipe(
        map((res: any) => {
          return res.content.map((item: any) => ({
            text: item.name,
            id: item.id,
          }));
        })
      );
  }
  getCashSummaryTableData(page: number, size: number): Observable<any> {
    return this._http.get<any>(`${this._api}/${API.getCashSummaryTableData}`, {
      params: { page, size },
    });
  }
  getCompleteExpensesPendingTableData(
    page: number,
    size: number,
    type: string = ''
  ): Observable<any> {
    let params: any = {
      page: page,
      size: size,
    };
    if (type) {
      params.filterBy = type;
    }
    return this._http.get<any>(
      `${this._api}/${API.getCompleteExpensesPendingTableData}`,
      { params }
    );
  }
  getExpensesPendingInvoicesTableData(
    page: number,
    size: number
  ): Observable<any> {
    return this._http.get<any>(
      `${this._api}/${API.getExpensesPendingInvoicesTableData}`,
      {
        params: { page, size },
      }
    );
  }
  getByCreditCard(page: number, size: number, creditId: any): Observable<any> {
    let params: any = {
      page,
      size,
    };
    if (creditId) {
      params.creditId = creditId;
    }
    return this._http.get<any>(`${this._api}/${API.getByCreditCard}`, {
      params,
    });
  }
  RdeleteStatement(id: any): Observable<any> {
    return this._http.post<any>(
      `${this._api}/${API.RdeleteStatement}/${id}`,
      {}
    );
  }
  getcreditCardOptions(
    page: number,
    size: number = 50,
    search: string = ''
  ): Observable<any> {
    const context = new HttpContext().set(REQ_SHOW_LOADING_ICON, false);
    return this._http
      .get<any>(`${this._api}/${API.RgetBucketOptions}`, {
        params: { page, size, search, type: 'CREDIT_CARD' },
        context,
      })
      .pipe(
        map((res) => {
          return res.content.map((item: any) => {
            return { text: item.name, id: item.id };
          });
        })
      );
  }
  RgetexpenseOptions(
    page: number,
    size: number = 50,
    search: string = ''
  ): Observable<any> {
    const context = new HttpContext().set(REQ_SHOW_LOADING_ICON, false);
    return this._http
      .get<any>(`${this._api}/${API.RgetexpenseOptions}`, {
        params: { page, size, search },
        context,
      })
      .pipe(
        map((res) => {
          return res.content.map((item: any) => {
            return { text: item.label, id: item.id, code: item.code };
          });
        })
      );
  }
  RuploadStatement(attachment: any, creditCardId: any): Observable<any> {
    return this._http.post<any>(
      `${this._api}/${API.RuploadStatement}/${creditCardId}`,
      attachment
    );
  }
  Radvancesearch3(page: number, size: number, payload: any): Observable<any> {
    return this._http.post<any>(
      `${this._api}/${API.Radvancesearch3}`,
      payload,
      {
        params: { page, size },
      }
    );
  }
  RgetExpensePayment(id: any): Observable<any> {
    return this._http.get<any>(`${this._api}/${API.RgetExpensePayment}/${id}`);
  }
  RgetCurrencies(): Observable<any> {
    return this._http.get<any>(`${this._api}/${API.RgetCurrencies}`).pipe(
      map((res) => {
        return res.map((item: any) => {
          return { id: item.id, text: item.name };
        });
      })
    );
  }
  selectAsInvoice(paymentId: any, attachmentId: any): Observable<any> {
    return this._http.post(
      `${this._api}/${API.selectAsInvoice}/${paymentId}/${attachmentId}`,
      {}
    );
  }
  selectAsVatInvoice(paymentId: any, fileId: any): Observable<any> {
    return this._http.post(
      `${this._api}/${API.selectAsVatInvoice}/${paymentId}/${fileId}`,
      {}
    );
  }
  addInvoice(paymentId: any, id: any): Observable<any> {
    return this._http.post(
      `${this._api}/${API.addInvoice}/${paymentId}/${id}`,
      {}
    );
  }
  deleteInvoice(id: any): Observable<any> {
    return this._http.post(`${this._api}/${API.deleteInvoice}/${id}`, {});
  }
  RshowSupplierDetails(beneficiaryId: any): Observable<any> {
    return this._http.get(
      `${this._api}/${API.RshowSupplierDetails}/${beneficiaryId}`
    );
  }
  saveExpensePaymentTask(
    paymentId: any,
    taskName: any,
    data: any
  ): Observable<any> {
    return this._http.post(
      `${this._api}/${API.saveExpensePaymentTask}/${paymentId}/${taskName}`,
      data
    );
  }
  completeExpensePayment(
    paymentId: any,
    taskName: any,
    data: any
  ): Observable<any> {
    return this._http.post(
      `${this._api}/${API.completeExpensePayment}/${paymentId}/${taskName}`,
      data
    );
  }
  ///////////////////////
  createManualTransaction(id: any, payload: any): Observable<any> {
    return this._http.post(
      `${this._api}/${API.createManualTransaction(id)}`,
      payload
    );
  }
  confirmStatement(id: any): Observable<any> {
    return this._http.post<any>(
      `${this._api}/${API.RconfirmStatement(id)}`,
      {}
    );
  }
  transaction_license(): Observable<any> {
    return this._http.get<any>(`${this._api}/${API.transaction_license}`);
  }
  RsearchExpenses(
    page: number,
    size: number = 50,
    search: string
  ): Observable<any> {
    const context = new HttpContext().set(REQ_SHOW_LOADING_ICON, false);
    return this._http
      .get<any>(`${this._api}/${API.RsearchExpenses}`, {
        params: { page, size, search },
        context,
      })
      .pipe(
        map((res: any) => {
          return res.content.map((item: any) => ({
            text: item.label,
            id: item.id,
            code: item.code,
          }));
        })
      );
  }
  linkedTransactionIdOptions(id: any, params: any): Observable<any> {
    const context = new HttpContext().set(REQ_SHOW_LOADING_ICON, false);
    return this._http
      .get<any>(`${this._api}/${API.linkedTransactionIdOptions(id)}`, {
        context,
        params,
      })
      .pipe(
        map((res: any) => {
          return res.map((item: any) => ({
            text: item.id,
            id: item.id,
            transaction: item,
          }));
        })
      );
  }
  getTransactions(id: any): Observable<any> {
    return this._http.get<any>(`${this._api}/${API.getTransactions(id)}`);
  }
  getStatementData(id: any): Observable<any> {
    return this._http.get<any>(`${this._api}/${API.getStatementData(id)}`);
  }
  wrongMatch(id: any): Observable<any> {
    return this._http.post<any>(`${this._api}/${API.wrongMatch(id)}`, {});
  }
  confirmRefundAction(payload: string, id: any): Observable<any> {
    return this._http.post<any>(
      `${this._api}/${API.confirmRefundAction(id)}`,
      payload
    );
  }
  confirmExpenseAction(payload: string, id: any): Observable<any> {
    return this._http.post<any>(
      `${this._api}/${API.confirmExpenseAction(id)}`,
      payload
    );
  }
  confirmReplenishmentAction(payload: string, id: any): Observable<any> {
    return this._http.post<any>(
      `${this._api}/${API.confirmReplenishmentAction(id)}`,
      payload
    );
  }
  RgetParameter(): Observable<any> {
    return this._http.get<any>(`${this._api}/${API.RgetParameter}`, {
      params: { code: 'EXPENSE_LOCAL_CURRENCY' },
    });
  }
  getReviewDetailsBucket(id: any): Observable<any> {
    return this._http.get<any>(
      `${this._api}/${API.getReviewDetailsBucket(id)}`
    );
  }
  getReviewDetailsExpense(id: any): Observable<any> {
    return this._http.get<any>(
      `${this._api}/${API.getReviewDetailsExpense(id)}`
    );
  }
  reconciliationTransactionUpdate(payload: any): Observable<any> {
    return this._http.post<any>(
      `${this._api}/${API.reconciliationTransactionUpdate}`,
      { payload }
    );
  }
  reconciliationTransactionDelete(id: any): Observable<any> {
    return this._http.delete<any>(
      `${this._api}/${API.reconciliationTransactionDelete(id)}`
    );
  }
  matchWithExpense(id: any, data: any): Observable<any> {
    return this._http.post<any>(
      `${this._api}/${API.matchWithExpense(id)}`,
      data
    );
  }
  confirmAlreadyMatched(id: any, transactionId: any): Observable<any> {
    return this._http.post<any>(
      `${this._api}/${API.confirmAlreadyMatched(id)}`,
      {},
      { params: { transactionId } }
    );
  }
  searchExpenseTodo(
    page: number,
    size: number,
    searchObj: any
  ): Observable<any> {
    return this._http.post<any>(
      `${this._api}/${API.searchExpenseTodo}`,
      searchObj,
      {
        params: { page, size, sort: 'id,desc' },
      }
    );
  }
}
