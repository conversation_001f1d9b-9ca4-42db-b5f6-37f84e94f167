import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { RecurringCcPaymentsIssuesMgmtComponent } from './components/recurring-cc-payments-issues-mgmt/recurring-cc-payments-issues-mgmt.component';

const routes: Routes = [
  { path: '', component: RecurringCcPaymentsIssuesMgmtComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class RecurringCcPaymentsIssuesMgmtRoutingModule {}
