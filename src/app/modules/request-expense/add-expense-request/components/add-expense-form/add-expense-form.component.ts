import { Component, OnInit } from '@angular/core';
import { AddExpenseService } from '../../services/add-expense.service';
import { Observable } from 'rxjs';
import { PaginationRequest } from '@maids/cc-lib/common';
import { FormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { CCDialog } from '@maids/cc-lib/dialog';
import { CCNotificationService } from '@maids/cc-lib/services';
import { CCFileUploaderConfig } from '@maids/cc-lib/file-uploader';

@Component({
  selector: 'app-add-expense-form',
  templateUrl: './add-expense-form.component.html',
  styleUrls: ['./add-expense-form.component.scss'],
})
export class AddExpenseFormComponent implements OnInit {
  formGroup = this.formBuilder.group({
    expense: [null],
    expenseToPost: [null],
    paymentMethod: [null],
    selectedBucket: [null],
    beneficiaryType: [null],
    beneficiaryIdSupplier: [null],
    beneficiaryIdHousemaid: [null],
    beneficiaryIdOfficeStaff: [null],
    beneficiaryName: [null],
    international: [null],
    beneficiaryEid: [null],
    beneficiaryMobileNumber: [null],
    beneficiaryIban: [null],
    beneficiaryHasNoIban: [null],
    beneficiaryAccountNumber: [null],
    beneficiaryAccountName: [null],
    swift: [null],
    beneficiaryAddress: [null],
    relatedToType: [null],
    relatedToIdTeam: [null],
    relatedToIdApplicant: [null],
    relatedToIdHousmaid: [null],
    relatedToIdOfficeStaff: [null],
    expenseAttachment: [null],
    amount: [null],
    currency: [null],
    notes: [null],
    invoiceNumber: [null],
    invoiceAttached: [null],
    invoiceAttachment: [null],
    taxable: [null],
    vatAmount: [null],
    attachedInvoiceIsValidVatInvoice: [null],
    invoiceVatAttachment: [null],
    loanAmount: [null],
    cashier: [null],
  });
  readonly cashPayerOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.addExpenseService.getCashiersOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  config: CCFileUploaderConfig = {
    maxFilesize: 5,
  };
  constructor(
    private addExpenseService: AddExpenseService,
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    private ccDialog: CCDialog,
    public readonly notifications: CCNotificationService,
    private router: Router
  ) {}
  readonly suppliersAjaxOptions = (
    pageReq: PaginationRequest
  ): Observable<any> => {
    return this.addExpenseService.suppliersAjaxOptionsExpense(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly officeStaffOptions = (
    pageReq: PaginationRequest
  ): Observable<any> => {
    return this.addExpenseService.officeStaffOptionsExpense(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly applicantOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.addExpenseService.applicantOptionsExpense(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly housemaidOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.addExpenseService.housemaidOptionsExpense(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly holderOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.addExpenseService.holderOptionsExpense(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly teamOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.addExpenseService.teamOptionsExpense(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly expenseOptionsAPI = (
    pageReq: PaginationRequest
  ): Observable<any> => {
    return this.addExpenseService.getExpenses(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  paymentMethodOptions: any[] = [
    { id: 'CASH', text: 'Cash' },
    { id: 'CREDIT_CARD', text: 'Credit Card' },
    { id: 'BANK_TRANSFER', text: 'Bank Transfer' },
    { id: 'MONEY_TRANSFER', text: 'Money Transfer' },
  ];
  paymentMethodsOptions: any[] = [
    { id: 'CASH', text: 'Cash' },
    { id: 'CREDIT_CARD', text: 'Credit Card' },
    { id: 'BANK_TRANSFER', text: 'Bank Transfer' },
    { id: 'MONEY_TRANSFER', text: 'Money Transfer' },
    { id: 'CHEQUE', text: 'Cheque' },
    { id: 'SALARY', text: 'Salary' },
    { id: 'INVOICED', text: 'Invoiced' },
  ];
  supplier: any | null = null;
  payment: any | null = null;
  suppliersOptions: any[] = [];
  beneficiaryOptions: any[] = [];
  currencyOptions: any[] = [];
  relatedOptions: any[] = [];
  expenseOptions: any[] = [];
  bucketOptions: any[] = [];
  showBeneficiary: boolean = false;
  showBucket: boolean = false;
  disableExpense: boolean = false;
  // ajaxExpense: boolean = false;
  page: string = this.route.snapshot.queryParamMap.get('page')!;
  id: number = +this.route.snapshot.queryParamMap.get('id')!;
  name: string = this.route.snapshot.queryParamMap.get('name')!;
  from: string = this.route.snapshot.queryParamMap.get('from')!;
  ibanValidated: boolean = false;
  ngOnInit(): void {
    if (!this.page && (!this.id || !this.name)) {
      this.cancel();
    }
    this.getCurrencies();
    if (this.id) {
      this.expenseOptions = [{ id: this.id, text: this.name }];
      this.formGroup.controls['expense'].setValue(this.id);
      this.disableExpense = true;
      this.formGroup.controls['beneficiaryType'].setValue(null);
      this.formGroup.controls['paymentMethod'].setValue(null),
        this.getExpenseDetails(this.id);
    } else if (this.page) {
      this.getExpenseForPage(this.page);
    }
    // else {
    //   this.ajaxExpense = true;
    // }
    this.formValueChanges();
  }
  formValueChanges() {
    this.formGroup.controls['expense'].valueChanges.subscribe((val) => {
      if (val) {
        this.paymentMethodsOptions = [];
        this.formGroup.controls['paymentMethod'].setValue('');
        this.formGroup.controls['beneficiaryType'].setValue(null);
        this.getExpenseDetails(val);
      }
    });
    this.formGroup.controls['paymentMethod'].valueChanges.subscribe((val) => {
      this.formGroup.controls['selectedBucket'].setValue(null);
      this.formGroup.controls['selectedBucket'].clearValidators();
      this.formGroup.controls['selectedBucket'].updateValueAndValidity();
      this.showBucket = false;
      if (val == 'CASH' && this.payment?.fromCashBuckets?.length > 1) {
        this.showBucket = true;
        this.formGroup.controls['selectedBucket'].addValidators(
          Validators.required
        );
        this.formGroup.controls['selectedBucket'].updateValueAndValidity();
        this.bucketOptions = this.payment?.fromCashBuckets.map((item: any) => {
          return { id: item.id, text: item.name };
        });
        this.formGroup.controls['selectedBucket'].setValue(
          this.payment?.fromCashBucket?.id
        );
      }
      if (
        val == 'CREDIT_CARD' &&
        this.payment.fromCreditCardBuckets?.length > 1
      ) {
        this.showBucket = true;
        this.formGroup.controls['selectedBucket'].addValidators(
          Validators.required
        );
        this.formGroup.controls['selectedBucket'].updateValueAndValidity();
        this.bucketOptions = this.payment?.fromCreditCardBuckets.map(
          (item: any) => {
            return { id: item.id, text: item.name };
          }
        );
        this.formGroup.controls['selectedBucket'].setValue(
          this.payment?.fromCreditCardBucket?.id
        );
      }
      if (val == 'CASH') {
        this.formGroup.controls['cashier'].addValidators(Validators.required);
        this.formGroup.controls['cashier'].updateValueAndValidity();
      } else {
        this.formGroup.controls['cashier'].clearValidators();
        this.formGroup.controls['cashier'].updateValueAndValidity();
      }
    });
    this.formGroup.controls['beneficiaryIdSupplier'].valueChanges.subscribe(
      (val) => {
        if (val) {
          if (this.payment.requireInvoice) {
            this.getSupplierDetails(val);
          }
        }
      }
    );
    this.formGroup.controls['beneficiaryHasNoIban'].valueChanges.subscribe(
      (val) => {
        if (val) {
          this.formGroup.controls['beneficiaryIban'].setValue('');
        } else {
          this.formGroup.controls['beneficiaryAccountNumber'].setValue('');
        }
      }
    );
    this.formGroup.controls['taxable'].valueChanges.subscribe((val) => {
      if (val === true && this.payment?.requireInvoice) {
        this.formGroup.controls['vatAmount'].addValidators(Validators.required);
        this.formGroup.controls['vatAmount'].updateValueAndValidity();
      } else {
        this.formGroup.controls['vatAmount'].removeValidators(Validators.required);
        this.formGroup.controls['vatAmount'].updateValueAndValidity();
      }
    });
  }
  getExpenseDetails(expense: any) {
    this.addExpenseService.getExpenseDetails(expense).subscribe((res: any) => {
      this.payment = res;
      this.showBeneficiary = true;
      if (res.paymentMethods.length) {
        this.paymentMethodsOptions = res.paymentMethods.map((item: any) => {
          return { id: item.value, text: item.label };
        });
        this.paymentMethodOptions = res.paymentMethods.map((item: any) => {
          return { id: item.value, text: item.label };
        });
      } else {
        this.paymentMethodsOptions = [
          { id: 'CASH', text: 'Cash' },
          { id: 'CREDIT_CARD', text: 'Credit Card' },
          { id: 'BANK_TRANSFER', text: 'Bank Transfer' },
          { id: 'MONEY_TRANSFER', text: 'Money Transfer' },
          { id: 'CHEQUE', text: 'Cheque' },
          { id: 'SALARY', text: 'Salary' },
          { id: 'INVOICED', text: 'Invoiced' },
        ];
      }
      this.formGroup.controls['paymentMethod'].setValue(null);
      this.formGroup.controls['amount'].setValue(res.defaultAmount);
      if (Object.entries(res.beneficiaryType).length) {
        this.beneficiaryOptions = [
          { id: res.beneficiaryType.value, text: res.beneficiaryType.label },
        ];
        this.showBeneficiary = false;
        this.formGroup.controls['beneficiaryType'].setValue(
          res.beneficiaryType.value
        );
      } else {
        this.showBeneficiary = false;
      }
      if (res.suppliers && res.suppliers.length) {
        this.suppliersOptions = [...res.suppliers].map((item) => {
          return { id: item.id, text: item.name };
        });
      }
      this.payment.showAmountAsLoan =
        this.payment.relatedTos.find(
          (item: any) => item.relatedToType.value === 'MAID'
        ) &&
        this.payment.allowToAddLoan &&
        this.payment.loanType;
      if (res.relatedTos.length) {
        let relatedTos: any[] = [];
        res.relatedTos.forEach((item: any) => {
          relatedTos.push({
            id: item.relatedToType.value,
            text: item.relatedToType.value,
          });
        });
        this.relatedOptions = relatedTos;
        if (relatedTos.length === 1) {
          this.formGroup.controls['relatedToType'].setValue(relatedTos[0].id);
        }
      }
    });
  }
  getSupplierDetails(supplier: any) {
    this.addExpenseService
      .getSupplierDetails(supplier)
      .subscribe((res: any) => {
        this.supplier = res;
        this.formGroup.controls['taxable'].setValue(
          res.vatRegistered !== '' ? this.supplier.vatRegistered : ''
        );
      });
  }
  getCurrencies() {
    this.addExpenseService.getCurrencies().subscribe((res: any) => {
      let aedId = 0;
      this.currencyOptions = res.map((item: any) => {
        if (item.code == 'aed') {
          aedId = item.id;
        }
        return { id: item.id, text: item.name };
      });
      this.formGroup.controls['currency'].setValue(aedId ? aedId : '');
    });
  }
  getExpenseForPage(page: any) {
    this.addExpenseService.getExpenseForPage(page).subscribe((res: any) => {
      if (res && res?.length) {
        this.expenseOptions = res.map((item: any) => ({
          id: item.id,
          text: item.label,
        }));
      }
    });
  }
  addSupplier() {
    window.open('#!/accounting/v2/suppliers-list/form', '_blank');
  }
  cancel() {
    this.router.navigateByUrl('/accounting/v2/request-expense');
  }
  save() {
    let payload: any;
    payload = {
      expenseRequestType: 'NEW_REQUEST',
      expense: { id: +this.formGroup.controls['expense'].value },
      currency: { id: +this.formGroup.controls['currency'].value },
      invoiceAttached: this.formGroup.controls['invoiceAttached'].value
        ? this.formGroup.controls['invoiceAttached'].value
        : '',
      taxable: this.formGroup.controls['taxable'].value
        ? this.formGroup.controls['taxable'].value
        : '',
      attachedInvoiceIsValidVatInvoice: this.formGroup.controls[
        'attachedInvoiceIsValidVatInvoice'
      ].value
        ? this.formGroup.controls['attachedInvoiceIsValidVatInvoice'].value
        : '',
      autoReplenishment: false,
      loanAmount: this.formGroup.controls['loanAmount'].value
        ? this.formGroup.controls['loanAmount'].value
        : null,
      invoiceNumber: this.formGroup.controls['invoiceNumber'].value
        ? this.formGroup.controls['invoiceNumber'].value
        : null,
      swift: this.formGroup.controls['swift'].value
        ? this.formGroup.controls['swift'].value
        : null,
      paymentMethod: this.formGroup.controls['paymentMethod'].value,
      beneficiaryType: this.formGroup.controls['beneficiaryType'].value
        ? this.formGroup.controls['beneficiaryType'].value
        : null,
      beneficiaryName: this.formGroup.controls['beneficiaryName'].value
        ? this.formGroup.controls['beneficiaryName'].value
        : null,
      beneficiaryAddress: this.formGroup.controls['beneficiaryAddress'].value
        ? this.formGroup.controls['beneficiaryAddress'].value
        : null,
      beneficiaryMobileNumber: this.formGroup.controls[
        'beneficiaryMobileNumber'
      ].value
        ? this.formGroup.controls['beneficiaryMobileNumber'].value
        : null,
      beneficiaryIban: this.formGroup.controls['beneficiaryIban'].value
        ? this.formGroup.controls['beneficiaryIban'].value
        : null,
      beneficiaryAccountName: this.formGroup.controls['beneficiaryAccountName']
        .value
        ? this.formGroup.controls['beneficiaryAccountName'].value
        : null,
      beneficiaryAccountNumber: this.formGroup.controls[
        'beneficiaryAccountNumber'
      ].value
        ? this.formGroup.controls['beneficiaryAccountNumber'].value
        : null,
      beneficiaryHasNoIban: this.formGroup.controls['beneficiaryHasNoIban']
        .value
        ? this.formGroup.controls['beneficiaryAccountNumber'].value
        : null,
      amount: this.formGroup.controls['amount'].value,
      notes: this.formGroup.controls['notes'].value,
      vatAmount: this.formGroup.controls['vatAmount'].value
        ? this.formGroup.controls['vatAmount'].value
        : null,
      attachments: [],
      relatedToType: this.formGroup.controls['relatedToType'].value
    };
    if (this.formGroup.controls['expenseAttachment'].value) {
      payload.attachments.push(
        this.formGroup.controls['expenseAttachment'].value[0]
      );
    }
    if (this.formGroup.controls['invoiceAttachment'].value) {
      payload.attachments.push(
        this.formGroup.controls['invoiceAttachment'].value[0]
      );
    }
    if (this.formGroup.controls['invoiceVatAttachment'].value) {
      payload.attachments.push(
        this.formGroup.controls['invoiceVatAttachment'].value[0]
      );
    }
    if (this.formGroup.controls['beneficiaryType'].value == 'SUPPLIER') {
      payload.beneficiaryId =
        this.formGroup.controls['beneficiaryIdSupplier'].value;
    }
    if (this.formGroup.controls['beneficiaryType'].value == 'MAID') {
      payload.beneficiaryId =
        this.formGroup.controls['beneficiaryIdHousemaid'].value;
    }
    if (this.formGroup.controls['beneficiaryType'].value == 'OFFICE_STAFF') {
      payload.beneficiaryId =
        this.formGroup.controls['beneficiaryIdOfficeStaff'].value;
    }
    if (this.formGroup.controls['relatedToType'].value == 'TEAM') {
      payload.relatedToId = this.formGroup.controls['relatedToIdTeam'].value;
    }
    if (this.formGroup.controls['relatedToType'].value == 'APPLICANT') {
      payload.relatedToId =
        this.formGroup.controls['relatedToIdApplicant'].value;
    }
    if (this.formGroup.controls['relatedToType'].value == 'MAID') {
      payload.relatedToId =
        this.formGroup.controls['relatedToIdHousmaid'].value;
    }
    if (this.formGroup.controls['relatedToType'].value == 'OFFICE_STAFF') {
      payload.relatedToId =
        this.formGroup.controls['relatedToIdOfficeStaff'].value;
    }

    if (this.formGroup.controls['expenseToPost'].value) {
      payload.expenseToPost = {
        name: this.formGroup.controls['expenseToPost'].value,
      };
    }

    if (this.showBucket) {
      payload.bucket = { id: this.formGroup.controls['selectedBucket'].value };
    }
    if (
      (this.formGroup.controls['paymentMethod'].value == 'CASH' &&
        this.payment?.fromCashBuckets?.length === 1) ||
      (this.formGroup.controls['paymentMethod'].value == 'CREDIT_CARD' &&
        this.payment?.fromCreditCardBuckets?.length === 1)
    ) {
      payload.bucket = {
        id:
          this.formGroup.controls['paymentMethod'].value == 'CASH'
            ? this.payment.fromCashBuckets[0].id
            : this.payment.fromCreditCardBuckets[0].id,
      };
    }
    if (
      this.formGroup.controls['beneficiaryType'].value == 'NOT_DETERMINED' &&
      this.formGroup.controls['paymentMethod'].value == 'BANK_TRANSFER'
    ) {
      payload.international = this.formGroup.controls['international'].value
        ? true
        : false;
    }
    if (
      this.formGroup.controls['paymentMethod'].value == 'BANK_TRANSFER' &&
      this.formGroup.controls['beneficiaryHasNoIban'].value == false
    ) {
      if (this.ibanValidated) {
        payload.beneficiaryIban =
          this.formGroup.controls['beneficiaryIban'].value;
      } else {
        this.validateIban(this.formGroup.controls['beneficiaryIban'].value);
        return;
      }
    }
    if (this.formGroup.controls['paymentMethod'].value == 'CASH') {
      payload.cashier = { id: this.formGroup.controls['cashier'].value };
    }
    this.submitForm(payload);
  }
  submitForm(payload: any) {
    this.addExpenseService.createExpense(payload).subscribe({
      next: (res: any) => {
        this.notifications.notifySuccess('Expense request added successfully!');
        if (this.page === 'CASHIER') {
          this.router.navigateByUrl('/accounting/v2/cashier');
        } else {
          this.router.navigateByUrl('/accounting/v2/request-expense');
        }
      },
    });
  }
  validateIban(iban: number) {
    this.addExpenseService.validateIBAN(iban).subscribe({
      next: (res: any) => {
        if (res && res.picklistItemInfo) {
          this.ibanValidated = true;
          this.save();
        }
        if (res.validations && res.validations.iban.code != '001') {
          this.notifications.notifyError(res.validations.iban.message);
        }
      },
    });
  }
}
