import { Component, OnInit } from '@angular/core';
import { DewaExpenseService } from '../../services/dewa-expense.service';
import { CCNotificationService } from '@maids/cc-lib/services';
import { FormBuilder } from '@angular/forms';
import { Router } from '@angular/router';
import { CCFileUploaderConfig } from '@maids/cc-lib/file-uploader';

@Component({
  selector: 'app-dewa-expense-form',
  templateUrl: './dewa-expense-form.component.html',
  styleUrls: ['./dewa-expense-form.component.scss'],
})
export class DewaExpenseFormComponent implements OnInit {
  formGroup = this.formBuilder.group({
    invoiceAttachment: [null],
    usage: [null],
    invoiceNumber: [null],
    amount: [null],
    vatAmount: [null],
    attachments: [null],
    electricity: [null],
    housing: [null],
    water: [null],
    sewage: [null],
  });
  usageOptions: any[] = [];
  supplier: any | null = null;
  config: CCFileUploaderConfig = {
    maxFilesize: 25,
  };
  constructor(
    private dewaExpenseService: DewaExpenseService,
    public readonly notifications: CCNotificationService,
    private formBuilder: FormBuilder,
    private router: Router
  ) {}
  ngOnInit(): void {
    this.getDewaUsage();
    this.formGroup.controls['usage'].valueChanges.subscribe((val) => {      
      let usage = this.usageOptions.find((usage) => +usage.id === +val.id);
      if (usage?.expense?.beneficiaryType.value !== 'SUPPLIER') {
        this.notifications.notifyError(
          'Expense beneficiaryType should be SUPPLIER.'
        );
        return;
      }
      if (usage?.expense?.suppliers && usage?.expense?.suppliers.length !== 1) {
        this.notifications.notifyError('Expense should have one supplier.');
        return;
      }
      this.supplier = usage?.expense?.suppliers[0];
    });
  }
  getDewaUsage() {
    this.dewaExpenseService.getDewaUsage().subscribe((res) => {
      this.usageOptions = [...res].map((item) => {
        return {
          id: item.id,
          text: `${item.usageText} (${item.serviceType.label})`,
          expense: item.expense,
        };
      });
    });
  }
  sendRequest() {
    let amount = +this.formGroup.controls['amount'].value;
    let electricity = +this.formGroup.controls['electricity'].value;
    let housing = +this.formGroup.controls['housing'].value;
    let water = +this.formGroup.controls['water'].value;
    let sewage = +this.formGroup.controls['sewage'].value;
    if (amount !== electricity + housing + water + sewage) {
      this.notifications.notifyError(
        'The total amount does not match the sum of the individual expenses'
      );
      return;
    }
    let payload: any;
    payload = {
      attachments: this.formGroup.controls['invoiceAttachment'].value,
      expenseRequestType: 'DEWA',
      expense: {
        id: this.usageOptions.find(
          (_) => +_.id === +this.formGroup.controls['usage'].value.id
        ).expense.id,
      },
      amount:amount,
      electricity: electricity,
      housing:housing,
      sewage: sewage,
      water: water,
      invoiceNumber: this.formGroup.controls['invoiceNumber'].value,
      telecomPhones: [{ id: this.formGroup.controls['usage'].value.id }],
      vatAmount: this.formGroup.controls['vatAmount'].value
        ? +this.formGroup.controls['vatAmount'].value
        : null,
    };
    this.dewaExpenseService.createDewaExpense(payload).subscribe({
      next: (res: any) => {
        this.notifications.notifySuccess(
          'DEWA expense request added successfully!'
        );
        this.cancel();
      },
    });
  }
  cancel() {
    this.router.navigateByUrl('/accounting/v2/request-expense');
  }
}
