import { Component, OnInit } from '@angular/core';
import { InsuranceExpensesService } from '../../services/insurance-expenses.service';
import { FormBuilder, Validators } from '@angular/forms';
import { CCNotificationService } from '@maids/cc-lib/services';
import { Router } from '@angular/router';
import { CCFileUploaderConfig } from '@maids/cc-lib/file-uploader';

@Component({
  selector: 'app-insurance-expenses-form',
  templateUrl: './insurance-expenses-form.component.html',
  styleUrls: ['./insurance-expenses-form.component.scss'],
})
export class InsuranceExpensesFormComponent implements OnInit {
  expense: any | null = null;
  supplier: any | null = null;
  paymentMethodsOptions: any[] = [];
  formGroup = this.formBuilder.group({
    paymentMethod: [null, [Validators.required]],
    amount: [null, [Validators.required]],
    invoiceAttachment: [null],
    vatAmount: [null],
  });
  config: CCFileUploaderConfig = {
    maxFilesize: 5,
  };
  constructor(
    private insuranceExpensesService: InsuranceExpensesService,
    private formBuilder: FormBuilder,
    public readonly notifications: CCNotificationService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.getExpenseName();
  }
  getExpenseName() {
    this.insuranceExpensesService
      .getExpenseName('INSURANCE_EXPENSE_CODE')
      .subscribe((res) => {
        this.getExpenseByCode(res[0].value);
      });
  }
  getExpenseByCode(code: string) {
    this.insuranceExpensesService.getExpenseByCode(code).subscribe((res) => {
      if (Array.isArray(res)) {
        this.expense = res[0];
      } else {
        this.expense = res;
      }
      if (this.expense) {
        if (
          this.expense.suppliers &&
          Array.isArray(this.expense.suppliers) &&
          this.expense.suppliers.length == 1
        ) {
          this.paymentMethodsOptions = { ...this.expense }.paymentMethods.map(
            (item: any) => ({
              id: item.value,
              text: item.label,
            })
          );
          this.insuranceExpensesService
            .getSupplier(this.expense.suppliers[0].id)
            .subscribe((res: any) => {
              this.supplier = res;
              if (this.supplier?.vatRegistered) {
                this.formGroup.controls['vatAmount'].setValidators([
                  Validators.required,
                ]);
              } else {
                this.formGroup.controls['vatAmount'].clearValidators();
              }
            });
          this.formGroup.controls['vatAmount'].updateValueAndValidity();
        } else {
          this.notifications.notifyError(
            'Expense should have only one supplier'
          );
        }
      }
    });
  }
  sendRequest() {
    let payload: any = {
      amount: +this.formGroup.controls['amount'].value,
      expense: {id:this.expense.id},
      expenseRequestType: 'INSURANCE',
      paymentMethod: this.formGroup.controls['paymentMethod'].value.id,
      vatAmount: +this.formGroup.controls['vatAmount'].value,
      attachments: [],
    };
    if (this.formGroup.controls['invoiceAttachment'].value) {
      payload.attachments?.push(
        this.formGroup.controls['invoiceAttachment'].value[0]
      );
    }
    this.insuranceExpensesService.createExpense(payload).subscribe({
      next: (res) => {
        this.notifications.notifySuccess(
          'Insurance expense request added successfully!'
        );
        this.cancel();
      },
    });
  }
  cancel() {
    this.router.navigateByUrl('/accounting/v2/request-expense');
  }
}
