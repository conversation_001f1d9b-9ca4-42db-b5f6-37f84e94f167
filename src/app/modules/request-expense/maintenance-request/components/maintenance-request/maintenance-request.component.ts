import { Component, OnInit } from '@angular/core';
import { MaintenanceRequestServiceService } from '../../services/maintenance-request-service.service';
import { PaginationRequest } from '@maids/cc-lib/common';
import { map, Observable } from 'rxjs';
import { FormBuilder, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { CCNotificationService } from '@maids/cc-lib/services';
import { CCFileUploaderConfig } from '@maids/cc-lib/file-uploader';

@Component({
  selector: 'app-maintenance-request',
  templateUrl: './maintenance-request.component.html',
  styleUrls: ['./maintenance-request.component.scss'],
})
export class MaintenanceRequestComponent implements OnInit {
  formGroup = this.fb.group({
    selectedExpense: ['', Validators.required],
    description: ['', Validators.required],
    haveQuotationReady: ['', Validators.required],
    selectedSupplier: [''],
    cost: [''], 
    quotationAttachment: [''],
    isTheInvoiceAttached: [''],
    invoiceAttachment: [''],
    invoiceContainVat: [''],
    vatAmount: [''],
    attachedValidVatInvoice: [''],
    invoiceVatAttachment: [''],
  });
  config: CCFileUploaderConfig = {
    maxFilesize: 25,
  };
  readonly suppliersAPIOptions = (
    pageReq: PaginationRequest
  ): Observable<any> => {
    return this.maintenanceRequestService.MRgetSuppliers(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  suppliersOptions: any[] = [];
  expenseOptions: any[] = [];
  disableInvoiceContainVat: boolean = false;
  constructor(
    private maintenanceRequestService: MaintenanceRequestServiceService,
    private fb: FormBuilder,
    private router: Router,
    public readonly notifications: CCNotificationService
  ) {}

  ngOnInit(): void {
    this.getExpenseForPage();
    this.handleFormChanges();
  }
  handleFormChanges() {
    this.formGroup.controls['selectedSupplier'].valueChanges.subscribe(
      (val) => {
        if (val.vatRegistered==true || val.vatRegistered==false) {
          this.disableInvoiceContainVat = true;
          this.formGroup.controls['invoiceContainVat'].setValue(val.vatRegistered)
        }else {
          this.formGroup.controls['invoiceContainVat'].setValue('');
          this.disableInvoiceContainVat = false;
        }
      }
    );
    this.formGroup.controls['haveQuotationReady'].valueChanges.subscribe(
      (value) => {
        if (value === true) {
          this.formGroup.controls['selectedSupplier'].addValidators(
            Validators.required
          );
          this.formGroup.controls['cost'].addValidators(Validators.required);
          this.formGroup.controls['quotationAttachment'].addValidators(
            Validators.required
          );
          this.formGroup.controls['isTheInvoiceAttached'].addValidators(
            Validators.required
          );
          this.formGroup.controls['selectedSupplier'].updateValueAndValidity();
          this.formGroup.controls['cost'].updateValueAndValidity();
          this.formGroup.controls[
            'quotationAttachment'
          ].updateValueAndValidity();
          this.formGroup.controls[
            'isTheInvoiceAttached'
          ].updateValueAndValidity();
        } else {
          this.formGroup.controls['selectedSupplier'].clearValidators();
          this.formGroup.controls['cost'].clearValidators();
          this.formGroup.controls['quotationAttachment'].clearValidators();
          this.formGroup.controls['isTheInvoiceAttached'].clearValidators();
          this.formGroup.controls['selectedSupplier'].updateValueAndValidity();
          this.formGroup.controls['cost'].updateValueAndValidity();
          this.formGroup.controls[
            'quotationAttachment'
          ].updateValueAndValidity();
          this.formGroup.controls[
            'isTheInvoiceAttached'
          ].updateValueAndValidity();
        }
      }
    );

    this.formGroup.controls['isTheInvoiceAttached'].valueChanges.subscribe(
      (value) => {
        const invoiceAttachmentControl =
          this.formGroup.controls['invoiceAttachment'];
        if (value === false) {
          invoiceAttachmentControl?.addValidators(Validators.required);
        } else {
          invoiceAttachmentControl?.clearValidators();
        }
        invoiceAttachmentControl?.updateValueAndValidity();
      }
    );

    this.formGroup.controls['invoiceContainVat'].valueChanges.subscribe(
      (value) => {
        const vatAmountControl = this.formGroup.controls['vatAmount'];
        const attachedValidVatInvoiceControl =
          this.formGroup.controls['attachedValidVatInvoice'];
        if (value === true) {
          vatAmountControl?.addValidators(Validators.required);
          attachedValidVatInvoiceControl?.addValidators(Validators.required);
        } else {
          vatAmountControl?.clearValidators();
          attachedValidVatInvoiceControl?.clearValidators();
        }
        vatAmountControl?.updateValueAndValidity();
        attachedValidVatInvoiceControl?.updateValueAndValidity();
      }
    );

    this.formGroup.controls['attachedValidVatInvoice'].valueChanges.subscribe(
      (value) => {
        const invoiceVatAttachmentControl =
          this.formGroup.controls['invoiceVatAttachment'];
        if (value === false) {
          invoiceVatAttachmentControl?.addValidators(Validators.required);
        } else {
          invoiceVatAttachmentControl?.clearValidators();
        }
        invoiceVatAttachmentControl?.updateValueAndValidity();
      }
    );
    this.formGroup.controls['selectedExpense'].valueChanges.subscribe((val) => {
      if (val) {
        this.suppliersOptions = [];
        this.formGroup.controls['selectedSupplier'].setValue('')
        this.maintenanceRequestService
          .MRgetExpenses(val)
          .pipe(
            map((res: any) => {
              this.suppliersOptions = res.suppliers
                ? res.suppliers.map((value: any) => {
                    return { id: value.id, text: value.label };
                  })
                : [];
            })
          )
          .subscribe();
      }
    });
  }
  getExpenseForPage() {
    this.maintenanceRequestService
      .MRgetExpenseForPage()
      .subscribe((res: any) => {
        this.expenseOptions = res;
      });
  }
  create() {
    let data: any = {};
    data = {
      attachments: [],
      expense: { id: this.formGroup.controls['selectedExpense'].value },
      description: this.formGroup.controls['description'].value,
      quotationReady: this.formGroup.controls['haveQuotationReady'].value,
      isInvoiceAttached: this.formGroup.controls['isTheInvoiceAttached'].value,
      vatAmount: this.formGroup.controls['vatAmount'].value,
      taxable: this.formGroup.controls['invoiceContainVat'].value,
      attachedValidVatInvoice:
        this.formGroup.controls['attachedValidVatInvoice'].value,
    };
    if (this.formGroup.controls['invoiceContainVat'].value) {
      data.vatAmount = this.formGroup.controls['vatAmount'].value;
    }
    if (this.formGroup.controls['haveQuotationReady'].value) {
      data.supplier = {
        id: this.formGroup.controls['selectedSupplier'].value.id,
      };
      data.cost = this.formGroup.controls['cost'].value;
      if (this.formGroup.controls['quotationAttachment'].value) {
        data.attachments.push(
          this.formGroup.controls['quotationAttachment'].value[0]
        );
      }
    }
    if (
      !data.isInvoiceAttached &&
      this.formGroup.controls['invoiceAttachment'].value
    ) {
      data.attachments.push(
        this.formGroup.controls['invoiceAttachment'].value[0]
      );
    }
    if (
      data.taxable &&
      !data.attachedValidVatInvoice &&
      this.formGroup.controls['invoiceVatAttachment'].value
    ) {
      data.attachments.push(
        this.formGroup.controls['invoiceVatAttachment'].value[0]
      );
    }
    this.maintenanceRequestService.MRcreateExpense(data).subscribe({
      next: (res: any) => {
        this.notifications.notifySuccess('Done Successfully');
        this.cancel();
      },
    });
  }
  cancel() {
    this.router.navigateByUrl('/accounting/v2/request-expense');
  }
}
