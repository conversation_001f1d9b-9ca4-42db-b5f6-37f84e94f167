import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MaintenanceRequestRoutingModule } from './maintenance-request-routing.module';
import { MaintenanceRequestComponent } from './components/maintenance-request/maintenance-request.component';
import { CCSelectInputModule } from '@maids/cc-lib/select-input';
import { CCInputModule } from '@maids/cc-lib/input';
import { CCButtonModule } from '@maids/cc-lib/button';
import { CCFileUploaderModule } from '@maids/cc-lib/file-uploader';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CCTextareaModule } from '@maids/cc-lib/textarea';
import { CCRadioButtonModule } from '@maids/cc-lib/radio-button';

@NgModule({
  declarations: [MaintenanceRequestComponent],
  imports: [
    CommonModule,
    MaintenanceRequestRoutingModule,
    CCSelectInputModule,
    CCInputModule,
    CCButtonModule,
    CCTextareaModule,
    CCFileUploaderModule.forChild({}),
    CCRadioButtonModule,
    ReactiveFormsModule,
    FormsModule,
  ],
})
export class MaintenanceRequestModule {}
