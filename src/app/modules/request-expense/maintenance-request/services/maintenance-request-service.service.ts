import { HttpClient, HttpContext } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import {
  CCBackendEndpoint,
  REQ_SHOW_LOADING_ICON,
} from '@maids/cc-erp-services';
import { map, Observable } from 'rxjs';
import { API } from 'src/environments/api';

@Injectable({
  providedIn: 'root',
})
export class MaintenanceRequestServiceService {
  constructor(
    private _http: HttpClient,
    @Inject(CCBackendEndpoint) private _api: CCBackendEndpoint
  ) {}
  MRgetSuppliers(
    page: number,
    size: number = 20,
    search: string = ''
  ): Observable<any> {
    const context = new HttpContext().set(REQ_SHOW_LOADING_ICON, false);
    return this._http
      .get(`${this._api}/${API.MRgetSuppliers}`, {
        params: { page, size, search },
        context,
      })
      .pipe(
        map((val: any) => {
          return val.content.map((item: any) => {
            return {
              text: item.name,
              id: item.id,
              vatRegistered: item.vatRegistered,
            };
          });
        })
      );
  }
  MRgetExpenseForPage(): Observable<any> {
    return this._http
      .get(`${this._api}/${API.MRgetExpenseForPage}`, {
        params: { page: 'MAINTENANCE' },
      })
      .pipe(
        map((res: any) => {
          return res.map((val: any) => {
            return { id: val.id, text: val.label };
          });
        })
      );
  }
  MRgetExpenses(expenseId: any): Observable<any> {
    return this._http.get(`${this._api}/${API.MRgetExpenses}/${expenseId}`);
  }
  MRcreateExpense(payload: any): Observable<any> {
    return this._http.post(`${this._api}/${API.MRcreateExpense}`, payload);
  }
  getCashiersOptions(
    page: number,
    size: number = 50,
    search: string = ''
  ): Observable<any> {
    const context = new HttpContext().set(REQ_SHOW_LOADING_ICON, false);
    return this._http
      .get(`${this._api}/${API.getCashiersOptions}`, {
        params: { page, size, search },
        context,
      })
      .pipe(
        map((res: any) => {
          return res.content.map((item: any) => ({
            id: item.id,
            text: item.label,
          }));
        })
      );
  }
}
