import { Component, OnInit } from '@angular/core';
import { OneTimeExpenseRequestService } from '../../services/one-time-expense-request.service';
import { FormBuilder, Validators } from '@angular/forms';
import { PaginationRequest } from '@maids/cc-lib/common';
import { Observable } from 'rxjs';
import { CCNotificationService } from '@maids/cc-lib/services';
import { Router } from '@angular/router';
import { CCFileUploaderConfig } from '@maids/cc-lib/file-uploader';

@Component({
  selector: 'app-one-time-expense-request',
  templateUrl: './one-time-expense-request.component.html',
  styleUrls: ['./one-time-expense-request.component.scss'],
})
export class OneTimeExpenseRequestComponent implements OnInit {
  formGroup = this.fb.group({
    expenseAttachment: [''],
    invoiceAttachment: [''],
    invoiceVatAttachment: [''],
    selectedSupplier: [''],
    relatedToId: [''],
    relatedToIdTeam: [''],
    relatedToIdApplicant: [''],
    relatedToIdHousmaid: [''],
    relatedToIdOfficeStaff: [''],
    relatedToType: [''],
    selectedExpense: [''],
    paymentMethod: [''],
    selectedBucket: [''],
    selectedRequestor: [''],
    amount: [''],
    selectedCurrency: [''],
    notes: [''],
    invoiceAttached: [''],
    invoiceContainVat: [''],
    vatAmount: [''],
    attachedValidVatInvoice: [''],
    cashier: [''],
  });
  readonly cashierOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.oneTimeExpenseRequestService.getCashiersOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly suppliersAjaxOptions = (
    pageReq: PaginationRequest
  ): Observable<any> => {
    return this.oneTimeExpenseRequestService.getSuppliers(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly teamOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.oneTimeExpenseRequestService.teamOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly applicantOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.oneTimeExpenseRequestService.applicantOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly housemaidOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.oneTimeExpenseRequestService.housemaidOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly officeStaffOptions = (
    pageReq: PaginationRequest
  ): Observable<any> => {
    return this.oneTimeExpenseRequestService.officeStaffOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly requestorOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.oneTimeExpenseRequestService.requestorOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  currencyOptions: any[] = [];
  expenseOptions: any[] = [];
  supplierOptions: any[] = [];
  paymentMethodsOptions: any[] = [];
  relatedOptions: any[] = [];
  bucketOptions: any[] = [];
  showBucket: boolean = false;
  expense: any;
  config: CCFileUploaderConfig = {
    maxFilesize: 5,
  };
  constructor(
    private oneTimeExpenseRequestService: OneTimeExpenseRequestService,
    private fb: FormBuilder,
    public readonly notifications: CCNotificationService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.getCurrencies();
    this.getExpenses();
    this.handleFormChanges();
  }
  handleFormChanges() {
    this.formGroup.controls['paymentMethod'].valueChanges.subscribe((val) => {
      if (val) {
        this.showBucket = false;
        this.formGroup.controls['selectedBucket'].setValue('');
        if (
          val == 'CASH' &&
          this.expense.fromCashBuckets &&
          this.expense.fromCashBuckets.length > 1
        ) {
          this.showBucket = true;
          this.formGroup.controls['selectedBucket'].addValidators(
            Validators.required
          );
          this.formGroup.controls['selectedBucket'].updateValueAndValidity();
          this.bucketOptions = this.expense.fromCashBuckets.map((item: any) => {
            return { id: item.id, text: item.name };
          });
          this.formGroup.controls['selectedBucket'].setValue(
            this.expense?.fromCashBucket?.id
          );
        } else {
          this.formGroup.controls['selectedBucket'].removeValidators(
            Validators.required
          );
          this.formGroup.controls['selectedBucket'].updateValueAndValidity();
        }
        if (
          val == 'CREDIT_CARD' &&
          this.expense.fromCreditCardBuckets &&
          this.expense.fromCreditCardBuckets.length > 1
        ) {
          this.showBucket = true;
          this.formGroup.controls['selectedBucket'].addValidators(
            Validators.required
          );
          this.formGroup.controls['selectedBucket'].updateValueAndValidity();
          this.bucketOptions = this.expense.fromCreditCardBuckets.map(
            (item: any) => {
              return { id: item.id, text: item.name };
            }
          );
          this.formGroup.controls['selectedBucket'].setValue(
            this.expense?.fromCreditCardBucket?.id
          );
        } else {
          this.formGroup.controls['selectedBucket'].removeValidators(
            Validators.required
          );
          this.formGroup.controls['selectedBucket'].updateValueAndValidity();
        }
        if (val == 'CASH') {
          this.formGroup.controls['cashier'].addValidators(Validators.required);
          this.formGroup.controls['cashier'].updateValueAndValidity();
        } else {
          this.formGroup.controls['cashier'].removeValidators(
            Validators.required
          );
          this.formGroup.controls['cashier'].updateValueAndValidity();
        }
      } else {
        this.paymentMethodsOptions = [];
        this.bucketOptions = [];
        this.showBucket = false;
        this.formGroup.controls['selectedBucket'].setValue('');
        this.formGroup.controls['paymentMethod'].setValue('');
        this.formGroup.controls['selectedBucket'].removeValidators(
          Validators.required
        );
        this.formGroup.controls['selectedBucket'].updateValueAndValidity();
      }
    });
    this.formGroup.controls['selectedExpense'].valueChanges.subscribe((val) => {
      this.supplierOptions = [];
      this.paymentMethodsOptions = [];
      this.formGroup.controls['selectedSupplier'].setValue('');
      this.formGroup.controls['paymentMethod'].setValue('');
      if (val) {
        this.showBucket = false;
        this.oneTimeExpenseRequestService
          .getExpenses(val.id)
          .subscribe((res) => {
            this.supplierOptions = res.suppliers
              ? res.suppliers.map((item: any) => {
                  return { id: item.id, text: item.label };
                })
              : [];
            this.relatedOptions = res.relatedTos
              ? res.relatedTos.map((item: any) => {
                  return {
                    id: item.relatedToType.value,
                    text: item.relatedToType.value,
                  };
                })
              : [];
            this.paymentMethodsOptions = res.paymentMethods
              ? res.paymentMethods.map((item: any) => {
                  return { id: item.value, text: item.label };
                })
              : [];
            if (this.relatedOptions.length === 1) {
              this.formGroup.controls['relatedToType'].setValue(
                this.relatedOptions[0].id
              );
            }
            this.expense = res;
          });
      } else {
        this.supplierOptions = [];
        this.relatedOptions = [];
        this.expense = null;
      }
    });
    this.formGroup.controls['invoiceContainVat'].valueChanges.subscribe((val) => {
      if (val === true) {
        this.formGroup.controls['vatAmount'].addValidators(Validators.required);
        this.formGroup.controls['vatAmount'].updateValueAndValidity();
      } else {
        this.formGroup.controls['vatAmount'].removeValidators(Validators.required);
        this.formGroup.controls['vatAmount'].updateValueAndValidity();
      }
    });
  }
  getCurrencies() {
    return this.oneTimeExpenseRequestService
      .getCurrencies()
      .subscribe((res: any) => {
        this.currencyOptions = res;
      });
  }
  getExpenses() {
    return this.oneTimeExpenseRequestService
      .expenseOptions()
      .subscribe((res: any) => {
        this.expenseOptions = res;
      });
  }
  create() {
    if (this.formGroup.controls['relatedToType'].value === 'TEAM') {
      this.formGroup.controls['relatedToId'].setValue(
        this.formGroup.controls['relatedToIdTeam'].value
      );
    }
    if (this.formGroup.controls['relatedToType'].value === 'APPLICANT') {
      this.formGroup.controls['relatedToId'].setValue(
        this.formGroup.controls['relatedToIdApplicant'].value
      );
    }
    if (this.formGroup.controls['relatedToType'].value === 'MAID') {
      this.formGroup.controls['relatedToId'].setValue(
        this.formGroup.controls['relatedToIdHousmaid'].value
      );
    }
    if (this.formGroup.controls['relatedToType'].value === 'OFFICE_STAFF') {
      this.formGroup.controls['relatedToId'].setValue(
        this.formGroup.controls['relatedToIdOfficeStaff'].value
      );
    }
    let data: any = {};
    data = {
      expenseRequestType: 'ONE_TIME',
      beneficiaryType: 'SUPPLIER',
      beneficiaryId: this.formGroup.controls['selectedSupplier'].value.id,
      amount: this.formGroup.controls['amount'].value,
      attachments: [],
      currency: { id: this.formGroup.controls['selectedCurrency'].value },
      expense: { id: this.formGroup.controls['selectedExpense'].value.id },
      requestor: { id: this.formGroup.controls['selectedRequestor'].value },
      notes: this.formGroup.controls['notes'].value,
      relatedToId: this.formGroup.controls['relatedToId'].value,
      relatedToType: this.formGroup.controls['relatedToType'].value,
      invoiceAttached: this.formGroup.controls['invoiceAttached'].value,
    };
    if (this.formGroup.controls['expenseAttachment'].value) {
      data.attachments.push(
        this.formGroup.controls['expenseAttachment'].value[0]
      );
    }
    if (this.formGroup.controls['invoiceAttachment'].value) {
      data.attachments.push(
        this.formGroup.controls['invoiceAttachment'].value[0]
      );
    }
    if (this.formGroup.controls['invoiceVatAttachment'].value) {
      data.attachments.push(
        this.formGroup.controls['invoiceVatAttachment'].value[0]
      );
    }
    if (this.formGroup.controls['invoiceContainVat'].value === true) {
      data.vatAmount = this.formGroup.controls['vatAmount'].value;
    }
    if (this.showBucket) {
      data.bucket = { id: this.formGroup.controls['selectedBucket'].value };
    }
    if (
      (this.formGroup.controls['paymentMethod'].value === 'CASH' &&
        this.expense?.fromCashBuckets?.length === 1) ||
      (this.formGroup.controls['paymentMethod'].value === 'CREDIT_CARD' &&
        this.expense?.fromCreditCardBuckets?.length === 1)
    ) {
      data.bucket =
        this.formGroup.controls['paymentMethod'].value === 'CASH'
          ? { id: this.expense?.fromCashBuckets[0].id }
          : { id: this.expense?.fromCreditCardBuckets[0].id };
    }
    if (this.formGroup.controls['paymentMethod'].value) {
      data.paymentMethod = this.formGroup.controls['paymentMethod'].value;
    }
    if (this.formGroup.controls['paymentMethod'].value === 'CASH') {
      data.cashier = { id: this.formGroup.controls['cashier'].value };
    }
    this.oneTimeExpenseRequestService.createExpense(data).subscribe({
      next: (res) => {
        this.notifications.notifySuccess('Done Successfully');
        this.cancel();
      },
    });
  }
  cancel() {
    this.router.navigateByUrl('/accounting/v2/request-expense');
  }
}
