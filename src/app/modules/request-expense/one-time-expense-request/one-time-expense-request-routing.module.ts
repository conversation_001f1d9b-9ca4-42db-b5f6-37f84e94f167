import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { OneTimeExpenseRequestComponent } from './components/one-time-expense-request/one-time-expense-request.component';

const routes: Routes = [
  { path: '', component: OneTimeExpenseRequestComponent },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class OneTimeExpenseRequestRoutingModule {}
