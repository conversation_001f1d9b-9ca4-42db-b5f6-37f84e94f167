<div class="container acc-7849 ">
  <form [formGroup]="formGroup">
    <cc-select
      class="w-100"
      label="Expense"
      [data]="expenses"
      formControlName="expense"
      [required]="true"
      [emitFullSelectOption]="true"
    ></cc-select>
    <div formArrayName="usage" class="row">
      <label class="col-md-auto required-label">Usage :</label>
      <div
        class="col-md-auto"
        *ngFor="let control of usageArrayControls; let i = index"
      >
        <cc-checkbox [formControlName]="i" color="accent">
          {{ usageOptions[i]?.text }}
        </cc-checkbox>
      </div>
    </div>
    <cc-input
      label="Invoice Number"
      formControlName="invoiceNumber"
      [required]="true"
    ></cc-input>
    <cc-file-uploader
      label="Attach Invoice"
      formControlName="invoiceAttachment"
      [required]="true"
      tag="EXPENSE_REQUEST_INVOICE"
      [dropzoneConfig]="config"
    ></cc-file-uploader>
    <cc-input
      label="Total Amount (AED)"
      type="number"
      formControlName="amount"
      [required]="true"
    ></cc-input>
    <cc-input
      [ngClass]="{ 'd-none': !supplier?.vatRegistered }"
      label="VAT Amount (AED)"
      type="number"
      formControlName="vatAmount"
      [required]="true"
    ></cc-input>
  </form>
  <div class="d-flex justify-content-end">
    <div class="col-md-auto">
      <button
        cc-raised-button
        [disabled]="formValidation()"
        (click)="sendRequest()"
        color="accent"
      >
        Send Request
      </button>
    </div>
    <div class="col-md-auto">
      <button cc-raised-button (click)="cancel()">Cancel</button>
    </div>
  </div>
</div>
