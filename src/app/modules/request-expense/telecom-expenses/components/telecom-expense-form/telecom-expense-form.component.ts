import { Component, OnInit } from '@angular/core';
import { TelecomExpenseService } from '../../services/telecom-expense.service';
import { FormArray, FormBuilder, Validators } from '@angular/forms';
import { CCNotificationService } from '@maids/cc-lib/services';
import { Router } from '@angular/router';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators'; // Add this import
import { CCFileUploaderConfig } from '@maids/cc-lib/file-uploader';

@Component({
  selector: 'app-telecom-expense-form',
  templateUrl: './telecom-expense-form.component.html',
  styleUrls: ['./telecom-expense-form.component.scss'],
})
export class TelecomExpenseFormComponent implements OnInit {
  expenses: any | null = null;
  supplier: any | null = null;
  usageOptions: any[] = [];
  formGroup = this.formBuilder.group({
    expense: [null, [Validators.required]],
    usage: this.formBuilder.array([], [Validators.required]),
    invoiceNumber: [null, [Validators.required]],
    invoiceAttachment: [null, [Validators.required]],
    amount: [null, [Validators.required]],
    vatAmount: [null],
  });
  config: CCFileUploaderConfig = {
    maxFilesize: 5,
  };
  constructor(
    private telecomExpenseService: TelecomExpenseService,
    private formBuilder: FormBuilder,
    public readonly notifications: CCNotificationService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.getExpenses();
    this.formGroup.controls['expense'].valueChanges.subscribe((value) => {
      this.supplier = null;
      if (value) {
        this.getUsages();
      } else {
        this.usageOptions = [];
        (this.formGroup.get('usage') as FormArray).clear();
      }
    });
    this.subscribeToUsageChanges();
  }
  getUsages() {
    const expenseValue = this.formGroup.controls['expense'].value;
    if (!expenseValue) return;

    const usageArray = this.formGroup.get('usage') as FormArray;
    usageArray.clear();

    this.telecomExpenseService
      .getUsages(expenseValue.id)
      .subscribe((res) => {
        this.usageOptions = res;
        this.usageOptions.forEach(() => {
          usageArray.push(this.formBuilder.control(false));
        });
        console.log(this.usageOptions);
      });
  }

  subscribeToUsageChanges() {
    this.formGroup.controls['usage'].valueChanges
      .pipe(debounceTime(300), distinctUntilChanged())
      .subscribe((value) => {
        let notificationSent = false;
        value.forEach((checked: boolean, index: number) => {
          if (checked) {
            const usage = this.usageOptions[index];
            if (
              usage.expense.suppliers &&
              usage.expense.suppliers.length !== 1
            ) {
              if (!notificationSent) {
                this.notifications.notifyError(
                  'Expense should have one supplier'
                );
                notificationSent = true;
              }
              this.supplier = null;
              return;
            }
            this.supplier = Array.isArray(usage.expense.suppliers)
              ? usage.expense.suppliers[0]
              : usage.expense.suppliers;
            if (this.supplier.vatRegistered) {
              this.formGroup.controls['vatAmount'].setValidators([
                Validators.required,
              ]);
            } else {
              this.formGroup.controls['vatAmount'].clearValidators();
            }
            this.formGroup.controls['vatAmount'].updateValueAndValidity();
          }
        });
      });
  }

  getExpenses() {
    this.telecomExpenseService.getExpenses().subscribe((res) => {
      this.expenses = res;
    });
  }

  get usageArrayControls() {
    return (this.formGroup.get('usage') as FormArray).controls;
  }

  getSelectedUsageIds(): number[] {
    return this.formGroup.value.usage
      .map((checked: boolean, index: number) =>
        checked ? this.usageOptions[index].id : null
      )
      .filter((id: number | null) => id !== null);
  }
  sendRequest() {
    let payload: any;
    payload = {
      expenseRequestType: 'TELECOM',
      expense: { id: this.formGroup.controls['expense'].value.id },
      telecomPhones: this.getSelectedUsageIds().map((id) => ({ id })),
      invoiceNumber: this.formGroup.controls['invoiceNumber'].value,
      attachments: [this.formGroup.controls['invoiceAttachment'].value[0]],
      amount: +this.formGroup.controls['amount'].value,
      vatAmount: this.formGroup.controls['vatAmount'].value
        ? this.formGroup.controls['vatAmount'].value
        : null,
    };
    this.telecomExpenseService.sendRequest(payload).subscribe({
      next: (res: any) => {
        this.notifications.notifySuccess(
          'Telecom expense request added successfully!'
        );
        this.cancel();
      },
    });
  }

  formValidation() {
    return (
      this.formGroup.invalid ||
      !this.formGroup.controls['usage'].value.includes(true)
    );
  }

  cancel() {
    this.router.navigateByUrl('/accounting/v2/request-expense');
  }
}
