import { Component, OnInit } from '@angular/core';
import { TicketingExpensesService } from '../../services/ticketing-expenses.service';
import { PaginationRequest } from '@maids/cc-lib/common';
import { map, Observable } from 'rxjs';
import { FormBuilder, FormControl, Validators } from '@angular/forms';
import { CCNotificationService } from '@maids/cc-lib/services';
import { CCValidatorFn } from '@maids/cc-lib/validation';
import { ActivatedRoute, Router } from '@angular/router';
import { CCDialog } from '@maids/cc-lib/dialog';
import { CCFileUploaderConfig } from '@maids/cc-lib/file-uploader';

@Component({
  selector: 'app-ticketing-expenses-form',
  templateUrl: './ticketing-expenses-form.component.html',
  styleUrls: ['./ticketing-expenses-form.component.scss'],
})
export class TicketingExpensesFormComponent implements OnInit {
  formGroup = this.formBuilder.group({
    expense: [null, [Validators.required]],
    relatedToType: [null, [Validators.required]],
    selectedBucket: [null],
    relatedToIdTeam: [null],
    relatedToIdApplicant: [null],
    relatedToIdHousemaid: [null],
    relatedToIdOfficeStaff: [null],
    relatedToId: [null],
    singleBucketId: [null],
    paymentMethod: [null],
    type: [null, [Validators.required]],
    departOn: [null, [Validators.required]],
    returnOn: [null],
    departureCountry: [null, [Validators.required]],
    departureAirport: [null, [Validators.required]],
    arrivalCountry: [null, [Validators.required]],
    arrivalAirport: [null, [Validators.required]],
    amount: [null, [Validators.required]],
    currency: [null, [Validators.required]],
    beneficiaryId: [null, [Validators.required]],
    ticketNumber: [null],
    ticketAttachment: [null, [Validators.required]],
    paymentLink: [null, [Validators.required]],
    percentageDeductedFromEmployee: [null],
    notes: [null, [Validators.required]],
    cashier: [null],
  });
  readonly cashPayerOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.ticketingExpensesService.getCashiersOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };  
  config: CCFileUploaderConfig = {
    maxFilesize: 5,
  };
  readonly expenseOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.ticketingExpensesService
      .getTicketingExpenses('TICKETING', pageReq.searchString)
      .pipe(
        map((res) => {
          this.allExpenseOptions = res;
          return res.map((item: any) => {
            return {
              text: item.label,
              id: item.id,
              code: item.code,
            };
          });
        })
      );
  };
  suppliersOptions: any[] = [];
  allExpenseOptions: any[] = [];
  bucketOptions: any[] = [];
  paymentMethodOptions: any[] = [];
  relatedOptions: any[] = [];
  payment: any | null = null;
  showBucket: boolean = false;
  singleBucket: boolean = false;
  isExpenseCode: boolean = false;
  readonly holderOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.ticketingExpensesService.getTicketingUsers(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly teamOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.ticketingExpensesService
      .getTeams(pageReq.page, pageReq.size, pageReq.searchString)
      .pipe(
        map((res) => {
          return res.map((item: any) => {
            return { text: item.label, id: item.id };
          });
        })
      );
  };
  readonly applicantOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.ticketingExpensesService
      .getApplicants(pageReq.page, pageReq.size, pageReq.searchString)
      .pipe(
        map((res) => {
          return res.content.map((item: any) => {
            return { text: item.name, id: item.id };
          });
        })
      );
  };
  readonly housemaidOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.ticketingExpensesService
      .getHousemaids(pageReq.page, pageReq.size, pageReq.searchString)
      .pipe(
        map((res) => {
          return res.content.map((item: any) => {
            return { text: item.label, id: item.id };
          });
        })
      );
  };
  readonly officeStaffOptions = (
    pageReq: PaginationRequest
  ): Observable<any> => {
    return this.ticketingExpensesService
      .getOfficeStaffs(pageReq.page, pageReq.size, pageReq.searchString)
      .pipe(
        map((res) => {
          return res.content.map((item: any) => {
            return { text: item.label, id: item.id };
          });
        })
      );
  };
  readonly countryOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.ticketingExpensesService
      .getCountries(pageReq.page, pageReq.size, pageReq.searchString)
      .pipe(
        map((res) => {
          return res.map((item: any) => {
            return { text: item.label, id: item.id };
          });
        })
      );
  };
  readonly airportOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.ticketingExpensesService
      .getAirports(pageReq.page, pageReq.size, pageReq.searchString)
      .pipe(
        map((res) => {
          return res.map((item: any) => {
            return { text: item.label, id: item.id };
          });
        })
      );
  };
  readonly getCurrencies = (pageReq: PaginationRequest): Observable<any> => {
    return this.ticketingExpensesService
      .getCurrencies(pageReq.page, pageReq.size, pageReq.searchString)
      .pipe(
        map((res) => {
          return res.map((item: any) => {
            return { text: item.label, id: item.id };
          });
        })
      );
  };
  constructor(
    private ticketingExpensesService: TicketingExpensesService,
    private formBuilder: FormBuilder,
    public readonly notifications: CCNotificationService,
    private route: ActivatedRoute,
    private router: Router,
    private ccDialog: CCDialog
  ) {}

  ngOnInit(): void {
    this.isExpenseCode = true;
    this.formValidation();
  }
  formValidation() {
    this.formGroup.controls['expense'].valueChanges.subscribe((value) => {
      this.paymentMethodOptions = [];
      this.formGroup.controls['paymentMethod'].setValue('');
      this.showBucket = false;
      this.singleBucket = false;
      if (value) {
        this.getTicketingExpense(value);
        let options: any;
        options = this.allExpenseOptions.filter(
          (item: any) => item.id == value
        )[0];
        this.paymentMethodOptions = options;
        if (
          options?.paymentMethods?.some((pm: any) => pm.value == 'CASH') &&
          options.fromCashBuckets?.length > 1
        ) {
          this.showBucket = true;
          this.bucketOptions = options?.fromCashBuckets?.map((item: any) => {
            return { text: item.name, id: item.id };
          });
          this.formGroup.controls['selectedBucket'].setValue(
            options?.fromCashBucket?.id
          );
        }
        if (
          options?.paymentMethods?.some((pm: any) => pm.value == 'CASH') &&
          options.fromCashBuckets?.length == 1
        ) {
          this.singleBucket = true;
          this.formGroup.controls['singleBucketId'].setValue(
            options?.fromCashBuckets[0]?.id
          );
        }
        if (
          options?.paymentMethods?.some(
            (pm: any) => pm.value == 'CREDIT_CARD'
          ) &&
          options.fromCreditCardBuckets?.length > 1
        ) {
          this.showBucket = true;
          this.bucketOptions = options?.fromCreditCardBuckets?.map(
            (item: any) => {
              return { text: item.name, id: item.id };
            }
          );
          this.formGroup.controls['selectedBucket'].setValue(
            options?.fromCreditCardBucket?.id
          );
        }
        if (
          options?.paymentMethods?.some(
            (pm: any) => pm.value == 'CREDIT_CARD'
          ) &&
          options.fromCreditCardBuckets?.length == 1
        ) {
          this.singleBucket = true;
          this.formGroup.controls['singleBucketId'].setValue(
            options?.fromCreditCardBuckets[0]?.id
          );
        }
        this.formGroup.controls['relatedToType'].reset();
        this.formGroup.controls['relatedToType'].enable();
      } else {
        this.formGroup.controls['relatedToType'].reset();
        this.formGroup.controls['relatedToType'].disable();
        this.relatedOptions = [];
      }
    });
    this.formGroup.controls['paymentMethod'].valueChanges.subscribe((value) => {
      this.showBucket = false;
      this.formGroup.controls['selectedBucket'].setValue(null);
      if (
        (value == 'CASH' && this.payment?.fromCashBuckets?.length > 1) ||
        (value == 'CREDIT_CARD' &&
          this.payment?.fromCreditCardBuckets?.length > 1)
      ) {
        this.showBucket = true;
        if (value == 'CASH') {
          this.bucketOptions = this.payment?.fromCashBuckets?.map(
            (item: any) => {
              return { text: item.name, id: item.id };
            }
          );
        }
        if (value == 'CREDIT_CARD') {
          this.bucketOptions = this.payment?.fromCreditCardBuckets?.map(
            (item: any) => {
              return { text: item.name, id: item.id };
            }
          );
        }
        this.formGroup.controls['selectedBucket'].setValue(
          this.formGroup.controls['paymentMethod'].value == 'CASH'
            ? this.payment?.fromCashBucket?.id
            : this.payment?.fromCreditCardBucket?.id
        );
      }
    });
    this.formGroup.controls['type'].valueChanges.subscribe((value) => {
      if (value == 'ONE_WAY') {
        this.formGroup.controls['returnOn'].setValue(null);
        this.formGroup.controls['returnOn'].clearValidators();
        this.formGroup.controls['returnOn'].updateValueAndValidity();
      }
      if (value == 'ROUND_TRIP') {
        this.formGroup.controls['returnOn'].setValue(null);
        this.formGroup.controls['returnOn'].setValidators([Validators.required]);
        this.formGroup.controls['returnOn'].updateValueAndValidity();
      }
    });
  }
  getTicketingExpense(id: number) {
    this.ticketingExpensesService.getTicketingExpense(id).subscribe({
      next: (res) => {
        this.payment = res;
        if (res.suppliers) {
          this.suppliersOptions = [...res.suppliers].map((item: any) => {
            return {
              text: item.label,
              id: item.id,
              isTicketNumberRequired: item.isTicketNumberRequired,
            };
          });
        }
        this.paymentMethodOptions = res.paymentMethods.map((item: any) => {
          return { text: item.label, id: item.value };
        });
        if (this.paymentMethodOptions.length > 1) {
          this.formGroup.controls['paymentMethod'].setValidators([
            Validators.required,
          ]);
          this.formGroup.controls['paymentMethod'].updateValueAndValidity();
        }else{
          this.formGroup.controls['paymentMethod'].setValidators([]);
          this.formGroup.controls['paymentMethod'].updateValueAndValidity();
        }
        if (res.relatedTos.length) {
          let relatedTos: any[] = [];
          res.relatedTos.forEach((item: any) => {
            relatedTos.push({
              id: item.relatedToType.value,
              text: this.formatText(item.relatedToType.value),
            });
          });
          this.relatedOptions = relatedTos;
        }
      },
    });
  }
  sendRequest() {
    if (this.formGroup.controls['relatedToType'].value === 'TEAM') {
      this.formGroup.controls['relatedToId'].setValue(
        this.formGroup.controls['relatedToIdTeam'].value
      );
    }
    if (this.formGroup.controls['relatedToType'].value === 'APPLICANT') {
      this.notifications.notifyError(
        "Booking a ticket for an AT applicant should be done through the applicant's profile"
      );
      return;
    }
    if (this.formGroup.controls['relatedToType'].value === 'MAID') {
      this.formGroup.controls['relatedToId'].setValue(
        this.formGroup.controls['relatedToIdHousemaid'].value
      );
    }
    if (this.formGroup.controls['relatedToType'].value === 'OFFICE_STAFF') {
      this.formGroup.controls['relatedToId'].setValue(
        this.formGroup.controls['relatedToIdOfficeStaff'].value
      );
    }
    let payload: any;
    payload = {
      expenseRequestType: 'TICKETING',
      beneficiaryId: +this.formGroup.controls['beneficiaryId'].value.id,
      beneficiaryType: 'SUPPLIER',
      relatedToId: this.formGroup.controls['relatedToId'].value,
      relatedToType: this.formGroup.controls['relatedToType'].value,
      amount: this.formGroup.controls['amount'].value,
      autoReplenishment: false,
      currency: { id: this.formGroup.controls['currency'].value },
      disabled: false,
      expense: { id: this.formGroup.controls['expense'].value },
      notes: this.formGroup.controls['notes'].value,
      paymentLink: this.formGroup.controls['paymentLink'].value,
      percentageDeductedFromEmployee:
        this.formGroup.controls['percentageDeductedFromEmployee'].value,
      ticket: {
        type: this.formGroup.controls['type'].value,
        departOn: this.formGroup.controls['departOn'].value
          ? this.formGroup.controls['departOn'].value + ' 00:00:00'
          : null,
        returnOn:
          this.formGroup.controls['type'].value == 'ROUND_TRIP'
            ? this.formGroup.controls['returnOn'].value + ' 00:00:00'
            : null,
        ticketNumber: this.formGroup.controls['ticketNumber'].value,
        departureCountry: {
          id: this.formGroup.controls['departureCountry'].value,
        },
        departureAirport: {
          id: this.formGroup.controls['departureAirport'].value,
        },
        arrivalCountry: { id: this.formGroup.controls['arrivalCountry'].value },
        arrivalAirport: { id: this.formGroup.controls['arrivalAirport'].value },
      },
    };
    if (this.formGroup.controls['ticketAttachment'].value[0].id) {
      payload.attachments = [
        this.formGroup.controls['ticketAttachment'].value[0],
      ];
    }
    if (this.showBucket) {
      payload.bucket = { id: +this.formGroup.controls['selectedBucket'].value };
    }
    if (!this.showBucket && this.singleBucket) {
      payload.bucket = { id: this.formGroup.controls['singleBucketId'].value };
    }
    if (
      (this.formGroup.controls['paymentMethod'].value == 'CASH' &&
        this.payment.fromCashBuckets && this.payment.fromCashBuckets.length === 1) ||
      (this.formGroup.controls['paymentMethod'].value == 'CREDIT_CARD' &&
        this.payment.fromCreditCardBuckets && this.payment.fromCreditCardBuckets.length === 1)
    ) {
      payload.bucket = {
        id:
          this.formGroup.controls['paymentMethod'].value == 'CASH'
            ? this.payment.fromCashBuckets[0].id
            : this.payment.fromCreditCardBuckets[0].id,
      };
    }
    if (this.formGroup.controls['paymentMethod'].value) {
      payload.paymentMethod = this.formGroup.controls['paymentMethod'].value;
    }
    if (this.formGroup.controls['paymentMethod'].value == 'CASH') {
      payload.cashier = { id: this.formGroup.controls['cashier'].value };
    }
    this.ticketingExpensesService.createTicketRequest(payload).subscribe({
      next: (res) => {
        this.notifications.notifySuccess(
          'Ticketing expense request added successfully!'
        );
        this.cancel();
      },
    });
  }
  cancel() {
    this.router.navigateByUrl('/accounting/v2/request-expense');
  }

  addSupplier() {
    window.open('#!/accounting/v2/suppliers-list/form', '_blank');
  }
  formatText(value: string): string {
    return value
      .replace(/_/g, ' ') // Replace underscores with spaces
      .toLowerCase() // Convert to lowercase
      .replace(/\b\w/g, (char) => char.toUpperCase()); // Capitalize each word
  }
}
