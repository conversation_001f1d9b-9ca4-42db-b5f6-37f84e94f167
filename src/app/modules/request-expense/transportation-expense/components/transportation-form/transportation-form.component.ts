import { Component, OnDestroy, OnInit } from '@angular/core';
import { TransportationService } from '../../services/transportation.service';
import { FormBuilder } from '@angular/forms';
import { PaginationRequest } from '@maids/cc-lib/common';
import { Observable, Subscription } from 'rxjs';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { MediaService } from '@maids/cc-lib/services';
import { CCNotificationService } from '@maids/cc-lib/services';
import { CCDialog } from '@maids/cc-lib/dialog';
import { METHODS } from 'http';
import { Router } from '@angular/router';
import { CCFileUploaderConfig } from '@maids/cc-lib/file-uploader';

@Component({
  selector: 'app-transportation-form',
  templateUrl: './transportation-form.component.html',
  styleUrls: ['./transportation-form.component.scss'],
})
export class TransportationFormComponent implements OnInit, OnDestroy {
  taxiRidesInERP: any | null = null;
  ERPgridCols: CCGridColumn[] = [
    { field: 'bookingId', header: 'Booking ID' },
    { field: 'erpAmount', header: 'Amount in ERP' },
    { field: 'passenger', header: 'Passenger' },
    { field: 'purpose', header: 'Purpose' },
    { field: 'pickupLocation', header: 'Pickup location' },
    { field: 'dropOffLocation', header: 'Drop off location' },
    { field: 'date', header: 'Date', type: 'date' },
    { field: 'attachment', header: 'Attachment on ERP' },
  ];
  taxiRidesInExcel: any | null = null;
  ExcelgridCols: CCGridColumn[] = [
    { field: 'bookingId', header: 'Booking ID' },
    { field: 'billAmount', header: 'Amount in excel sheet' },
    { field: 'passenger', header: 'Passenger' },
    { field: 'date', header: 'Date', type: 'date' },
    { field: 'pickupLocation', header: 'Pickup location' },
    { field: 'dropOffLocation', header: 'Drop off location' },
  ];
  config: CCFileUploaderConfig = {
    maxFilesize: 5,
  };
  formGroup = this.formBuilder.group({
    auditExcelAttachment: [null],
    invoiceAttachment: [null],
    paymentMethod: [null],
    beneficiaryId: [null],
    beneficiaryType: ['SUPPLIER'],
    invoiceNumber: [null],
    amount: [null],
    vatAmount: [null],
    requestFrom: [null],
    requestTo: [null],
    disabled: [false],
  });
  paymentMethodOptions: any[] = [
    { id: 'CASH', text: 'Cash' },
    { id: 'CREDIT_CARD', text: 'Credit Card' },
    { id: 'BANK_TRANSFER', text: 'Bank Transfer' },
    { id: 'MONEY_TRANSFER', text: 'Money Transfer' },
  ];
  readonly holderOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.transportationService.transportationHolders(
      pageReq.searchString
    );
  };
  suppliersOptions: any[] = [];
  paymentMethodsOptions: any[] = [];
  expense: any | null = null;
  supplier: any | null = null;
  showAuditingSection: boolean = false;
  difference: any;
  showButton: boolean = false;
  beneficiaryObj: any;
  private subscriptions: Subscription[] = [];
  constructor(
    private transportationService: TransportationService,
    private formBuilder: FormBuilder,
    private mediaService: MediaService,
    public readonly notifiactions: CCNotificationService,
    private ccDialog: CCDialog,
    private router: Router
  ) {}
  ngOnInit(): void {
    this.transportationService
      .transportationExpenses()
      .subscribe((res: any) => {
        if (res[0]) {
          this.getExpenseByCode(res[0].value);
        }
      });
    this.formGroup.controls['beneficiaryId'].valueChanges.subscribe((val) => {
      if (val) {
        this.getSupplierInfo();
      }
    });
    const fieldsToWatch = [
      'beneficiaryId',
      'requestFrom',
      'requestTo',
      'amount',
    ];
    fieldsToWatch.forEach((field) => {
      const control = this.formGroup.get(field);
      if (control) {
        const sub = control.valueChanges.subscribe(() => {
          if (this.areAllRequiredFieldsFilled()) {
            setTimeout(() => {
              this.transportationExpenseRequestNeedsInvoice();
            }, 300);
          }
        });
        this.subscriptions.push(sub);
      }
    });
  }
  private areAllRequiredFieldsFilled(): boolean {
    const { requestFrom, requestTo, beneficiaryId, amount } =
      this.formGroup.controls;
    return !!(
      beneficiaryId.value &&
      requestFrom.value &&
      requestTo.value &&
      amount.value
    );
  }
  transportationExpenseRequestNeedsInvoice() {
    const { requestFrom, requestTo, beneficiaryId, amount } =
      this.formGroup.controls;
    this.transportationService
      .transportationExpenseRequestNeedsInvoice(
        requestFrom.value,
        requestTo.value,
        beneficiaryId.value.text,
        amount.value
      )
      .subscribe((res: any) => {
        this.showButton = res.enableSendRequest;
        this.difference = res.difference;
        this.showAuditingSection = res.showAuditingSection;
      });
  }
  getExpenseByCode(code: any) {
    this.transportationService
      .transportationGetExpenses(code)
      .subscribe((res: any) => {
        if (Array.isArray(res)) {
          this.expense = res[0];
        } else {
          this.expense = res;
        }
        this.suppliersOptions = { ...this.expense }.suppliers.map(
          (item: any) => ({ id: item.id, text: item.label })
        );
        this.paymentMethodsOptions = { ...this.expense }.paymentMethods.map(
          (item: any) => ({ id: item.value, text: item.label })
        );
      });
  }
  getSupplierInfo() {
    this.transportationService
      .transportationGetSupplierInfo(
        this.formGroup.controls['beneficiaryId'].value.id
      )
      .subscribe((res: any) => {
        this.supplier = res;
      });
  }
  addSupplier() {
    window.open('#!//suppliers-list/form', '_blank');
  }
  auditInvoice() {
    if (this.formGroup.controls['auditExcelAttachment'].value.id) {
      this.notifiactions.notifyError(
        'Please attach audit excel sheet before auditing.'
      );
      return;
    }
    let payload: any;
    payload = {
      attachments: this.formGroup.controls['auditExcelAttachment'].value,
      requestFrom: this.formGroup.controls['requestFrom'].value + ' 00:00:00',
      requestTo: this.formGroup.controls['requestTo'].value + ' 23:59:59',
    };
    this.transportationService
      .transportationAuditInvoice(this.beneficiaryObj.text, payload)
      .subscribe({
        next: (res: any) => {
          this.taxiRidesInERP = res.erpTaxiOrders;
          this.taxiRidesInExcel = res.attachmentTaxiOrders;
        },
      });
  }
  exportCSV(getERPRecords: any) {
    if (!this.formGroup.controls['auditExcelAttachment'].value) {
      this.notifiactions.notifyError('No audit excel sheet attached!');
      return;
    }
    this.mediaService.downloadFile(
      'accounting/expenseRequestTodo/TransportationExpenseGenerateCSV?supplierName=' +
        this.formGroup.controls['beneficiaryId'].value.text +
        '&getERPRecords=' +
        getERPRecords,
      '',
      {
        method: 'POST',
        body: {
          attachments: this.formGroup.controls['auditExcelAttachment'].value,
          requestFrom:
            this.formGroup.controls['requestFrom'].value + ' 00:00:00',
          requestTo: this.formGroup.controls['requestTo'].value + ' 23:59:59',
        },
      }
    );
  }
  downloadAttachment(element: any) {
    this.mediaService.downloadFile(
      'public/download/' + element.attachment.uuid
    );
  }
  sendRequest() {
    let payload: any;
    payload = {
      expenseRequestType: 'TRANSPORTATION',
      attachments: this.formGroup.controls['invoiceAttachment'].value
        ? this.formGroup.controls['invoiceAttachment'].value
        : null,
      requestFrom: this.formGroup.controls['requestFrom'].value + ' 00:00:00',
      requestTo: this.formGroup.controls['requestTo'].value + ' 23:59:59',
      beneficiaryId: this.formGroup.controls['beneficiaryId'].value.id,
      autoReplenishment: false,
      invoiceNumber: this.formGroup.controls['invoiceNumber'].value,
      disabled: false,
      beneficiaryType: 'SUPPLIER',
      paymentMethod: this.formGroup.controls['paymentMethod'].value,
      amount: +this.formGroup.controls['amount'].value,
      vatAmount: this.formGroup.controls['vatAmount'].value
        ? +this.formGroup.controls['vatAmount'].value
        : null,
    };
    this.transportationService.transportationCreateExpense(payload).subscribe({
      next: (res: any) => {
        this.notifiactions.notifySuccess(
          ' Transportation expense request added successfully!'
        );
        this.cancel();
      },
    });
  }
  cancel() {
    this.router.navigateByUrl('/accounting/v2/request-expense');
  }
  ngOnDestroy() {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }
}
