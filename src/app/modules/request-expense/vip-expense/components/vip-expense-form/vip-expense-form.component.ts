import { Component, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { VipExpenseService } from '../../services/vip-expense.service';
import { PaginationRequest } from '@maids/cc-lib/common';
import { Observable } from 'rxjs';
import { CCNotificationService } from '@maids/cc-lib/services';
import { CCDialog } from '@maids/cc-lib/dialog';
import { Router } from '@angular/router';
import { CCFileUploaderConfig } from '@maids/cc-lib/file-uploader';

@Component({
  selector: 'app-vip-expense-form',
  templateUrl: './vip-expense-form.component.html',
  styleUrls: ['./vip-expense-form.component.scss'],
})
export class VipExpenseFormComponent implements OnInit {
  formGroup = this.formBuilder.group({
    expense: ['', [Validators.required]],
    paymentMethod: ['', [Validators.required]],
    beneficiaryType: [''],
    beneficiaryId: [''],
    beneficiaryIdSupplier: [''],
    beneficiaryIdHousemaid: [''],
    beneficiaryIdOfficeStaff: [''],
    beneficiaryName: [''],
    international: [false],
    beneficiaryMobileNumber: [''],
    beneficiaryIban: [''],
    beneficiaryHasNoIban: [false],
    beneficiaryAccountNumber: [''],
    beneficiaryAccountName: [''],
    swift: [''],
    address: [''],
    expenseAttachment: [''],
    amount: ['', [Validators.required]],
    currency: ['', [Validators.required]],
    notes: ['', [Validators.required]],
    paymentAlreadyPaid: [''],
    bucket: [''],
    cashier: [''],
  });
  config: CCFileUploaderConfig = {
    maxFilesize: 5,
  };
  paymentMethodsOptions: any[] = [];
  beneficiaryOptions: any[] = [
    { id: 'SUPPLIER', text: 'Supplier' },
    { id: 'MAID', text: 'Maid' },
    { id: 'OFFICE_STAFF', text: 'Office Staff' },
    { id: 'TAXI_DRIVER', text: 'Taxi Driver' },
    { id: 'NOT_DETERMINED', text: 'Not Determined' },
  ];
  readonly cashPayerOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.vipExpensesService.getCashiersOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly holderOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.vipExpensesService.vipExpensesHolders(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly bucketOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.vipExpensesService.vipBuckets(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly teamOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.vipExpensesService.vipOfficeStaffTeam(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly applicantOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.vipExpensesService.vipApplicants(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  suppliersOptions: any[] = [];
  readonly suppliersAjaxOptions = (
    pageReq: PaginationRequest
  ): Observable<any> => {
    return this.vipExpensesService.vipSuppliers(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly housemaidOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.vipExpensesService.vipHouseMaids(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  readonly officeStaffOptions = (
    pageReq: PaginationRequest
  ): Observable<any> => {
    return this.vipExpensesService.vipOfficeStaffs(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  expenseOptions: any[] = [];
  currencyOptions: any[] = [];
  payment: any;
  showBeneficiary: boolean = true;
  beneficiaryType: string = '';
  ibanValidated: boolean = false;
  constructor(
    private formBuilder: FormBuilder,
    private vipExpensesService: VipExpenseService,
    public readonly notifications: CCNotificationService,
    private ccDialog: CCDialog,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.vipGetCurrencies();
    this.vipExpenseForPage('VIP');
    this.formGroup.controls['expense'].valueChanges.subscribe((val: any) => {
      if (val) {
        this.vipExpenses(val);
      }
    });
    this.formGroup.controls['beneficiaryHasNoIban'].valueChanges.subscribe(
      (val: any) => {
        if (val) {
          this.formGroup.controls['beneficiaryIban'].setValue('');
        } else {
          this.formGroup.controls['beneficiaryAccountNumber'].setValue('');
        }
      }
    );
    this.formGroup.controls['beneficiaryType'].valueChanges.subscribe(
      (val: any) => {
        if (val == 'SUPPLIER') {
          this.formGroup.controls['beneficiaryId'].setValue(
            this.formGroup.controls['beneficiaryIdSupplier'].value
          );
        }
        if (val == 'MAID') {
          this.formGroup.controls['beneficiaryId'].setValue(
            this.formGroup.controls['beneficiaryIdHousemaid'].value
          );
        }
        if (val == 'OFFICE_STAFF') {
          this.formGroup.controls['beneficiaryId'].setValue(
            this.formGroup.controls['beneficiaryIdOfficeStaff'].value
          );
        }
        if (val == 'NOT_DETERMINED') {
          this.formGroup.controls['beneficiaryName'].addValidators(
            Validators.required
          );
          this.formGroup.controls['beneficiaryName'].updateValueAndValidity();
          this.formGroup.controls['paymentMethod'].valueChanges.subscribe(
            (val) => {
              if (val == 'BANK_TRANSFER') {
                this.formGroup.controls['international'].addValidators(
                  Validators.required
                );
                this.formGroup.controls[
                  'international'
                ].updateValueAndValidity();
                this.formGroup.controls['beneficiaryAccountName'].addValidators(
                  Validators.required
                );
                this.formGroup.controls[
                  'beneficiaryAccountName'
                ].updateValueAndValidity();
                this.formGroup.controls[
                  'beneficiaryHasNoIban'
                ].valueChanges.subscribe((val) => {
                  if (val == false) {
                    this.formGroup.controls['beneficiaryIban'].addValidators(
                      Validators.required
                    );
                    this.formGroup.controls[
                      'beneficiaryIban'
                    ].updateValueAndValidity();
                    this.formGroup.controls['beneficiaryIban'].enable();
                  } else {
                    this.formGroup.controls[
                      'beneficiaryAccountNumber'
                    ].addValidators(Validators.required);
                    this.formGroup.controls[
                      'beneficiaryAccountNumber'
                    ].updateValueAndValidity();
                    this.formGroup.controls[
                      'beneficiaryIban'
                    ].clearValidators();
                    this.formGroup.controls[
                      'beneficiaryIban'
                    ].updateValueAndValidity();
                    this.formGroup.controls['beneficiaryIban'].disable();
                  }
                });
              } else {
                this.formGroup.controls['international'].clearValidators();
                this.formGroup.controls[
                  'international'
                ].updateValueAndValidity();
                this.formGroup.controls['beneficiaryIban'].clearValidators();
                this.formGroup.controls[
                  'beneficiaryIban'
                ].updateValueAndValidity();
                this.formGroup.controls[
                  'beneficiaryAccountNumber'
                ].clearValidators();
                this.formGroup.controls[
                  'beneficiaryAccountNumber'
                ].updateValueAndValidity();
                this.formGroup.controls[
                  'beneficiaryAccountName'
                ].clearValidators();
                this.formGroup.controls[
                  'beneficiaryAccountName'
                ].updateValueAndValidity();
              }
              if (val == 'MONEY_TRANSFER') {
                this.formGroup.controls[
                  'beneficiaryMobileNumber'
                ].addValidators(Validators.required);
                this.formGroup.controls[
                  'beneficiaryMobileNumber'
                ].updateValueAndValidity();
              } else {
                this.formGroup.controls[
                  'beneficiaryMobileNumber'
                ].clearValidators();
                this.formGroup.controls[
                  'beneficiaryMobileNumber'
                ].updateValueAndValidity();
              }
              if (val == 'CASH') {
                this.formGroup.controls['cashier'].addValidators(
                  Validators.required
                );
                this.formGroup.controls['cashier'].updateValueAndValidity();
              } else {
                this.formGroup.controls['cashier'].clearValidators();
                this.formGroup.controls['cashier'].updateValueAndValidity();
              }
            }
          );
        } else {
          this.formGroup.controls['beneficiaryName'].clearValidators();
          this.formGroup.controls['beneficiaryName'].updateValueAndValidity();
        }
      }
    );
    this.formGroup.controls['paymentAlreadyPaid'].valueChanges.subscribe(
      (val) => {
        if (val == true) {
          this.formGroup.controls['bucket'].addValidators(Validators.required);
          this.formGroup.controls['bucket'].updateValueAndValidity();
        } else {
          this.formGroup.controls['bucket'].removeValidators(
            Validators.required
          );
          this.formGroup.controls['bucket'].updateValueAndValidity();
        }
      }
    );
    this.formGroup.controls['international'].valueChanges.subscribe((val) => {
      if (val == true) {
        this.formGroup.controls['swift'].addValidators(Validators.required);
        this.formGroup.controls['swift'].updateValueAndValidity();
        this.formGroup.controls['address'].addValidators(Validators.required);
        this.formGroup.controls['address'].updateValueAndValidity();
      } else {
        this.formGroup.controls['swift'].clearValidators();
        this.formGroup.controls['swift'].updateValueAndValidity();
        this.formGroup.controls['address'].clearValidators();
        this.formGroup.controls['address'].updateValueAndValidity();
      }
    });
  }
  vipExpenses(val: any) {
    this.vipExpensesService.vipExpenses(val).subscribe((res: any) => {
      this.payment = res;
      this.formGroup.controls['paymentMethod'].setValue('');
      this.paymentMethodsOptions = [];
      if (res?.paymentMethods?.length) {
        this.paymentMethodsOptions = res.paymentMethods.map((item: any) => {
          return { id: item.value, text: item.label };
        });
      } else {
        this.paymentMethodsOptions = [];
      }
      this.formGroup.controls['amount'].setValue(
        res.defaultAmount ? res.defaultAmount : ''
      );
      if (res.beneficiaryType) {
        this.beneficiaryOptions = [
          { id: res.beneficiaryType.value, text: res.beneficiaryType.label },
        ];
        this.showBeneficiary = false;
        this.beneficiaryType = res.beneficiaryType.value;
        this.formGroup.controls['beneficiaryType'].setValue(
          res.beneficiaryType.value
        );
        if (res.suppliers && res.suppliers.length) {
          this.suppliersOptions = [...res.suppliers].map((item: any) => {
            return { id: item.id, text: item.label };
          });
        } else {
          this.suppliersOptions = [];
        }
      }
    });
  }
  vipExpenseForPage(page: string) {
    this.vipExpensesService.vipExpenseForPage(page).subscribe((res: any) => {
      this.expenseOptions = res;
    });
  }
  vipGetCurrencies() {
    this.vipExpensesService.vipGetCurrencies().subscribe((res: any) => {
      this.currencyOptions = res;
    });
  }
  addSupplier() {
    window.open('#!/accounting/suppliers-list/form', '_blank');
  }
  save() {
    let payload: any = null;
    payload = {
      expenseRequestType: 'VIP',
      disabled: false,
      autoReplenishment: false,
      beneficiaryType: this.formGroup.controls['beneficiaryType'].value,
      expense: { id: this.formGroup.controls['expense'].value },
      currency: { id: this.formGroup.controls['currency'].value },
      bucket: this.formGroup.controls['bucket'].value
        ? { id: this.formGroup.controls['bucket'].value }
        : '',
      beneficiaryId: this.formGroup.controls['beneficiaryId'].value
        ? +this.formGroup.controls['beneficiaryId'].value
        : '',
      paymentAlreadyPaid: this.formGroup.controls['paymentAlreadyPaid'].value
        ? this.formGroup.controls['paymentAlreadyPaid'].value
        : '',
      paymentMethod: this.formGroup.controls['paymentMethod'].value
        ? this.formGroup.controls['paymentMethod'].value
        : '',
      amount: +this.formGroup.controls['amount'].value,
    };
    if (
      this.formGroup.controls['beneficiaryType'].value == 'NOT_DETERMINED' &&
      this.formGroup.controls['paymentMethod'].value == 'BANK_TRANSFER'
    ) {
      payload.international =
        this.formGroup.controls['international'].value == true ? true : false;
    }

    if (this.formGroup.controls['beneficiaryType'].value == 'SUPPLIER') {
      payload.beneficiaryId =
        this.formGroup.controls['beneficiaryIdSupplier'].value;
    }
    if (this.formGroup.controls['beneficiaryType'].value == 'MAID') {
      payload.beneficiaryId =
        this.formGroup.controls['beneficiaryIdHousemaid'].value;
    }
    if (this.formGroup.controls['beneficiaryType'].value == 'OFFICE_STAFF') {
      payload.beneficiaryId =
        this.formGroup.controls['beneficiaryIdOfficeStaff'].value;
    }
    if (this.formGroup.controls['beneficiaryType'].value == 'NOT_DETERMINED') {
      if (this.formGroup.controls['paymentMethod'].value == 'MONEY_TRANSFER') {
        payload.beneficiaryMobileNumber =
          this.formGroup.controls['beneficiaryMobileNumber'].value;
      }
      if (
        this.formGroup.controls['paymentMethod'].value == 'BANK_TRANSFER' &&
        this.formGroup.controls['beneficiaryHasNoIban'].value == false
      ) {
        if (this.ibanValidated) {
          payload.beneficiaryIban =
            this.formGroup.controls['beneficiaryIban'].value;
        } else {
          this.validateIban(this.formGroup.controls['beneficiaryIban'].value);
          return;
        }
      }
    }
    if (this.formGroup.controls['paymentMethod'].value == 'CASH') {
      payload.cashier = { id: this.formGroup.controls['cashier'].value };
    }
    this.submitForm(payload);
  }

  submitForm(payload: any) {
    this.vipExpensesService.vipCreateRequest(payload).subscribe({
      next: (res: any) => {
        this.notifications.notifySuccess(
          'VIP expense request added successfully!'
        );
        this.cancel();
      },
    });
  }
  validateIban(iban: number) {
    this.vipExpensesService.vipValidateAddIBAN(iban).subscribe({
      next: (res: any) => {
        if (res && res.picklistItemInfo) {
          this.ibanValidated = true;
          this.save();
        }
        if (res.validations && res.validations.iban.code != '001') {
          this.notifications.notifyError(res.validations.iban.message);
        }
      },
    });
  }
  cancel() {
    this.router.navigateByUrl('/accounting/v2/request-expense');
  }
}
