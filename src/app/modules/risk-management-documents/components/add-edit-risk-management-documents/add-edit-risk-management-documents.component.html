<cc-card>
  <cc-card-title style="font-weight: bolder">
    <b>Set up screen</b>
  </cc-card-title>
  <form #form="ngForm" [formGroup]="riskDocumentForm" (ngSubmit)="save()" class="row d-flex justify-content-around">
    <div class="col-md-6">
      <cc-input [required]="true" formControlName="name" label="Name"></cc-input>
    </div>
    <div class="col-md-6">
      <cc-select [required]="true" label="Government Entity" formControlName="governmentEntity" search="true"
                 [lazyPageFetcher]="governmentEntities"
      >
      </cc-select>
    </div>
    <div class="col-md-6">
      <cc-select [required]="true" formControlName="type" [emitFullSelectOption]="true" label="Type" search="true"
                 [lazyPageFetcher]="types">
      </cc-select>
    </div>
    <div class="col-md-6">
      <cc-input formControlName="risk" label="Risk"></cc-input>
    </div>
    <div class="col-md-6">
      <cc-select formControlName="importance" [required]="true" label="Importance" search="true"
                 [lazyPageFetcher]="importances">
      </cc-select>
    </div>
    <div class="col-md-6">
      <cc-datepicker [required]="true" [minDate]="minDate" formControlName="expiryDate" label="Expiry Date">

      </cc-datepicker>
    </div>
    <div class="col-md-6">
      <cc-select formControlName="relatesTo" [multiple]="true" label="Related To" [data]="relatedTos">
      </cc-select>
    </div>
    <div class="col-md-6">
      <cc-datepicker formControlName="issuanceDate" label="Issuance Date">
      </cc-datepicker>
    </div>
    <div class="col-md-6">
      <cc-datepicker [required]="form.value.type?.code == 'vehicles'" formControlName="registrationDate"
                     label="Registration Date"
                     [hidden]="form.value.type?.code !='vehicles'">
      </cc-datepicker>
    </div>
    <div class="col-md-6">
      <cc-datepicker [required]="form.value.type?.code == 'vehicles'" formControlName="insuranceExpirationDate"
                     label="Insurance Expiration Date"
                     [hidden]="form.value.type?.code !='vehicles'">
      </cc-datepicker>
    </div>
    <div class="col-md-6">
      <cc-select
        style="top:-1.5em"
        class="col-12 col-sm-12 col-md-12 col-lg-4"
        formControlName="accountablePerson"
        entityId="'accountablePerson'"
        [search]="true"
        [multiple]="true"
        [required]="true"
        [lazyPageFetcher]="getRoles"
        idProperty="id"
        textProperty="label"
        label="Accountable Person"
      >
      </cc-select
      >
    </div>
    <div class="col-md-6">
    </div>
    <div class="col-md-12 mt-3">
      <cc-file-uploader [dropzoneConfig]="{ maxFiles:5, maxFilesize: 10 }" [tag]="'RISK_DOCUMENT_MGMT_FILE'" label="Upload attachment"
                        formControlName="attachments" name="file"
                        (fileRemoved)="onFileRemoved($event)"></cc-file-uploader>
    </div>
    <div class="col-md-12" style="max-width: 98% !important; background-color: #F5F5F5; border-radius: 10px; padding: 1em; margin: 1em 0">
      <span class="text-left"> Requires Renewal </span>
      <cc-slide-toggle
        class="example-margin float-right" (change)="checkLayers($event)" formControlName="requiresRenewal">
      </cc-slide-toggle>
      <div class="col-md-12" style="transition: all 0.5s ease-in-out; padding: 3em"
           *ngIf="riskDocumentForm.value.requiresRenewal">
        <ng-container class="col-md-11" style="padding: 2em; margin: 0 .5em">
          <ng-container formArrayName="layers">
            <ng-container *ngFor="let layerForm of layers.controls; let i = index">
              <div class="row d-flex justify-content-around w-100 my-2 p-1" style="background-color: #EDEDED; margin: .2em 0 0 0; border-radius: 20px;"
                   formGroupName="{{ i }}">
                <div class="col-md-12" style="margin: 0 0 .5em 0">
                  Layer {{ i == 0 ? '1' : '2' }}:
                </div>
                <div class="col-md-6">
                  <cc-input type="number" [ccValidateBy]="[daysBeforeExpiryValidate]" label="Days Before Expiry"
                            formControlName="daysBeforeExpiry"></cc-input>
                </div>
                <div class="col-md-5">
                  <cc-select
                    style="top:-1.5em"
                    class="col-12 col-sm-12 col-md-12 col-lg-4"
                    [multiple]="true"
                    formControlName="assignee"
                    entityId="'assignee'"
                    [search]="true"
                    [lazyPageFetcher]="getRoles"
                    idProperty="id"
                    textProperty="label"
                    label="{{ i == 0 ? 'Assignee' : 'Notified User' }}"
                  ></cc-select>
                </div>
                <div class="col-md-1" [hidden]="i > 0">
                </div>
                <div class="col-md-1" [hidden]="i == 0">
                  <cc-icon class="delete-layer" style="color: red; cursor: pointer; position: relative; top: .5em"
                           (click)="deleteLayer(i)">delete
                  </cc-icon>
                </div>
              </div>
            </ng-container>
          </ng-container>
          <div class="row">
            <div class="col-md-2">
              <button
                class="iconed-btn ml-2"
                cc-flat-button
                color="accent"
                type="button"
                (click)="addNewLayer()"
              >
                <cc-icon class="icon">add</cc-icon>
                Add New Layer
              </button>
            </div>
          </div>
        </ng-container>
      </div>
    </div>
    <div class="col-md-12" style="max-width: 98% !important; background-color: #F5F5F5; border-radius: 10px; padding: 1em; margin: 1em .5em">
      <span class="text-left"> Requires Visit </span>
      <cc-slide-toggle
        class="example-margin float-right" formControlName="requiresVisit">
      </cc-slide-toggle>
      <div class="col-md-12 mt-2" style="padding: 3em"
           *ngIf="riskDocumentForm.value.requiresVisit">
        <div class="row">
          <div class="col-md-4">
            <cc-input formControlName="serviceReportName"
                      label="Service Report Name"></cc-input>
          </div>
          <div class="col-md-4">
            <cc-input
              formControlName="frequencyOfVisit" type="number"
              [ccValidateBy]="[frequencyOfVisitValidate]"
              label="Frequency of Visits"></cc-input>
          </div>
          <div class="cold-md-4">
            <cc-radio-group style="top: -.25em; position: relative;" [ccValidateBy]="[requireVisitValidate]"
                            formControlName="frequencyType">
              <cc-radio-button style="margin: 1em 1em;" value="WEEKLY">
                Weekly
              </cc-radio-button>
              <cc-radio-button value="MONTHLY">
                Monthly
              </cc-radio-button>
            </cc-radio-group>
          </div>
          <div class="col-md-4">
            <cc-datepicker [ccValidateBy]="[requireVisitValidate]" formControlName="lastVisitDate"
                           label="Last Visit Date"></cc-datepicker>
          </div>
          <div class="col-md-4">
            <cc-datepicker [ccValidateBy]="[requireVisitValidate]" formControlName="nextVisitDate"
                           label="Next Visit Date"></cc-datepicker>
          </div>
        </div>
      </div>
    </div>
    <div class="col-12 text-center">
      <button type="button" cc-raised-button class="m-1" color="primary" (click)="back()">
        Cancel
      </button>
      <button cc-raised-button class="m-1" [disabled]="!riskDocumentForm.valid" color="accent">
        Save
      </button>
    </div>
  </form>
</cc-card>
