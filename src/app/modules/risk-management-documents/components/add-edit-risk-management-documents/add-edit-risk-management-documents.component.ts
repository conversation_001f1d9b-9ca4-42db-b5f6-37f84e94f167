import {ChangeDetectionStrategy, ChangeDetectorRef, Component, OnDestroy, OnInit} from '@angular/core';
import {Store} from '@ngrx/store';
import {Location} from '@angular/common'
import {ActivatedRoute, Router} from '@angular/router';
import * as RiskManagementDocumentsActions from "../../store/risk-management-documents.actions";
import * as RiskManagementDocumentsSelectors from "../../store/risk-management-documents.selectors";
import {map} from 'rxjs/operators';
import {HttpParams} from "@angular/common/http";
import {
  RiskManagementDocumentsModel,
  RiskManagementDocumentsFormDataModel
} from "../../models/risk-management-documents.model";
import {Observable, Subscription} from "rxjs";
import {Actions, createEffect, ofType} from "@ngrx/effects";
import {CCNotificationService} from '@maids/cc-lib/services';
import {Form, FormArray, FormBuilder, FormControl, FormGroup, Validators} from "@angular/forms";
import {PaginationRequest} from "@maids/cc-lib/common";
import {RiskManagementDocumentsStoreService} from "../../services/risk-management-documents-store.service";
import {CCFileRemovedEvent} from "@maids/cc-lib/file-uploader";
import {SelectOption} from "@maids/cc-lib/select-input";
import {CCPicklistService} from "@maids/cc-erp-services";
import {RiskManagementDocumentsService} from "../../services/risk-management-documents.service";
import {CCValidatorFn} from "@maids/cc-lib/validation";
import * as moment from 'moment'

@Component({
  selector: 'app-add-edit-risk-management-documents',
  templateUrl: './add-edit-risk-management-documents.component.html',
  styleUrls: ['./add-edit-risk-management-documents.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [RiskManagementDocumentsStoreService]
})
export class RiskManagementDocumentsAddEditComponent implements OnInit, OnDestroy {

  private readonly subscriptions = new Subscription();

  riskDocumentForm: FormGroup = this.fb.group({
    name: ['', Validators.required],
    governmentEntity: ['', Validators.required],
    type: ['', Validators.required],
    risk: [''],
    importance: [''],
    expiryDate: [''],
    relatesTo: [''],
    issuanceDate: [''],
    registrationDate: [''],
    insuranceExpirationDate: [''],
    accountablePerson: '',
    active: true,
    requiresVisit: false,
    requiresRenewal: false,
    layers: this.fb.array([]),
    serviceReportName: [''],
    frequencyOfVisit: [''],
    frequencyType: [''],
    lastVisitDate: [''],
    nextVisitDate: [''],
    attachments: ['']
  });

  constructor(
    private _actions: Actions,
    private _store: Store,
    private store: RiskManagementDocumentsStoreService,
    private service: RiskManagementDocumentsService,
    private _route: ActivatedRoute,
    private location: Location,
    private notificaitonService: CCNotificationService,
    private picklistService: CCPicklistService,
    private fb: FormBuilder,
    private route: Router,
    private _cdr: ChangeDetectorRef
  ) {
  }

  readonly riskManagementDocumentsForm$ = this._store.select(RiskManagementDocumentsSelectors.selectRiskManagementDocumentsFormData);
  model: RiskManagementDocumentsFormDataModel = {}

  get layers(): FormArray {
    return this.riskDocumentForm.controls["layers"] as FormArray;
  }

  addNewLayer(): void {
    const layerTwo = this.fb.group({
      daysBeforeExpiry: 0,
      assignee: [],
      type: 'LAYER_TWO'
    });
    this.layers.push(layerTwo);
    console.log(this.riskDocumentForm.value);
  }

  deleteLayer(index: number): void {
    // Only delete if there are more than 1 layer and not trying to delete the first layer
    if (this.layers.length > 1 && index > 0) {
      // Remove the layer at the specified index
      this.layers.removeAt(index);
      this._cdr.markForCheck();
    }
  }

  minDate: Date = new Date();
  readonly getUsers = (api: string, pageReq: PaginationRequest) => {
    return this.store.fetchUsers(
      api,
      pageReq,
      this.riskDocumentForm?.value?.accountablePerson
    );
  };

  save(): void {
    let payload = {
      ...this.riskDocumentForm.value,
      status:  'ACTIVE', // this.riskDocumentForm.value.active ? 'ACTIVE' : 'DISABLED',
      relatesTo: this.riskDocumentForm.value?.relatesTo,
      importance: {id: this.riskDocumentForm.value?.importance},
      governmentEntity: {id: this.riskDocumentForm.value?.governmentEntity},
      type: {id: this.riskDocumentForm.value?.type.id},
      accountablePerson: this.riskDocumentForm.value?.accountablePerson.map((e: any) => {
        return {
          id: e
        }
      }),
    };
    if (this.riskDocumentForm.value.requiresRenewal) {
      if (this.riskDocumentForm.value.layers.length > 0) {
        if (this.riskDocumentForm.value.layers.some((layer: any, index: any) => {
          return parseInt(layer.daysBeforeExpiry) > parseInt(this.riskDocumentForm.value.layers[0].daysBeforeExpiry) && index != 0;
        })) {
          this.notificaitonService.notifyError("Days before expiry for Layer Two must be less than  Days before expiry for layer one");
          return;
        }
      }
      let validateAssignee = true;
      this.riskDocumentForm.value.layers.forEach((layer: any) => {
        if (!layer.assignee || layer.assignee?.length == 0) {
          validateAssignee = false;
        }
      });
      if (!validateAssignee) {
        this.notificaitonService.notifyError("You must select assignee roles for all layers");
        return;
      }
      payload = {
        ...payload,
        layers: this.riskDocumentForm.value.layers.map((e: any) => {
          return {
            daysBeforeExpiry: e.daysBeforeExpiry,
            roleAssignee: e.assignee.map((e: any) => {
              return {
                id: e
              }
            }),
            creationRenewalToDoDate: moment(this.riskDocumentForm.value.expiryDate)
              .subtract(e.daysBeforeExpiry, "days").format("YYYY-MM-DD"),
            sendLayerTwoEmailDate: moment(this.riskDocumentForm.value.expiryDate)
              .subtract(e.daysBeforeExpiry, "days").format("YYYY-MM-DD"),
            type: e.type
          }
        })
      }
    }
    payload = this.checkRequiresVisit(payload);
    payload = this.checkRequiresRenewal(payload);
    this.service.addDocument(payload).subscribe(result => {
      this.notificaitonService.notifySuccess("Risk management document added successfully.");
      this.route.navigateByUrl('accounting/v2/risk-documents-mgmt');
    }, error => {
      this.notificaitonService.notifyError(error.error.message);
    })
  }

  checkRequiresRenewal(payload: any) {
    if (!this.riskDocumentForm.value?.requiresRenewal)
      payload = {
        ...payload,
        layers: null
      };
    return payload
  }

  checkRequiresVisit(payload: any) {
    if (!this.riskDocumentForm.value?.requiresVisit) {
      payload = {
        ...payload,
        frequencyOfVisit: null,
        serviceReportName: null,
        nextVisitDate: null,
        lastVisitDate: null,
        frequencyType: null,
      };
      delete payload.frequencyOfVisit;
      delete payload.serviceReportName;
      delete payload.nextVisitDate;
      delete payload.lastVisitDate;
      delete payload.frequencyType;
    }
    return payload
  }

  requireVisitValidate: CCValidatorFn = (control) => {
    if (
      this.riskDocumentForm.controls['requiresVisit'].value == true &&
      !control.value
    ) {
      return {error: 'This field is required'};
    }
    return null;
  }
  frequencyOfVisitValidate: CCValidatorFn = (control) => {
    if (
      this.riskDocumentForm.controls['requiresVisit'].value == true &&
      control.value <= 0 ||
      !control.value
    ) {
      return {error: 'Frequency of visit must be greater than 0'};
    }
    return null;
  };

  vehiclesTypeRequiredValidate: CCValidatorFn = (control) => {
    if (
      this.riskDocumentForm.controls['type'].value?.code == 'vehicles' &&
      !control.value
    ) {
      return {error: 'This field is required'};
    }
    return null;
  }
  daysBeforeExpiryValidate: CCValidatorFn = (control) => {
    if (
      this.riskDocumentForm.controls['requiresRenewal'].value == true &&
      control.value <= 0 ||
      !control.value
    ) {
      return {error: 'Days before expiry must be greater than 0'};
    }
    return null;
  };

  onFileRemoved(evt: CCFileRemovedEvent) {
    console.clear();
    console.log(evt);
  }

  checkLayers(event: any) {
    console.log(event?.checked);
    if (event?.checked) {
      this.pushLayer();
    } else {
      this.layers.clear();
    }
  }

  pushLayer() {
    if (this.layers.length == 0) {
      const layerOne = this.fb.group({
        daysBeforeExpiry: 0,
        assignee: [],
        type: 'LAYER_ONE'
      });
      this.layers.push(layerOne);
    }
  }

  ngOnInit(): void {
    this.pushLayer();
    this.riskDocumentForm.valueChanges.subscribe((value) => {
      console.log(this.riskDocumentForm);
      if (value.requiresVisit) {
        // this.riskDocumentForm.controls['nextVisitDate'].setValidators([Validators.required]);
        // this.riskDocumentForm.controls['lastVisitDate'].setValidators([Validators.required]);
        this.riskDocumentForm.controls['serviceReportName'].setValidators([Validators.required]);
        this.riskDocumentForm.controls['frequencyType'].setValidators([Validators.required]);
      } else {
        this.riskDocumentForm.controls['nextVisitDate'].clearValidators();
        this.riskDocumentForm.controls['nextVisitDate'].setErrors(null);
        this.riskDocumentForm.controls['lastVisitDate'].clearValidators();
        this.riskDocumentForm.controls['lastVisitDate'].setErrors(null);
        this.riskDocumentForm.controls['serviceReportName'].clearValidators();
        this.riskDocumentForm.controls['serviceReportName'].setErrors(null);
        this.riskDocumentForm.controls['frequencyOfVisit'].clearValidators();
        this.riskDocumentForm.controls['frequencyOfVisit'].setErrors(null);
        this.riskDocumentForm.controls['frequencyType'].clearValidators();
        this.riskDocumentForm.controls['frequencyType'].setErrors(null);
        this._cdr.markForCheck();
      }
    })
  };

  relatedTos: SelectOption[] = [
    {id: "CENTER", text: "Center"},
    {id: "ACCOMMODATION", text: "Accommodation"},
    {id: "KIOSK", text: "Kiosk"}
  ];

  readonly getRoles = (pageReq: PaginationRequest) => {
    console.log("pageReq", pageReq);
    return this.service.getAllActiveRoles({
      page: pageReq.page,
      size: !!pageReq.size ? pageReq.size : 10,
    }, false).pipe(map(response =>
      response.content.map(item =>
        (<SelectOption>{
            id: item["id"],
            text: item["name"],
            data: item
          }
        ))));
  };

  readonly importances = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.service
      .getPicklist({
        code: 'risk_document_management_importance',
        page: pageReq.page,
        pageSize: pageReq.size,
        search: pageReq.searchString,
      })
      .pipe(
        map((val: any[]) => {
          return val.map(
            (opt) => ({id: opt.id, text: opt.label, code: opt.code} as SelectOption)
          ).filter((value, index, self) => self.map((e: any) => e.text).indexOf(value.text) === index);
        })
      );
  };

  readonly types = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.service
      .getPicklist({
        code: 'risk_document_management_type',
        page: pageReq.page,
        pageSize: pageReq.size,
        search: pageReq.searchString,
      })
      .pipe(
        map((val: any[]) => {
          return val.map(
            (opt) => ({id: opt.id, text: opt.label, code: opt.code} as SelectOption)
          ).filter((value, index, self) => self.map((e: any) => e.text).indexOf(value.text) === index);
        })
      );
  };

  readonly governmentEntities = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.service
      .getPicklist({
        code: 'risk_document_management_government_entity',
        page: pageReq.page,
        pageSize: pageReq.size,
        search: pageReq.searchString
      })
      .pipe(
        map((val: any[]) => {
          return val.map(
            (opt) => ({id: opt.id, text: opt.label} as SelectOption)
          ).filter((value, index, self) => self.map((e: any) => e.text).indexOf(value.text) === index);
        })
      );
  };


  back() {
    this.location.back()
  }

  ngOnDestroy() {
    this.subscriptions.unsubscribe();
  }
}
