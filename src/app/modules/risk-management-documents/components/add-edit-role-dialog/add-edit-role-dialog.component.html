<ng-container>
  <cc-card>
    <cc-card-title
    ><strong>
      {{ roleId ? "Change Assigned Users" : "Add New Role" }}
    </strong>
    </cc-card-title>
    <form
      [formGroup]="form"
      class="row"
      style="padding: 1em"
    >
      <div class="row">
        <div [ngClass]="!!roleId ? 'col-md-12 mb-4' : 'col-md-12'">
          <cc-input
            [hidden]="!!roleId"
            formControlName="name"
            label="New Role:"
            placeholder="Enter Name"
            [required]="true"
          >
          </cc-input>
          <cc-label
            [hidden]="!roleId"
            label="New Role:"
          >
            Role Name: <span><b>{{ this.form.value.name }}</b></span>
          </cc-label>
        </div>
        <div class="col-md-12">
          <cc-select
            style="top:-1.5em"
            #responsibleUser
            cc-paginated
            class="col-12 col-sm-12 col-md-12 col-lg-4"
            #holderPage="ccPaginated"
            [multiple]="true"
            [required]="true"
            [api]="'/public/user/page'"
            formControlName="responsibleUser"
            entityId="'responsibleUser'"
            [search]="true"
            [pageFetcher]="getUsers"
            [modelOptions]="this.form.value?.responsibleUserOptions!"
            idProperty="id"
            textProperty="label"
            label="Responsible Users"
          ></cc-select>
        </div>
        <div class="col-12 text-center">
          <button type="button" cc-flat-button (click)="onCancelClick()">Cancel</button>
          <button
            cc-raised-button
            class="m-1"
            [disabled]="!form.valid"
            color="accent"
            (click)="onSubmit()"
          >
            Save
          </button>
        </div>
      </div>
    </form>
  </cc-card>
</ng-container>
