<ng-container>
  <cc-card>
    <cc-card-title
    ><strong>{{ this.data.document.name }} History</strong>
    </cc-card-title>
    <cc-datagrid
      class="my-2"
      [data]="riskManagementDocumentHistoryList.content"
      [columns]="gridCols"
      [length]="riskManagementDocumentHistoryList.totalElements"
      [pageOnFront]="false"
      [pageIndex]="riskManagementDocumentHistoryList.number"
      [pageSize]="riskManagementDocumentHistoryList.size"
      [pageSizeOptions]="[10, 20, 30, 40, 50]"
      (page)="getNextPage($event)"
      [stickyHeader]="true"
      [columnMovable]="true"
      [columnHideable]="true"
      [showColumnMenuButton]="true"
      [showColumnMenuHeader]="false"
      [columnMenuButtonIcon]="'settings'"
    ></cc-datagrid>
    <div class="col-12 text-right mt-2">
      <button type="button" color="accent" cc-flat-button (click)="onCancelClick()">Close</button>
    </div>
  </cc-card>
</ng-container>

