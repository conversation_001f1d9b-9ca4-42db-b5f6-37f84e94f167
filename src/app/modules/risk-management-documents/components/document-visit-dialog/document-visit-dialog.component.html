<ng-container>
  <cc-card>
    <cc-card-title
    ><strong>Edit Service Visit</strong>
    </cc-card-title>
    <form [formGroup]="form" class="row" style="padding: 1em">
      <div class="row">
        <div class="col-md-4">
          <cc-input [required]="true" formControlName="serviceReportName"
                    label="Service Report Name"></cc-input>
        </div>
        <div class="col-md-4">
          <cc-input [required]="true"
                    [ccValidateBy]="[frequencyOfVisitValidate]"
                    formControlName="frequencyOfVisit" type="number"
                    label="Frequency of Visits"></cc-input>
        </div>
        <div class="col-md-4">
          <div>
            <cc-radio-group [required]="true" formControlName="frequencyType">
              <cc-radio-button style="margin: 1em 1em;" value="WEEKLY">
                Weekly
              </cc-radio-button>
              <cc-radio-button value="MONTHLY">
                Monthly
              </cc-radio-button>
            </cc-radio-group>
          </div>
        </div>
        <div class="col-md-4">
          <cc-datepicker [required]="true" formControlName="lastVisitDate"
                         label="Last Visit Date"></cc-datepicker>
        </div>
        <div class="col-md-4">
          <cc-datepicker [required]="true" formControlName="nextVisitDate"
                         label="Next Visit Date"></cc-datepicker>
        </div>
        <div class="col-12 text-center">
          <button type="button" cc-flat-button (click)="onCancelClick()">Cancel</button>
          <button
            cc-raised-button
            class="m-1"
            [disabled]="!form.valid"
            color="accent"
            (click)="onSubmit(form)"
          >
            Save
          </button>
        </div>
      </div>
    </form>
    <div class="col-md-12">
      <strong>{{ this.data.document.name }} Visits History</strong>
    </div>
    <cc-datagrid
      class="my-2"
      [data]="riskManagementDocumentsVisits.content"
      [columns]="visitsHistoryCols"
      [length]="riskManagementDocumentsVisits.totalElements"
      [pageOnFront]="false"
      [pageIndex]="riskManagementDocumentsVisits.number"
      [pageSize]="riskManagementDocumentsVisits.size"
      [pageSizeOptions]="[10, 20, 30, 40, 50]"
      (page)="getNextPageVisit($event)"
      [stickyHeader]="true"
      [columnMovable]="true"
      [columnHideable]="true"
      [showColumnMenuButton]="true"
      [showColumnMenuHeader]="false"
      [columnMenuButtonIcon]="'settings'"
    ></cc-datagrid>
  </cc-card>
  <div class="col-12 text-right mt-2">
    <button type="button" color="accent" cc-flat-button (click)="onCancelClick()">Close</button>
  </div>
</ng-container>
