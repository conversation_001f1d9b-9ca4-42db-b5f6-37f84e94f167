<div class="acc-7580">
  <ng-container>
    <cc-card>
      <cc-card-title><strong>Edit Document</strong> </cc-card-title>
      <form [formGroup]="form" class="row" style="padding: 1em">
        <div class="row">
          <div class="col-md-6">
            <cc-input
              formControlName="name"
              label="Name"
              placeholder="Enter Name"
              [required]="true"
            >
            </cc-input>
          </div>
          <div class="col-md-6">
            <cc-select
              [required]="true"
              formControlName="type"
              label="Type"
              search="true"
              [lazyPageFetcher]="types"
              [modelOptions]="[
                {
                  id: this.data.document?.type.id,
                  text: this.data.document?.type.label
                }
              ]"
            >
            </cc-select>
          </div>
          <div class="col-md-6">
            <cc-select
              style="top: -1.5em"
              #responsibleUser
              cc-paginated
              class="col-12 col-sm-12 col-md-12 col-lg-4"
              #holderPage="ccPaginated"
              [multiple]="true"
              [required]="true"
              [api]="'accounting/riskDocumentsManagementRole/getActiveRoles'"
              formControlName="accountablePerson"
              entityId="'accountablePerson'"
              [search]="true"
              [pageFetcher]="getRoles"
              [modelOptions]="this.form.value?.accountablePersonOpts!"
              idProperty="id"
              textProperty="text"
              label="Accountable Person"
            ></cc-select>
          </div>
          <div class="col-md-6">
            <cc-select
              formControlName="relatesTo"
              [multiple]="true"
              label="Related To"
              [data]="relatedTos"
            >
            </cc-select>
          </div>
          <div class="col-md-6">
            <cc-datepicker
              formControlName="issuanceDate"
              label="Issuance Date"
            ></cc-datepicker>
          </div>
          <div class="col-md-6">
            <cc-datepicker
              [minDate]="minDate"
              formControlName="expiryDate"
              [required]="true"
              label="Expiry Date"
            ></cc-datepicker>
          </div>
          <div class="col-md-12 mt-3">
            <cc-file-uploader
              [tag]="getTag()"
              [dropzoneConfig]="config"
              label="Upload attachment"
              formControlName="attachments"
              name="file"
              (fileRemoved)="onFileRemoved($event)"
            ></cc-file-uploader>
          </div>
          <div class="col-12 text-center">
            <button type="button" cc-flat-button (click)="onCancelClick()">
              Cancel
            </button>
            <button
              cc-raised-button
              class="m-1"
              [disabled]="!form.valid"
              color="accent"
              (click)="onSubmit(form)"
            >
              Save
            </button>
          </div>
        </div>
      </form>
    </cc-card>
  </ng-container>
</div>
