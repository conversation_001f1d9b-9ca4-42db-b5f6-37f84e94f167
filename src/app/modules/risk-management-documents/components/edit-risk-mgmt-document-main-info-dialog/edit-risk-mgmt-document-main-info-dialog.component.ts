import {Component, Inject, OnInit} from '@angular/core';
import {<PERSON><PERSON>uilder, FormGroup, Validators} from "@angular/forms";
import {RiskManagementDocumentsStoreService} from "../../services/risk-management-documents-store.service";
import {MAT_DIALOG_DATA, MatDialogRef} from "@angular/material/dialog";
import {RiskManagementDocumentsService} from "../../services/risk-management-documents.service";
import {CCNotificationService} from "@maids/cc-lib/services";
import {PageableResponseModel, PagedEntity, PaginatedEntity, PaginationRequest} from "@maids/cc-lib/common";
import {CCFileRemovedEvent} from "@maids/cc-lib/file-uploader";
import {Observable} from "rxjs";
import {SelectOption} from "@maids/cc-lib/select-input";
import {map} from "rxjs/operators";
import {CCPicklistService} from "@maids/cc-erp-services";
import * as moment from 'moment'

@Component({
  selector: 'app-edit-risk-mgmt-document-main-info-dialog',
  templateUrl: './edit-risk-mgmt-document-main-info-dialog.component.html',
  styleUrls: ['./edit-risk-mgmt-document-main-info-dialog.component.scss'],
  providers: [RiskManagementDocumentsStoreService, RiskManagementDocumentsService],
})
export class EditRiskMgmtDocumentMainInfoDialogComponent implements OnInit {

  constructor(private formBuilder: FormBuilder, private store: RiskManagementDocumentsStoreService,
              public dialogRef: MatDialogRef<EditRiskMgmtDocumentMainInfoDialogComponent>,
              @Inject(MAT_DIALOG_DATA) public data: any,
              private service: RiskManagementDocumentsService,
              private notificaitonService: CCNotificationService,
              private picklistService: CCPicklistService) {
  }

  relatedTos: SelectOption[] = [
    {id: "CENTER", text: "Center"},
    {id: "ACCOMMODATION", text: "Accommodation"},
    {id: "KIOSK", text: "Kiosk"}
  ];

  accountablePerson: [] = [];
  relatesTo: [] = [];
  accountablPersonModel: [] = [];

  // Store predefined roles for lookup
  predefinedRoles: any[] = [];

  readonly importances = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.service.getPicklist({
      page: pageReq.page,
      pageSize: pageReq.size,
      code: 'risk_document_management_importance',
      search: pageReq.searchString,
    })
      .pipe(
        map((val: any[]) => {
          return val.map(
            (opt) => ({id: opt.id, text: opt.label, code: opt.code} as SelectOption)
          ).filter((value, index, self) => self.map((e: any) => e.text).indexOf(value.text) === index);
        })
      );
  };

  readonly types = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.service.getPicklist({
      page: pageReq.page,
      pageSize: pageReq.size,
      code: 'risk_document_management_type',
      search: pageReq.searchString,
    })
      .pipe(
        map((val: any[]) => {
          return val.map(
            (opt) => ({id: opt.id, text: opt.label, code: opt.code} as SelectOption)
          ).filter((value, index, self) => self.map((e: any) => e.text).indexOf(value.text) === index);
        })
      );
  };

  readonly governmentEntities = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.service.getPicklist({
      page: pageReq.page,
      pageSize: pageReq.size,
      code: 'risk_document_management_government_entity',
      search: pageReq.searchString,
    })
      .pipe(
        map((val: any[]) => {
          return val.map(
            (opt) => ({id: opt.id, text: opt.label} as SelectOption)
          ).filter((value, index, self) => self.map((e: any) => e.text).indexOf(value.text) === index);
        })
      );
  };


  form: FormGroup = this.formBuilder.group({
    name: ['', Validators.required],
    type: ['', Validators.required],
    expiryDate: [''],
    relatesTo: [''],
    accountablePerson: '',
    issuanceDate: '',
    relatesToOpts: [],
    accountablePersonOpts: [],
    attachments: []
  });

  onFileRemoved(evt: CCFileRemovedEvent) {
    console.clear();
    console.log(evt);
    if (this.data.document.attachments.length > 0) {
      let attachement = evt.model;
      let ids = this.data.document.attachments.map(e => e.id);
      if (ids.includes(attachement.id)) {
        this.notificaitonService.notifyError("Document's old attachments can't be removed.");
        // Reset the form attachments to keep the original document attachments
        this.form.patchValue({
          attachments: this.data.document.attachments
        });
        return;
      }
    }
  }

  readonly getRoles = (api: string, pageReq: PaginationRequest): Observable<PaginatedEntity<any>> => {
    // If there's a search string and it matches a predefined value's ID, return that value
    if (pageReq.searchString && this.predefinedRoles.some(role => role.id === pageReq.searchString)) {
      const matchingRole = this.predefinedRoles.find(role => role.id === pageReq.searchString);
      return new Observable<PaginatedEntity<any>>(observer => {
        observer.next({
          content: [matchingRole],
          number: 0,
          numberOfElements: 1,
          pageable: {
            pageNumber: 0,
            pageSize: 1,
            offset: 0
          },
          size: 1,
          sort: "",
          totalElements: 1,
          totalPages: 1
        });
        observer.complete();
      });
    }

    return this.service.getAllActiveRoles({
      page: pageReq.page,
      size: !!pageReq.size ? pageReq.size : 20,
      search: pageReq.searchString
    }, false).pipe(
      map((response: PageableResponseModel<any>) => {
        // Store the fetched roles for future reference
        this.predefinedRoles = response.content?.map(el => ({
          id: el.id,
          label: el.name,
          text: el.name
        }));

        return {
          content: response.content?.map(el => {
            return {
              id: el.id,
              label: el.name,
              text: el.name
            }
          }),
          number: response.number,
          numberOfElements: response.number,
          pageable: {
            pageNumber: pageReq.page,
            pageSize: pageReq.size,
            offset: 0
          },
          size: pageReq.size,
          sort: "",
          totalElements: response.totalElements,
          totalPages: response.totalPages
        }
      })
    );
  };


  tagIndex = 0;
  ngOnInit(): void {
    console.log(this.data.document);
    if (this.data && this.data.document) {
      // Initialize predefinedRoles with the existing accountable persons
      if (this.data.document.accountablePerson && this.data.document.accountablePerson.length > 0) {
        this.predefinedRoles = this.data.document.accountablePerson.map((e: any) => ({
          id: e.id,
          label: e.label,
          text: e.label,
          name: e.label
        }));
      }

      this.form.patchValue({
        name: this.data.document.name,
        type: this.data.document.type.id,
        expiryDate: this.data.document.expiryDate,
        relatesTo: this.data.document.relatesTo.map((e: any) => e?.value),
        issuanceDate: this.data.document.issuanceDate,
        accountablePerson: this.data.document.accountablePerson.map((e: any) => e.id),
        accountablePersonOpts: this.data && this.data?.document ? this.data?.document?.accountablePerson.map((e: any) => {
          return {
            id: e.id,
            text: e.label,
            label: e.label
          }
        }) : [],
        attachments: this.data.document.attachments
      });
      this.tagIndex = this.data.document.attachments.length > 0 ? this.data.document.attachments.length : 0;
    }
  }

  minDate: Date = new Date();

  getTag() {
    this.tagIndex++;
    return `${this.tagIndex}_${moment().format()}`;
  }

  onSubmit(data: any) {
    let payload = {
      id: this.data.document.id,
      name: this.form.value.name,
      expiryDate: this.form.value.expiryDate,
      relatesTo: this.form.value.relatesTo,
      status: this.data.document.status,
      // status: (this.data.document.status != 'EXPIRED' && this.data.document.status != 'UNDER_RENEWAL')
      //   ? this.form.value.active ? 'ACTIVE' : 'DISABLED' : this.data.document.status,
      issuanceDate: this.form.value.issuanceDate,
      type: {
        id: this.form.value.type
      },
      accountablePerson: this.form.value.accountablePerson.map((e: any) => {
        return {
          id: !!e.id ? e.id : parseFloat(e)
        }
      }),
      // Use only the attachments from the form, which will be the new files
      attachments: this.form.value.attachments && this.form.value.attachments.length > 0 ?
        this.form.value.attachments.map((a: any) => {
          return {
            ...a,
            temporary: false
          }
        }) : null
    }
    console.log(payload);
    console.log(this.data.document);
    this.service.updateDocument(payload).subscribe(res => {
      this.notificaitonService.notifySuccess("Document updated successfully.");
      this.dialogRef.close(true);
    });
  }

  onCancelClick(): void {
    this.dialogRef.close(false);
  }
}
