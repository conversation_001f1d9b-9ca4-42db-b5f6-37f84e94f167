<div class="row">
  <div class="col-md-6"></div>
  <div class="col-md-6 text-right my-2">
    <button
      class="iconed-btn ml-2"
      cc-raised-button
      color="accent"
      (click)="routeToRoles()"
    >
      <cc-icon class="icon">add</cc-icon>
      Manage Roles
    </button>
    <button
      class="iconed-btn ml-2"
      cc-raised-button
      color="primary"
      (click)="routeToAdd()"
    >
      <cc-icon class="icon">add</cc-icon>
      Add New Document
    </button>
  </div>
</div>

<cc-tab-group style="padding: 2.5em" [selectedIndex]="selected.value"
              (selectedIndexChange)="selected.setValue($event)" color="primary">
  <cc-tab *ngFor="let tab of tabs; let index = index" [label]="tab" color="primary">
    <div [hidden]="tab != 'List of Documents'">
      <cc-accordion class="my-5">
        <cc-panel #panel>
          <cc-panel-title style="margin-top: -.5em;">
            <cc-icon class="icon panel-icon">filter_alt</cc-icon>
            Filters
          </cc-panel-title>
          <cc-panel-body>
            <form class="acc-7580" #form="ngForm" [formGroup]="filter">
              <div class="row d-flex justify-content-around">
                <div class="col-md-4">
                  <cc-select
                    #statusSelector
                    [lazyPageFetcher]="governmentEntities"
                    [emitFullSelectOption]="true"
                    formControlName="governmentEntity"
                    label="Government Entity"
                  >
                  </cc-select
                  >
                </div>
                <div class="col-md-4">
                  <cc-input
                    label="Name"
                    placeholder="Type Name"
                    formControlName="name"
                  >
                  </cc-input>
                </div>
                <div class="col-md-4">
                  <cc-select
                    #typesSelector
                    [lazyPageFetcher]="types"
                    [emitFullSelectOption]="true"
                    formControlName="type"
                    label="Type"
                  >
                  </cc-select
                  >
                </div>
              </div>
              <div class="row d-flex justify-content-around">
                <div class="col-md-4">
                  <cc-select
                    formControlName="relatesTo"
                    label="Related To"
                    [data]="relatedTos"
                  >
                  </cc-select
                  >
                </div>
                <div class="col-md-4">
                  <cc-select
                    style="top:-1.5em"
                    class="col-12 col-sm-12 col-md-12 col-lg-4"
                    formControlName="accountablePerson"
                    entityId="'accountablePerson'"
                    [search]="true"
                    [lazyPageFetcher]="getRoles"
                    idProperty="id"
                    textProperty="label"
                    label="Accountable Person"
                  >
                  </cc-select
                  >
                </div>
                <div class="col-md-4 d-flex">
                  <div class="col-md-6 pr-1 pl-0">
                    <cc-datepicker formControlName="fromExpiryDate"
                                   label="Expiry Date (From)">
                    </cc-datepicker>
                  </div>
                  <div class="col-md-6 p-0">
                    <cc-datepicker formControlName="toExpiryDate"
                                   label="Expiry Date (To)">
                    </cc-datepicker>
                  </div>
                </div>
                <div class="col-md-12 text-center mb-2" style="margin-top: -1em;">
                  <button color="primary" type="button" style="margin: 0 1em 0 1em" class="filter-btn" cc-raised-button (click)="searchDocuments()">
                    <cc-icon class="icon panel-icon">search</cc-icon>
                    Search
                  </button>
                  <button type="button" class="filter-btn" cc-raised-button (click)="resetFilters()">
                    <cc-icon class="icon panel-icon">refresh</cc-icon>
                    Reset
                  </button>
                </div>
              </div>
            </form>
          </cc-panel-body>
        </cc-panel>
      </cc-accordion>

      <ng-container *ngIf="filteredGovs && filteredGovs.length > 0; else noDataFound">
        <cc-accordion>
          <cc-panel #panel (open)="applySearch(gov)" class="my-2 document-panel document-gov-acc"
                    *ngFor="let gov of filteredGovs"
                    #forDirective>
            <cc-panel-title>{{ gov.label }}</cc-panel-title>
            <cc-panel-body class="my-2">
              <cc-datagrid
                class="my-2"
                [data]="riskManagementDocumentsList.content"
                [columns]="gridCols"
                [length]="riskManagementDocumentsList.totalElements"
                [pageOnFront]="false"
                [pageIndex]="riskManagementDocumentsList.number"
                [pageSize]="riskManagementDocumentsList.size"
                [pageSizeOptions]="[10, 20, 30, 40, 50]"
                (page)="getNextPage($event)"
                [stickyHeader]="true"
                [columnMovable]="true"
                [columnHideable]="true"
                [showColumnMenuButton]="true"
                [showColumnMenuHeader]="false"
                [columnMenuButtonIcon]="'settings'"
              >
                <cc-grid-actions-list *ccActionData="let ctx of riskManagementDocumentsList.content; row as row; index as index; last as last; first as first; even as even; odd as odd" [renderedActionsCount]="renderedActions">
                  <a role="button" [hidden]="!row?.attachments[0]" *cc-action cc-collapsible-action (click)="this.preview(row)" label="" icon="attachment">
                    <span cc-action-label>Attachment</span>
                  </a>
                  <a role="button" [hidden]="!row?.requiresVisit" *cc-action cc-collapsible-action (click)="this.showVisitModal(row)" icon="home">
                    <ng-container cc-action-label class="label-visit">Visits</ng-container>
                  </a>
                  <a role="button" *cc-action cc-collapsible-action (click)="this.showEditModal(row)" icon="edit">
                    <span cc-action-label style="text-align: center">Edit</span>
                  </a>
                  <a role="button" *cc-action cc-collapsible-action (click)="this.showHistoryModal(row)" icon="history">
                    <span cc-action-label style="text-align: center">History</span>
                  </a>
                </cc-grid-actions-list>
              </cc-datagrid>
            </cc-panel-body>
          </cc-panel>
        </cc-accordion>
      </ng-container>

      <ng-template #noDataFound>
        <div class="alert alert-info text-center my-4">
          <h4>No data found</h4>
          <p>No documents match your search criteria.</p>
        </div>
      </ng-template>

    </div>
    <div [hidden]="tab == 'List of Documents'">
      <div class="acc-7580 row d-flex justify-content-around">
        <div class="col-md-12 text-center" style="margin-top: 1em;">
          <button color="primary" type="button" class="filter-btn" cc-raised-button (click)="resetFilters()">
            <cc-icon class="icon panel-icon">refresh</cc-icon>
            Refresh
          </button>
        </div>
      </div>
      <div class="row mt-5">
        <div class="col-md-10">
          <cc-label><strong>Initiating the Renewal Process</strong></cc-label>
        </div>
      </div>
      <cc-datagrid
        class="my-2"
        [data]="pendingRenewalTodos.content"
        [columns]="pendingRenewalGridCols"
        [length]="pendingRenewalTodos.totalElements"
        [pageOnFront]="false"
        [pageIndex]="pendingRenewalTodos.number"
        [pageSize]="pendingRenewalTodos.size"
        [pageSizeOptions]="[10, 20, 30, 40, 50]"
        (page)="getNextPagePendingRenewal($event)"
        [stickyHeader]="true"
        [columnMovable]="true"
        [columnHideable]="true"
        [showColumnMenuButton]="true"
        [showColumnMenuHeader]="false"
        [columnMenuButtonIcon]="'settings'"
        [rowClassFormatter]="rowClassFormatter"
        [rowStyleFormatter]="rowRenewalStyleFormatter"
      ></cc-datagrid>
      <div class="row mt-5">
        <div class="col-md-10">
          <cc-label><strong>Planned Visit</strong></cc-label>
        </div>
      </div>
      <cc-datagrid
        class="my-2"
        [data]="riskManagementDocumentsVisits.content"
        [columns]="pendingVisitsGridCols"
        [length]="riskManagementDocumentsVisits.totalElements"
        [pageOnFront]="false"
        [pageIndex]="riskManagementDocumentsVisits.number"
        [pageSize]="riskManagementDocumentsVisits.size"
        [pageSizeOptions]="[10, 20, 30, 40, 50]"
        (page)="getNextPageVisit($event)"
        [stickyHeader]="true"
        [columnMovable]="true"
        [columnHideable]="true"
        [showColumnMenuButton]="true"
        [showColumnMenuHeader]="false"
        [columnMenuButtonIcon]="'settings'"
        [rowStyleFormatter]="rowStyleFormatter"
      ></cc-datagrid>
    </div>

  </cc-tab>
</cc-tab-group>


