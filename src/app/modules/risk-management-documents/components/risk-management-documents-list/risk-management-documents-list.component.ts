import {
  ChangeDetectionStrategy, ChangeDetectorRef,
  Component, Input,
  OnDestroy,
  OnInit,
} from '@angular/core';

import {PageEvent} from '@angular/material/paginator';
import {HttpParams} from '@angular/common/http';
import {RiskManagementDocumentsModel} from '../../models/risk-management-documents.model';
import {groupBy, Observable, Subscription} from 'rxjs';
import {CCGridColumn, CCGridRowClassFormatter, CCGridRowStyleFormatter} from '@maids/cc-lib/datagrid';
import {API} from 'src/environments/api';
import {RiskManagementDocumentsStoreService} from '../../services/risk-management-documents-store.service';
import {FormBuilder, FormControl, FormGroup} from '@angular/forms';
import {AutoUnsubscribe, PageableResponseModel, PaginationRequest} from '@maids/cc-lib/common';
import {Sort} from '@angular/material/sort';
import {SelectOption} from "@maids/cc-lib/select-input";
import {map} from "rxjs/operators";
import {CCPicklistService} from "@maids/cc-erp-services";
import {RiskManagementDocumentsService} from "../../services/risk-management-documents.service";
import {AddEditRoleDialogComponent} from "../add-edit-role-dialog/add-edit-role-dialog.component";
import {CCDialog} from "@maids/cc-lib/dialog";
import {CCNotificationService, MediaService} from "@maids/cc-lib/services";
import {UpdateTodoDialogComponent} from "../update-todo-dialog/update-todo-dialog.component";
import {
  EditRiskMgmtDocumentMainInfoDialogComponent
} from "../edit-risk-mgmt-document-main-info-dialog/edit-risk-mgmt-document-main-info-dialog.component";
import {DocumentHistoryDialogComponent} from "../document-history-dialog/document-history-dialog.component";
import {DocumentVisitDialogComponent} from "../document-visit-dialog/document-visit-dialog.component";
import {CCPreviewAttachmentComponent} from "@maids/cc-lib/preview-attachment";
import {MatDialog} from '@angular/material/dialog';
import {Router} from "@angular/router";
import * as moment from 'moment'
import { CCAuthService } from "@maids/cc-erp-services";
import {User} from "@maids/cc-erp-services/src/lib/models/user.model";

@Component({
  selector: 'app-risk-management-documents-list',
  templateUrl: './risk-management-documents-list.component.html',
  styleUrls: ['./risk-management-documents-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [RiskManagementDocumentsStoreService],
})
@AutoUnsubscribe
export class RiskManagementDocumentsListComponent implements OnInit {
  private readonly subscriptions = new Subscription();

  tabs = ['List of Documents', 'To-dos'];
  selected = new FormControl(0);

  readonly importances = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.service.getPicklist({
      code: 'risk_document_management_importance',
      page: pageReq.page,
      pageSize: pageReq.size,
      search: pageReq.searchString,
    })
      .pipe(
        map((val: any[]) => {
          return val.map(
            (opt) => ({id: opt.id, text: opt.label, code: opt.code} as SelectOption)
          ).filter((value, index, self) => self.map((e: any) => e.text).indexOf(value.text) === index);
        })
      );
  };

  readonly types = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.service.getPicklist({
      code: 'risk_document_management_type',
      page: pageReq.page,
      pageSize: pageReq.size,
      search: pageReq.searchString,
    })
      .pipe(
        map((val: any[]) => {
          return val.map(
            (opt) => ({id: opt.id, text: opt.label, code: opt.code} as SelectOption)
          ).filter((value, index, self) => self.map((e: any) => e.text).indexOf(value.text) === index);
        })
      );
  };

  readonly governmentEntities = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.service
      .getPicklist({
        code: 'risk_document_management_government_entity',
        page: pageReq.page,
        pageSize: pageReq.size,
        search: pageReq.searchString,
      })
      .pipe(
        map((val: any[]) => {
          return val.map(
            (opt) => ({id: opt.id, code: opt.code, text: opt.label} as SelectOption)
          ).filter((value, index, self) => self.map((e: any) => e.text).indexOf(value.text) === index);
        })
      );
  };


  readonly getRoles = (pageReq: PaginationRequest) => {
    console.log(pageReq);
    return this.service.getAllActiveRoles({
      page: pageReq.page,
      size: !!pageReq.size ? pageReq.size : 20
    }, false).pipe(map(response =>
      response.content.map(item =>
        (<SelectOption>{
            id: item["id"],
            text: item["name"],
            data: item
          }
        ))));
  };

  relatedTos: SelectOption[] = [
    {id: "CENTER", text: "Center"},
    {id: "ACCOMMODATION", text: "Accommodation"},
    {id: "KIOSK", text: "Kiosk"}
  ];

  constructor(
    private fb: FormBuilder,
    private store: RiskManagementDocumentsStoreService,
    private service: RiskManagementDocumentsService,
    private picklistService: CCPicklistService,
    private ccDialog: CCDialog,
    private notificaitonService: CCNotificationService,
    private cdr: ChangeDetectorRef,
    private mediaService: MediaService,
    private dialog: MatDialog,
    private route: Router,
    private authService: CCAuthService
  ) {
  }


  filter: FormGroup = this.fb.group({
    name: '',
    fromExpiryDate: '',
    toExpiryDate: '',
    governmentEntity: '',
    type: '',
    accountablePerson: '',
    relatesTo: ''
  })

  startIndex: number = 0;
  currentUser: User;

  riskManagementDocumentsList: PageableResponseModel<any> = {
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0
  };
  gridCols: CCGridColumn[] = [
    {
      field: 'index',
      header: 'Index',
      formatter: (rowData: any) => {
        return `<span>${this.startIndex + this.riskManagementDocumentsList.content.indexOf(rowData) + 1}</span>`;
      }
    },
    {field: 'name', header: 'Name'},
    {
      field: 'type', header: 'Type', formatter: (rowData) => {
        return `<span>${rowData.type?.label}</span>`
      }
    },
    {field: 'issuanceDate', header: 'Issuance Date'},
    {field: 'expiryDate', header: 'Expiry Date'},
    {
      field: 'accountablePerson', header: 'Accountable Person', formatter: (rowData) => {
        return `<span>${rowData.accountablePerson.map((e: any) => e.label).join(", ")}</span>`
      }
    },
    {
      field: 'relatesTo',
      header: 'Related To',
      formatter: (rowData) => {
        return `<span>${rowData.relatesTo
          ?.map((e: any) => e.label).join(", ")}</span>`
      }
    },
    {
      field: 'status', header: 'Status', formatter: (rowData: any) =>
        rowData.status == 'ACTIVE' || rowData.status == 'DISABLED' ?
          `<span class="active-status">${this.formatStatus(rowData.status)}</span>`
          :
          `<span class="active-status">${this.formatStatus(rowData.status)}</span>`
    },
  ];

  @Input() renderedActions = 0;
  visible = true;

  log(ctx: any) {}
  riskManagementDocumentsVisits: PageableResponseModel<any> = {
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0
  };
  pendingVisitsGridCols: CCGridColumn[] = [
    {
      field: 'operations',
      header: 'Actions',
      sortable: false,
      type: 'button',
      buttonConfig: {
        mode: 'multiple',
        disabled: false,
        buttons: [
          {
            type: 'raised',
            text: 'Visit Done',
            tooltip: '',
            color: 'accent',
            mode: 'single',
            disabled: false,
            hidden: (row: any) => {
              return row.status?.value == "VISIT_DONE";
            },
            callback: (row: any) => this.showVisitDone(row),
          },
          {
            type: 'raised',
            text: 'Disable Document',
            color: 'light',
            mode: 'single',
            disabled: false,
            hidden: (row: any) => {
              return !row?.riskDocumentsManagement.active;
            },
            callback: (row: any) => this.disableDocument(row?.riskDocumentsManagement.id),
          },
        ],
      },
    },
    {
      field: 'serviceReportName', header: 'Service Report Name', formatter: (rowData: any) =>
        `<span>${rowData.riskDocumentsManagement?.serviceReportName}</span>`
    },
    {
      field: 'dateOfVisit', header: 'Date of Visit', formatter: (rowData: any) =>
        `<span>${rowData.visitDate}</span>`
    },
    {field: 'dueDateForNextVisit', header: 'Due date for Next Visit',},
    {
      field: 'status', header: 'Status', formatter: (rowData: any) =>
        `<span class="active-status">${rowData.status?.label || this.formatStatus(rowData.status?.value)}</span>`
    },
  ];
  pageable: any = {
    page: 0,
    size: 20
  };

  pendingRenewalTodos: PageableResponseModel<any> = {
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0
  };
  pendingRenewalGridCols: CCGridColumn[] = [
    {
      field: 'operations',
      header: 'Actions',
      sortable: false,
      type: 'button',
      buttonConfig: {
        mode: 'multiple',
        disabled: false,
        buttons: [
          {
            type: 'raised',
            text: 'On It',
            tooltip: '',
            color: 'accent',
            mode: 'single',
            disabled: false,
            hidden: (row: any) => {
              return row.status.value != "PENDING_RENEWAL";
            },
            callback: (row: any) => this.changePendingRenewalTodoStatus(row, row.type == 'VEHICLE' ? 'RTA_TESTING' : ''),
          },
          {
            type: 'raised',
            text: 'Done RTA Testing',
            tooltip: '',
            color: 'accent',
            mode: 'single',
            disabled: false,
            hidden: (row: any) => {
              return row.status.value != "RTA_TESTING";
            },
            callback: (row: any) => this.changePendingRenewalTodoStatus(row, row.type == 'VEHICLE' ? 'PENDING_INSURANCE' : ''),
          },
          {
            type: 'raised',
            text: 'Done Insurance',
            tooltip: '',
            color: 'accent',
            mode: 'single',
            disabled: false,
            hidden: (row: any) => {
              return row.status.value != "PENDING_INSURANCE";
            },
            callback: (row: any) => this.changePendingRenewalTodoStatus(row, 'PENDING_REGISTRATION'),
          },
          {
            type: 'raised',
            text: 'Done Renewal',
            tooltip: '',
            color: 'accent',
            mode: 'single',
            disabled: false,
            hidden: (row: any) => {
              return row.type !== 'NON_VEHICLE' || row.status.value == "DONE_RENEWAL" || row.status.value == "PENDING_RENEWAL";
            },
            callback: (row: any) => this.doneRenewal(row, false),
          },
          {
            type: 'raised',
            text: 'Done Registration',
            tooltip: '',
            color: 'accent',
            mode: 'single',
            disabled: false,
            hidden: (row: any) => {
              return row.type !== 'VEHICLE' || row.status.value !== "PENDING_REGISTRATION";
            },
            callback: (row: any) => this.doneRenewal(row, true),
          },
          {
            type: 'raised',
            text: 'Disable Document',
            color: 'light',
            mode: 'single',
            disabled: false,
            hidden: (row: any) => {
              return !row?.riskDocumentsManagement.active;
            },
            callback: (row: any) => this.disableDocument(row?.riskDocumentsManagement.id),
          },
        ],
      },
    },
    {
      field: 'governmentEntity', header: 'Government Entity', formatter: (rowData: any) =>
        `<span>${rowData.riskDocumentsManagement?.governmentEntity.label}</span>`
    },
    {
      field: 'name', header: 'Name', formatter: (rowData: any) =>
        `<span>${rowData.riskDocumentsManagement?.name}</span>`
    },
    {
      field: 'type', header: 'Type', formatter: (rowData: any) =>
        `<span>${rowData.riskDocumentsManagement?.type.label}</span>`
    },
    {
      field: 'expiryDate', header: 'Expiry Date', formatter: (rowData: any) =>
        `<span>${rowData.riskDocumentsManagement?.expiryDate.split(' ')[0]}</span>`
    },
    {
      field: 'accountablePerson', header: 'Accountable Person', formatter: (rowData: any) =>
        `<span>${rowData.riskDocumentsManagement.accountablePerson?.map((e: any) => e.label).join(", ")}</span>`
    },
    {
      field: 'relatesTo', header: 'Related To', formatter: (rowData: any) =>
        `<span>${rowData.riskDocumentsManagement.relatesTo?.map((e: any) => e.label).join(", ")}</span>`
    },
    {
      field: 'status', header: 'Status', formatter: (row: any) =>
        row.type == 'NON_VEHICLE' && row.status?.value == 'UNDER_RENEWAL' && this.currentUser.id !== row.assignee?.id ?
          `<span class="visit-status">${row.status?.label || this.formatStatus(row.status.value)}
                <br /> <span class="working-on-user">${row.assignee?.fullName} is working on it</span>
                </span>`
          : `<span class="working_on active-status">${row.status?.label || this.formatStatus(row.status.value)}</span>`
    }
  ];

  rowClassFormatter: CCGridRowClassFormatter = {
    'blink': (row, index) => {
      let eventdate = moment(row.riskDocumentsManagement?.expiryDate);
      let todaysdate = moment();
      return eventdate.diff(todaysdate, 'days') >= 0
        && eventdate.diff(todaysdate, 'days') <= 4;
    },
  }

  rowStyleFormatter: CCGridRowStyleFormatter = {
    'background-color': (row, index) => {
      return {
        condition: row.status?.value == 'PENDING_VISIT',
        value: '#FAE0C0'
      }
    },
    'border-radius': (row, index) => {
      return {
        condition: true,
        value: '20px'
      }
    },
  }

  rowRenewalStyleFormatter: CCGridRowStyleFormatter = {
    'background-color': (row, index) => {
      return {
        condition: row.type == 'NON_VEHICLE' && row.status?.value == 'UNDER_RENEWAL',
        value: '#CAE7CB'
      }
    },
  }

  /**
   * Converts status values like "UNDER_RENEWAL" to a more readable format like "Under Renewal"
   * @param status The status value to convert
   * @returns The formatted status string
   */
  formatStatus(status: string | { value: string }): string {
    if (!status) return '';

    // Handle special case for status values that might be objects with a 'value' property
    if (typeof status === 'object' && status?.value) {
      status = status?.value;
    }

    // Split the status by underscore
    const parts = typeof status === 'string' && status.split('_');

    // Capitalize the first letter of each part and make the rest lowercase
    const formattedParts = parts.map(part => {
      return part.charAt(0).toUpperCase() + part.slice(1).toLowerCase();
    });

    // Join the parts with a space
    return formattedParts.join(' ');
  }


  routeToRoles() {
    this.route.navigateByUrl("accounting/v2/risk-documents-mgmt/roles")
    // this.route.navigate(["/risk-documents-mgmt/roles"])
  }

  routeToAdd() {
    this.route.navigateByUrl("accounting/v2/risk-documents-mgmt/add-new-document")
    // this.route.navigate(["/risk-documents-mgmt/add-new-document"])
  }

  showHistoryModal(_data: any) {
    const dialogRef = this.ccDialog.originalOpen(DocumentHistoryDialogComponent, {
      width: '1000px',
      data: {document: _data},
    });
    const sub = dialogRef.afterClosed().subscribe((result: boolean) => {
      if (result) this.getTodos({page: 0, size: 20});
    });
    this.subscriptions.add(sub);
  }

  doneRenewal(row: any, registration: boolean) {
    const dialogRef = this.ccDialog.originalOpen(UpdateTodoDialogComponent, {
      width: '900px',
      data: {type: registration ? "VEHICLE" : "NON_VEHICLE", todo: row},
    });
    const sub = dialogRef.afterClosed().subscribe((result: boolean) => {
      if (result) {
        this.getTodos({page: 0, size: 20});
        this.cdr.markForCheck();
      }
    });
    this.subscriptions.add(sub);
  }

  changePendingRenewalTodoStatus(row: any, status: string = "") {
    if (row.type == "NON_VEHICLE") {
      this.service.changeStatus(row.id, "UNDER_RENEWAL").subscribe(res => {
        this.riskManagementDocumentsVisits.content = [];
        this.pendingRenewalTodos.content = [];
        this.getTodos({
          page: 0,
          size: 20
        });
        this.cdr.markForCheck();
      }, error => {
        this.notificaitonService.notifyError(error.error.message);
      });
    } else if (row.type == "VEHICLE") {
      this.service.changeStatus(row.id, status).subscribe(res => {
        this.riskManagementDocumentsVisits.content = [];
        this.pendingRenewalTodos.content = [];
        this.getTodos({
          page: 0,
          size: 20
        });
        this.cdr.markForCheck();
      }, error => {
        this.notificaitonService.notifyError(error.error.message);
      });
    }
  }


  showEditModal(_data: any) {
    const dialogRef = this.ccDialog.originalOpen(EditRiskMgmtDocumentMainInfoDialogComponent, {
      width: '900px',
      data: {document: _data},
    });
    const sub = dialogRef.afterClosed().subscribe((result: boolean) => {
      if (result) {
        this.service.searchDocument(this.pageable, this.apifilter, true).subscribe(res => {
          this.riskManagementDocumentsList = res;
          this.startIndex = res.number * res.size;
          this.cdr.markForCheck();
        });
        this.getTodos({page: 0, size: 20});
      }
    });
    this.subscriptions.add(sub);
  }

  showVisitModal(_data: any) {
    const dialogRef = this.ccDialog.originalOpen(DocumentVisitDialogComponent, {
      width: '1000px',
      data: {document: _data},
    });
    const sub = dialogRef.afterClosed().subscribe((result: boolean) => {
      if (result) {
        this.service.searchDocument(this.pageable, this.apifilter, true).subscribe(res => {
          this.riskManagementDocumentsList = res;
          this.startIndex = res.number * res.size;
          this.cdr.markForCheck();
        });
        this.getTodos({page: 0, size: 20});
      }
    });
    this.subscriptions.add(sub);
  }

  todoRenewalGridCols: CCGridColumn[] = [
    {field: 'governmentEntity', header: 'Government Entity'},
    {field: 'name', header: 'Name'},
    {field: 'type', header: 'Type'},
    {field: 'expiryDate', header: 'Expiry Date'},
    {field: 'accountablePerson', header: 'Accountable Person'},
    {
      field: 'relatesTo',
      header: 'Related To',
      formatter: (rowData) => {
        return `<span>${rowData.relatesTo
          ?.map((e: any) => e.label).join(", ")}</span>`
      }
    },
    {
      field: 'status',
      header: 'Status',
      formatter: (rowData: any) => `<span>${this.formatStatus(rowData.status)}</span>`
    },
  ];

  governments = [];

  ngOnInit(): void {
    this.authService.loggedUser$.subscribe((user: any) => {
      this.currentUser = user;
    });

    this.picklistService
      .getPicklist({
        code: 'risk_document_management_government_entity',
      }).subscribe(res => {
      this.filteredGovs = res;
      this.governments = res;
      this.cdr.markForCheck();
    });

    this.getTodos({
      page: 0,
      size: 20,
      sort: 'riskDocumentsManagement.riskDocumentsManagement,ASC'
    });
  }

  disableDocument(id: any) {
    this.ccDialog.confirm(
      'Disable Document',
      'Please note that this action will close the todos and mark the document as inactive. ' +
      'Are you sure you want to proceed?',
      () => {
        this.service.disableDocument(id, false).subscribe({
          next: (res: any) => {
            this.notificaitonService.notifySuccess('Document Disabled Successfully');
            this.getTodos({page: 0, size: 20})
            this.cdr.markForCheck();
          },
          error: (err: any) =>
            this.notificaitonService.notifyError(err.error.message),
        })

      },
      () => {
      },
      'Proceed',
      'Cancel')
  }

  showVisitDone(todo: any) {
    const dialogRef = this.ccDialog.originalOpen(UpdateTodoDialogComponent, {
      width: '600px',
      data: {
        todo: todo,
        type: 'VISIT'
      },
    });
    const sub = dialogRef.afterClosed().subscribe((result) => {
      if (result) {
        this.getTodos({page: 0, size: 20})
      }
    });
    this.subscriptions.add(sub);
  }

  getNextPageVisit(event: any) {
    this.service.getAllVisitsTodos({
      page: event.pageIndex,
      size: event.pageSize,
    }).subscribe(res => {
      this.riskManagementDocumentsVisits = res;
      this.cdr.markForCheck();
    });
  }

  getNextPagePendingRenewal(event: any) {
    this.service.getAllRenewalTodos({
      page: event.pageIndex,
      size: event.pageSize,
    }).subscribe(res => {
      this.pendingRenewalTodos = res;
      this.cdr.markForCheck();
    });
  }

  getTodos(event: any) {
    this.service.getAllVisitsTodos({
      page: event.page,
      size: event.size,
      sort: event?.sort
    }).subscribe(res => {
      this.riskManagementDocumentsVisits = res;
      this.cdr.markForCheck();
    });
    this.service.getAllRenewalTodos({
      page: event.page,
      size: event.size,
      sort: event?.sort
    }).subscribe(res => {
      this.pendingRenewalTodos = res;
      this.cdr.markForCheck();
      let todoId = parseInt(this.route.url.split('/')[this.route.url.split('/').length - 1]);
      if (!isNaN(todoId)) {
        this.service.getTodo(todoId).subscribe(response => {
          if (response != null) {
            if (response.type == 'VEHICLE' && response.status.value == 'PENDING_RENEWAL') {
              this.changePendingRenewalTodoStatus(response, 'RTA_TESTING')
            }
            if (response.type == 'NON_VEHICLE' && response.status.value == 'PENDING_RENEWAL') {
              this.changePendingRenewalTodoStatus(response)
            }
          }
        })
      }
    });
  }

  apifilter: any = [];

  filteredGovs: any = [];

  // Array to store government entity codes with no data
  noDataGov: string[] = [];

  // Counter for pending searches
  pendingSearches: number = 0;

  // Flag to indicate if search is completed
  searchCompleted: boolean = false;

  resetFilters() {
    this.apifilter = [];
    this.filteredGovs = [];
    this.filter.reset();
    this.ngOnInit();
  }

  searchDocuments() {
    this.filteredGovs = [];
    this.apifilter = [];
    this.riskManagementDocumentsList.content = [];
    this.noDataGov = []; // Reset noDataGov array when starting a new search
    this.pendingSearches = 0; // Reset pending searches counter
    this.searchCompleted = false; // Reset search completed flag

    let temp: any = [];
    setTimeout(() => {
      // Apply government entity filter if selected
      if (this.filter.value.governmentEntity) {
        this.filteredGovs = this.governments.filter((e: any) => e.code == this.filter.value.governmentEntity.code);
      } else {
        // Apply other filters to all government entities
        // this.filteredGovs = this.governments;
        this.filteredGovs = this.governments;
      }

      // Check if there are any additional filters
      let hasAdditionalFilters = false;

      if (this.filter.value.name) hasAdditionalFilters = true;
      if (this.filter.value.type) hasAdditionalFilters = true;
      if (this.filter.value.relatesTo) hasAdditionalFilters = true;
      if (this.filter.value.accountablePerson) hasAdditionalFilters = true;
      if (this.filter.value.fromExpiryDate) hasAdditionalFilters = true;
      if (this.filter.value.toExpiryDate) hasAdditionalFilters = true;
      if (this.filter.value.status) hasAdditionalFilters = true;

      // If there are additional filters and we have government entities
      if (hasAdditionalFilters && this.governments.length > 0) {
        // Set the number of pending searches
        this.pendingSearches = this.filter.value.governmentEntity ? temp.length : this.governments.length;

        // Create a copy of the filters for batch processing
        const allFilters = [];

        // Prepare all filters for each government entity
        this.filter.value.governmentEntity ? this.filteredGovs.forEach(g => {
            const filters = this.prepareFilters(g);
            allFilters.push({
              gov: g,
              filters: filters
            });
          }) :
          this.governments.forEach(g => {
            const filters = this.prepareFilters(g);
            allFilters.push({
              gov: g,
              filters: filters
            });
          });

        // Process all searches at once
        this.batchSearch(allFilters);
      } else {
        // If no additional filters, mark search as completed
        this.searchCompleted = true;
      }

      this.cdr.markForCheck();
    }, 100);
  }

  /**
   * Prepares filters for a government entity
   * @param gov Government entity
   * @returns Array of filters
   */
  prepareFilters(gov: any): any[] {
    const filters = [];

    // Add government entity filter
    filters.push({
      "property": "governmentEntity.code",
      "operation": "=",
      "value": `${gov.code}`
    });

    // Add other filters based on form values
    if (this.filter.value.fromExpiryDate) {
      filters.push({
        "property": "fromExpiryDate",
        "operation": ">=",
        "value": this.filter.value.fromExpiryDate
      });
    }

    if (this.filter.value.toExpiryDate) {
      filters.push({
        "property": "toExpiryDate",
        "operation": "<=",
        "value": this.filter.value.toExpiryDate
      });
    }

    if (this.filter.value.type) {
      filters.push({
        "property": "type.code",
        "operation": "=",
        "value": this.filter.value.type.code
      });
    }

    if (this.filter.value.name) {
      filters.push({
        "property": "name",
        "operation": "like",
        "value": this.filter.value.name
      });
    }

    if (this.filter.value.relatesTo) {
      filters.push({
        "property": "relatesTo",
        "operation": "=",
        "value": this.filter.value.relatesTo
      });
    }

    if (this.filter.value.accountablePerson) {
      filters.push({
        "property": "accountablePerson.id",
        "operation": "=",
        "value": this.filter.value.accountablePerson
      });
    }

    return filters;
  }

  /**
   * Process batch search for all government entities
   * @param allFilters Array of filters for each government entity
   */
  batchSearch(allFilters: any[]) {
    // Create a copy of filteredGovs to work with
    const originalGovs = this.filteredGovs.length > 0 ? [...this.filteredGovs] : [...this.governments];

    // Counter for completed searches
    let completedSearches = 0;

    this.filteredGovs = [];
    // Process each government entity
    allFilters.forEach(item => {
      const gov = item.gov;
      const filters = item.filters;

      let pageable: {} = {};
      this.store.search$.subscribe(e => {
        pageable = e.params;
      });

      // Execute search for this government entity
      this.service.searchDocument(pageable, filters, true).subscribe(res => {
        completedSearches++;

        // If no results found for this government entity, add it to noDataGov array
        if (res.content.length === 0) {
          if (this.noDataGov.indexOf(gov.code) === -1) {
            this.noDataGov.push(gov.code);
          }
        } else {
          // If this is the first government entity with data, update the grid
          if (this.riskManagementDocumentsList.content.length === 0) {
            this.riskManagementDocumentsList = res;
            this.startIndex = res.number * res.size;
          }
        }

        // If all searches are completed, filter out government entities with no data
        if (completedSearches === allFilters.length) {
          // Filter out government entities with no data
          this.filteredGovs = originalGovs.filter(g => this.noDataGov.indexOf(g.code) === -1);
          this.searchCompleted = true;
          this.cdr.markForCheck();
        }
      });
    });
  }

  applySearch(gov: any, loading: boolean = true) {
    this.riskManagementDocumentsList.content = [];
    this.apifilter = [];
    this.apifilter.push({
      "property": "governmentEntity.code",
      "operation": "=",
      "value": `${gov.code}`
    })
    if (this.apifilter.filter((e: any) => e.property == "fromExpiryDate")?.length == 0 &&
      !!this.filter.value.fromExpiryDate) {
      this.apifilter.push({
        "property": "fromExpiryDate",
        "operation": ">=",
        "value": this.filter.value.fromExpiryDate
      });
    }
    if (this.apifilter.filter((e: any) => e.property == "toExpiryDate")?.length == 0 &&
      !!this.filter.value.toExpiryDate) {
      {
        this.apifilter.push({
          "property": "toExpiryDate",
          "operation": "<=",
          "value": this.filter.value.toExpiryDate
        })
      }
    }
    if (this.apifilter.filter((e: any) => e.property == "type.code")?.length == 0) {
      if (this.filter.value.type) {
        this.apifilter.push({
          "property": "type.code",
          "operation": "=",
          "value": this.filter.value.type.code
        })
      }
    }
    if (this.apifilter.filter((e: any) => e.property == "name")?.length == 0) {
      if (this.filter.value.name) {
        this.apifilter.push({
          "property": "name",
          "operation": "like",
          "value": `%${this.filter.value.name}%`
        })
      }
    }
    if (this.apifilter.filter((e: any) => e.property == "relatesTo")?.length == 0) {
      if (this.filter.value.relatesTo) {
        this.apifilter.push({
          "property": "relatesTo",
          "operation": "=",
          "value": this.filter.value.relatesTo
        })
      }
    }
    if (this.apifilter.filter((e: any) => e.property == "accountablePerson.id")?.length == 0) {
      if (this.filter.value.accountablePerson) {
        this.apifilter.push({
          property: "accountablePerson.id",
          operation: "=",
          value: this.filter.value.accountablePerson
        })
      }
    }
    let pageable: {} = {};
    this.store.search$.subscribe(e => {
      pageable = e.params
    })
    this.service.searchDocument(pageable, this.apifilter, loading).subscribe(res => {
      this.pageable = pageable;
      this.riskManagementDocumentsList = res;
      this.startIndex = res.number * res.size;

      // If no results found for this government entity, add it to noDataGov array
      if (res.content.length === 0) {
        console.log('No data found for government entity:', gov.label);
        if (this.noDataGov.indexOf(gov.code) === -1) {
          this.noDataGov.push(gov.code);
        }
      }

      this.cdr.markForCheck();
    });
  }

  getNextPage(e: PageEvent) {
    this.pageable = {
      ...this.pageable,
      page: e.pageIndex,
      size: e.pageSize
    };
    this.service.searchDocument({
      page: e.pageIndex,
      size: e.pageSize
    }, this.apifilter, true).subscribe(res => {
      this.riskManagementDocumentsList = res;
      this.startIndex = res.number * res.size;
      this.cdr.markForCheck();
    })
  }

  preview(row: any) {
    return this.mediaService
      .getFile('public/download/' + row?.attachments[row?.attachments.length - 1].uuid)
      .subscribe((res: any) => {
        const ext = row?.attachments[row?.attachments.length - 1]?.extension?.toLowerCase();
        const isImage = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'tiff'].includes(ext);
        const isPdf = ext === 'pdf';
        if (isPdf || isImage) {
          this.previewFile(res, row?.attachments[row?.attachments.length - 1].name);
        } else {
          this.mediaService.downloadFile('public/download/' + row?.attachments[row?.attachments.length - 1].uuid)
        };
      });
  }

  previewFile(data: any, fileName: string) {
    let blob = new Blob([data], {type: data.type});
    const blobUrl = URL.createObjectURL(blob);
    let file_type = data.type.split('/')[1];
    this.dialog.open(CCPreviewAttachmentComponent, {
      width: '100%',
      data: {
        url: blobUrl,
        blob: blob,
        type: file_type,
        filename: fileName,
      },
    });
  }
}
