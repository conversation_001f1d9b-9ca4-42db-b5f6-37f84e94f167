<div class="row">
  <div class="col-md-6"></div>
  <div class="col-md-6 text-right my-2">
    <button
      class="iconed-btn ml-2"
      cc-raised-button
      color="primary"
      (click)="openAddRole()"
    >
      <cc-icon class="icon">add</cc-icon>
      Add Role
    </button>
  </div>
</div>
<cc-datagrid
  class="my-2"
  [data]="riskManagementRolesList.content"
  [columns]="gridCols"
  [length]="riskManagementRolesList.totalElements"
  [pageOnFront]="false"
  [pageIndex]="riskManagementRolesList.number"
  [pageSize]="riskManagementRolesList.size"
  [pageSizeOptions]="[10, 20, 30, 40, 50]"
  (page)="getNextPage($event)"
  [stickyHeader]="true"
  [columnMovable]="true"
  [columnHideable]="true"
  [showColumnMenuButton]="true"
  [showColumnMenuHeader]="false"
  [columnMenuButtonIcon]="'settings'"
></cc-datagrid>
