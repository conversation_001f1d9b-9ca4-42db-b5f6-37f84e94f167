<ng-container>
  <cc-card>
    <cc-card-title
    > {{ data.type == 'VISIT' ? 'Visit Done' : data.type == 'NON_VEHICLE' ? 'Done Renewal' : 'Done Registration' }}
    </cc-card-title>
    <form
      [formGroup]="form"
      class="row"
      style="padding: 1em"
    >
      <div class="row">
        <div class="col-md-6" [hidden]="data.type != 'NON_VEHICLE'">
          <cc-datepicker label="New Issuance Date" [required]="data.type == 'NON_VEHICLE'"
                         formControlName="newIssuanceDate"></cc-datepicker>
        </div>
        <div class="col-md-6" [hidden]="data.type != 'NON_VEHICLE'">
          <cc-datepicker label="New Expiry Date" [required]="data.type == 'NON_VEHICLE'"
                         formControlName="newExpiryDate"></cc-datepicker>
        </div>
        <div class="col-md-6" [hidden]="data.type != 'VEHICLE'">
          <cc-datepicker label="New Vehicle Expiration Date" [required]="data.type == 'VEHICLE'"
                         formControlName="newVehicleExpirationDate"></cc-datepicker>
        </div>
        <div class="col-md-6" [hidden]="data.type != 'VEHICLE'">
          <cc-datepicker label="Registration Date" [required]="data.type == 'VEHICLE'"
                         formControlName="registrationDate"></cc-datepicker>
        </div>
        <div class="col-md-6" [hidden]="data.type != 'VEHICLE'">
          <cc-datepicker label="Insurance Expiration Date" [required]="data.type == 'VEHICLE'"
                         formControlName="insuranceExpirationDate"></cc-datepicker>
        </div>
        <div class="col-md-6" [hidden]="data.type != 'VEHICLE'">
          <cc-datepicker label="Issuance Date"
                         formControlName="issuanceDate"></cc-datepicker>
        </div>
        <div class="col-md-6" [hidden]="data.type != 'VISIT'">
          <cc-datepicker label="New Visit Date" [required]="data.type == 'VISIT'"
                         formControlName="newVisitDate"></cc-datepicker>
        </div>
        <div class="col-md-6" [hidden]="data.type != 'VISIT'">
          <cc-datepicker label="Due Date For Next Visit" [required]="data.type == 'VISIT'"
                         formControlName="dueDateForNextVisit"></cc-datepicker>
        </div>
        <div class="col-md-12 mt-3">
          <cc-file-uploader [tag]="getTag()" [dropzoneConfig]="{ maxFiles: 1, maxFilesize: 10 }" label="Upload attachment" formControlName="attachments" name="file"
                            (fileRemoved)="onFileRemoved($event)"></cc-file-uploader>
        </div>
        <div class="col-12 text-center">
          <button type="button" cc-flat-button (click)="onCancelClick()">Cancel</button>
          <button
            cc-raised-button
            class="m-1"
            [disabled]="!form.valid"
            color="accent"
            (click)="onSubmit()"
          >
            Confirm
          </button>
        </div>
      </div>
    </form>
  </cc-card>
</ng-container>
