import {Component, Inject, OnInit} from '@angular/core';
import {FormBuilder, FormGroup} from '@angular/forms';
import {RiskManagementDocumentsStoreService} from '../../services/risk-management-documents-store.service';
import {MatDialogRef, MAT_DIALOG_DATA} from '@angular/material/dialog';
import {RiskManagementDocumentsService} from '../../services/risk-management-documents.service';
import {CCNotificationService} from '@maids/cc-lib/services';
import {CCFileRemovedEvent} from "@maids/cc-lib/file-uploader";
import * as moment from "moment/moment";

@Component({
  selector: 'app-update-todo-dialog',
  templateUrl: './update-todo-dialog.component.html',
  styleUrls: ['./update-todo-dialog.component.scss'],
  providers: [RiskManagementDocumentsStoreService, RiskManagementDocumentsService],
})
export class UpdateTodoDialogComponent implements OnInit {
  constructor(
    private fb: FormBuilder,
    private store: RiskManagementDocumentsStoreService,
    public dialogRef: MatDialogRef<UpdateTodoDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private service: RiskManagementDocumentsService,
    private notificaitonService: CCNotificationService
  ) {
  }

  ngOnInit(): void {
    console.log(this.data);
    this.tagIndex = this.data.document.attachments.length > 0 ? this.data.document.attachments.length : 0;
  }

  onFileRemoved(evt: CCFileRemovedEvent) {
    console.clear();
    console.log(evt);
  }

  form: FormGroup = this.fb.group({
    newExpiryDate: [''],
    newIssuanceDate: [''],
    // ---------------- //
    newVehicleExpirationDate: [''],
    registrationDate: [''],
    insuranceExpirationDate: [''],
    issuanceDate: [''],
    // ---------------- //
    newVisitDate: [''],
    dueDateForNextVisit: [''],
    attachments: ['']
  });

  tagIndex = 0;
  onSubmit() {
    let payload = {}
    switch (this.data.type) {
      case 'VISIT':
        payload = {
          id: this.data.todo.id,
          newVisitDate: this.form.value.newVisitDate,
          dueDateForNextVisit: this.form.value.dueDateForNextVisit,
          attachments: this.form.value.attachments
        };
        this.service.visitDone(payload).subscribe(res => {
          this.notificaitonService.notifySuccess("Visit done successfully.");
          this.dialogRef.close(true);
        });
        break;
      case 'VEHICLE':
        payload = {
          // riskDocumentsManagementLayer: this.data.
          id: this.data.todo.id,
          newVehicleExpirationDate: this.form.value.newVehicleExpirationDate,
          registrationDate: this.form.value.newIssuanceDate,
          insuranceExpirationDate: this.form.value.newIssuanceDate,
          issuanceDate: this.form.value.issuanceDate,
          attachments: this.form.value.attachments
        };
        this.service.doneRegistration(payload).subscribe(res => {
          this.notificaitonService.notifySuccess("Registration done successfully.");
          this.dialogRef.close(true);
        });
        break;
      case 'NON_VEHICLE':
        payload = {
          id: this.data.todo.id,
          newExpiryDate: this.form.value.newExpiryDate,
          newIssuanceDate: this.form.value.newIssuanceDate,
          attachments: this.form.value.attachments
        };
        this.service.doneRenewal(payload).subscribe(res => {
          this.notificaitonService.notifySuccess("Renewal done successfully.");
          this.dialogRef.close(true);
        });
        break;
    }
  }

  getTag() {
    this.tagIndex++;
    return `${this.tagIndex}`;
  }

  onCancelClick(): void {
    this.dialogRef.close(false);
  }
}
