import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { CCRoutes } from '@maids/cc-lib/common';

import { RiskManagementDocumentsListComponent } from './components/risk-management-documents-list/risk-management-documents-list.component';
import { RiskManagementDocumentsAddEditComponent } from './components/add-edit-risk-management-documents/add-edit-risk-management-documents.component';
import { RolesListComponent } from './components/roles-list/roles-list.component';

const routes: CCRoutes = [
  {
    path: '',
    component: RiskManagementDocumentsListComponent,
    data: { label: '', pageCode: 'riskDocumentsMgmt' },
  },
  {
    path: 'on_it/:id',
    component: RiskManagementDocumentsListComponent,
    data: { label: '', pageCode: 'riskDocumentsMgmt' },
  },
  {
    path: 'add-new-document',
    component: RiskManagementDocumentsAddEditComponent,
    data: { label: 'Add New Document', pageCode: 'riskDocumentsMgmt' },
  },
  {
    path: 'roles',
    component: RolesListComponent,
    data: { label: 'Manage Roles', pageCode: 'riskDocumentsMgmt' },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class RiskManagementDocumentsModuleRoutingModule {}
