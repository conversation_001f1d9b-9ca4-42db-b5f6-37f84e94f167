import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';

import {CCButtonModule} from '@maids/cc-lib/button';
import {CCBreadcrumbsModule} from '@maids/cc-lib/layout';
import {CCDatagridModule} from '@maids/cc-lib/datagrid';
import {CCIconModule} from '@maids/cc-lib/icon';
import {CCAdvancedSearchModule} from '@maids/cc-lib/advanced-search';
import {CCCardModule} from '@maids/cc-lib/card';
import {CCConnectFormModule, CCPaginationModule} from '@maids/cc-lib/common';

import {RiskManagementDocumentsModuleRoutingModule} from './risk-management-documents-module-routing.module';

import {
  RiskManagementDocumentsListComponent
} from './components/risk-management-documents-list/risk-management-documents-list.component';
import {
  RiskManagementDocumentsAddEditComponent
} from './components/add-edit-risk-management-documents/add-edit-risk-management-documents.component';

import {RiskManagementDocumentsStoreModule} from './store/risk-management-documents-store.module';
import {AddEditRoleDialogComponent} from './components/add-edit-role-dialog/add-edit-role-dialog.component';
import {RolesListComponent} from './components/roles-list/roles-list.component';
import {CCInputModule} from "@maids/cc-lib/input";
import {CCSelectInputModule} from "@maids/cc-lib/select-input";
import {UpdateTodoDialogComponent} from './components/update-todo-dialog/update-todo-dialog.component';
import {
  EditRiskMgmtDocumentMainInfoDialogComponent
} from './components/edit-risk-mgmt-document-main-info-dialog/edit-risk-mgmt-document-main-info-dialog.component';
import {CCTabsModule} from "@maids/cc-lib/tabs";
import {CCDialogModule} from "@maids/cc-lib/dialog";
import {CCDatepickerModule, CCDaterangePickerModule} from "@maids/cc-lib/date";
import {CCCheckboxModule} from "@maids/cc-lib/checkbox";
import {CCSlideToggleModule} from "@maids/cc-lib/slide-toggle";
import {CCRadioButtonModule} from "@maids/cc-lib/radio-button";
import {CCFileUploaderModule} from "@maids/cc-lib/file-uploader";
import {DocumentHistoryDialogComponent} from "./components/document-history-dialog/document-history-dialog.component";
import {CCValidateByModule} from "@maids/cc-lib/validation";
import {CCAccordionModule} from "@maids/cc-lib/accordion";
import {DocumentVisitDialogComponent} from "./components/document-visit-dialog/document-visit-dialog.component";

@NgModule({
  declarations: [
    RiskManagementDocumentsListComponent,
    RiskManagementDocumentsAddEditComponent,
    AddEditRoleDialogComponent,
    RolesListComponent,
    UpdateTodoDialogComponent,
    EditRiskMgmtDocumentMainInfoDialogComponent,
    DocumentHistoryDialogComponent,
    DocumentVisitDialogComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    CCDialogModule,
    // cc lib modules
    CCBreadcrumbsModule,
    CCButtonModule,
    CCDatagridModule,
    CCIconModule,
    CCAdvancedSearchModule,
    CCCardModule,
    CCConnectFormModule,
    //routing module
    RiskManagementDocumentsModuleRoutingModule,
    // store module
    RiskManagementDocumentsStoreModule,
    CCInputModule,
    CCPaginationModule,
    CCSelectInputModule,
    CCTabsModule,
    CCDaterangePickerModule,
    CCDatepickerModule,
    CCCheckboxModule,
    CCSlideToggleModule,
    CCRadioButtonModule,
    CCFileUploaderModule,
    CCValidateByModule,
    CCAccordionModule,
  ],
})
export class RiskManagementDocumentsModule {
}
