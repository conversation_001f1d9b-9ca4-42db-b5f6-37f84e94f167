import { NgModule } from "@angular/core";
import { EffectsModule } from "@ngrx/effects";
import { StoreModule } from "@ngrx/store";
import { RiskManagementDocumentsEffects } from "./risk-management-documents.effects";
import * as fromRiskManagementDocuments from "./risk-management-documents.reducer";

@NgModule({
  imports:[
    StoreModule.forFeature(fromRiskManagementDocuments.RiskManagementDocumentsFeatureKey, fromRiskManagementDocuments.reducer),
    EffectsModule.forFeature([RiskManagementDocumentsEffects])
  ],
  exports:[
    StoreModule,
    EffectsModule,
  ]
})
export class RiskManagementDocumentsStoreModule{}

