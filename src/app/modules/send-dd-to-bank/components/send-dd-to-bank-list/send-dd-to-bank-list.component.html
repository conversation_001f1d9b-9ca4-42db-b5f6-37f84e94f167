<div class="acc-7935">
  <cc-accordion class="my-2">
    <cc-panel>
      <cc-panel-title class="row align-items-center mx-1">
        <cc-icon>filter_alt</cc-icon> Basic Search</cc-panel-title
      >
      <cc-panel-body>
        <form [formGroup]="searchForm" class="row">
          <div class="col">
            <cc-input
              label="Family Name"
              type="text"
              formControlName="clientName"
            ></cc-input>
          </div>
          <div class="col">
            <cc-input
              label="Contract ID"
              type="text"
              formControlName="contractId"
              type="number"
            ></cc-input>
          </div>
          <div class="col">
            <cc-select
              label="Payment Type"
              [data]="paymentTypeOptions"
              formControlName="paymentType"
            ></cc-select>
          </div>
          <div class="col">
            <cc-select
              label="Bank Name"
              [data]="bankNameOptions"
              formControlName="bankName"
            ></cc-select>
          </div>
        </form>
        <hr />
        <div class="d-flex justify-content-center">
          <button
            cc-raised-button
            color="primary"
            style="padding-left: 30px"
            (click)="search()"
          >
            <cc-icon class="icon">search</cc-icon> Search
          </button>
        </div>
      </cc-panel-body>
    </cc-panel>
  </cc-accordion>
  <div
    class="d-flex justify-content-between my-2 align-items-center"
    *ngIf="records"
  >
    <div class="col-md-auto row">
      <button
        class="col-md-auto"
        cc-raised-button
        color="accent"
        [disabled]="records?.totalElements == 0"
        (click)="
          !isSelectAll && checkIfSelectedPage()
            ? unSelectPage()
            : selectPage(true)
        "
      >
        {{ !isSelectAll && checkIfSelectedPage() ? "Unselect" : "Select" }}
        Page
      </button>
      <button
        class="col-md-auto mx-1"
        cc-raised-button
        color="accent"
        [disabled]="records?.totalElements == 0"
        (click)="selectAll()"
      >
        {{ isSelectAll ? "Unselect" : "Select" }} All Recoreds
      </button>
    </div>
    <div class="col-md-auto row justify-content-end">
      <button
        class="col-md-auto mx-1"
        cc-raised-button
        [disabled]="records?.totalElements == 0"
        (click)="exportExcelDD()"
      >
        Export CSV
      </button>

      <button
        class="col-md-auto mx-1"
        cc-raised-button
        (click)="sentToBankBatch()"
      >
        Send to Bank in Batch
      </button>
      <button
        class="col-md-auto"
        cc-raised-button
        (click)="sendToBankInBatchByRpa()"
      >
        Send to bank in batch by RPA
      </button>
    </div>
  </div>
  <cc-datagrid
    class="my-2"
    [data]="records?.content"
    [columns]="gridCols"
    [length]="records?.totalElements"
    [pageOnFront]="false"
    [pageIndex]="records?.number"
    [pageSize]="records?.size"
    [pageSizeOptions]="[20]"
    (page)="getNextPage($event)"
    (sortChange)="onSortChange($event)"
    [stickyHeader]="true"
    [columnMovable]="true"
    [columnHideable]="true"
    [showColumnMenuButton]="true"
    [showColumnMenuHeader]="false"
    [columnMenuButtonIcon]="'settings'"
    [cellTemplate]="{
      select: select,
      client: client,
      contractPaymentTerm: contractPaymentTerm,
      notes: notes
    }"
  ></cc-datagrid>
  <ng-template #select let-row let-index="index" let-col="colDef">
    <cc-checkbox
      color="accent"
      (ngModelChange)="checkIfAllSelected()"
      [(ngModel)]="selectedDDPayments[row.id]"
    ></cc-checkbox>
  </ng-template>
  <ng-template #client let-row let-index="index" let-col="colDef">
    <a
      class="cc-secondary"
      style="font-weight: bold"
      [href]="
        '#!/client/details/' +
        row.directDebit.contractPaymentTerm.contract.client.id
      "
      >{{ row.directDebit.contractPaymentTerm.contract.client.name }}</a
    >
  </ng-template>
  <ng-template #contractPaymentTerm let-row let-index="index" let-col="colDef">
    <a
      style="color: gray; font-weight: bold"
      *ngIf="!row.directDebit.contractPaymentTerm.isActive"
      [href]="
        '#!/accounting/contract-payments-files/' +
        row.directDebit.contractPaymentTerm.contract.id
      "
      >{{ row.applicationId }} Inactive</a
    >
    <a
      class="cc-secondary"
      style="font-weight: bold"
      *ngIf="row.directDebit.contractPaymentTerm.isActive"
      [href]="
        '#!/accounting/contract-payments-files/' +
        row.directDebit.contractPaymentTerm.contract.id
      "
      >{{ row.applicationId }}</a
    >
  </ng-template>
  <ng-template #notes let-row let-index="index" let-col="colDef">
    <div>
      <span class="desc-row" *ngIf="row?.directDebit?.dataEntryNotes">
        {{ row?.directDebit?.dataEntryNotes }}
      </span>
      <button
        cc-icon-button
        (click)="showNote(row?.directDebit?.id, row?.directDebit?.dataEntryNotes)"
      >
        <i class="material-icons">edit</i>
      </button>
    </div>
  </ng-template>
</div>
