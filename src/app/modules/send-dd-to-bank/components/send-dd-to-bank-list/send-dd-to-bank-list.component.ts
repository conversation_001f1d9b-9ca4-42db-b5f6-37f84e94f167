import { Component, OnInit } from '@angular/core';
import { SendDdToBankService } from '../../services/send-dd-to-bank.service';
import { FormBuilder } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { PageEvent } from '@angular/material/paginator';
import { Sort } from '@angular/material/sort';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { CCDialog } from '@maids/cc-lib/dialog';
import { BankBatchComponent } from '../bank-batch/bank-batch.component';
import { API } from 'src/environments/api';
@Component({
  selector: 'app-send-dd-to-bank-list',
  templateUrl: './send-dd-to-bank-list.component.html',
  styleUrls: ['./send-dd-to-bank-list.component.scss'],
})
export class SendDdToBankListComponent implements OnInit {
  records: any | null = null;
  searchForm = this.formBuilder.group({
    clientName: [null],
    contractId: [null],
    paymentType: [null],
    bankName: [null],
  });
  paymentTypeOptions: any[] = [];
  bankNameOptions: any[] = [];
  selectedDDPayments: boolean[] = [];
  isSelectAll: boolean = false;
  currentPageIndex: number = 0;
  currentPageSize: number = 20;
  currentSort: string = '';
  constructor(
    private sendDdToBankService: SendDdToBankService,
    private formBuilder: FormBuilder,
    private route: ActivatedRoute,
    public readonly notifications: CCNotificationService,
    private ccDialog: CCDialog,
    private mediaService: MediaService
  ) {}

  ngOnInit(): void {
    this.sendDdToBankService.searchSubject.next({
      params: {
        page: 0,
        size: 20,
        sort: 'creationDate,DESC',
        ddcontractpayments: true,
      },
    });
    this.getDDTable();
    this.getBankNames();
    this.getTypeOfPayment();
  }
  gridCols: CCGridColumn[] = [
    { field: 'select', header: 'Select' },
    {
      field: 'client',
      header: 'Family',
      sortable: true,
      sortProp: {
        arrowPosition: 'before',
        id: 'directDebit.contractPaymentTerm.contract.client.name',
        start: 'desc',
      },
    },
    {
      field: 'contractPaymentTerm',
      header: 'DD Reference',
      sortable: true,
      sortProp: {
        arrowPosition: 'before',
        id: 'applicationId',
        start: 'desc',
      },
      class: 'max',
    },
    {
      field: 'startDate',
      header: 'Payment Date',
      sortable: true,
      type: 'date',
      sortProp: {
        arrowPosition: 'before',
        id: 'startDate',
        start: 'desc',
      },
      class: 'max',
    },
    {
      field: 'creationDate',
      header: 'Creation Date',
      sortable: true,
      type: 'date',
      sortProp: {
        arrowPosition: 'before',
        id: 'creationDate',
        start: 'desc',
      },
      class: 'max',
    },
    {
      field: 'amount',
      header: 'Amount',
      sortable: true,
      sortProp: {
        arrowPosition: 'before',
        id: 'amount',
        start: 'desc',
      },
      class: 'max',
    },
    { field: 'type', header: 'DD Type' },
    {
      field: 'paymentType',
      header: 'Payment Type',
      formatter(rowData) {
        return rowData.directDebit && rowData.directDebit.paymentType
          ? rowData.directDebit.paymentType.name
          : '';
      },
    },
    { field: 'paymentsCount', header: 'Number of Payments' },
    { field: 'bankName', header: 'Bank Name' },
    { field: 'attachment', header: 'Download DD' },
    {
      field: 'sent',
      header: 'Sent to the Bank',
      type: 'button',
      sortable: false,
      buttonConfig: {
        mode: 'single',
        disabled: false,
        type: 'raised',
        color: 'accent',
        text: 'Sent to the Bank',
        callback: (row: any) => {
          this.sentToBank(row.id);
        },
        hidden: (row: any) => {
          return false;
        },
      },
    },
  ];
  getDDTable() {
    if (this.searchForm.controls['clientName'].value) {
      this.sendDdToBankService.searchSubject.next({
        params: {
          ...this.sendDdToBankService.searchSubject.getValue().params,
          clientName: this.searchForm.controls['clientName'].value,
        },
      });
    } else {
      this.sendDdToBankService.searchSubject.next({
        params: {
          ...this.sendDdToBankService.searchSubject.getValue().params,
          clientName: '',
        },
      });
    }
    if (this.searchForm.controls['contractId'].value) {
      this.sendDdToBankService.searchSubject.next({
        params: {
          ...this.sendDdToBankService.searchSubject.getValue().params,
          contractId: this.searchForm.controls['contractId'].value,
        },
      });
    } else {
      this.sendDdToBankService.searchSubject.next({
        params: {
          ...this.sendDdToBankService.searchSubject.getValue().params,
          contractId: '',
        },
      });
    }
    if (this.searchForm.controls['paymentType'].value) {
      this.sendDdToBankService.searchSubject.next({
        params: {
          ...this.sendDdToBankService.searchSubject.getValue().params,
          paymentType: this.searchForm.controls['paymentType'].value,
        },
      });
    } else {
      this.sendDdToBankService.searchSubject.next({
        params: {
          ...this.sendDdToBankService.searchSubject.getValue().params,
          paymentType: '',
        },
      });
    }
    if (this.searchForm.controls['bankName'].value) {
      this.sendDdToBankService.searchSubject.next({
        params: {
          ...this.sendDdToBankService.searchSubject.getValue().params,
          bankName: this.searchForm.controls['bankName'].value.replace(
            / /g,
            '+'
          ),
        },
      });
    } else {
      this.sendDdToBankService.searchSubject.next({
        params: {
          ...this.sendDdToBankService.searchSubject.getValue().params,
          bankName: '',
        },
      });
    }
    this.sendDdToBankService
      .getDDTable()
      .subscribe((res: any) => {
        this.records = res;
        if (this.isSelectAll) {
          this.selectPage();
        }
      });
  }
  getNextPage(event: PageEvent) {
    this.sendDdToBankService.searchSubject.next({
      params: {
        page: event.pageIndex,
        size: event.pageSize,
        sort: this.sendDdToBankService.searchSubject.getValue().params.sort,
        ddcontractpayments: true,
      },
    });
    this.currentPageIndex = event.pageIndex;
    this.currentPageSize = event.pageSize;
    this.currentSort =
      this.sendDdToBankService.searchSubject.getValue().params.sort;
    this.getDDTable();
  }
  readonly getBankNames = () => {
    this.sendDdToBankService.getBankNames().subscribe((res: any) => {
      this.bankNameOptions = res;
    });
  };
  readonly getTypeOfPayment = () => {
    this.sendDdToBankService.getTypeOfPayment().subscribe((res: any) => {
      this.paymentTypeOptions = res;
    });
  };
  onSortChange(event: Sort) {
    this.sendDdToBankService.searchSubject.next({
      params: {
        page: this.sendDdToBankService.searchSubject.getValue().params.page,
        size: this.sendDdToBankService.searchSubject.getValue().params.size,
        sort: event.active + ',' + event.direction,
        ddcontractpayments: true,
      },
    });
    this.currentPageIndex =
      this.sendDdToBankService.searchSubject.getValue().params.page;
    this.currentPageSize =
      this.sendDdToBankService.searchSubject.getValue().params.size;
    this.currentSort = event.active + ',' + event.direction;
    this.getDDTable();
  }
  sentToBank(id: number) {
    this.sendDdToBankService.sentToBank(id).subscribe((res: any) => {
      this.getDDTable();
      this.unSelectAll();
      this.notifications.notifySuccess(res);
    });
  }
  checkIfSelectedPage(): boolean {
    let selected = true;
    this.records?.content.forEach((element: any) => {
      if (!this.selectedDDPayments[element.id]) {
        selected = false;
      }
    });
    return selected;
  }
  unSelectPage() {
    this.isSelectAll = false;
    this.records?.content.forEach((element: any) => {
      this.selectedDDPayments[element.id] = false;
    });
  }
  selectPage(justPage?: any) {
    if (justPage) {
      if (this.isSelectAll) {
        this.selectedDDPayments = [];
      }
      this.isSelectAll = false;
    }
    this.records?.content.forEach((element: any) => {
      this.selectedDDPayments[element.id] = true;
    });
  }
  selectAll() {
    if (this.isSelectAll) {
      this.unSelectAll();
      return;
    }
    this.isSelectAll = true;
    this.selectPage();
  }
  unSelectAll() {
    this.isSelectAll = false;
    this.selectedDDPayments = [];
  }
  checkIfAllSelected() {
    let keys = Object.keys(this.selectedDDPayments);
    keys.forEach((key: any) => {
      if (this.selectedDDPayments[key]) {
        this.isSelectAll = false;
      }
    });
  }
  sendToBankInBatchByRpa() {
    let ddPaymentsIds: any[] = [];
    if (Object.keys(this.selectedDDPayments).length === 0) {
      this.notifications.notifyError(
        'You must select at least one direct debit file'
      );
      return;
    }
    this.selectedDDPayments.forEach((element, index) => {
      ddPaymentsIds.push(index);
    });
    let params: any = {};
    if (this.searchForm.controls['clientName'].value) {
      params.clientName = this.searchForm.controls['clientName'].value;
    }
    if (this.searchForm.controls['contractId'].value) {
      params.contractId = this.searchForm.controls['contractId'].value;
    }
    if (this.searchForm.controls['paymentType'].value) {
      params.paymentType = this.searchForm.controls['paymentType'].value;
    }
    if (this.searchForm.controls['bankName'].value) {
      params.bankName = this.searchForm.controls['bankName'].value.replace(
        / /g,
        '+'
      );
    }

    this.sendDdToBankService
      .sendToBankInBatchByRpa(params, ddPaymentsIds)
      .subscribe({
        next: (res: any) => {
          this.notifications.notifySuccess('Done');
          this.getDDTable();
          this.unSelectAll();
        },
      });
  }
  sentToBankBatch() {
    if (Object.keys(this.selectedDDPayments).length === 0) {
      this.notifications.notifyError(
        'You must select at least one direct debit file'
      );
      return;
    }
    if (this.isSelectAll) {
      this.sentToBankBatchAll();
      return;
    }
    let ddPaymentsIds: any[] = [];
    this.selectedDDPayments.forEach((element, index) => {
      if (element) {
        ddPaymentsIds.push(index);
      }
    });
    let params: any={};
    if (this.searchForm.controls['clientName'].value) {
      params.clientName = this.searchForm.controls['clientName'].value;
    }
    if (this.searchForm.controls['contractId'].value) {
      params.contractId = this.searchForm.controls['contractId'].value;
    }
    if (this.searchForm.controls['paymentType'].value) {
      params.paymentType = this.searchForm.controls['paymentType'].value;
    }
    if (this.searchForm.controls['bankName'].value) {
      params.bankName = this.searchForm.controls['bankName'].value.replace(
        / /g,
        '+'
      );
    }
    this.sendDdToBankService
      .sendmultipleddfs(params, ddPaymentsIds)
      .subscribe((res: any) => {
        this.ccDialog
          .originalOpen(BankBatchComponent, {
            data: {
              selectedDDsNum: ddPaymentsIds.length,
              data: res,
              batchFSnum: res.length,
              model: {
                clientName: this.searchForm.controls['clientName'].value,
                contractId: this.searchForm.controls['contractId'].value,
                paymentType: this.searchForm.controls['paymentType'].value,
                bankName: this.searchForm.controls['bankName'].value,
              },
            },
          })
          .afterClosed()
          .subscribe((res: boolean) => {
            if (res) {
              this.getDDTable();
              this.unSelectAll();
            }
          });
      });
  }
  sentToBankBatchAll() {
    let params: any;
    if (this.searchForm.controls['clientName'].value) {
      params.clientName = this.searchForm.controls['clientName'].value;
    }
    if (this.searchForm.controls['contractId'].value) {
      params.contractId = this.searchForm.controls['contractId'].value;
    }
    if (this.searchForm.controls['paymentType'].value) {
      params.paymentType = this.searchForm.controls['paymentType'].value;
    }
    if (this.searchForm.controls['bankName'].value) {
      params.bankName = this.searchForm.controls['bankName'].value.replace(
        / /g,
        '+'
      );
    }
    this.sendDdToBankService.sendallddfs(params).subscribe((res: any) => {
      this.ccDialog
        .originalOpen(BankBatchComponent, {
          data: {
            selectedDDsNum: this.records.totalElements,
            data: res,
            batchFSnum: res.length,
            model: {
              clientName: this.searchForm.controls['clientName'].value,
              contractId: this.searchForm.controls['contractId'].value,
              paymentType: this.searchForm.controls['paymentType'].value,
              bankName: this.searchForm.controls['bankName'].value,
            },
          },
        })
        .afterClosed()
        .subscribe((res: boolean) => {
          if (res) {
            this.getDDTable();
            this.unSelectAll();
          }
        });
    });
  }
  exportExcelDD() {
    if (
      !this.isSelectAll &&
      Object.keys(this.selectedDDPayments).length === 0
    ) {
      this.notifications.notifyError(
        'You must select at least one direct debit file'
      );
      return;
    }
    if (this.isSelectAll) {
      this.exportExcelDDAll();
      return;
    }
    let ddPaymentsIds: any[] = [];
    this.selectedDDPayments.forEach((element, index) => {
      if (element) {
        ddPaymentsIds.push(index);
      }
    });
    let params: any = {};
    params.sort = this.sendDdToBankService.searchSubject.getValue().params.sort;
    this.mediaService.downloadFile(API.exportddfs, '', {
      body: ddPaymentsIds,
      method: 'POST',
      params: params,
    });
  }
  exportExcelDDAll() {
    let params: any = {};
    if (this.searchForm.controls['clientName'].value) {
      params.clientName = this.searchForm.controls['clientName'].value;
    }
    if (this.searchForm.controls['contractId'].value) {
      params.contractId = this.searchForm.controls['contractId'].value;
    }
    if (this.searchForm.controls['paymentType'].value) {
      params.paymentType = this.searchForm.controls['paymentType'].value;
    }
    if (this.searchForm.controls['bankName'].value) {
      params.bankName = this.searchForm.controls['bankName'].value.replace(
        / /g,
        '+'
      );
    }
    this.records.totalElements = 2003;
    if (this.records.totalElements > 2000) {
      this.sendDdToBankService
        .sendExportallddfs(params)
        .subscribe((res: any) => {
          this.notifications.notifySuccess(res);
        });
    } else {
      this.mediaService.downloadFile(API.downloadExportallddfs, '', {
        method: 'POST',
      });
    }
  }
  search() {
    this.sendDdToBankService.searchSubject.next(
      this.sendDdToBankService.initialSearch
    );
    if (this.searchForm.controls['clientName'].value) {
      this.sendDdToBankService.searchSubject.next({
        params: {
          ...this.sendDdToBankService.searchSubject.getValue().params,
          clientName: this.searchForm.controls['clientName'].value,
        },
      });
    } else {
      this.sendDdToBankService.searchSubject.next({
        params: {
          ...this.sendDdToBankService.searchSubject.getValue().params,
          clientName: '',
        },
      });
    }
    if (this.searchForm.controls['contractId'].value) {
      this.sendDdToBankService.searchSubject.next({
        params: {
          ...this.sendDdToBankService.searchSubject.getValue().params,
          contractId: this.searchForm.controls['contractId'].value,
        },
      });
    } else {
      this.sendDdToBankService.searchSubject.next({
        params: {
          ...this.sendDdToBankService.searchSubject.getValue().params,
          contractId: '',
        },
      });
    }
    if (this.searchForm.controls['paymentType'].value) {
      this.sendDdToBankService.searchSubject.next({
        params: {
          ...this.sendDdToBankService.searchSubject.getValue().params,
          paymentType: this.searchForm.controls['paymentType'].value,
        },
      });
    } else {
      this.sendDdToBankService.searchSubject.next({
        params: {
          ...this.sendDdToBankService.searchSubject.getValue().params,
          paymentType: '',
        },
      });
    }
    if (this.searchForm.controls['bankName'].value) {
      this.sendDdToBankService.searchSubject.next({
        params: {
          ...this.sendDdToBankService.searchSubject.getValue().params,
          bankName: this.searchForm.controls['bankName'].value.replace(
            / /g,
            '+'
          ),
        },
      });
    } else {
      this.sendDdToBankService.searchSubject.next({
        params: {
          ...this.sendDdToBankService.searchSubject.getValue().params,
          bankName: '',
        },
      });
    }
    this.selectedDDPayments=[]
    this.getDDTable();
  }
}
