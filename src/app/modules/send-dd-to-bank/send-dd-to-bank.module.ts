import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { SendDdToBankRoutingModule } from './send-dd-to-bank-routing.module';
import { SendDdToBankListComponent } from './components/send-dd-to-bank-list/send-dd-to-bank-list.component';
import { CCButtonModule } from '@maids/cc-lib/button';
import { CCDatagridModule } from '@maids/cc-lib/datagrid';
import { CCInputModule } from '@maids/cc-lib/input';
import { CCTextareaModule } from '@maids/cc-lib/textarea';
import { CCSelectInputModule } from '@maids/cc-lib/select-input';
import { CCIconModule } from '@maids/cc-lib/icon';
import { CCCheckboxModule } from '@maids/cc-lib/checkbox';
import { CCDialogModule } from '@maids/cc-lib/dialog';
import { CCAccordionModule } from '@maids/cc-lib/accordion';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BankBatchComponent } from './components/bank-batch/bank-batch.component';
import { AddNewNoteComponent } from './components/add-new-note/add-new-note.component';

@NgModule({
  declarations: [SendDdToBankListComponent, BankBatchComponent,AddNewNoteComponent],
  imports: [
    CommonModule,
    SendDdToBankRoutingModule,
    CCButtonModule,
    CCDatagridModule,
    CCInputModule,
    CCTextareaModule,
    CCSelectInputModule,
    CCIconModule,
    CCCheckboxModule,
    CCDialogModule,
    CCAccordionModule,
    ReactiveFormsModule,
    FormsModule,
  ],
})
export class SendDdToBankModule {}
