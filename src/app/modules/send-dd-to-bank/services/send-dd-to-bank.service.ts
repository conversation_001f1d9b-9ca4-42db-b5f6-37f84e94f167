import { HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { CCBackendEndpoint } from '@maids/cc-erp-services';
import { SearchModel } from '@maids/cc-lib/common';
import { BehaviorSubject, map, Observable } from 'rxjs';
import { API } from 'src/environments/api';

@Injectable({
  providedIn: 'root',
})
export class SendDdToBankService {
  initialSearch: any = {
    params: {
      page: 0,
      size: 20,
      sort: '',
      ddcontractpayments: true,
    },
  };
  searchSubject = new BehaviorSubject<any>(this.initialSearch);
  constructor(
    private _http: HttpClient,
    @Inject(CCBackendEndpoint) private _api: CCBackendEndpoint
  ) {}
  getDDTable(): Observable<any> {
    return this._http.get<any>(
      `${this._api}/${API.getDDTable}`,
      { params: this.searchSubject.getValue().params }
    );
  }
  getBankNames(): Observable<any> {
    return this._http.get(`${this._api}/${API.getBankNames}`).pipe(
      map((res: any) => {
        return res.map((item: any) => {
          return { text: item, id: item };
        });
      })
    );
  }
  getTypeOfPayment(): Observable<any> {
    return this._http.get(`${this._api}/${API.getTypeOfPayments}`).pipe(
      map((res: any) => {
        return res.map((item: any) => {
          return { text: item.name, id: item.id };
        });
      })
    );
  }
  sentToBank(id: number): Observable<any> {
    return this._http.get(`${this._api}/${API.sentDDToBank}/${id}`);
  }
  sendToBankInBatchByRpa(params: any, payload: any): Observable<any> {
    return this._http.post(
      `${this._api}/${API.sendToBankInBatchByRpa}`,
      payload,
      { params }
    );
  }
  sendmultipleddfs(params: any, payload: any): Observable<any> {
    return this._http.post(`${this._api}/${API.sendmultipleddfs}`, payload, {
      params,
    });
  }
  sendallddfs(params: any): Observable<any> {
    return this._http.post(
      `${this._api}/${API.sendallddfs}`,
      {},
      {
        params,
      }
    );
  }
  confirmSentToBankBatch(params: any, payload: any): Observable<any> {
    return this._http.post(
      `${this._api}/${API.confirmSentDDToBankBatch}`,
      payload,
      { params }
    );
  }
  sendExportallddfs(params: any): Observable<any> {
    return this._http.post(
      `${this._api}/${API.sendExportallddfs}`,
      {},
      { params }
    );
  }
}
