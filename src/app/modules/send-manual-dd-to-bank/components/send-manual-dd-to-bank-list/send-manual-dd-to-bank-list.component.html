<div class="acc-7932">
  <div class="my-2">
    <h6 class="count">Record Count:{{ count }}</h6>
  </div>
  <cc-datagrid
    class="my-2"
    [data]="records?.content"
    [columns]="gridCols"
    [length]="records?.totalElements"
    [pageOnFront]="false"
    [pageIndex]="records?.number"
    [pageSize]="records?.size"
    [pageSizeOptions]="[20]"
    (page)="getNextPage($event)"
    [stickyHeader]="true"
    [columnMovable]="true"
    [columnHideable]="true"
    [showColumnMenuButton]="true"
    [showColumnMenuHeader]="false"
    [columnMenuButtonIcon]="'settings'"
    [cellTemplate]="{ attachments: attachments, confirm: confirm }"
  ></cc-datagrid>
  <ng-template #attachments let-row let-index="index" let-col="colDef">
    <a
      class="red-text"
      (click)="download(getAttachment(row.attachments).uuid,index)"
      >{{ getAttachment(row.attachments).name }}</a
    >
  </ng-template>
  <ng-template #confirm let-row let-index="index" let-col="colDef">
    <button cc-raised-button (click)="confirmSentToBankBatch(row.id)" [disabled]="!allowedIndexs.includes(index)"  >
      Confirm Sending to Bank
    </button>
  </ng-template>
</div>
