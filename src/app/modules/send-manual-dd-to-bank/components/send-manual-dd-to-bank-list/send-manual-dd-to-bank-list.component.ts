import { Component, OnInit } from '@angular/core';
import { SendManualDdToBankService } from '../../services/send-manual-dd-to-bank.service';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';

@Component({
  selector: 'app-send-manual-dd-to-bank-list',
  templateUrl: './send-manual-dd-to-bank-list.component.html',
  styleUrls: ['./send-manual-dd-to-bank-list.component.scss'],
})
export class SendManualDdToBankListComponent implements OnInit {
  records: any | null = null;
  count: number = 0;
  allowedIndexs: number[] = [];
  gridCols: CCGridColumn[] = [
    {
      field: 'attachments',
      header: 'Manual DDs Excel Sheet',
    },
    { field: 'creationDate', header: 'Date' },
    { field: 'confirm', header: 'Confirm Sending to Bank' },
  ];
  constructor(
    private sendManualDdToBankService: SendManualDdToBankService,
    private mediaService: MediaService,
    public readonly notifications: CCNotificationService
  ) {}

  ngOnInit(): void {
    this.getSendManualDDToBank();
  }
  getSendManualDDToBank(page: number = 0, size: number = 20) {
    this.sendManualDdToBankService
      .getSendManualDDToBank({ page, size })
      .subscribe((res) => {
        this.records = res;
        this.count = res.totalElements;
        this.allowedIndexs = [];
      });
  }
  getNextPage(event: any) {
    this.getSendManualDDToBank(event.pageIndex, event.pageSize);
  }
  getAttachment(attachments: any) {
    let file = attachments.find((x: any) => {
      return x.tag == 'manual_dd_batch_file';
    });
    return file;
  }
  download(uuid: string, index: number) {
    let path = 'public/download/' + uuid;
    this.mediaService.downloadFile(path);
    this.allowedIndexs.push(index);
  }
  confirmSentToBankBatch(id: number) {
    this.sendManualDdToBankService
      .confirmSentToBankBatch(id)
      .subscribe((res) => {
        this.notifications.notifySuccess(res);
        this.getSendManualDDToBank();
        this.allowedIndexs = [];
      });
  }
}
