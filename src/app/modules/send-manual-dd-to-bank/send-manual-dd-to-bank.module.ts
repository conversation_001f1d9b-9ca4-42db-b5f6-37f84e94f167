import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { SendManualDdToBankRoutingModule } from './send-manual-dd-to-bank-routing.module';
import { SendManualDdToBankListComponent } from './components/send-manual-dd-to-bank-list/send-manual-dd-to-bank-list.component';
import { CCDatagridModule } from '@maids/cc-lib/datagrid';
import { CCButtonModule } from '@maids/cc-lib/button';

@NgModule({
  declarations: [SendManualDdToBankListComponent],
  imports: [
    CommonModule,
    SendManualDdToBankRoutingModule,
    CCDatagridModule,
    CCButtonModule,
  ],
})
export class SendManualDdToBankModule {}
