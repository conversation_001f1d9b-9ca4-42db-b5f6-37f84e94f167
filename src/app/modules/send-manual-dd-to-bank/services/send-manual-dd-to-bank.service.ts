import { HttpClient } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import { CCBackendEndpoint } from '@maids/cc-erp-services';
import { Observable } from 'rxjs';
import { API } from 'src/environments/api';

@Injectable({
  providedIn: 'root',
})
export class SendManualDdToBankService {
  constructor(
    private _http: HttpClient,
    @Inject(CCBackendEndpoint) private _api: CCBackendEndpoint
  ) {}
  getSendManualDDToBank(params: any): Observable<any> {
    return this._http.get(`${this._api}/${API.getSendManualDDToBank}`, {
      params,
    });
  }
  confirmSentToBankBatch(id: number): Observable<any> {
    return this._http.post(`${this._api}/${API.confirmSentToBankBatch}/${id}`,{});
  }
}
