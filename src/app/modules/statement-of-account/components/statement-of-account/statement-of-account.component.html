<div class="container-fluid mt-2 p-0 ACC-7845">
  <cc-accordion [titleTemplate]="searchTemplate">
    <div *ccPanel="''">
      <ng-container>
        <div class="container">
          <form [formGroup]="filterForm">
            <div class="row">
              <div class="col-sm-6">
                <cc-select
                  [data]="statementTypeOptions"
                  formControlName="type"
                  [label]="'Type'"
                  [required]="true"
                ></cc-select>
              </div>
              <div class="col-sm-6">
                <cc-select
                  [lazyPageFetcher]="
                    filterForm.value.type == 'bucket'
                      ? getbucketOptions
                      : filterForm.value.type == 'revenue'
                      ? getrevenueOptions
                      : filterForm.value.type == 'expense'
                      ? getexpenseOptions
                      : getexpenseOptions
                  "
                  [search]="true"
                  [disabled]="!filterForm.value.type"
                  *ngIf="showAccount"
                  formControlName="account"
                  [label]="'Account'"
                ></cc-select>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-6">
                <cc-datepicker
                  [label]="'From Date'"
                  class="w-100"
                  formControlName="dateSearch"
                  [color]="'primary'"
                ></cc-datepicker>
              </div>
              <div class="col-sm-6">
                <cc-datepicker
                  [label]="'To Date'"
                  class="w-100"
                  [minDate]="filterForm.value.dateSearch"
                  formControlName="dateSearch2"
                  [color]="'primary'"
                ></cc-datepicker>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-6">
                <cc-select
                  [data]="searchTextOptions"
                  formControlName="descriptionOperation"
                  [label]="'Description Operation'"
                ></cc-select>
              </div>
              <div class="col-sm-6">
                <cc-input
                  [disabled]="!filterForm.value.descriptionOperation"
                  [label]="'Discription'"
                  formControlName="description"
                ></cc-input>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-6">
                <cc-select
                  [data]="searchAmountOptions"
                  formControlName="amountOperations"
                  [label]="'Amount Operation'"
                ></cc-select>
              </div>
              <div class="col-sm-6">
                <cc-input
                  [disabled]="!filterForm.value.amountOperations"
                  [label]="'Amount'"
                  formControlName="amount"
                ></cc-input>
              </div>
            </div>
            <div class="row">
              <div class="col-sm-12 text-center">
                <button
                  [disabled]="
                    !filterForm.value.type ||
                    !filterForm.value.account ||
                    (!filterForm.value.dateSearch &&
                      filterForm.value.dateSearch2) ||
                    (filterForm.value.dateSearch &&
                      !filterForm.value.dateSearch2)
                  "
                  (click)="submitFilter('HTML')"
                  cc-raised-button
                  type="submit"
                >
                  <cc-icon class="icon panel-icon" color="primary"
                    >search</cc-icon
                  >
                  Search
                </button>
              </div>
            </div>
            <div class="row text-center mt-2">
              <div class="col-md-4 offset-2">
                <button
                  [disabled]="
                    !filterForm.value.type ||
                    !filterForm.value.account ||
                    (!filterForm.value.dateSearch &&
                      filterForm.value.dateSearch2) ||
                    (filterForm.value.dateSearch &&
                      !filterForm.value.dateSearch2)
                  "
                  (click)="submitFilter('PDF')"
                  cc-raised-button
                >
                  Export As PDF
                </button>
              </div>
              <div class="col-md-4">
                <button
                  [disabled]="
                    !filterForm.value.type ||
                    !filterForm.value.account ||
                    (!filterForm.value.dateSearch &&
                      filterForm.value.dateSearch2) ||
                    (filterForm.value.dateSearch &&
                      !filterForm.value.dateSearch2)
                  "
                  (click)="submitFilter('EXCEL')"
                  cc-raised-button
                >
                  Export As EXCEL
                </button>
              </div>
            </div>
          </form>
        </div>
      </ng-container>
    </div>
  </cc-accordion>
  <ng-template #searchTemplate let-panel>
    <cc-icon style="margin-right: 10px">filter_list</cc-icon>
    <strong>{{ filterPanel.title }}</strong>
  </ng-template>
</div>
<div class="ACC-7845" [innerHtml]="html"></div>
