import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnInit,
} from '@angular/core';
import { SelectOption } from '@maids/cc-lib/select-input';
import { IRequestOptions, PaginationRequest } from '@maids/cc-lib/common';
import { FormBuilder, FormGroup } from '@angular/forms';
import { StatementOfAccountService } from '../../services/statement-of-account.service';
import { map } from 'rxjs';
import { ChangeDetection } from '@angular/cli/lib/config/workspace-schema';
import { DomSanitizer } from '@angular/platform-browser';
import { MatDialog } from '@angular/material/dialog';
import { CCPreviewAttachmentComponent } from '@maids/cc-lib/preview-attachment';
import { MediaService } from '@maids/cc-lib/services';
import { API } from '../../../../../environments/api';

@Component({
  selector: 'app-statement-of-account',
  templateUrl: './statement-of-account.component.html',
  styleUrls: ['./statement-of-account.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class StatementOfAccountComponent implements OnInit {
  filterPanel: { title: string } = {
    title: ' Advanced Search',
  };
  html!: any;
  statementTypeOptions: SelectOption[] = [
    { id: 'bucket', text: 'Bucket' },
    { id: 'revenue', text: 'Revenue' },
    { id: 'expense', text: 'Expense' },
  ];
  searchTextOptions: SelectOption[] = [
    { id: '=', text: 'Equals' },
    { id: '!=', text: 'Not Equals' },
    { id: 'like', text: 'Contains' },
    { id: 'not like', text: 'Not Contains' },
    { id: 'starts with', text: 'Starts With' },
    { id: 'ends with', text: 'Ends With' },
  ];
  searchAmountOptions: SelectOption[] = [
    { id: '=', text: 'Equals' },
    { id: '!=', text: 'Not Equals' },
    { id: '>', text: 'Greater Than' },
    { id: '<', text: 'Less Than' },
  ];
  showAccount: boolean = true;
  filterForm = this.formBuilder.group({
    type: [''],
    dateSearch: [new Date().toISOString().split('T')[0]],
    dateSearch2: [new Date().toISOString().split('T')[0]],
    account: [''],
    amountOperations: [''],
    amount: [''],
    description: [''],
    descriptionOperation: [''],
  });
  constructor(
    private formBuilder: FormBuilder,
    private service: StatementOfAccountService,
    private _cdr: ChangeDetectorRef,
    private sanitized: DomSanitizer,
    private dialog: MatDialog,
    private _mediaService: MediaService
  ) {}

  ngOnInit(): void {
    this.filterForm.controls['type'].valueChanges.subscribe((value) => {
      this.filterForm.controls['account'].reset();
      if (value) {
        this.showAccount = false;
        setTimeout(() => {
          this.showAccount = true;
        }, 0);
      }
    });
  }
  resetAdvancedSearch() {
    this.filterForm.patchValue({
      type: '',
      dateSearch: '',
      dateSearch2: '',
      account: '',
      amountOperations: '',
      amount: '',
      description: '',
      descriptionOperation: '',
    });
  }
  readonly getbucketOptions = (pageReq: PaginationRequest) => {
    return this.service.getbucketOptions(pageReq);
  };
  readonly getrevenueOptions = (pageReq: PaginationRequest) => {
    return this.service.getrevenueOptions(pageReq);
  };
  readonly getexpenseOptions = (pageReq: PaginationRequest) => {
    return this.service.getexpenseOptions(pageReq);
  };

  advancedSearchAction(type: string) {}

  getTableData(type: string) {}

  submitFilter(type: string) {
    let _filter: { [key: string]: string | object | [] } = {
      selectFilters: [],
    };
    if (this.filterForm.value?.dateSearch) {
      _filter = {
        ..._filter,
        fromDate: this.filterForm.value?.dateSearch + ' 00:00:00',
      };
    }
    if (this.filterForm.value?.dateSearch2) {
      _filter = {
        ..._filter,
        toDate: this.filterForm.value?.dateSearch2 + ' 00:00:00',
      };
    }
    if (this.filterForm.value?.type) {
      _filter[this.filterForm.value?.type] = {
        code: this.filterForm.value.account,
      };
    }
    if (
      this.filterForm.value?.descriptionOperation &&
      this.filterForm.value?.description
    ) {
      (_filter['selectFilters'] as Array<{}>).push({
        property: 'description',
        operation: this.filterForm.value.descriptionOperation,
        value: this.filterForm.value.description,
      });
    }

    if (
      this.filterForm.value?.amountOperations &&
      this.filterForm.value?.amount
    ) {
      (_filter['selectFilters'] as Array<{}>).push({
        property: 'amount',
        operation: this.filterForm.value.amountOperations,
        value: +this.filterForm.value.amount,
      });
    }
    if (type == 'HTML') {
      this.service.getData(_filter, type).subscribe((response: any) => {
        this.html = this.sanitized.bypassSecurityTrustHtml(response);
        this._cdr.markForCheck();
      });
    }
    if (type == 'PDF') {
      this.service.getData(_filter, type).subscribe((response: any) => {
        this.previewFile(response);
      });
    }
    if (type == 'EXCEL') {
      this._mediaService.downloadFile(
        [API.getStatementOfAccountData, type].join('/'),
        'EXCEL',
        { method: 'POST', body: _filter }
      );
    }
  }

  previewFile(data: any) {
    let blob = new Blob([data], { type: data.type });
    const blobUrl = URL.createObjectURL(blob);
    let file_type = data.type.split('/')[1];
    this.dialog.open(CCPreviewAttachmentComponent, {
      width: '100%',
      data: {
        url: blobUrl,
        blob: blob,
        type: file_type,
        filename:'AccountBalance.pdf'
      },
    });
  }
}
