import {Inject, Injectable} from '@angular/core';
import {HttpClient, HttpContext, HttpParams, HttpResponse} from "@angular/common/http";
import {CCBackendEndpoint, REQ_SHOW_LOADING_ICON} from "@maids/cc-erp-services";
import {map, Observable, shareReplay} from "rxjs";
import {PageableResponseModel, PaginatedEntity, PaginationRequest} from "@maids/cc-lib/common";
import {API} from "../../../../environments/api";
import {IGenericModel} from "../Models/genericModel";
import {SelectOption} from "@maids/cc-lib/select-input";

@Injectable({
  providedIn: 'root'
})
export class StatementOfAccountService {

  constructor(
    private _http: HttpClient,
    @Inject(CCBackendEndpoint) private _api: CCBackendEndpoint,
  ) {
  }


  getbucketOptions(pageReq: PaginationRequest): Observable<SelectOption[]> {
    const paramsObject = {
      page: pageReq.page,
      size: pageReq.size ?? 100,
      search: pageReq.searchString ?? '',
    };
    const params = new HttpParams({fromObject: paramsObject});
    return this._http.get<PaginatedEntity<IGenericModel>>([this._api, API.getBuckets].join('/'), {
      params: params,
      context: new HttpContext().set(REQ_SHOW_LOADING_ICON, false)
    }).pipe(map(response => response.content.map(item =>
      (<SelectOption>{
          id: item["code"],
          text: item["name"],
          data: item
        }
      ))));
  }
  getrevenueOptions(pageReq: PaginationRequest): Observable<SelectOption[]> {
    const paramsObject = {
      page: pageReq.page,
      size: pageReq.size ?? 100,
      search: pageReq.searchString ?? '',
    };
    const params = new HttpParams({fromObject: paramsObject});
    return this._http.get<PaginatedEntity<IGenericModel>>([this._api, API.getRevenues].join('/'), {
      params: params,
      context: new HttpContext().set(REQ_SHOW_LOADING_ICON, false)
    }).pipe(map(response => response.content.map(item =>
      (<SelectOption>{
          id: item["code"],
          text: item["name"],
          data: item
        }
      ))));
  }
  getexpenseOptions(pageReq: PaginationRequest): Observable<SelectOption[]> {
    const paramsObject = {
      page: pageReq.page,
      size: pageReq.size ?? 100,
      search: pageReq.searchString ?? '',
    };
    const params = new HttpParams({fromObject: paramsObject});
    return this._http.get<PaginatedEntity<IGenericModel>>([this._api, API.getExpensesList].join('/'), {
      params: params,
      context: new HttpContext().set(REQ_SHOW_LOADING_ICON, false)
    }).pipe(map(response => response.content.map(item =>
      (<SelectOption>{
          id: item["code"],
          text: item["name"],
          data: item
        }
      ))));
  }

  getData(filter: { [key: string]: string | object | [] }, type: string) {
    if(type == 'HTML')
      return this._http.post<any>([this._api, API.getStatementOfAccountData, type].join('/'), filter, {
        context: new HttpContext().set(REQ_SHOW_LOADING_ICON, true)
      })
    else
    {
      return this._http.post([this._api, API.getStatementOfAccountData, type].join('/'), filter, {
        context: new HttpContext().set(REQ_SHOW_LOADING_ICON, true),
        responseType: 'blob'
      })
    }
  }
}
