import {NgModule} from '@angular/core';
import {CommonModule} from '@angular/common';
import {CCRoutes} from "@maids/cc-lib/common";
import {RouterModule} from "@angular/router";
import {StatementOfAccountComponent} from "./components/statement-of-account/statement-of-account.component";


const routes: CCRoutes = [{
  path: '',
  children: [
    {
      path: '',
      component: StatementOfAccountComponent, data: {label: 'Statement Of Account'}
    }
  ],
  data: {label: 'Statement Of Account'}
}];

@NgModule({
  declarations: [],
  imports: [
    RouterModule.forChild(routes),
    CommonModule
  ],
  exports: [RouterModule]
})
export class StatementOfAccountRoutingModule {
}
