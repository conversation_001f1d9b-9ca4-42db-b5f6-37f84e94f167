import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { StatementOfAccountComponent } from './components/statement-of-account/statement-of-account.component';
import { StatementOfAccountRoutingModule } from './statement-of-account-routing.module';
import {CCSelectInputModule} from "@maids/cc-lib/select-input";
import {CCInputModule} from "@maids/cc-lib/input";
import {CCAmountInputModule} from "@maids/cc-lib/masked-input";
import {CCButtonModule} from "@maids/cc-lib/button";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {CCAccordionModule} from "@maids/cc-lib/accordion";
import {CCIconModule} from "@maids/cc-lib/icon";
import {CCPaginationModule} from "@maids/cc-lib/common";
import {CCDatepickerModule} from "@maids/cc-lib/date";



@NgModule({
  declarations: [
    StatementOfAccountComponent
  ],
  imports: [
    CommonModule,
    CCInputModule,
    CCAmountInputModule,
    CCButtonModule,
    ReactiveFormsModule,
    FormsModule,
    CCAccordionModule,
    CCSelectInputModule,
    StatementOfAccountRoutingModule,
    CCIconModule,
    CCPaginationModule,
    CCDatepickerModule,
  ],
})
export class StatementOfAccountModule { }
