<div class="acc-8620">
  <cc-dialog-header>
    <h1 cc-dialog-title>Purchase in one bill</h1>
    <a
      role="button"
      type="button"
      cc-icon-button
      cc-dialog-close-button
      cc-dialog-close
    ></a>
  </cc-dialog-header>
  <cc-dialog-content>
    <form [formGroup]="formGroup">
      <cc-select
        [data]="paymentMethodsOptions"
        label="Payment Method"
        formControlName="paymentMethod"
        [disabled]="true"
      ></cc-select>
      <cc-file-uploader
        label="Upload Receipt"
        formControlName="invoiceAttachment"
        tag="EXPENSE_PAYMENT_INVOICE"
        [required]="true"
      ></cc-file-uploader>
      <cc-radio-group formControlName="tax" class="d-flex justify-content-between">
        <div class="col-md-auto">
          <cc-radio-button [value]="true"> Tax Invoice </cc-radio-button>
        </div>
        <div class="col-md-auto">
          <cc-radio-button [value]="false">NON-Tax Invoice</cc-radio-button>
        </div>
      </cc-radio-group>
      <cc-file-uploader
        label="Upload Vat Invoice"
        formControlName="vatInvoiceAttachment"
        tag="EXPENSE_PAYMENT_VAT_INVOICE"
        [required]="formGroup.controls['tax'].value === true"
      >
      </cc-file-uploader>
      <cc-input
        label="VAT Amount"
        formControlName="vatAmount"
        [required]="formGroup.controls['tax'].value === true"
        type="number"
      ></cc-input>
      <cc-input
        label="Total bill Amount (AED)"
        formControlName="totalBillAmount"
        type="number"
        [disabled]="true"
      ></cc-input>
    </form>
  </cc-dialog-content>
  <cc-dialog-actions>
    <button cc-raised-button cc-dialog-close>Cancel</button>
    <button
      cc-raised-button
      color="primary"
      (click)="purchaseInOneBill()"
      [disabled]="formGroup.invalid"
    >
      Purchase
    </button>
  </cc-dialog-actions>
</div>
