import { Component, Inject, OnInit } from '@angular/core';
import { StockKeeperTodoService } from '../../services/stock-keeper-todo.service';
import { FormBuilder } from '@angular/forms';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { CCNotificationService } from '@maids/cc-lib/services';
import { CCDialogRef } from '@maids/cc-lib/dialog';

@Component({
  selector: 'app-purchase',
  templateUrl: './purchase.component.html',
  styleUrls: ['./purchase.component.scss'],
})
export class PurchaseComponent implements OnInit {
  formGroup = this.fb.group({
    invoiceAttachment: [''],
    totalAmount: [''],
    tax: [''],
    vatAmount: [''],
    vatInvoiceAttachment: [''],
    paymentMethod: [''],
    totalBillAmount: [''],
  });
  paymentMethodsOptions: any[] = [];
  constructor(
    private stockKeeperTodoService: StockKeeperTodoService,
    private fb: FormBuilder,
    @Inject(MAT_DIALOG_DATA) public data: any,
    public readonly notifications: CCNotificationService,
    private ccDialogRef: CCDialogRef<PurchaseComponent>
  ) {}

  ngOnInit(): void {
    this.paymentMethodsOptions = this.data.response.paymentMethods.map(
      (item: any) => {
        return {
          text: item.label,
          id: item.value,
        };
      }
    );
    if (this.paymentMethodsOptions.length == 1) {
      this.formGroup.controls['paymentMethod'].setValue(
        this.paymentMethodsOptions[0].id
      );
    }
    this.formGroup.controls['tax'].setValue(this.data.response.taxable);
    this.formGroup.controls['totalBillAmount'].setValue(
      this.data.response.totalBillAmount
    );
  }
  purchaseInOneBill() {
    let payload: any;
    payload = {
      vatAmount: this.formGroup.controls['vatAmount'].value
        ? this.formGroup.controls['vatAmount'].value
        : null,
      attachments: [],
    };
    if (this.formGroup.controls['tax'].value == true) {
      payload.taxInvoice = true;
      payload.nonTaxInvoice = false;
    } else {
      payload.taxInvoice = false;
      payload.nonTaxInvoice = true;
    }
    if (this.formGroup.controls['invoiceAttachment'].value) {
      payload.attachments.push(
        this.formGroup.controls['invoiceAttachment'].value[0]
      );
    }
    if (this.formGroup.controls['vatInvoiceAttachment'].value) {
      payload.attachments.push(
        this.formGroup.controls['vatInvoiceAttachment'].value[0]
      );
    }
    this.stockKeeperTodoService
      .purchaseInOneBill(this.data.currentRow.id, payload)
      .subscribe({
        next: (res: any) => {
          this.ccDialogRef.close(true);
          this.notifications.notifySuccess('Purchased Successfully');
        },
        error: (err: any) => {
          this.notifications.notifyError(err.error.message);
        },
      });
  }
}
