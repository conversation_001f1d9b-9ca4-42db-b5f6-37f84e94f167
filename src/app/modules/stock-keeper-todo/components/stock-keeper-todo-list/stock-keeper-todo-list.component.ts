import { Component, OnInit } from '@angular/core';
import { StockKeeperTodoService } from '../../services/stock-keeper-todo.service';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import * as moment from 'moment';
import { CCDialog } from '@maids/cc-lib/dialog';
import { MaintenanceRequestComponent } from '../maintenance-request/maintenance-request.component';
import { OrderReceivedComponent } from '../order-received/order-received.component';
import { CCNotificationService } from '@maids/cc-lib/services';

@Component({
  selector: 'app-stock-keeper-todo-list',
  templateUrl: './stock-keeper-todo-list.component.html',
  styleUrls: ['./stock-keeper-todo-list.component.scss'],
})
export class StockKeeperTodoListComponent implements OnInit {
  records: any;
  constructor(
    private stockKeeperTodoService: StockKeeperTodoService,
    private ccDialog: CCDialog,
    public readonly notifications: CCNotificationService
  ) {}
  gridCols: CCGridColumn[] = [
    { field: 'todoName', header: 'To-do Name' },
    { field: 'status', header: 'Status' },
    {
      field: 'creationDate',
      header: 'Creation Date',
      formatter(rowData, colDef) {
        return moment(rowData.creationDate).format('DD/MM/YYYY HH:mm');
      },
    },
  ];
  ngOnInit(): void {
    this.getStockKeeperTasks();
  }
  getStockKeeperTasks() {
    this.stockKeeperTodoService.getStockKeeperTasks().subscribe((res) => {
      this.records = res;
    });
  }
  orderReceived(rowData: any) {
    if (rowData.type == 'MAINTENANCE') {
      this.stockKeeperTodoService
        .getBillInfoForMaintenanceRequest(rowData.id)
        .subscribe({
          next: (res) => {
            this.ccDialog
              .originalOpen(MaintenanceRequestComponent, {
                data: { response: res, currentRow: rowData },
              })
              .afterClosed()
              .subscribe({
                next: (res: any) => {
                  if (res == true) {
                    this.getStockKeeperTasks();
                  }
                },
                error: (err: any) => {
                  this.notifications.notifyError(err.error.message);
                },
              });
          },
          error: (err: any) => {
            this.notifications.notifyError(err.error.message);
          },
        });
    }
    if (rowData.type == 'PURCHASING') {
      this.stockKeeperTodoService.getOrderObject(rowData.id).subscribe({
        next: (res) => {
          this.ccDialog
            .originalOpen(OrderReceivedComponent, {
              data: { response: res, currentRow: rowData },
            })
            .afterClosed()
            .subscribe((res: any) => {
              if (res == true) {
                this.getStockKeeperTasks();
              }
            });
        },
        error: (err: any) => {
          this.notifications.notifyError(err.error.message);
        },
      });
    }
  }
}
