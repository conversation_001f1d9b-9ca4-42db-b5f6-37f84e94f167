import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { StockKeeperTodoRoutingModule } from './stock-keeper-todo-routing.module';
import { StockKeeperTodoListComponent } from './components/stock-keeper-todo-list/stock-keeper-todo-list.component';
import { OrderReceivedComponent } from './components/order-received/order-received.component';
import { CCDatagridModule } from '@maids/cc-lib/datagrid';
import { CCDialogModule } from '@maids/cc-lib/dialog';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CCButtonModule } from '@maids/cc-lib/button';
import { MaintenanceRequestComponent } from './components/maintenance-request/maintenance-request.component';
import { PurchaseComponent } from './components/purchase/purchase.component';
import { CCInputModule } from '@maids/cc-lib/input';
import { CCSelectInputModule } from '@maids/cc-lib/select-input';
import { CCFileUploaderModule } from '@maids/cc-lib/file-uploader';
import { CCRadioButtonModule } from '@maids/cc-lib/radio-button';
import { CCCheckboxModule } from '@maids/cc-lib/checkbox';

@NgModule({
  declarations: [
    StockKeeperTodoListComponent,
    OrderReceivedComponent,
    MaintenanceRequestComponent,
    PurchaseComponent,
  ],
  imports: [
    CommonModule,
    StockKeeperTodoRoutingModule,
    CCDatagridModule,
    CCButtonModule,
    CCInputModule,
    CCSelectInputModule,
    CCFileUploaderModule.forChild({}),
    CCRadioButtonModule,CCCheckboxModule,
    CCDialogModule,
    FormsModule,
    ReactiveFormsModule,
  ],
})
export class StockKeeperTodoModule {}
