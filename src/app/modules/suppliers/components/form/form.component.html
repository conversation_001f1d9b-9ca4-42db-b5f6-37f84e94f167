<div class="container">
  <div class="row my-2" [formGroup]="formGroup">
    <div class="col-12">
      <cc-input
        label="Supplier ID"
        formControlName="supplierId"
        [disabled]="true"
      ></cc-input>
    </div>
    <div class="col-12">
      <cc-input
        label="Supplier Name"
        formControlName="name"
        [required]="true"
        [disabled]="formGroup.controls['synced'].value === true"
      ></cc-input>
    </div>
    <div class="col-12">
      <span *ngIf="formGroup.controls['synced'].value"
        >Synced with <PERSON>Binder</span
      >
    </div>
    <div class="col-12">
      <cc-input
        label="Supplier Location"
        formControlName="location"
        [disabled]="formGroup.controls['synced'].value === true"
      ></cc-input>
    </div>
    <div class="col-12">
      <cc-input
        label="Supplier Phone Number"
        formControlName="phoneNumber"
        [disabled]="formGroup.controls['synced'].value === true"
      ></cc-input>
    </div>
    <div class="col-12">
      <cc-input
        label="Supplier Website"
        formControlName="webSite"
        [disabled]="formGroup.controls['synced'].value === true"
      ></cc-input>
    </div>
    <div class="col-12">
      <cc-input
        label="Supplier Email"
        formControlName="email"
        [disabled]="formGroup.controls['synced'].value === true"
      ></cc-input>
    </div>
    <div class="col-12">
      <cc-select
        [lazyPageFetcher]="getExpenses"
        label="Expenses Linked to the Supplier"
        formControlName="expenses"
        [multiple]="true"
        [emitFullSelectOption]="true"
      ></cc-select>
    </div>
    <div class="col-12">
      <cc-radio-group formControlName="vatRegistered" class="col-12 px-0 row">
        <div class="col-md-auto">Vat Registered</div>
        <div class="col-md-auto">
          <cc-radio-button [value]="true">Yes</cc-radio-button>
        </div>
        <div class="col-md-auto">
          <cc-radio-button [value]="false">No</cc-radio-button>
        </div>
      </cc-radio-group>
    </div>
    <div class="col-12">
      <cc-checkbox formControlName="isTicketNumberRequired"
        >Require Ticket Number</cc-checkbox
      >
    </div>
    <div class="col-12">
      <cc-radio-group
        formControlName="paymentMethodType"
        class="col-12 px-0 row"
      >
        <div class="col-md-auto">
          <cc-radio-button (click)="switchToMultiple()" value="single"
            >Single Payment Method</cc-radio-button
          >
        </div>
        <div class="col-md-auto">
          <cc-radio-button value="multiple"
            >Multiple Payment Method</cc-radio-button
          >
        </div>
      </cc-radio-group>
    </div>
    <div
      class="col-12"
      *ngIf="formGroup.controls['paymentMethodType'].value == 'single'"
    >
      <cc-select
        label="Payment Method"
        [data]="paymentMethodOptions"
        formControlName="paymentMethods"
      ></cc-select>
    </div>
    <div class="col-12 my-2">
      <cc-select
        label="Payment Methods"
        formControlName="paymentMethods"
        [multiple]="true"
        [data]="paymentMethodOptions"
        *ngIf="formGroup.controls['paymentMethodType'].value == 'multiple'"
      ></cc-select>
      <!-- CREDIT_CARD -->
      <div
        class="row"
        *ngIf="
          formGroup.controls['paymentMethods'].value?.includes('CREDIT_CARD')
        "
      >
        <div class="col-12">
          <cc-textarea
            label="Name in Financial Statement"
            formControlName="nameInFinancialStatement"
            [required]="
              formGroup.controls['paymentMethods'].value?.includes(
                'CREDIT_CARD'
              )
            "
          ></cc-textarea>
        </div>
      </div>
      <!-- BANK_TRANSFER -->
      <div
        class="row"
        *ngIf="
          formGroup.controls['paymentMethods'].value?.includes('BANK_TRANSFER')
        "
      >
        <div class="col-12">
          <cc-radio-group
            formControlName="international"
            class="col-12 px-0 row"
          >
            <div class="col-md-auto">
              <cc-radio-button [value]="false">Local</cc-radio-button>
            </div>
            <div class="col-md-auto">
              <cc-radio-button [value]="true">International</cc-radio-button>
            </div>
          </cc-radio-group>
        </div>
        <div class="col-12 row">
          <div class="col-8">
            <cc-input
              label="IBAN"
              formControlName="iban"
              [required]="formGroup.controls['hasNoIban'].value === false"
            ></cc-input>
          </div>
          <div class="col-4 mt-1">
            <button
              cc-raised-button
              color="accent"
              (click)="validateIBAN()"
              [disabled]="!formGroup.controls['iban'].value"
            >
              Validate
            </button>
          </div>
          <div class="col-4">
            <cc-input
              label="Bank Name"
              formControlName="bankName"
              [disabled]="true"
              *ngIf="validIban"
            ></cc-input>
          </div>
        </div>
        <div class="col-12">
          <cc-checkbox formControlName="hasNoIban">
            No IBAN available, enter account number
          </cc-checkbox>
        </div>
        <div class="col-12">
          <cc-input
            label="Account Number"
            formControlName="accountNumber"
            [required]="
              formGroup.controls['hasNoIban'].value === true &&
              formGroup.controls['paymentMethods'].value?.includes(
                'BANK_TRANSFER'
              )
            "
          ></cc-input>
        </div>
        <div class="col-12">
          <cc-input
            label="Account Name"
            formControlName="accountName"
            [required]="
              formGroup.controls['paymentMethods'].value?.includes(
                'BANK_TRANSFER'
              )
            "
          ></cc-input>
        </div>
        <div
          class="col-12 row"
          *ngIf="formGroup.controls['international'].value === true"
        >
          <div class="col-8">
            <cc-input
              label="Swift"
              formControlName="swift"
              [required]="
                formGroup.controls['international'].value === true &&
                formGroup.controls['paymentMethods'].value?.includes(
                  'BANK_TRANSFER'
                )
              "
            ></cc-input>
          </div>
          <div class="col-4 mt-1">
            <button
              cc-raised-button
              color="accent"
              (click)="validateSwiftCode()"
              [disabled]="!formGroup.controls['swift'].value"
            >
              Validate
            </button>
          </div>
        </div>
        <div class="col-12 d-flex" *ngIf="validSwift">
          <div class="col-5 px-0">
            <cc-input
              label="Branch country name"
              formControlName="branchCountryName"
              [disabled]="true"
            ></cc-input>
          </div>
          <div class="col-5">
            <cc-input
              label="Branch city name"
              formControlName="branchCityName"
              [disabled]="true"
            ></cc-input>
          </div>
        </div>
        <div
          class="col-12"
          *ngIf="formGroup.controls['international'].value === true"
        >
          <cc-input
            label="Supplier Address"
            formControlName="address"
            [required]="
              formGroup.controls['international'].value === true &&
              formGroup.controls['paymentMethods'].value?.includes(
                'BANK_TRANSFER'
              )
            "
          ></cc-input>
        </div>
      </div>
      <!-- MONEY_TRANSFER -->
      <div
        class="row"
        *ngIf="
          formGroup.controls['paymentMethods'].value?.includes('MONEY_TRANSFER')
        "
      >
        <cc-phone-input
          class="col-8"
          label="Mobile Phone"
          formControlName="mobileNumber"
        ></cc-phone-input>
        <hr />
      </div>
    </div>
    <!-- ///////////////////////////////// -->
  </div>
</div>
<div class="row" *ngIf="editMode">
  <div class="col-12">
    <cc-datagrid
      [data]="historyPaymentDetails"
      [columns]="gridCols"
      [showPaginator]="false"
      [cellTemplate]="{ select: select }"
      [noResultTemplate]="noResultTemplate"
    ></cc-datagrid>
    <ng-template #select let-row>
      <button cc-raised-button color="accent" (click)="activateMethod(row)">
        Activate
      </button>
    </ng-template>
    <ng-template #noResultTemplate> No Data... </ng-template>
  </div>
</div>
<div class="d-flex col-12 justify-content-end my-4">
  <div class="col-md-auto">
    <button cc-raised-button (click)="goBack()">Cancel</button>
  </div>
  <div class="col-md-auto px-0">
    <button
      cc-raised-button
      color="primary"
      [disabled]="formGroup.invalid"
      (click)="save()"
    >
      {{ editMode ? "Update" : "Save" }}
    </button>
  </div>
</div>
