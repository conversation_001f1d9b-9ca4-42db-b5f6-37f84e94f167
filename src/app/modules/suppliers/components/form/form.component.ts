import { Component, OnInit } from '@angular/core';
import { SuppliersService } from '../../services/suppliers.service';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { PaginationRequest } from '@maids/cc-lib/common';
import { BehaviorSubject, merge, Observable, of, startWith } from 'rxjs';
import { SelectOption } from '@maids/cc-lib/select-input';
import { ActivatedRoute, Router } from '@angular/router';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { CCNotificationService } from '@maids/cc-lib/services';
import { CCDialog } from '@maids/cc-lib/dialog';

@Component({
  selector: 'app-form',
  templateUrl: './form.component.html',
  styleUrls: ['./form.component.scss'],
})
export class FormComponent implements OnInit {
  formGroup = this.fb.group({
    name: ['', Validators.required],
    phoneNumber: [''],
    webSite: [''],
    expenses: [''],
    email: [''],
    vatRegistered: [''],
    isTicketNumberRequired: [''],
    paymentMethods: [''],
    singlePaymentMethod: [''],
    paymentMethodType: [''],
    synced: [''],
    supplierId: [''],
    levelAfterReplenishment: [''],
    replenishmentLevel: [''],
    autoReplenishment: [''],
    bucketType: [''],
    disabled: [''],
    wealthBucketType: [''],
    initialBalance: [''],
    location: [''],
    code: [''],
    /////////////////
    nameInFinancialStatement: [''],
    mobileNumber: [''],
    iban: [''],
    international: [false],
    bankName: [''],
    accountNumber: [''],
    accountName: [''],
    address: [''],
    branchCountryName: [''],
    branchCityName: [''],
    swift: [''],
    hasNoIban: [false],
  });
  editMode: boolean = false;
  historyPaymentDetails: any;
  validIban: boolean = false;
  validSwift: boolean = false;
  showDropdown: boolean = false;
  constructor(
    private suppliersService: SuppliersService,
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    public readonly notifications: CCNotificationService,
    private ccDialog: CCDialog
  ) {
    if (this.route.snapshot.params['id']) {
      this.editMode = true;
    }
  }
  paymentMethodOptions: any[] = [
    { id: 'CASH', text: 'Cash' },
    { id: 'CREDIT_CARD', text: 'Credit Card' },
    { id: 'BANK_TRANSFER', text: 'Bank Transfer' },
    { id: 'MONEY_TRANSFER', text: 'Money Transfer' },
    { id: 'INVOICED', text: 'Invoiced' },
  ];
  refillOptions: any[] = [];
  readonly holderOptions = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> =>
    this.suppliersService.getHolderOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  getExpenses = (pageReq: PaginationRequest): Observable<SelectOption[]> =>
    this.suppliersService.MSgetExpensesByCode(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );

  ngOnInit(): void {
    if (this.route.snapshot.params['id']) {
      this.getSupplierData();
    }
    this.handleFormValidation();
  }
  emptyPaymentMethods() {
    this.formGroup.controls['paymentMethods'].setValue([]);
    this.formGroup.controls['singlePaymentMethod'].setValue('');
    this.formGroup.controls['mobileNumber'].setValue('');
    this.formGroup.controls['iban'].setValue('');
    this.formGroup.controls['nameInFinancialStatement'].setValue('');
    this.formGroup.controls['international'].setValue(false);
    this.formGroup.controls['bankName'].setValue('');
    this.formGroup.controls['accountNumber'].setValue('');
    this.formGroup.controls['accountName'].setValue('');
    this.formGroup.controls['address'].setValue('');
    this.formGroup.controls['branchCountryName'].setValue('');
    this.formGroup.controls['branchCityName'].setValue('');
    this.formGroup.controls['swift'].setValue('');
    this.formGroup.controls['hasNoIban'].setValue(false);
  }
  handleFormValidation() {
    this.formGroup.controls['paymentMethods'].valueChanges.subscribe((val) => {
      if (val) {
        if (val.includes('CREDIT_CARD')) {
          this.formGroup.controls['nameInFinancialStatement'].addValidators(
            Validators.required
          );
          this.formGroup.controls[
            'nameInFinancialStatement'
          ].updateValueAndValidity();
        } else {
          this.formGroup.controls['nameInFinancialStatement'].removeValidators(
            Validators.required
          );
          this.formGroup.controls[
            'nameInFinancialStatement'
          ].updateValueAndValidity();
        }
        if (val.includes('BANK_TRANSFER')) {
          this.formGroup.controls['accountName'].addValidators(
            Validators.required
          );
          this.formGroup.controls['accountName'].updateValueAndValidity();
          this.formGroup.controls['hasNoIban'].valueChanges.subscribe((val) => {
            if (val === true) {
              this.formGroup.controls['accountNumber'].addValidators(
                Validators.required
              );
              this.formGroup.controls['accountNumber'].updateValueAndValidity();
              this.formGroup.controls['iban'].clearValidators();
              this.formGroup.controls['iban'].updateValueAndValidity();
              this.formGroup.controls['iban'].setValue('');
              this.formGroup.controls['iban'].disable();
            } else {
              this.formGroup.controls['accountNumber'].clearValidators();
              this.formGroup.controls['accountNumber'].updateValueAndValidity();
              this.formGroup.controls['iban'].enable();
              this.formGroup.controls['iban'].addValidators(
                Validators.required
              );
              this.formGroup.controls['iban'].updateValueAndValidity();
            }
          });
        } else {
          this.formGroup.controls['accountName'].clearValidators();
          this.formGroup.controls['accountName'].updateValueAndValidity();
          this.formGroup.controls['accountNumber'].clearValidators();
          this.formGroup.controls['accountNumber'].updateValueAndValidity();
          this.formGroup.controls['iban'].clearValidators();
          this.formGroup.controls['iban'].updateValueAndValidity();
          this.formGroup.controls['swift'].clearValidators();
          this.formGroup.controls['swift'].updateValueAndValidity();
          this.formGroup.controls['address'].clearValidators();
          this.formGroup.controls['address'].updateValueAndValidity();
        }
        if (val.includes('MONEY_TRANSFER')) {
          this.formGroup.controls['mobileNumber'].addValidators(
            Validators.required
          );
          this.formGroup.controls['mobileNumber'].updateValueAndValidity();
        } else {
          this.formGroup.controls['mobileNumber'].clearValidators();
          this.formGroup.controls['mobileNumber'].updateValueAndValidity();
        }
      }
    });
    this.formGroup.controls['international'].valueChanges.subscribe(() => {
      this.updateInternationalValidators();
    });
  }

  updateInternationalValidators() {
    const isInternational =
      this.formGroup.controls['international'].value === true;
    const hasBankTransfer =
      this.formGroup.controls['paymentMethods'].value?.includes(
        'BANK_TRANSFER'
      );

    if (isInternational && hasBankTransfer) {
      this.formGroup.controls['swift'].addValidators(Validators.required);
      this.formGroup.controls['swift'].updateValueAndValidity();
      this.formGroup.controls['address'].addValidators(Validators.required);
      this.formGroup.controls['address'].updateValueAndValidity();
    } else {
      this.formGroup.controls['swift'].clearValidators();
      this.formGroup.controls['swift'].updateValueAndValidity();
      this.formGroup.controls['address'].clearValidators();
      this.formGroup.controls['address'].updateValueAndValidity();
    }
  }
  getSupplierData() {
    this.suppliersService
      .getSupplierData(this.route.snapshot.params['id'])
      .subscribe((res: any) => {
        this.formGroup.patchValue(res);
        if (res.name === 'Capital Medical Centre for Health') {
        }
        this.formGroup.controls['synced'].setValue(!!res.supplierId);
        let paymentMethods: any[] = [];
        if (res.paymentMethods?.length == 1) {
          this.formGroup.controls['paymentMethodType'].setValue('single');
        } else if (res.paymentMethods?.length > 1) {
          this.formGroup.controls['paymentMethodType'].setValue('multiple');
        }
        res.paymentMethods?.forEach((element: any) => {
          paymentMethods.push(element.value);
        });
        if (res.expenses) {
          this.formGroup.controls['expenses'].setValue(
            res.expenses.map((expense: any) => {
              return { id: expense.id, text: expense.label };
            })
          );
        }
        this.formGroup.controls['paymentMethods'].setValue(paymentMethods);
        this.historyPaymentDetails = res.historyPaymentDetails;
      });
  }

  save() {
    let data: any;
    if (this.formGroup.valid) {
      data = {
        name: this.formGroup.controls['name'].value,
        location: !!this.formGroup.controls['location'].value
          ? this.formGroup.controls['location'].value
          : null,
        phoneNumber: !!this.formGroup.controls['phoneNumber'].value
          ? this.formGroup.controls['phoneNumber'].value
          : null,
        webSite: !!this.formGroup.controls['webSite'].value
          ? this.formGroup.controls['webSite'].value
          : null,
        email: !!this.formGroup.controls['email'].value
          ? this.formGroup.controls['email'].value
          : null,
        vatRegistered: !!this.formGroup.controls['vatRegistered'].value
          ? this.formGroup.controls['vatRegistered'].value
          : null,
        isTicketNumberRequired: !!this.formGroup.controls[
          'isTicketNumberRequired'
        ].value
          ? this.formGroup.controls['isTicketNumberRequired'].value
          : null,
        paymentMethods: [],
        nameInFinancialStatement: !!this.formGroup.controls[
          'nameInFinancialStatement'
        ].value
          ? this.formGroup.controls['nameInFinancialStatement'].value
          : null,
        mobileNumber: !!this.formGroup.controls['mobileNumber'].value
          ? this.formGroup.controls['mobileNumber'].value
          : null,
        iban: !!this.formGroup.controls['iban'].value
          ? this.formGroup.controls['iban'].value
          : null,
        international: !!this.formGroup.controls['international'].value
          ? this.formGroup.controls['international'].value
          : null,
        bankName: !!this.formGroup.controls['bankName'].value
          ? this.formGroup.controls['bankName'].value
          : null,
        accountNumber: !!this.formGroup.controls['accountNumber'].value
          ? this.formGroup.controls['accountNumber'].value
          : null,
        accountName: !!this.formGroup.controls['accountName'].value
          ? this.formGroup.controls['accountName'].value
          : null,
        address: !!this.formGroup.controls['address'].value
          ? this.formGroup.controls['address'].value
          : null,
        swift: !!this.formGroup.controls['swift'].value
          ? this.formGroup.controls['swift'].value
          : null,
        hasNoIban: !!this.formGroup.controls['hasNoIban'].value
          ? this.formGroup.controls['hasNoIban'].value
          : null,
      };
      if (!!this.formGroup.controls['expenses'].value) {
        data.expenses = this.formGroup.controls['expenses'].value.map(
          (expense: any) => {
            return { id: +expense.id };
          }
        );
      }
      if (typeof this.formGroup.controls['paymentMethods'].value == 'string') {
        data.paymentMethods.push(
          this.formGroup.controls['paymentMethods'].value
        );
      } else if (
        typeof this.formGroup.controls['paymentMethods'].value == 'object'
      ) {
        data.paymentMethods = this.formGroup.controls['paymentMethods'].value;
      }

      // Check if validation is needed
      const needsIbanValidation =
        !this.validIban &&
        !this.formGroup.controls['hasNoIban'].value &&
        this.formGroup.controls['international'].value == false &&
        this.formGroup.controls['paymentMethods'].value?.includes(
          'BANK_TRANSFER'
        );

      const needsSwiftValidation =
        !this.validSwift &&
        this.formGroup.controls['international'].value == true &&
        this.formGroup.controls['paymentMethods'].value?.includes(
          'BANK_TRANSFER'
        );

      if (needsIbanValidation) {
        this.validateIBAN(true);
        return; // Don't proceed with submit until validation is complete
      }

      if (needsSwiftValidation) {
        this.validateSwiftCode(true);
        return; // Don't proceed with submit until validation is complete
      }

      // Only submit if no validation is needed
      this.submit(data);
    }
  }
  submit(payload: any) {
    if (this.editMode) {
      payload = {
        ...payload,
        id: +this.route.snapshot.params['id'],
      };
      this.suppliersService.updateSupplier(payload).subscribe({
        next: () => {
          this.notifications.notifySuccess('Supplier Updated Successfully');
          this.goBack();
        },
        error: (err: any) => {
          this.notifications.notifyError(err.error.message);
        },
      });
    } else {
      this.suppliersService.createSupplier(payload).subscribe({
        next: () => {
          this.notifications.notifySuccess('Supplier Added Successfully');
          this.goBack();
        },
        error: (err: any) => {
          this.notifications.notifyError(err.error.message);
        },
      });
    }
  }
  validateSwiftCode(goToSave: boolean = false) {
    this.suppliersService
      .MSvalidateSwiftCode(this.formGroup.controls['swift'].value)
      .subscribe({
        next: (res: any) => {
          if (!res.valid) {
            this.notifications.notifyError('Swift code is invalid');
            this.formGroup.controls['branchCountryName'].setValue('');
            this.formGroup.controls['branchCityName'].setValue('');
            this.formGroup.controls['bankName'].setValue('');
          } else {
            this.notifications.notifySuccess('Swift code is valid');
            this.formGroup.controls['branchCountryName'].setValue(
              res.branchCountryName
            );
            this.formGroup.controls['branchCityName'].setValue(
              res.branchCityName
            );
            this.validSwift = true;
            if (goToSave) {
              this.save();
            }
          }
        },
      });
  }
  validateIBAN(goToSave: boolean = false) {
    this.suppliersService
      .MSvalidateIBAN(this.formGroup.controls['iban'].value)
      .subscribe({
        next: (res: any) => {
          if (res.validations.iban.code !== '001') {
            this.notifications.notifyError(res.validations.iban.message);
            this.validIban = false;
          } else {
            this.notifications.notifySuccess(res.validations.iban.message);
            this.formGroup.controls['bankName'].setValue(res.bank_data.bank);
            this.validIban = true;
            if (goToSave) {
              this.save();
            }
          }
        },
        error: (err) => {
          this.notifications.notifyError(
            `${err.message ? err.message : 'Can not check the number'}`
          );
        },
      });
  }
  gridCols: CCGridColumn[] = [
    { field: 'changeDate', header: 'Change Date', width: '200px' },
    { field: 'paymentMethods', header: 'Payment Method' },
    { field: 'iban', header: 'IBAN' },
    { field: 'hasNoIban', header: 'Has No Iban', width: '100px' },
    { field: 'accountNumber', header: 'Account Number' },
    { field: 'accountName', header: 'Account Name' },
    { field: 'swift', header: 'Swift' },
    { field: 'address', header: 'Address' },
    { field: 'mobileNumber', header: 'Mobile Number' },
    { field: 'select', header: 'Select' },
  ];
  activateMethod(element: any) {
    if (element.paymentMethods && element.paymentMethods.includes(',')) {
      this.formGroup.controls['paymentMethodType'].setValue('multiple');
      let paymentMethods: any[] = [];
      element.paymentMethods.split(',').forEach((element: any) => {
        paymentMethods.push(element.trim());
      });
      this.formGroup.controls['paymentMethods'].setValue(paymentMethods);
    } else {
      this.formGroup.controls['paymentMethodType'].setValue('single');
      this.formGroup.controls['paymentMethods'].setValue(
        element.paymentMethods
      );
    }
    if (
      this.formGroup.controls['paymentMethods'].value?.includes(
        'MONEY_TRANSFER'
      )
    ) {
      this.formGroup.controls['mobileNumber'].setValue(element.mobileNumber);
    }
    if (
      this.formGroup.controls['paymentMethods'].value?.includes('BANK_TRANSFER')
    ) {
      this.formGroup.controls['iban'].setValue(element.iban);
      this.formGroup.controls['accountName'].setValue(element.accountName);
      this.formGroup.controls['swift'].setValue(element.swift);
      this.formGroup.controls['hasNoIban'].setValue(element.hasNoIban);
      this.formGroup.controls['accountNumber'].setValue(element.accountNumber);
      this.formGroup.controls['address'].setValue(element.address);
    }
    if (
      this.formGroup.controls['paymentMethods'].value?.includes('CREDIT_CARD')
    ) {
      this.formGroup.controls['nameInFinancialStatement'].setValue(
        element.nameInFinancialStatement
      );
    }
  }
  goBack() {
    this.router.navigateByUrl('/accounting/v2/suppliers-list');
  }
  switchToMultiple() {
    if (this.formGroup.controls['paymentMethods'].value?.length > 1) {
      this.ccDialog.confirm(
        '',
        'Switching to a single payment method will clear your previous entries. Continue?',
        () => {
          this.emptyPaymentMethods();
        },
        () => {
          this.formGroup.controls['paymentMethodType'].setValue('multiple');
        },
        'Yes',
        'No'
      );
    }
  }
}
