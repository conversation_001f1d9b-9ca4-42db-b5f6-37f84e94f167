import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { SuppliersRoutingModule } from './suppliers-routing.module';
import { SuppliersListComponent } from './components/suppliers-list/suppliers-list.component';
import { FormComponent } from './components/form/form.component';
import { CCDatagridModule } from '@maids/cc-lib/datagrid';
import { CCInputModule } from '@maids/cc-lib/input';
import { CCSelectInputModule } from '@maids/cc-lib/select-input';
import { CCDialogModule } from '@maids/cc-lib/dialog';
import { CCButtonModule } from '@maids/cc-lib/button';
import { CCIconModule } from '@maids/cc-lib/icon';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CCSlideToggleModule } from '@maids/cc-lib/slide-toggle';
import { CCCheckboxModule } from '@maids/cc-lib/checkbox';
import { CCFileUploaderModule } from '@maids/cc-lib/file-uploader';
import { CCTextareaModule } from '@maids/cc-lib/textarea';
import { CCRadioButtonModule } from '@maids/cc-lib/radio-button';
import { CCPhoneInputModule } from '@maids/cc-lib/masked-input';

@NgModule({
  declarations: [SuppliersListComponent, FormComponent],
  imports: [
    CommonModule,
    SuppliersRoutingModule,
    CCPhoneInputModule,
    CCDatagridModule,
    CCRadioButtonModule,
    CCInputModule,
    CCSelectInputModule,
    CCTextareaModule,
    CCDialogModule,
    CCButtonModule,
    CCIconModule,
    CCSlideToggleModule,
    CCCheckboxModule,
    CCFileUploaderModule,
    ReactiveFormsModule,
    FormsModule,
],
})
export class SuppliersModule {}
