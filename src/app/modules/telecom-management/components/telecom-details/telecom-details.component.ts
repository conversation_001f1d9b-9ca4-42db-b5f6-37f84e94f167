import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Observable } from 'rxjs';
import { TelecomManagementService } from '../../services/telecom-management.service';
import { Store } from '@ngrx/store';
import {
  deleteTelecomDetails,
  storeTelecomDetails,
} from '../../store/telecom-management.actions';

@Component({
  selector: 'app-telecom-details',
  templateUrl: './telecom-details.component.html',
  styleUrls: ['./telecom-details.component.scss'],
})
export class TelecomDetailsComponent implements OnInit, OnDestroy {
  selectedOption: string = 'details';
  id: number = 0;
  constructor(
    private route: ActivatedRoute,
    private telecomManagementService: TelecomManagementService,
    private store: Store
  ) {
    this.id = this.route.snapshot.params['id'];
  }

  ngOnInit(): void {
    this.store.dispatch(storeTelecomDetails({ id: this.id }));
    this.selectedOption = this.route.snapshot.queryParams['option'];
  }
  getMonthName(dateString: string): string {
    const date = new Date(dateString);
    const options: Intl.DateTimeFormatOptions = { month: 'short' };
    return date.toLocaleDateString('en-US', options);
  }
  setOption(option: string) {
    this.selectedOption = option;
  }
  ngOnDestroy(): void {
    this.store.dispatch(deleteTelecomDetails({ id: this.id }));
  }
}
