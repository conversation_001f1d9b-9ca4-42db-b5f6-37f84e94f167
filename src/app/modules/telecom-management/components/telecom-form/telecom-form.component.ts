import { Component, OnChang<PERSON>, OnInit, SimpleChanges } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { CCPicklistService } from '@maids/cc-erp-services';
import { PaginationRequest } from '@maids/cc-lib/common';
import { SelectOption } from '@maids/cc-lib/select-input';
import { map, Observable } from 'rxjs';
import { TelecomManagementService } from '../../services/telecom-management.service';
import { ActivatedRoute, Router } from '@angular/router';
import { CCNotificationService } from '@maids/cc-lib/services';
import { Store } from '@ngrx/store';
import { selectTelecomDetails } from '../../store/telecom-management.selectors';
import { restoreTelecomDetails } from '../../store/telecom-management.actions';

@Component({
  selector: 'app-telecom-form',
  templateUrl: './telecom-form.component.html',
  styleUrls: ['./telecom-form.component.scss'],
})
export class TelecomFormComponent implements OnInit, OnChanges {
  telecomForm: any;
  formGroup!: FormGroup;
  readonly servicesTypes = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.picklistService
      .getPicklist({
        code: 'SERVICE_TYPE',
        search: pageReq.searchString,
      })
      .pipe(
        map((val: any[]) => {
          return val.map(
            (opt) => ({ id: opt.id, text: opt.label } as SelectOption)
          );
        })
      );
  };
  readonly paymentMethods = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.picklistService
      .getPicklist({
        code: 'PAYMENT_METHOD',
        search: pageReq.searchString,
      })
      .pipe(
        map((val: any[]) => {
          return val.map(
            (opt) => ({ id: opt.id, text: opt.label } as SelectOption)
          );
        })
      );
  };
  readonly expenses$ = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> =>
    this.telecomManagementService.getExpenses(pageReq.searchString ?? '').pipe(
      map((val: any[]) => {
        return val.map(
          (opt) => ({ id: opt.id, text: opt.label } as SelectOption)
        );
      })
    );
  constructor(
    private picklistService: CCPicklistService,
    private formBuilder: FormBuilder,
    private telecomManagementService: TelecomManagementService,
    private router: Router,
    private route: ActivatedRoute,
    public notification: CCNotificationService,
    private store: Store
  ) {}
  ngOnChanges(changes: SimpleChanges): void {}

  ngOnInit(): void {
    this.formGroup = this.formBuilder.group({
      id: [null],
      name: [null, Validators.required],
      number: [null, Validators.required],
      dueEvery: [null],
      primaryExpense: [null, Validators.required],
      secondryExpense: [null],
      holders: [null],
      usageText: [null, Validators.required],
      serviceType: [null],
      paymentMethod: [null],
      notes: [null],
      monthOfLastBill: [null],
      lastBillAmount: [null],
    });
    this.store.dispatch(restoreTelecomDetails());
    if (this.route.snapshot.params['id']) {
      this.store.select(selectTelecomDetails).subscribe((form) => {
        if (form) {
          this.telecomForm = form;
          this.formGroup.patchValue({
            ...form,
            paymentMethod: form.paymentMethod
              ? {
                  id: form.paymentMethod.id,
                  text: form.paymentMethod.label,
                }
              : null,
            primaryExpense: form.primaryExpense
              ? {
                  id: form.primaryExpense.id,
                  text: form.primaryExpense.label,
                }
              : null,
            secondryExpense: form.secondryExpense
              ? {
                  id: form.secondryExpense.id,
                  text: form.secondryExpense.label,
                }
              : null,
            serviceType: form.serviceType
              ? {
                  id: form.serviceType.id,
                  text: form.serviceType.label,
                }
              : null,
            lastBillAmount: form.lastBillAmount,
          });
          this.formGroup.controls['monthOfLastBill'].disable();
        }
      });
    }
  }
  cancel() {
    this.router.navigateByUrl('accounting/v2/telecom-management');
  }
  save() {
    let payload;
    payload = {
      name: this.formGroup.controls['name'].value,
      number: this.formGroup.controls['number'].value,
      dueEvery: this.formGroup.controls['dueEvery'].value,
      holders: this.formGroup.controls['holders'].value,
      usageText: this.formGroup.controls['usageText'].value,
      serviceType: this.formGroup.controls['serviceType'].value
        ? { id: this.formGroup.controls['serviceType'].value.id }
        : null,
      paymentMethod: this.formGroup.controls['paymentMethod'].value
        ? { id: this.formGroup.controls['paymentMethod'].value.id }
        : null,
      primaryExpense: this.formGroup.controls['primaryExpense'].value
        ? { id: this.formGroup.controls['primaryExpense'].value.id }
        : null,
      secondryExpense: this.formGroup.controls['secondryExpense'].value
        ? { id: this.formGroup.controls['secondryExpense'].value.id }
        : null,
      notes: this.formGroup.controls['notes'].value
        ? this.formGroup.controls['notes'].value
        : null,
    };
    if (this.formGroup.valid) {
      this.telecomManagementService.addPhone(payload).subscribe({
        next: (res: any) => {
          this.notification.notifySuccess('Phone Added Successfully', 2000);
          this.cancel();
        },
        error: (err) => {
          this.notification.notifyError(err.error.message, 2000);
        },
      });
    }
  }
  update() {
    let payload;
    payload = {
      id: this.formGroup.controls['id'].value,
      name: this.formGroup.controls['name'].value,
      number: this.formGroup.controls['number'].value,
      dueEvery: this.formGroup.controls['dueEvery'].value,
      holders: this.formGroup.controls['holders'].value,
      usageText: this.formGroup.controls['usageText'].value,
      serviceType: this.formGroup.controls['serviceType'].value.id
        ? {
            id: this.formGroup.controls['serviceType'].value.id,
          }
        : null,
      paymentMethod: this.formGroup.controls['paymentMethod'].value.id
        ? {
            id: this.formGroup.controls['paymentMethod'].value.id,
          }
        : null,
      primaryExpense: this.formGroup.controls['primaryExpense'].value.id
        ? {
            id: this.formGroup.controls['primaryExpense'].value.id,
          }
        : null,
      secondryExpense: this.formGroup.controls['secondryExpense'].value.id
        ? {
            id: this.formGroup.controls['secondryExpense'].value.id,
          }
        : null,
      notes: this.formGroup.controls['notes'].value
        ? this.formGroup.controls['notes'].value
        : null,
    };
    this.telecomManagementService.updatePhone(payload).subscribe({
      next: (res: any) => {
        this.notification.notifySuccess('Phone Updated Successfully', 2000);
        this.cancel();
      },
      error: (err) => {
        this.notification.notifyError(err.error.message, 2000);
      },
    });
  }
}
