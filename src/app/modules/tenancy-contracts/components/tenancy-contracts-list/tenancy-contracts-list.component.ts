import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormControl,
  FormGroup,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { TenancyContractsService } from '../../services/tenancy-contracts.service';
import { CCPicklistService } from '@maids/cc-erp-services';
import { PageableResponseModel, PaginationRequest } from '@maids/cc-lib/common';
import { SelectOption } from '@maids/cc-lib/select-input';
import { Observable, distinctUntilChanged, map, of, Subscription } from 'rxjs';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { Sort } from '@angular/material/sort';
import { PageEvent } from '@angular/material/paginator';
import { CCAsyncValidatorFn, CCValidatorFn } from '@maids/cc-lib/validation';
import { CCDialog } from '@maids/cc-lib/dialog';
import { CCNotificationService } from '@maids/cc-lib/services';
import { Router } from '@angular/router';
import { UploadAttachmentsComponent } from '../upload-attachments/upload-attachments.component';
import { AttachmentsComponent } from '../attachments/attachments.component';
@Component({
  selector: 'app-tenancy-contracts-list',
  templateUrl: './tenancy-contracts-list.component.html',
  styleUrls: ['./tenancy-contracts-list.component.scss'],
})
export class TenancyContractsListComponent implements OnInit, OnDestroy {
  contracts: any | null = null;
  searchForm!: FormGroup;
  private subscriptions: Subscription[] = [];

  constructor(
    private formBuilder: FormBuilder,
    private tenancyContractsService: TenancyContractsService,
    private picklistService: CCPicklistService,
    private ccDialog: CCDialog,
    public readonly notifications: CCNotificationService,
    private router: Router
  ) {}
  startDateOperatorsOptions = [
    { id: '=', text: 'Equal' },
    { id: '<', text: 'Before' },
    { id: '>', text: 'After' },
    { id: 'between', text: 'Between' },
  ];

  expiryDateOperatorsOptions = [
    { id: '=', text: 'Equal' },
    { id: '<', text: 'Before' },
    { id: '>', text: 'After' },
    { id: 'between', text: 'Between' },
  ];

  secondPartyNameOperationsOptions = [
    { id: '=', text: 'Equal' },
    { id: '<>', text: 'Not Equal' },
    { id: 'like', text: 'Contains' },
  ];

  typeOfDocumentOperationsOptions = [
    { id: '=', text: 'Equal' },
    { id: '<>', text: 'Not Equal' },
  ];
  readonly docsTypesOptions = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.picklistService
      .getPicklist({
        code: 'PICKLIST_TENANCY_TYPE_OF_DOCUMENT',
        page: pageReq.page,
        pageSize: pageReq.size,
        search: pageReq.searchString,
      })
      .pipe(
        map((val: any[]) => {
          return val.map(
            (opt) => ({ id: opt.id, text: opt.label } as SelectOption)
          );
        })
      );
  };

  ngOnInit(): void {
    this.searchForm = this.formBuilder.group({
      active: new FormControl(true),
      expiryDate1: new FormControl(null),
      expiryDate2: new FormControl(null),
      expiryDateOperator: new FormControl(null),
      secondPartyName: new FormControl(null),
      secondPartyNameOperation: new FormControl(null),
      startDate1: new FormControl(null),
      startDate2: new FormControl(null),
      startDateOperator: new FormControl(null),
      typeOfDocument: new FormControl(null),
      typeOfDocumentOperation: new FormControl(null),
    });
    this.gettenancyContracts();
    this.formValidation();
  }
  gettenancyContracts() {
    this.searchForm.value.expiryDate1
      ? (this.searchForm.value.expiryDate1 =
          this.searchForm.value.expiryDate1 + ' 00:00:00')
      : null;
    this.searchForm.value.expiryDate2
      ? (this.searchForm.value.expiryDate2 =
          this.searchForm.value.expiryDate2 + ' 00:00:00')
      : null;
    this.searchForm.value.startDate1
      ? (this.searchForm.value.startDate1 =
          this.searchForm.value.startDate1 + ' 00:00:00')
      : null;
    this.searchForm.value.startDate2
      ? (this.searchForm.value.startDate2 =
          this.searchForm.value.startDate2 + ' 00:00:00')
      : null;
    this.searchForm.value.typeOfDocument
      ? (this.searchForm.value.typeOfDocument = {
          id: this.searchForm.value.typeOfDocument,
        })
      : null;
    this.tenancyContractsService
      .gettenancyContracts(this.searchForm.value)
      .pipe(
        map((res: any) => {
          this.contracts = res;
          return res;
        })
      )
      .subscribe();
  }
  getNextPage(e: PageEvent) {
    this.tenancyContractsService.searchSubject.next({
      params: {
        page: e.pageIndex,
        size: e.pageSize,
        sort: this.tenancyContractsService.searchSubject.getValue().params.sort,
      },
    });
    this.gettenancyContracts();
  }
  onSortChange(event: Sort) {
    this.tenancyContractsService.searchSubject.next({
      params: {
        sort: `${event.active},${event.direction}`,
        page: this.tenancyContractsService.searchSubject.getValue().params.page,
        size: this.tenancyContractsService.searchSubject.getValue().params.size,
      },
    });
    this.gettenancyContracts();
  }
  exportCSV() {
    if (this.searchForm.valid) {
      this.tenancyContractsService
        .exporttenancytoCSV(this.searchForm.value)
        .subscribe((res: any) => {
          this.downloadFile(res, 'Tenancy Contracts.csv');
        });
    }
  }
  downloadFile(data: Blob, filename: string) {
    const url = window.URL.createObjectURL(data);
    const a = document.createElement('a');
    document.body.appendChild(a);
    a.setAttribute('style', 'display: none');
    a.href = url;
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
    a.remove();
  }
  gridCols: CCGridColumn[] = [
    {
      field: 'actions',
      header: '',
      sortable: false,
      type: 'button',
      buttonConfig: {
        mode: 'menu',
        icon: 'more_vert',
        disabled: false,
        buttons: [
          {
            type: 'stroked',
            color: 'basic',
            text: 'Edit',
            icon: 'edit',
            mode: 'single',
            disabled: false,
            hidden: (row: any) => {
              return false;
            },
            callback: (row: any) => this.edit(row),
          },
          {
            type: 'stroked',
            text: 'Delete',
            icon: 'delete',
            color: 'warn',
            mode: 'single',
            disabled: false,
            hidden: (row: any) => {
              return false;
            },
            callback: (row: any) => this.delete(row),
          },
          {
            type: 'stroked',
            text: 'Upload Attachment',
            icon: 'cloud_upload',
            color: 'basic',
            mode: 'single',
            disabled: false,
            hidden: (row: any) => {
              return false;
            },
            callback: (row: any) => this.uploadAttachment(row),
          },
        ],
      },
    },
    { field: 'name', header: 'Name', sortable: true },
    { field: 'startDate', header: 'Start Date', sortable: true },
    { field: 'endDate', header: 'End Date', sortable: true },
    { field: 'description', header: 'Description', sortable: true },
    { field: 'tenantName', header: 'First party name', sortable: true },
    { field: 'ownerName', header: 'Second party name', sortable: true },
    { field: 'attachments', header: 'Attachments' },
    { field: 'typeOfDocument.label', header: 'Document type' },
  ];
  searchDate() {
    if (this.searchForm.valid) {
      this.gettenancyContracts();
    }
  }
  edit(element: any) {
    this.router.navigateByUrl(
      `/accounting/v2/tenancy-contracts/tenancy-contracts-form/${element.id}`
    );
  }
  delete(element: any) {
    this.ccDialog.confirm(
      'Warning',
      'Are you sure you want to delete this company?',
      () => {
        this.tenancyContractsService
          .deletetenancyContracts(element.id)
          .subscribe({
            next: (res: any) => {
              this.notifications.notifySuccess('Company Deleted Successfully');
              this.gettenancyContracts();
            },
            error: (err: any) =>
              this.notifications.notifyError(err.error.message),
          });
      }
    );
  }
  uploadAttachment(element: any) {
    this.ccDialog
      .originalOpen(UploadAttachmentsComponent, { data: element })
      .afterClosed()
      .subscribe((res: any) => {
        if (res) {
          this.gettenancyContracts();
        }
      });
  }
  addTenancyContract() {
    this.router.navigateByUrl('/accounting/v2/tenancy-contracts/tenancy-contracts-form');
  }
  openAttachmentsDialog(element: any) {
    this.ccDialog.originalOpen(AttachmentsComponent, {
      data: element.attachments,
    });
  }
  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions to prevent memory leaks
    this.subscriptions.forEach(sub => sub.unsubscribe());
    this.subscriptions = [];

    this.tenancyContractsService.searchSubject.next({
      params: {
        page: 0,
        size: 20,
        sort: '',
      },
    });
  }
  formValidation() {
    // Use flags to prevent infinite loops
    let isUpdatingValidation = false;

    const updateValidationSafely = (callback: () => void) => {
      if (!isUpdatingValidation) {
        isUpdatingValidation = true;
        callback();
        isUpdatingValidation = false;
      }
    };

    this.subscriptions.push(
      this.searchForm.controls['startDateOperator'].valueChanges.subscribe(
        (val) => {
          updateValidationSafely(() => {
            if (val) {
              this.searchForm.controls['startDate1'].addValidators([
                Validators.required,
              ]);
              this.searchForm.controls['startDate1'].updateValueAndValidity({ emitEvent: false });
              if (val === 'between') {
                this.searchForm.controls['startDate2'].addValidators([
                  Validators.required,
                ]);
                this.searchForm.controls['startDate2'].updateValueAndValidity({ emitEvent: false });
              } else {
                this.searchForm.controls['startDate2'].clearValidators();
                this.searchForm.controls['startDate2'].updateValueAndValidity({ emitEvent: false });
              }
            } else {
              this.searchForm.controls['startDate1'].clearValidators();
              this.searchForm.controls['startDate1'].updateValueAndValidity({ emitEvent: false });
              this.searchForm.controls['startDate2'].clearValidators();
              this.searchForm.controls['startDate2'].updateValueAndValidity({ emitEvent: false });
            }
          });
        }
      )
    );

    this.subscriptions.push(
      this.searchForm.controls['expiryDateOperator'].valueChanges.subscribe(
        (val) => {
          updateValidationSafely(() => {
            if (val) {
              this.searchForm.controls['expiryDate1'].addValidators([
                Validators.required,
              ]);
              this.searchForm.controls['expiryDate1'].updateValueAndValidity({ emitEvent: false });
              if (val === 'between') {
                this.searchForm.controls['expiryDate2'].addValidators([
                  Validators.required,
                ]);
                this.searchForm.controls['expiryDate2'].updateValueAndValidity({ emitEvent: false });
              } else {
                this.searchForm.controls['expiryDate2'].clearValidators();
                this.searchForm.controls['expiryDate2'].updateValueAndValidity({ emitEvent: false });
              }
            } else {
              this.searchForm.controls['expiryDate1'].clearValidators();
              this.searchForm.controls['expiryDate1'].updateValueAndValidity({ emitEvent: false });
              this.searchForm.controls['expiryDate2'].clearValidators();
              this.searchForm.controls['expiryDate2'].updateValueAndValidity({ emitEvent: false });
            }
          });
        }
      )
    );

    this.subscriptions.push(
      this.searchForm.controls['secondPartyNameOperation'].valueChanges.subscribe(
        (val) => {
          updateValidationSafely(() => {
            if (val) {
              this.searchForm.controls['secondPartyName'].addValidators([
                Validators.required,
              ]);
              this.searchForm.controls['secondPartyName'].updateValueAndValidity({ emitEvent: false });
            } else {
              this.searchForm.controls['secondPartyName'].clearValidators();
              this.searchForm.controls['secondPartyName'].updateValueAndValidity({ emitEvent: false });
            }
          });
        }
      )
    );

    this.subscriptions.push(
      this.searchForm.controls['typeOfDocumentOperation'].valueChanges.subscribe(
        (val) => {
          updateValidationSafely(() => {
            if (val) {
              this.searchForm.controls['typeOfDocument'].addValidators([
                Validators.required,
              ]);
              this.searchForm.controls['typeOfDocument'].updateValueAndValidity({ emitEvent: false });
            } else {
              this.searchForm.controls['typeOfDocument'].clearValidators();
              this.searchForm.controls['typeOfDocument'].updateValueAndValidity({ emitEvent: false });
            }
          });
        }
      )
    );

    this.subscriptions.push(
      this.searchForm.controls['startDate1'].valueChanges.subscribe((val) => {
        updateValidationSafely(() => {
          if (val) {
            this.searchForm.controls['startDateOperator'].setValidators([
              Validators.required,
            ]);
            this.searchForm.controls['startDateOperator'].updateValueAndValidity({ emitEvent: false });
          } else {
            this.searchForm.controls['startDateOperator'].clearValidators();
            this.searchForm.controls['startDateOperator'].updateValueAndValidity({ emitEvent: false });
          }
        });
      })
    );

    this.subscriptions.push(
      this.searchForm.controls['expiryDate1'].valueChanges.subscribe((val) => {
        updateValidationSafely(() => {
          if (val) {
            this.searchForm.controls['expiryDateOperator'].setValidators([
              Validators.required,
            ]);
            this.searchForm.controls['expiryDateOperator'].updateValueAndValidity({ emitEvent: false });
          } else {
            this.searchForm.controls['expiryDateOperator'].clearValidators();
            this.searchForm.controls['expiryDateOperator'].updateValueAndValidity({ emitEvent: false });
          }
        });
      })
    );

    this.subscriptions.push(
      this.searchForm.controls['secondPartyName'].valueChanges.subscribe(
        (val) => {
          updateValidationSafely(() => {
            if (val) {
              this.searchForm.controls['secondPartyNameOperation'].setValidators([
                Validators.required,
              ]);
              this.searchForm.controls[
                'secondPartyNameOperation'
              ].updateValueAndValidity({ emitEvent: false });
            } else {
              this.searchForm.controls[
                'secondPartyNameOperation'
              ].clearValidators();
              this.searchForm.controls[
                'secondPartyNameOperation'
              ].updateValueAndValidity({ emitEvent: false });
            }
          });
        }
      )
    );

    this.subscriptions.push(
      this.searchForm.controls['typeOfDocument'].valueChanges.subscribe((val) => {
        updateValidationSafely(() => {
          if (val) {
            this.searchForm.controls['typeOfDocumentOperation'].setValidators([
              Validators.required,
            ]);
            this.searchForm.controls[
              'typeOfDocumentOperation'
            ].updateValueAndValidity({ emitEvent: false });
          } else {
            this.searchForm.controls['typeOfDocumentOperation'].clearValidators();
            this.searchForm.controls[
              'typeOfDocumentOperation'
            ].updateValueAndValidity({ emitEvent: false });
          }
        });
      })
    );
  }
}
