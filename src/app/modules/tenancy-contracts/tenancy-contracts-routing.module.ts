import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { TenancyContractsListComponent } from './components/tenancy-contracts-list/tenancy-contracts-list.component';
import { TenancyContractsFormComponent } from './components/tenancy-contracts-form/tenancy-contracts-form.component';

const routes: Routes = [
  {
    path: '',
    component: TenancyContractsListComponent,
  },
  {
    path: 'tenancy-contracts-form',
    component: TenancyContractsFormComponent,
    data: {
      label: "Add Company's Contract & Agreements",
      pageCode: 'Company\'sContractAndAgreementsDetails',
    },
  },
  {
    path: 'tenancy-contracts-form/:tenancyId',
    component: TenancyContractsFormComponent,
    data: {
      label: "Company's Contract & Agreements Details",
      pageCode: 'Company\'sContractAndAgreementsDetails',
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class TenancyContractsRoutingModule {}
