<form class="w-100" [formGroup]="searchForm">
  <cc-advanced-search
    class="my-2"
    formControlName="search"
    name="search"
    [metaApi]="transactionPostingSetupMetaSearch"
    (onFilter)="searchDGData()"
    (onReset)="resetForm()"
  ></cc-advanced-search>
</form>

<div class="text-right my-2">
  <button
    class="iconed-btn ml-2"
    cc-flat-button
    color="accent"
    (click)="goAdd()"
  >
    <cc-icon class="icon panel-icon">add</cc-icon>
    Add A Posting Rule
  </button>
</div>

<cc-datagrid
  *ngIf="transactionPostingSetupList$ | async; let transactionPostingSetupList"
  class="my-2"
  [data]="transactionPostingSetupList?.content ?? []"
  [columns]="gridCols"
  [length]="transactionPostingSetupList?.totalElements ?? 0"
  [pageOnFront]="false"
  [pageIndex]="transactionPostingSetupList?.number ?? 0"
  [pageSize]="transactionPostingSetupList?.size ?? 0"
  [pageSizeOptions]="[20]"
  (page)="getNextPage($event)"
  [stickyHeader]="true"
  [columnMovable]="true"
  [columnHideable]="true"
  [showColumnMenuButton]="true"
  [showColumnMenuHeader]="false"
  [columnMenuButtonIcon]="'settings'"
>
  <cc-grid-actions-list
    *ccActionData="let ctx of transactionPostingSetupList?.content; row as row"
    [renderedActionsCount]="2"
    style="width: fit-content; gap: 2px"
  >
<button *cc-action cc-raised-button (click)="goEdit(row.id)" color="primary" >Edit</button>
<button *cc-action cc-raised-button (click)="deleteTransactionPostingSetup(row.id)" >Delete</button>
<ng-container *ngIf="row.active">
  <button *cc-action cc-raised-button (click)="disable(row.id)" >Disable</button>
</ng-container>
<ng-container *ngIf="!row.active">
  <button *cc-action cc-raised-button color="accent" (click)="enable(row.id)" >Enable</button>
</ng-container>
</cc-grid-actions-list>
</cc-datagrid>
