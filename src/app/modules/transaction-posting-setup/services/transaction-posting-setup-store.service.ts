import {Inject, Injectable} from '@angular/core';
import {Observable, BehaviorSubject} from 'rxjs';
import {map} from 'rxjs/operators';
import {Store} from '@ngrx/store';
import {BaseStoreService, SearchModel} from '@maids/cc-lib/common';
import * as TransactionPostingSetupActions from '../store/transaction-posting-setup.actions';
import * as TransactionPostingSetupSelectors from '../store/transaction-posting-setup.selectors';
import {initialState} from '../store/transaction-posting-setup.reducer';
import {SortDirection} from '@angular/material/sort';
import {TransactionPostingSetupFormDataModel} from "../models/transaction-posting-setup.model";

@Injectable()
export class TransactionPostingSetupStoreService extends BaseStoreService {
  searchSubject = new BehaviorSubject<SearchModel<any>>(initialState.search);

  //SELECTORS
  readonly search$ = this.store.select(
    TransactionPostingSetupSelectors.selectSearch
  );

  readonly transactionForm$ = this.store.select(
    TransactionPostingSetupSelectors.selectTransactionPostingSetupFormData
  );

  readonly transactionPostingSetupList$ = this.store.select(
    TransactionPostingSetupSelectors.selectTransactionPostingSetupList
  );

  // constructor(public store: Store) {
  //   super(store);
  // }

  //UPDATERS
  readonly updateSearchQueryParams = (event: {
    pageIndex: number;
    pageSize: number;
  }) => {
    this.store.dispatch(
      TransactionPostingSetupActions.updateSearchQueryParams({event})
    );
    this.loadData(this.searchSubject.getValue());
  };

  readonly updateSearch = (event: any) => {
    this.store.dispatch(TransactionPostingSetupActions.updateSearch({event}));
  };

  readonly updateSearchSortQueryParams = (event: {
    active: string;
    direction: SortDirection;
  }) => {
    this.store.dispatch(
      TransactionPostingSetupActions.updateSearchSortQueryParams({event})
    );
  };
  //ACTIONS

  readonly loadData = (search: SearchModel<any>) => {
    this.store.dispatch(
      TransactionPostingSetupActions.fetchTransactionPostingSetupList({
        search: search.search,
        params: search.params,
      })
    );
  };

  readonly delete = (Id: number) => {
    this.store.dispatch(
      TransactionPostingSetupActions.deleteTransactionPostingSetup({Id})
    );
  };

  UpdateTransactionForm(_form: TransactionPostingSetupFormDataModel) {
    this.store.dispatch(TransactionPostingSetupActions.updateFormData({_form}))
  }

  pickListsCodes(): string[] {
    //return array of picklist codes you need in this feature
    return [
      'ProspectType',
    ];
  }

  resetState(): void {
    //dispatch the action that resets the state
    this.store.dispatch(
      TransactionPostingSetupActions.resetTransactionPostingSetupState()
    );
  }
}
