<cc-dialog-header>
  <h1 cc-dialog-title>Add Payment</h1>
  <a
    role="button"
    type="button"
    cc-icon-button
    cc-dialog-close-button
    cc-dialog-close
  ></a>
</cc-dialog-header>
<cc-dialog-content>
  <form [formGroup]="formGroup">
    <cc-select
      label="Contract"
      formControlName="contract"
      [required]="true"
      [data]="data.contractOptions"
      [search]="false"
    ></cc-select>
    <cc-select
      label="Type of Payment"
      formControlName="typeOfPayment"
      [required]="true"
      [data]="data.typeOfPaymentOptions"
      [search]="false"
    ></cc-select>

    <div>
      <cc-checkbox formControlName="replacementOfBouncedPayment"
        >Replacement of Bounced Payment</cc-checkbox
      >
    </div>
    <div
      class="col-12 px-0"
      [ngClass]="{
        'd-none':
          formGroup.controls['replacementOfBouncedPayment'].value == false
      }"
    >
      <cc-select
        label="Bounced Payment"
        formControlName="bouncedPayment"
        [data]="bouncedPaymentOptions"
        [required]="
          formGroup.controls['replacementOfBouncedPayment'].value == true
        "
      ></cc-select>
    </div>
    <cc-amount-input
      label="Amount"
      formControlName="amountOfPayment"
      [required]="true"
      symbol=" "
    ></cc-amount-input>
    <div class="row justify-content-between">
      <div class="col-md-auto">
        <cc-checkbox formControlName="vatPaidByClient"
          >Vat Paid By Family</cc-checkbox
        >
      </div>
      <div class="col-md-auto">
        <cc-checkbox formControlName="isInitial">Is Initial</cc-checkbox>
      </div>
      <div class="col-md-auto">
        <cc-checkbox formControlName="includeWorkerSalary"
          >Include Worker Salary</cc-checkbox
        >
      </div>
    </div>
    <cc-select
      [ngClass]="{ 'd-none': !notMonthlyPayment }"
      label="Vat Type"
      formControlName="vatType"
      [data]="vatTypeOptions"
      [required]="
        notMonthlyPayment && formGroup.controls['vatPaidByClient'].value == true
      "
    ></cc-select>
    <cc-select
      [ngClass]="{ 'd-none': !notMonthlyPayment }"
      label="Revenue"
      formControlName="revenue"
      [lazyPageFetcher]="revenueOptions"
      [required]="notMonthlyPayment"
      [(ngModel)]="revenueObj"
      [emitFullSelectOption]="true"
    ></cc-select>
    <cc-select
      [ngClass]="{ 'd-none': !notMonthlyPayment }"
      label="To Bucket"
      formControlName="toBucket"
      [lazyPageFetcher]="toBucketOptions"
      [required]="notMonthlyPayment"
      [(ngModel)]="toBucketObj"
      [emitFullSelectOption]="true"
    ></cc-select>
    <cc-textarea
      [ngClass]="{ 'd-none': !notMonthlyPayment }"
      label="Description"
      formControlName="description"
      [required]="notMonthlyPayment"
    ></cc-textarea>
    <cc-textarea label="Notes" formControlName="notes"></cc-textarea>
  </form>
</cc-dialog-content>
<cc-dialog-actions>
  <button cc-raised-button cc-dialog-close>Cancel</button>
  <button
    cc-raised-button
    color="primary"
    [disabled]="formGroup.invalid"
    (click)="data.element ? updatePayment() : savePayment()"
  >
    Save
  </button>
</cc-dialog-actions>
