import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { CCNotificationService } from '@maids/cc-lib/services';
import { UnknownWireTransferService } from '../../services/unknown-wire-transfer.service';
import { PaginationRequest } from '@maids/cc-lib/common';
import { Observable } from 'rxjs';
import { CCDialogRef } from '@maids/cc-lib/dialog';
@Component({
  selector: 'app-add-payment',
  templateUrl: './add-payment.component.html',
  styleUrls: ['./add-payment.component.scss'],
})
export class AddPaymentComponent implements OnInit {
  formGroup = this.formBuilder.group({
    contract: [''],
    typeOfPayment: [''],
    replacementOfBouncedPayment: [false],
    bouncedPayment: [''],
    amountOfPayment: [''],
    vatPaidByClient: [false],
    isInitial: [false],
    includeWorkerSalary: [{ value: false, disabled: true }],
    vatType: [''],
    // vatAmount: [''],
    revenue: [''],
    toBucket: [''],
    description: [''],
    notes: [''],
  });
  notMonthlyPayment: boolean = true;
  bouncedPaymentOptions: any[] = [];
  contractOptions: any[] = [];
  vatTypeOptions: any[] = [
    { id: 'IN', text: ' Input VAT' },
    { id: 'OUT', text: ' Output VAT' },
  ];
  revenueOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.unknownWireTransferService.UWTrevenueOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  toBucketOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.unknownWireTransferService.UWTtoBucketOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  revenueObj: any | null = null;
  toBucketObj: any | null = null;
  typeOfPaymentObj: any | null = null;
  contractObj: any | null = null;
  isMVContract: boolean = false;
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private formBuilder: FormBuilder,
    public readonly notifications: CCNotificationService,
    private unknownWireTransferService: UnknownWireTransferService,
    private ccDialogRef: CCDialogRef<AddPaymentComponent>
  ) {}

  ngOnInit(): void {
    if (this.data.element) {
      this.formGroup.patchValue(this.data.element);
      this.revenueObj = this.data.element.revenueObj;
      this.toBucketObj = this.data.element.toBucketObj;
      if (this.data.element.includeWorkerSalary) {
        this.formGroup.controls['includeWorkerSalary'].setValue(true);
        this.formGroup.controls['includeWorkerSalary'].enable();
      }
      let contract = this.data.contractOptions.find(
        (_: any) => _.id == parseInt(this.data.element.contract)
      ).type;
      if (contract.code === 'maidvisa.ae_prospect') {
        this.formGroup.controls['includeWorkerSalary'].enable();
        this.formGroup.controls['includeWorkerSalary'].setValue(this.data.element.includeWorkerSalary);
      } else {
        this.formGroup.controls['includeWorkerSalary'].disable();
        this.formGroup.controls['includeWorkerSalary'].setValue(false);
      }
    }
    this.formGroup.controls['contract'].valueChanges.subscribe((val) => {
      if (val) {
        this.unknownWireTransferService
          .getunreplacedbouncedpayments(val)
          .subscribe((res) => {
            this.bouncedPaymentOptions = res;
          });
        let contract = this.data.contractOptions.find(
          (_: any) => _.id == parseInt(val)
        ).type;
        if (contract.code === 'maidvisa.ae_prospect') {
          this.formGroup.controls['includeWorkerSalary'].enable();
        } else {
          this.formGroup.controls['includeWorkerSalary'].disable();
          this.formGroup.controls['includeWorkerSalary'].setValue(false);
        }
      }
    });
    this.formGroup.controls['typeOfPayment'].valueChanges.subscribe((val) => {
      if (val) {
        let typeOfPayment = this.data.typeOfPaymentOptions.find(
          (_: any) => _.id == parseInt(val)
        ).code;
        this.notMonthlyPayment = typeOfPayment !== 'monthly_payment';
        if (this.notMonthlyPayment && !this.data) {
          this.formGroup.controls['vatType'].setValue(null);
          this.formGroup.controls['revenue'].setValue(null);
          this.formGroup.controls['toBucket'].setValue(null);
          this.formGroup.controls['description'].setValue(null);
        }
      }
    });
    this.formGroup.controls['amountOfPayment'].valueChanges.subscribe((val) => {
      if (
        val &&
        this.formGroup.controls['replacementOfBouncedPayment'].value &&
        this.formGroup.controls['bouncedPayment'].value
      ) {
        setTimeout(() => {
          let bouncedPayment = this.bouncedPaymentOptions.find(
            (_: any) =>
              _.id == parseInt(this.formGroup.controls['bouncedPayment'].value)
          );
          bouncedPayment.amount = parseInt(bouncedPayment.amount);
          if (
            val > bouncedPayment.amount + this.data.matchingParameter ||
            val < bouncedPayment.amount - this.data.matchingParameter
          ) {
            this.notifications.notifyError(
              `Please enter an amount between ${
                bouncedPayment.amount + this.data.matchingParameter
              } and ${bouncedPayment.amount - this.data.matchingParameter}`
            );
          }
        }, 2000);
      }
    });
    this.formGroup.controls['bouncedPayment'].valueChanges.subscribe((val) => {
      if (
        this.formGroup.controls['amountOfPayment'].value &&
        this.formGroup.controls['replacementOfBouncedPayment'].value &&
        this.formGroup.controls['bouncedPayment'].value
      ) {
        setTimeout(() => {
          let bouncedPayment = this.bouncedPaymentOptions.find(
            (_: any) =>
              _.id == parseInt(this.formGroup.controls['bouncedPayment'].value)
          );
          bouncedPayment.amount = parseInt(bouncedPayment.amount);
          if (
            this.formGroup.controls['amountOfPayment'].value >
              bouncedPayment.amount + this.data.matchingParameter ||
            this.formGroup.controls['amountOfPayment'].value <
              bouncedPayment.amount - this.data.matchingParameter
          ) {
            this.notifications.notifyError(
              `Please enter an amount between ${
                bouncedPayment.amount + this.data.matchingParameter
              } and ${bouncedPayment.amount - this.data.matchingParameter}`
            );
          }
        }, 2000);
      }
    });
  }
  updatePayment() {
    let payload: any = {
      ...this.formGroup.value,
      _id: this.data.element._id,
      revenueObj: this.revenueObj,
      toBucketObj: this.toBucketObj,
      toBucket: this.toBucketObj ? this.toBucketObj.id : null,
      revenue: this.revenueObj ? this.revenueObj.id : null,
    };
    this.ccDialogRef.close(payload);
  }
  savePayment() {
    let payload: any = {
      ...this.formGroup.value,
      _id: 'Payment-' + Math.floor(Math.random() * 100000),
      revenueObj: this.revenueObj,
      toBucketObj: this.toBucketObj,
      toBucket: this.toBucketObj ? this.toBucketObj.id : null,
      revenue: this.revenueObj ? this.revenueObj.id : null,
    };
    this.ccDialogRef.close(payload);
  }
}
