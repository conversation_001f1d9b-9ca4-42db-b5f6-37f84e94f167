<div class="acc-8078">
  <div class="row align-items-center mb-2">
    <div class="col-3">
      <span>Wire Transfer Amount:</span>
    </div>
    <div class="col-3">AED {{ amount }}</div>
  </div>
  <div class="row align-items-center mb-2">
    <div class="col-3">
      <span>Wire Transfer Description:</span>
    </div>
    <div class="col-3">{{ description }}</div>
  </div>
  <div class="row align-items-center mb-2">
    <div class="col-3">
      <span>Wire Transfer Date:</span>
    </div>
    <div class="col-3">{{ date }}</div>
  </div>
  <div class="row mb-2">
    <div class="col-6">
    <div class="row align-items-center">
      <span class="col-6">Total Transactions:</span>
      <div class="col-6">
        <span>
          {{ count }}
        </span>
      </div>
    </div>
  </div>
    <div class="col-md-6 mb-2">
      <div class="d-flex justify-content-end">
        <button cc-raised-button color="accent" (click)="confirmTransactions()">
          Confirm Transactions
        </button>
      </div>
    </div>
  </div>
  <cc-datagrid
    class="my-2"
    *ngIf="records$ | async as records"
    [data]="records"
    [columns]="gridCols"
    [length]="records.length"
    [pageOnFront]="false"
    [pageIndex]="0"
    [pageSize]="20"
    [pageSizeOptions]="[20]"
    (page)="getNextPage($event)"
    [stickyHeader]="true"
    [columnMovable]="true"
    [columnHideable]="true"
    [showColumnMenuButton]="true"
    [showColumnMenuHeader]="false"
    [columnMenuButtonIcon]="'settings'"
    [noResultTemplate]="noResultTemplate"
  ></cc-datagrid>
  <ng-template #noResultTemplate> No Data </ng-template>
</div>
