import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { UnknownWireTransferService } from '../../services/unknown-wire-transfer.service';
import { PageableResponseModel } from '@maids/cc-lib/common';
import { BehaviorSubject, Observable } from 'rxjs';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { CCDialog } from '@maids/cc-lib/dialog';
import { EditTransactionComponent } from '../edit-transaction/edit-transaction.component';
import { CCNotificationService } from '@maids/cc-lib/services';
import { PageEvent } from '@angular/material/paginator';
@Component({
  selector: 'app-confirm-transaction',
  templateUrl: './confirm-transaction.component.html',
  styleUrls: ['./confirm-transaction.component.scss'],
})
export class ConfirmTransactionComponent implements OnInit {
  date = this.route.snapshot.queryParams['date'];
  relatesTo = this.route.snapshot.queryParams['relatesTo'];
  unknownWireTransferId = this.route.snapshot.queryParams['id'];
  description = this.route.snapshot.queryParams['description'];
  amount = this.route.snapshot.queryParams['amount'];
  private recordsSubject = new BehaviorSubject<any[]>([]);
  count: number = 0;
  records$: Observable<any[]> = this.recordsSubject.asObservable();
  gridCols: CCGridColumn[] = [
    {
      field: 'action',
      header: 'Actions',
      type: 'button',
      buttonConfig: {
        mode: 'single',
        disabled: false,
        type: 'raised',
        text: 'Edit',
        color: 'primary',
        callback: (row) => {
          this.edit(row);
        },
      },
    },
    {
      field: 'revenue',
      header: 'Revenue',
      formatter(rowData, colDef) {
        return rowData.revenue ? rowData.revenue.label : '-';
      },
    },
    {
      field: 'toBucket',
      header: 'To Bucket',
      formatter(rowData, colDef) {
        return rowData.toBucket ? rowData.toBucket.label : '-';
      },
    },
    { field: 'description', header: 'Description' },
    {
      field: 'amount',
      header: 'Amount',
      formatter(rowData, colDef) {
        return rowData.amount ? 'AED ' + rowData.amount : '-';
      },
    },
    { field: 'vatType', header: 'Vat Type' },
    { field: 'vatAmount', header: 'Vat Amount' },
    {
      field: 'transactionDate',
      header: 'Date of Transaction',
      formatter(rowData, colDef) {
        return rowData.transactionDate
          ? rowData.transactionDate.split(' ')[0]
          : '-';
      },
    },
  ];
  constructor(
    private route: ActivatedRoute,
    private unknownWireTransferService: UnknownWireTransferService,
    private ccDialog: CCDialog,
    public readonly notifications: CCNotificationService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.getWireTransferTempTransaction();
    this.records$.subscribe();
  }
  getWireTransferTempTransaction(page: number = 0, size: number = 20) {    
    this.unknownWireTransferService
      .getWireTransferTempTransaction(
        this.relatesTo,
        this.unknownWireTransferId,
        { page, size }
      )
      .subscribe((res: any) => {
        this.recordsSubject.next(res);
        this.count = res.length;
      });
  }
  getNextPage(event: PageEvent) {
    this.getWireTransferTempTransaction(event.pageIndex, event.pageSize);
  }
  edit(element: any) {
    this.ccDialog
      .originalOpen(EditTransactionComponent, {
        data: {
          transaction: {
            ...element,
            revenue: { id: element.revenue.id, text: element.revenue.label },
            toBucket: {
              id: element.toBucket.id,
              text: element.toBucket.label,
            },
          },
        },
      })
      .afterClosed()
      .subscribe((res: any) => {
        if (res) {
          this.getWireTransferTempTransaction();
        }
      });
  }
  confirmTransactions() {
    this.unknownWireTransferService
      .confirmWireTransferTempTransaction(
        this.recordsSubject.getValue().map((val: any) => val.id)
      )
      .subscribe((res: any) => {
        if (res) {
          this.notifications.notifySuccess(
            'Transaction confirmed successfully'
          );
          this.router.navigateByUrl('/accounting/v2/unknown-wire-transfer');
        }
      });
  }
}
