<cc-dialog-header>
  <h1 cc-dialog-title>Edit Transaction</h1>
  <a
    role="button"
    type="button"
    cc-icon-button
    cc-dialog-close-button
    cc-dialog-close
  ></a>
</cc-dialog-header>
<cc-dialog-content>
  <form [formGroup]="formGroup">
    <cc-amount-input
      label="Amount"
      formControlName="amount"
      [disabled]="true"
      symbol=" "
      [required]="true"
    ></cc-amount-input>
    <cc-datepicker
      label="Transaction Date"
      formControlName="transactionDate"
    ></cc-datepicker>
    <cc-datepicker
      label="Pnl Date"
      formControlName="pnlValueDate"
    ></cc-datepicker>
    <cc-select
      label="Vat Type"
      formControlName="vatType"
      [data]="vatTypeOptions"
      [required]="true"
    ></cc-select>
    <cc-amount-input
      [ngClass]="{ 'd-none': !formGroup.controls['vatType'].value }"
      label="Vat Amount"
      formControlName="vatAmount"
      symbol=" "
      [required]="formGroup.controls['vatType'].value"
    ></cc-amount-input>
    <cc-select
      label="Revenue"
      formControlName="revenue"
      [emitFullSelectOption]="true"
      [lazyPageFetcher]="revenueOptions"
      [required]="true"
    ></cc-select>
    <cc-select
      label="To Bucket"
      formControlName="toBucket"
      [emitFullSelectOption]="true"
      [lazyPageFetcher]="toBucketOptions"
      [required]="true"
    ></cc-select>
    <cc-select
      label="License"
      formControlName="license"
      [data]="licenseOptions"
      [emitFullSelectOption]="true"
      [required]="true"
    ></cc-select>
    <cc-textarea
      label="Description"
      formControlName="description"
      [required]="true"
    ></cc-textarea>
  </form>
</cc-dialog-content>
<cc-dialog-actions>
  <button cc-raised-button cc-dialog-close>Cancel</button>
  <button
    cc-raised-button
    color="primary"
    (click)="save()"
    [disabled]="formGroup.invalid"
  >
    Save
  </button>
</cc-dialog-actions>
