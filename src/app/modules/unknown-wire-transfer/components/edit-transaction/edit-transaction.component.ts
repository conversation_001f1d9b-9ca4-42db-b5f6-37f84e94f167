import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { CCDialogRef } from '@maids/cc-lib/dialog';
import { CCNotificationService } from '@maids/cc-lib/services';
import { UnknownWireTransferService } from '../../services/unknown-wire-transfer.service';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { PaginationRequest } from '@maids/cc-lib/common';
import { Observable } from 'rxjs';
@Component({
  selector: 'app-edit-transaction',
  templateUrl: './edit-transaction.component.html',
  styleUrls: ['./edit-transaction.component.scss'],
})
export class EditTransactionComponent implements OnInit {
  formGroup = this.formBuilder.group({
    id: [null],
    amount: [null],
    transactionDate: [null],
    pnlValueDate: [null],
    vatType: [null],
    vatAmount: [null],
    revenue: [null],
    toBucket: [null],
    license: [null],
    description: [null],
  });
  vatTypeOptions: any[] = [
    { id: 'IN', text: ' Input VAT' },
    { id: 'OUT', text: ' Output VAT' },
  ];
  revenueOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.unknownWireTransferService.UWTrevenueOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  toBucketOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.unknownWireTransferService.UWTtoBucketOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  licenseOptions: any[] = [];
  revenueObj: any | null = null;
  toBucketObj: any | null = null;
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private formBuilder: FormBuilder,
    public readonly notifications: CCNotificationService,
    private unknownWireTransferService: UnknownWireTransferService,
    private ccDialogRef: CCDialogRef<EditTransactionComponent>
  ) {}

  ngOnInit(): void {    
    this.formGroup.patchValue(this.data.transaction);
    this.unknownWireTransferService
      .UWTTransactionLicense()
      .subscribe((res: any) => {
        this.licenseOptions = res;
        // set here the license value
        this.formGroup.controls['license'].setValue(this.data.transaction.license?.id);
      });
  }
  save() {
    let payload: any;
    payload = {
      ...this.formGroup.value,
      revenue: {
        id: this.formGroup.controls['revenue'].value.id,
        name:
          this.formGroup.controls['revenue'].value.text ||
          this.formGroup.controls['revenue'].value.label,
      },
      toBucket: {
        id: this.formGroup.controls['toBucket'].value.id,
        name:
          this.formGroup.controls['toBucket'].value.text ||
          this.formGroup.controls['toBucket'].value.label,
      },
      license: {
        id: this.formGroup.controls['license'].value.id,
        name:
          this.formGroup.controls['license'].value.text ||
          this.formGroup.controls['license'].value.label,
      },
      pnlValueDate: this.formGroup.controls['pnlValueDate'].value,
    };
    this.unknownWireTransferService
      .updateWireTransferTempTransaction(payload)
      .subscribe((res: any) => {
        this.ccDialogRef.close(res);
        this.notifications.notifySuccess('Transaction Updated');
      });
  }
}
