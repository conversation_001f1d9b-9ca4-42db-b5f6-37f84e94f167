<div class="acc-8078">
  <div class="row align-items-center my-1">
    <div class="col-3">
      <span>Wire Transfer Amount:</span>
    </div>
    <div class="col-3">AED {{ getAmountLabel(amount) }}</div>
  </div>
  <div class="row align-items-center my-1">
    <div class="col-3">
      <span>Wire Transfer Description:</span>
    </div>
    <div class="col-3">{{ description }}</div>
  </div>
  <div class="row align-items-center my-1">
    <div class="col-3">
      <span>Wire Transfer Date:</span>
    </div>
    <div class="col-3">{{ date }}</div>
  </div>
  <div class="d-flex align-items-center justify-content-between my-1">
    <div class="row align-items-center col-6 px-0">
      <span class="col-6">Family:</span>
      <div class="col-6">
        <cc-select
          ccTooltip="enter 3 or more characters to search"
          ccTooltipPosition="right"
          [lazyPageFetcher]="clientsOptions"
          [(ngModel)]="clientId"
          [minSearchLength]="3"
          label="Select Family"
        >
        </cc-select>
      </div>
    </div>
    <div class="col-md-auto">
      <button cc-raised-button color="accent" (click)="addPayment()">
        Add Payment
      </button>
    </div>
  </div>
  <cc-datagrid
    class="my-2"
    *ngIf="records$ | async as records"
    [data]="records.content"
    [columns]="gridCols"
    [length]="records.totalElements"
    [pageOnFront]="false"
    [pageIndex]="records.number"
    [pageSize]="records.size"
    [pageSizeOptions]="[20]"
    (page)="getNextPage($event)"
    [stickyHeader]="true"
    [columnMovable]="true"
    [columnHideable]="true"
    [showPaginator]="false"
    [showColumnMenuButton]="true"
    [showColumnMenuHeader]="false"
    [columnMenuButtonIcon]="'settings'"
    [noResultTemplate]="noResultTemplate"
    [cellTemplate]="{ typeOfPayment: typeOfPayment }"
  >
    <cc-grid-actions-list
      *ccActionData="let ctx of records?.content; row as row"
      [renderedActionsCount]="2"
      style="width: fit-content; gap: 8px"
    >
      <button cc-raised-button *cc-action color="primary" (click)="edit(row)">
        Edit
      </button>
      <button cc-raised-button *cc-action (click)="delete(row)">
        Delete
      </button>
    </cc-grid-actions-list>
  </cc-datagrid>
  <ng-template #typeOfPayment let-row let-index="index" let-col="colDef">
    {{ getTypeOfPayment(row) }}
  </ng-template>
  <ng-template #noResultTemplate> No Data </ng-template>
  <div class="d-flex justify-content-end mt-2">
    <div class="col-md-auto">
      <button cc-raised-button (click)="cancel()">Cancel</button>
    </div>
    <div class="col-md-auto">
      <button
        cc-raised-button
        color="primary"
        (click)="savePayments()"
        [disabled]="(records$ | async)?.content?.length == 0"
      >
        Save
      </button>
    </div>
  </div>
</div>
