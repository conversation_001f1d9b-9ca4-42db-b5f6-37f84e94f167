import { Component, OnInit } from '@angular/core';
import { PageableResponseModel, PaginationRequest } from '@maids/cc-lib/common';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { tap } from 'rxjs/operators';
import { UnknownWireTransferService } from '../../services/unknown-wire-transfer.service';
import { ActivatedRoute, Router } from '@angular/router';
import { PageEvent } from '@angular/material/paginator';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { CCDialog } from '@maids/cc-lib/dialog';
import { AddPaymentComponent } from '../add-payment/add-payment.component';
import { CCNotificationService } from '@maids/cc-lib/services';
@Component({
  selector: 'app-matching-wire-transfer',
  templateUrl: './matching-wire-transfer.component.html',
  styleUrls: ['./matching-wire-transfer.component.scss'],
})
export class MatchingWireTransferComponent implements OnInit {
  amount = this.route.snapshot.queryParams['amount'];
  description = this.route.snapshot.queryParams['description'];
  date = this.route.snapshot.queryParams['date'];
  relatesTo = this.route.snapshot.queryParams['relatesTo'];
  unknownWireTransferId = this.route.snapshot.queryParams['id'];
  clientId: any | null = null;
  private recordsSubject = new BehaviorSubject<PageableResponseModel<any>>({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });

  records$: Observable<PageableResponseModel<any>> =
    this.recordsSubject.asObservable();
  matchingParameter: any | null = null;
  gridCols: CCGridColumn[] = [
    {
      field: 'contract',
      header: 'Contract Number',
      formatter(rowData, colDef) {
        return rowData.contract ? `Contr-${rowData.contract}` : '';
      },
    },
    {
      field: 'typeOfPayment',
      header: 'Type Of Payment',
    },
    {
      field: 'replacementOfBouncedPayment',
      header: 'Replacement of Bounced Payment',
      formatter(rowData, colDef) {
        return rowData.replacementOfBouncedPayment
          ? 'Yes-Payment' + rowData.replacementOfBouncedPayment
          : 'No';
      },
    },
    { field: 'amountOfPayment', header: 'Amount' },
    { field: 'description', header: 'Description' },
    {
      field: 'vatPaidByClient',
      header: 'Vat Paid By Family',
      formatter(rowData, colDef) {
        return rowData.vatPaidByClient ? 'Yes' : 'No';
      },
    },
    {
      field: 'isInitial',
      header: 'Is Initial',
      formatter(rowData, colDef) {
        return rowData.isInitial ? 'Yes' : 'No';
      },
    },
    {
      field: 'includeWorkerSalary',
      header: 'Include Worker Salary',
      formatter(rowData, colDef) {
        return rowData.includeWorkerSalary ? 'Yes' : 'No';
      },
    },
  ];
  clientsOptions = (pageReq: PaginationRequest): Observable<any> => {
    return this.unknownWireTransferService.UWTclientsOptions(
      pageReq.page,
      pageReq.size,
      pageReq.searchString
    );
  };
  typeOfPaymentOptions: any[] = [];
  contractOptions: any[] = [];
  cachedClientsOptions: any[] = [];

  constructor(
    private unknownWireTransferService: UnknownWireTransferService,
    private route: ActivatedRoute,
    private ccDialog: CCDialog,
    public readonly notifications: CCNotificationService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.unknownWireTransferService
      .UWTtypeOfPaymentOptions()
      .subscribe((res: any) => {
        this.typeOfPaymentOptions = res;
      });

    this.unknownWireTransferService
      .UWTmatchingParameter()
      .subscribe((res: any) => {
        this.matchingParameter = parseInt(res[0].value);
      });

    this.records$.subscribe();
  }
  getWireTransferTempPayment(page: number = 0, size: number = 50) {
    let params: any;
    this.unknownWireTransferService
      .getWireTransferTempPayment(this.relatesTo, this.unknownWireTransferId, {
        page,
        size,
      })
      .subscribe((res: any) => {
        this.recordsSubject.next(res);
      });
  }
  getNextPage(event: PageEvent) {
    this.getWireTransferTempPayment(event.pageIndex, event.pageSize);
  }
  addPayment() {
    if (!this.clientId) {
      this.notifications.notifyError(
        'Please Select Family Before Adding Payment.'
      );
    } else {
      this.unknownWireTransferService
        .UWTclientcontracts(this.clientId)
        .subscribe((res: any) => {
          this.contractOptions = res;
          this.ccDialog
            .originalOpen(AddPaymentComponent, {
              data: {
                typeOfPaymentOptions: this.typeOfPaymentOptions,
                contractOptions: this.contractOptions,
                matchingParameter: this.matchingParameter,
              },
              width: '600px',
            })
            .afterClosed()
            .subscribe((data: any) => {
              if (data) {
                const currentRecords = this.recordsSubject.value;
                const updatedContent = [...currentRecords.content, data];
                this.recordsSubject.next({
                  ...currentRecords,
                  content: updatedContent,
                });
              }
            });
        });
    }
  }
  getTypeOfPayment(rowData: any) {
    if (rowData) {
      let item = this.typeOfPaymentOptions.find(
        (_: any) => _.id == parseInt(rowData.typeOfPayment)
      );
      if (item) {
        return item.text ? item.text : '';
      }
    }
  }
  savePayments() {
    let payload: any;
    let tempPayments = this.recordsSubject.getValue().content;
    payload = tempPayments.map((payment: any) => {
      return {
        ...payment,
        relatedEntityId: this.unknownWireTransferId,
        contract: { id: parseInt(payment.contract) },
        bouncedPayment: payment.bouncedPayment
          ? { id: parseInt(payment.bouncedPayment) }
          : null,
        typeOfPayment: { id: parseInt(payment.typeOfPayment) },
        revenue: { id: parseInt(payment.revenue) },
        toBucket: { id: parseInt(payment.toBucket) },
        relatesTo: 'UNKNOWN_WIRE_TRANSFER',
        amountOfPayment: payment.amountOfPayment,
        description: payment.description,
        includeWorkerSalary: payment.includeWorkerSalary
          ? payment.includeWorkerSalary
          : false,
        isInitial: payment.isInitial
          ? payment.isInitial
          : false,
        notes: payment.notes
          ? payment.notes
          : null,
        replacementOfBouncedPayment: payment.replacementOfBouncedPayment
          ? payment.replacementOfBouncedPayment
          : false,
        // vatAmount: payment.vatAmount || null,
        vatPaidByClient: payment.vatPaidByClient || false,
        vatType: payment.vatType || null,
      };
    });

    this.unknownWireTransferService.UWTsavePayments(payload).subscribe({
      next: (res: any) => {
        this.router.navigateByUrl(
          `/accounting/v2/unknown-wire-transfer/confirm-transactions?id=${this.unknownWireTransferId}&date=${this.date}&amount=${this.amount}&relatesTo=UNKNOWN_WIRE_TRANSFER&description=${this.description}`
        );
      },
    });
  }
  edit(element: any) {
    this.ccDialog
      .originalOpen(AddPaymentComponent, {
        data: {
          element: element,
          typeOfPaymentOptions: this.typeOfPaymentOptions,
          contractOptions: this.contractOptions,
          matchingParameter: this.matchingParameter,
        },
        width: '600px',
      })
      .afterClosed()
      .subscribe((data: any) => {
        if (data) {
          const currentRecords = this.recordsSubject.getValue();
          const updatedContent = currentRecords.content.map((item: any) => {
            if (item._id == data._id) {
              return data;
            } else {
              return item;
            }
          });
          this.recordsSubject.next({
            ...currentRecords,
            content: updatedContent,
          });
        }
      });
  }
  delete(element: any) {
    this.ccDialog.confirm('', 'Are you sure you want to delete?', () => {
      const currentRecords = this.recordsSubject.value;
      const updatedContent = currentRecords.content.filter(
        (item: any) => item._id != element._id
      );
      this.recordsSubject.next({
        ...currentRecords,
        content: updatedContent,
      });
    });
  }
  cancel() {
    this.router.navigateByUrl(`/accounting/v2/unknown-wire-transfer`);
  }
  getAmountLabel(amount: number) {
    if (amount === null || amount == undefined)
        return amount;
    var parts = amount.toString().split(".");
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    return parts.join(".");
  }
}
