<div class="acc-8078">
  <cc-accordion>
    <cc-panel>
      <cc-panel-title>
        <cc-icon class="icon_filter">filter_alt</cc-icon
        ><span style="padding-left: 10px">Advanced Filter</span>
      </cc-panel-title>
      <cc-panel-body>
        <form [formGroup]="searchForm" class="d-flex justify-content-between">
          <div class="col-6 row">
            <div class="col-4">
              <cc-select
                label="Date of Transaction"
                [data]="searchDateOptions"
                formControlName="dateOptions"
              ></cc-select>
            </div>
            <div
              [ngClass]="{
                'col-4': searchForm.controls['dateOptions'].value == 'between',
                'col-8': searchForm.controls['dateOptions'].value != 'between'
              }"
            >
              <cc-datepicker
                [label]="
                  searchForm.controls['dateOptions'].value == 'between'
                    ? 'From'
                    : ''
                "
                formControlName="dateSearch"
              ></cc-datepicker>
            </div>
            <div
              class="col-4"
              [ngClass]="{
                'd-none': searchForm.controls['dateOptions'].value != 'between'
              }"
            >
              <cc-datepicker
                label="To"
                formControlName="dateSearch2"
              ></cc-datepicker>
            </div>
          </div>
          <div class="col-6 row">
            <div class="col-4">
              <cc-select
                label="Date of Creation"
                [data]="searchDateOptions"
                formControlName="creationDateOptions"
              ></cc-select>
            </div>
            <div
              [ngClass]="{
                'col-4':
                  searchForm.controls['creationDateOptions'].value == 'between',
                'col-8':
                  searchForm.controls['creationDateOptions'].value != 'between'
              }"
            >
              <cc-datepicker
                [label]="
                  searchForm.controls['creationDateOptions'].value == 'between'
                    ? 'From'
                    : ''
                "
                formControlName="creationDateSearch"
              ></cc-datepicker>
            </div>
            <div
              class="col-4"
              [ngClass]="{
                'd-none':
                  searchForm.controls['creationDateOptions'].value != 'between'
              }"
            >
              <cc-datepicker
                label="To"
                formControlName="creationDateSearch2"
              ></cc-datepicker>
            </div>
          </div>
        </form>
        <hr />
        <div class="row justify-content-center">
          <div class="col-md-auto">
            <button
              cc-raised-button
              (click)="getUnknownWireTransfers()"
              style="padding-left: 30px"
              color="primary"
            >
              <cc-icon class="icon">search</cc-icon>Search
            </button>
          </div>
          <div class="col-md-auto">
            <button
              cc-raised-button
              (click)="reset()"
              style="padding-left: 30px"
            >
              <cc-icon class="icon">restart_alt</cc-icon>Reset
            </button>
          </div>
        </div>
      </cc-panel-body>
    </cc-panel>
  </cc-accordion>
  <div class="d-flex justify-content-between my-2" *ngIf="records">
    <div class="col-md-auto px-0">
      <span
        >Transactions Sum:
        <span class="cc-secondary" style="font-weight: bold">{{
          records.totalSum ? (records.totalSum | number : "1.0-0") : "0"
        }}</span></span
      >
      <br />
      <span
        >Record Count:
        <span class="cc-secondary" style="font-weight: bold">{{
          records.totalElements
            ? (records.totalElements | number : "1.0-0")
            : "0"
        }}</span></span
      >
    </div>
    <div class="col-md-auto px-0">
      <button
        cc-raised-button
        (click)="downloadCSV()"
        style="padding-left: 30px"
      >
        <cc-icon class="icon">download</cc-icon>
        <span style="margin-top: 2px !important"> Generate CSV File</span>
      </button>
    </div>
  </div>
  <cc-datagrid
    class="my-2"
    [data]="records?.content ?? []"
    [columns]="gridCols"
    [length]="records?.totalElements ?? 0"
    [pageOnFront]="false"
    [pageIndex]="records?.number ?? 0"
    [pageSize]="records?.size ?? 0"
    [pageSizeOptions]="[50, 100, 200]"
    (page)="getNextPage($event)"
    (sortChange)="onSortChange($event)"
    [stickyHeader]="true"
    [columnMovable]="true"
    [columnHideable]="true"
    [showColumnMenuButton]="true"
    [showColumnMenuHeader]="false"
    [columnMenuButtonIcon]="'settings'"
  >
    <cc-grid-actions-list
      *ccActionData="let ctx of records?.content; row as row"
      [renderedActionsCount]="1"
      style="width: fit-content; gap: 8px"
    >
      <button
        *cc-action
        cc-raised-button
        (click)="match(row)"
        color="primary"
      >
        Match
      </button>
    </cc-grid-actions-list>
  </cc-datagrid>
</div>
