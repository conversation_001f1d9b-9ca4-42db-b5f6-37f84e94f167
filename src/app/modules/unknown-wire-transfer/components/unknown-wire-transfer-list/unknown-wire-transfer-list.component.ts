import { Component, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { UnknownWireTransferService } from '../../services/unknown-wire-transfer.service';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { PageEvent } from '@angular/material/paginator';
import { Sort } from '@angular/material/sort';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { Router } from '@angular/router';

@Component({
  selector: 'app-unknown-wire-transfer-list',
  templateUrl: './unknown-wire-transfer-list.component.html',
  styleUrls: ['./unknown-wire-transfer-list.component.scss'],
})
export class UnknownWireTransferListComponent implements OnInit {
  searchForm = this.formBuilder.group({
    dateOptions: [null],
    dateSearch: [null],
    dateSearch2: [null],
    creationDateOptions: [null],
    creationDateSearch: [null],
    creationDateSearch2: [null],
  });
  records: any;
  searchObj: any[] = [];
  searchDateOptions: any[] = [
    { id: '=', text: 'Equals' },
    { id: '<', text: 'Before' },
    { id: '>', text: 'After' },
    { id: 'between', text: 'Between' },
  ];
  gridCols: CCGridColumn[] = [
    {
      field: 'id',
      header: 'Name',
      formatter(rowData, colDef) {
        return `<a href='#!/accounting/add-edit-transactions/${rowData.id}' class='cc-secondary'" > ${rowData.id} </a>`;
      },
      sortable: true,
      sortProp: { arrowPosition: 'before', id: 'id' },
    },
    {
      field: 'fromBucketName',
      header: 'Bucket From Name',
      width: '200px',
      formatter(rowData, colDef) {
        return rowData.fromBucket ? rowData.fromBucket.name : '-';
      },
      sortable: true,
      sortProp: { arrowPosition: 'before', id: 'fromBucket.name' },
    },
    {
      field: 'fromBucketCode',
      header: 'Bucket From Code',
      width: '200px',
      formatter(rowData, colDef) {
        return rowData.fromBucket ? rowData.fromBucket.code : '-';
      },
      sortable: true,
      sortProp: { arrowPosition: 'before', id: 'fromBucket.code' },
      hide: true,
    },
    {
      field: 'revenueName',
      header: 'Revenue Name',
      width: '150px',
      formatter(rowData, colDef) {
        return rowData.revenue ? rowData.revenue.name : '-';
      },
      sortable: true,
      sortProp: { arrowPosition: 'before', id: 'revenue.name' },
    },
    {
      field: 'revenueCode',
      header: 'Revenue Code',
      width: '150px',
      formatter(rowData, colDef) {
        return rowData.revenue ? rowData.revenue.code : '-';
      },
      sortable: true,
      sortProp: { arrowPosition: 'before', id: 'revenue.code' },
      hide: true,
    },
    {
      field: 'expenseName',
      header: 'Expense Name',
      width: '150px',
      formatter(rowData, colDef) {
        return rowData.expense ? rowData.expense.name : '-';
      },
      sortable: true,
      sortProp: { arrowPosition: 'before', id: 'expense.name' },
    },
    {
      field: 'expenseCode',
      header: 'Expense Code',
      width: '150px',
      formatter(rowData, colDef) {
        return rowData.expense ? rowData.expense.code : '-';
      },
      sortable: true,
      sortProp: { arrowPosition: 'before', id: 'expense.code' },
      hide: true,
    },
    {
      field: 'toBucketName',
      header: 'Bucket To Name',
      width: '200px',
      formatter(rowData, colDef) {
        return rowData.toBucket ? rowData.toBucket.name : '-';
      },
      sortable: true,
      sortProp: { arrowPosition: 'before', id: 'toBucket.name' },
    },
    {
      field: 'toBucketCode',
      header: 'Bucket To Code',
      width: '150px',
      formatter(rowData, colDef) {
        return rowData.toBucket ? rowData.toBucket.code : '-';
      },
      sortable: true,
      sortProp: { arrowPosition: 'before', id: 'toBucket.code' },
      hide: true,
    },
    {
      field: 'bucketToPreBalance',
      header: 'Bucket To Pre Balance',
      sortable: true,
      width: '200px',
      sortProp: {
        arrowPosition: 'before',
        id: 'bucketToPreBalance',
      },
      hide: true,
    },
    { field: 'description', header: 'Description' },
    {
      field: 'amount',
      header: 'Transaction Amount',
      formatter(rowData, colDef) {
        if (!rowData.amount) return '0';
        return new Intl.NumberFormat('en-US', {
          maximumFractionDigits: 0,
        }).format(rowData.amount);
      },
      sortable: true,
      sortProp: { arrowPosition: 'before', id: 'amount' },
    },
    {
      field: 'date',
      header: 'Date of Transaction',
      sortable: true,
      sortProp: { arrowPosition: 'before', id: 'date' },
    },
    {
      field: 'creationDate',
      header: 'Date of Creation',
      sortable: true,
      sortProp: { arrowPosition: 'before', id: 'creationDate' },
    },
  ];
  constructor(
    private formBuilder: FormBuilder,
    private unknownWireTransferService: UnknownWireTransferService,
    private mediaService: MediaService,
    public readonly notifications: CCNotificationService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.getUnknownWireTransfers();
  }
  getUnknownWireTransfers() {
    this.searchObj = [];
    this.searchObj.push({
      property: 'revenue.name',
      operation: 'like',
      value: 'uwr',
      alternatives: ['revenue.code'],
    });
    this.searchObj.push({
      property: 'paymentId',
      operation: 'IS NULL',
      value: '',
    });
    if (
      this.searchForm.controls['dateOptions'].value &&
      this.searchForm.controls['dateSearch'].value
    ) {
      if (
        this.searchForm.controls['dateOptions'].value == 'between' &&
        this.searchForm.controls['dateSearch2'].value
      ) {
        this.searchObj.push({
          property: 'date',
          operation: this.searchForm.controls['dateOptions'].value,
          value: this.searchForm.controls['dateSearch'].value,
          secondValue: this.searchForm.controls['dateSearch2'].value,
        });
      } else {
        this.searchObj.push({
          property: 'date',
          operation: this.searchForm.controls['dateOptions'].value,
          value: this.searchForm.controls['dateSearch'].value,
        });
      }
    }
    if (
      this.searchForm.controls['creationDateOptions'].value &&
      this.searchForm.controls['creationDateSearch'].value
    ) {
      if (
        this.searchForm.controls['creationDateOptions'].value == 'between' &&
        this.searchForm.controls['creationDateSearch'].value
      ) {
        this.searchObj.push({
          property: 'creationDate',
          operation: this.searchForm.controls['creationDateOptions'].value,
          value: this.searchForm.controls['creationDateSearch'].value,
          secondValue: this.searchForm.controls['creationDateSearch2'].value,
        });
      } else {
        this.searchObj.push({
          property: 'creationDate',
          operation: this.searchForm.controls['creationDateOptions'].value,
          value: this.searchForm.controls['creationDateSearch'].value,
        });
      }
    }
    this.unknownWireTransferService
      .getUnknownWireTransfer(this.searchObj)
      .subscribe((res: any) => {
        this.records = res;
      });
  }
  getNextPage(event: PageEvent) {
    this.unknownWireTransferService.searchSubject.next({
      params: {
        sort: this.unknownWireTransferService.searchSubject.getValue().params
          .sort,
        page: event.pageIndex,
        size: event.pageSize,
      },
    });
    this.getUnknownWireTransfers();
  }
  onSortChange(event: Sort) {
    this.unknownWireTransferService.searchSubject.next({
      params: {
        sort: `${event.active},${event.direction}`,
        page: this.unknownWireTransferService.searchSubject.getValue().params
          .page,
        size: this.unknownWireTransferService.searchSubject.getValue().params
          .size,
      },
    });
    this.getUnknownWireTransfers();
  }
  reset() {
    this.searchForm.reset();
    this.getUnknownWireTransfers();
  }
  match(element: any) {
    this.router.navigateByUrl(
      `/accounting/v2/unknown-wire-transfer/matching-wire-transfer?date=${element.date}&amount=${element.amount}&relatesTo=UNKNOWN_WIRE_TRANSFER&description=${element.description}&id=${element.id}`
    );
  }
  downloadCSV() {
    let obj = {
      csvColumns: [
        'name',
        'bucketFromName',
        'revenueName',
        'expenseName',
        'bucketToName',
        'description',
        'transactionAmount',
        'dateOfTransaction',
        'dateOfCreation',
      ],
      filters: this.searchObj,
    };
    let params = { pageName: 'Unknown Wire Transfer' };
    if (this.records.totalElements < 2000) {
      this.mediaService.downloadFile(
        'accounting/transactions/csv/advancesearch',
        '',
        {
          method: 'POST',
          body: obj,
          params: params,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    } else {
      this.unknownWireTransferService
        .generateCSVFileAdvanced(params, obj)
        .subscribe((res: any) => {
          this.notifications.notifySuccess(res, 6000);
        });
    }
  }
}
