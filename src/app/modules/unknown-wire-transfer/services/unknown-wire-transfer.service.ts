import { HttpClient, HttpContext } from '@angular/common/http';
import { Inject, Injectable } from '@angular/core';
import {
  CCBackendEndpoint,
  REQ_SHOW_LOADING_ICON,
} from '@maids/cc-erp-services';
import { SearchModel } from '@maids/cc-lib/common';
import { BehaviorSubject, map, Observable } from 'rxjs';
import { API } from 'src/environments/api';

@Injectable({
  providedIn: 'root',
})
export class UnknownWireTransferService {
  initialSearch: SearchModel<any> = {
    params: {
      page: 0,
      size: 200,
      sort: '',
    },
    search: '',
  };
  searchSubject = new BehaviorSubject<SearchModel<any>>(this.initialSearch);
  constructor(
    private _http: HttpClient,
    @Inject(CCBackendEndpoint) private _api: CCBackendEndpoint
  ) {}
  getUnknownWireTransfer(payload: any): Observable<any> {
    return this._http.post<any>(
      `${this._api}/${API.getUnknownWireTransfer}`,
      payload,
      { params: this.searchSubject.getValue().params }
    );
  }
  generateCSVFileAdvanced(params: any, payload: any): Observable<any> {
    return this._http.post(
      `${this._api}/${API.generateCSVFileAdvanced}`,
      payload,
      {
        params,
        headers: { 'Content-Type': 'application/json' },
      }
    );
  }
  UWTclientsOptions(
    page: number,
    size: number = 20,
    search: string = ''
  ): Observable<any> {
    const context = new HttpContext().set(REQ_SHOW_LOADING_ICON, false);
    return this._http
      .get(`${this._api}/${API.UWTclientsOptions}`, {
        params: { page, size, search },
        context,
      })
      .pipe(
        map((res: any) => {
          return res.map((val: any) => {
            return {
              text: val.name,
              id: val.id,
            };
          });
        })
      );
  }
  UWTtoBucketOptions(
    page: number,
    size: number = 20,
    search: string = ''
  ): Observable<any> {
    const context = new HttpContext().set(REQ_SHOW_LOADING_ICON, false);

    return this._http
      .get(`${this._api}/${API.UWTtoBucketOptions}`, {
        params: { page, size, search },
        context,
      })
      .pipe(
        map((res: any) => {
          return res.content.map((val: any) => {
            return {
              text: val.name,
              id: val.id,
            };
          });
        })
      );
  }
  UWTrevenueOptions(
    page: number,
    size: number = 20,
    search: string = ''
  ): Observable<any> {
    const context = new HttpContext().set(REQ_SHOW_LOADING_ICON, false);

    return this._http
      .get(`${this._api}/${API.UWTrevenueOptions}`, {
        params: { page, size, search },
        context,
      })
      .pipe(
        map((res: any) => {
          return res.content.map((val: any) => {
            return {
              text: val.name,
              id: val.id,
            };
          });
        })
      );
  }
  UWTTransactionLicense(): Observable<any> {
    return this._http.get(`${this._api}/${API.UWTTransactionLicense}`).pipe(
      map((res: any) => {
        return res.map((val: any) => {
          return {
            text: val.name,
            id: val.id,
          };
        });
      })
    );
  }
  UWTtypeOfPaymentOptions(): Observable<any> {
    return this._http.get(`${this._api}/${API.UWTtypeOfPaymentOptions}`).pipe(
      map((res: any) => {
        return res.map((val: any) => {
          return {
            text: val.name,
            id: val.id,
            code: val.code,
          };
        });
      })
    );
  }
  UWTclientcontracts(clientId: any): Observable<any> {
    return this._http
      .get(`${this._api}/${API.UWTclientcontracts}/${clientId}`)
      .pipe(
        map((res: any) => {
          return res.map((val: any) => {
            return {
              text: 'Contr-' + val.id,
              id: val.id,
              type: val.contractProspectType,
            };
          });
        })
      );
  }
  UWTmatchingParameter(): Observable<any> {
    return this._http.get(`${this._api}/${API.UWTmatchingParameter}`, {
      params: { code: 'PARAMETER_MATCHING_WIRE_BOUNCED_PAYMENT' },
    });
  }
  getWireTransferTempPayment(
    relatesTo: any,
    unknownWireTransferId: any,
    params: any
  ): Observable<any> {
    return this._http.get(
      `${this._api}/${API.getWireTransferTempPayment}/${relatesTo}/${unknownWireTransferId}`,
      {
        params,
      }
    );
  }
  getunreplacedbouncedpayments(contractId: any): Observable<any> {
    return this._http
      .get(`${this._api}/${API.getunreplacedbouncedpayments}/${contractId}`)
      .pipe(
        map((res: any) => {
          return res.map((val: any) => {
            return {
              text: 'Payment ' + val.id + ' - AED ' + val.amountOfPayment,
              id: val.id,
              amount: val.amountOfPayment,
            };
          });
        })
      );
  }
  UWTsavePayments(payload: any): Observable<any> {
    return this._http.post(`${this._api}/${API.UWTsavePayments}`, payload, {
      headers: { 'Content-Type': 'application/json' },
    });
  }
  getWireTransferTempTransaction(
    relatesTo: any,
    unknownWireTransferId: any,
    params: any
  ): Observable<any> {
    return this._http.get(
      `${this._api}/${API.getWireTransferTempTransaction}/${relatesTo}/${unknownWireTransferId}`,
      { params }
    );
  }
  updateWireTransferTempTransaction(payload: any): Observable<any> {
    return this._http.post(
      `${this._api}/${API.updateWireTransferTempTransaction}`,
      payload,
      { headers: { 'Content-Type': 'application/json' } }
    );
  }
  confirmWireTransferTempTransaction(payload: any): Observable<any> {
    return this._http.post(
      `${this._api}/${API.confirmWireTransferTempTransaction}`,
      payload,
      { headers: { 'Content-Type': 'application/json' } }
    );
  }
}
