import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { UnknownWireTransferListComponent } from './components/unknown-wire-transfer-list/unknown-wire-transfer-list.component';
import { ConfirmTransactionComponent } from './components/confirm-transaction/confirm-transaction.component';
import { MatchingWireTransferComponent } from './components/matching-wire-transfer/matching-wire-transfer.component';

const routes: Routes = [
  {
    path: '',
    component: UnknownWireTransferListComponent,
    data: { label: '' },
  },
  {
    path: 'matching-wire-transfer',
    component: MatchingWireTransferComponent,
    data: { label: 'Matching Wire Transfer', pageCode: 'UnknownMatchingWireTransfer' },
  },

  {
    path: 'confirm-transactions',
    component: ConfirmTransactionComponent,
    data: { label: 'Confirm Transaction', pageCode: 'UnknownWireTransferConfirmTransactions' },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class UnknownWireTransferRoutingModule {}
