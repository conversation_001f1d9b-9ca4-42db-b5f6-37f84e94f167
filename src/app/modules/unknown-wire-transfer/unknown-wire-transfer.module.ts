import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { UnknownWireTransferRoutingModule } from './unknown-wire-transfer-routing.module';
import { UnknownWireTransferListComponent } from './components/unknown-wire-transfer-list/unknown-wire-transfer-list.component';
import { MatchingWireTransferComponent } from './components/matching-wire-transfer/matching-wire-transfer.component';
import { AddPaymentComponent } from './components/add-payment/add-payment.component';
import { EditTransactionComponent } from './components/edit-transaction/edit-transaction.component';
import { ConfirmTransactionComponent } from './components/confirm-transaction/confirm-transaction.component';
import { CCButtonModule } from '@maids/cc-lib/button';
import { CCAccordionModule } from '@maids/cc-lib/accordion';
import { CCInputModule } from '@maids/cc-lib/input';
import { CCCheckboxModule } from '@maids/cc-lib/checkbox';
import { CCDatepickerModule } from '@maids/cc-lib/date';
import { CCSelectInputModule } from '@maids/cc-lib/select-input';
import { CCIconModule } from '@maids/cc-lib/icon';
import { CCDialogModule } from '@maids/cc-lib/dialog';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CCDatagridModule } from '@maids/cc-lib/datagrid';
import { CCTextareaModule } from '@maids/cc-lib/textarea';
import { CCAmountInputModule } from '@maids/cc-lib/masked-input';
import { CCTooltipModule } from '@maids/cc-lib/tooltip';

@NgModule({
  declarations: [
    UnknownWireTransferListComponent,
    MatchingWireTransferComponent,
    AddPaymentComponent,
    EditTransactionComponent,
    ConfirmTransactionComponent,
  ],
  imports: [
    CommonModule,
    UnknownWireTransferRoutingModule,
    CCAccordionModule,
    CCDatagridModule,
    CCButtonModule,
    CCInputModule,
    CCCheckboxModule,
    CCTextareaModule,
    CCDatepickerModule,
    CCSelectInputModule,
    CCAmountInputModule,
    CCIconModule,
    CCTooltipModule,
    CCDialogModule,
    FormsModule,
    ReactiveFormsModule,
  ],
})
export class UnknownWireTransferModule {}
