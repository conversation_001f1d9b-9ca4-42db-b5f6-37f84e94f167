<div class="acc-8152">
  <cc-dialog-header>
    <h1 cc-dialog-title>Add Transaction</h1>
    <a
      role="button"
      type="button"
      cc-icon-button
      cc-dialog-close-button
      cc-dialog-close
    ></a
  ></cc-dialog-header>
  <cc-dialog-content>
    <form [formGroup]="formGroup" (keydown.enter)="preventEnter($event)">
      <cc-datepicker
        label="Transaction Date"
        formControlName="date"
        [required]="true"
      ></cc-datepicker>
      <cc-datepicker
        label="Pnl Date"
        formControlName="pnlValueDate"
        [required]="true"
      ></cc-datepicker>
      <cc-input
        label="Amount"
        formControlName="amount"
        [required]="true"
        type="number"
      ></cc-input>
      <cc-select
        [lazyPageFetcher]="expensesOptions"
        label="Expense"
        formControlName="expense"
        [required]="true"
        [emitFullSelectOption]="true"
      ></cc-select>
      <cc-select
        [data]="referenceForOptions"
        formControlName="selectedReferenceFor"
        label="For"
      ></cc-select>
      <cc-select
        *ngIf="formGroup.controls['selectedReferenceFor'].value == 'HOUSEMAID'"
        [multiple]="true"
        [emitFullSelectOption]="true"
        [lazyPageFetcher]="USrefHousemaidOptions"
        label="House Maid"
        formControlName="selectedReferencevalue"
      ></cc-select>
      <cc-select
        *ngIf="
          formGroup.controls['selectedReferenceFor'].value == 'OFFICE_STAFF'
        "
        [multiple]="true"
        [emitFullSelectOption]="true"
        [lazyPageFetcher]="USrefOfficeStaffOptions"
        label="Office Staff"
        formControlName="selectedReferencevalue"
      ></cc-select>
      <cc-select
        *ngIf="formGroup.controls['selectedReferenceFor'].value == 'CLIENT'"
        [multiple]="true"
        [emitFullSelectOption]="true"
        [lazyPageFetcher]="USrefClientOptions"
        label="Maid cc Family"
        formControlName="selectedReferencevalue"
      ></cc-select>
      <cc-select
        *ngIf="formGroup.controls['selectedReferenceFor'].value == 'APPLICANT'"
        [multiple]="true"
        [emitFullSelectOption]="true"
        [lazyPageFetcher]="USrefApplicantOptions"
        label="Applicant"
        formControlName="selectedReferencevalue"
      ></cc-select>
      <cc-select
        *ngIf="formGroup.controls['selectedReferenceFor'].value == 'PROSPECT'"
        [multiple]="true"
        [emitFullSelectOption]="true"
        [lazyPageFetcher]="USrefProspectOptions"
        label="Maid cc Prospect"
        formControlName="selectedReferencevalue"
      ></cc-select>
      <cc-select
        *ngIf="formGroup.controls['selectedReferenceFor'].value == 'CONTRACT'"
        [multiple]="true"
        [emitFullSelectOption]="true"
        [lazyPageFetcher]="USrefContractOptions"
        label="Contract"
        formControlName="selectedReferencevalue"
      ></cc-select>
      <cc-select
        *ngIf="
          formGroup.controls['selectedReferenceFor'].value == 'FREEDOM_OPERATOR'
        "
        [multiple]="true"
        [emitFullSelectOption]="true"
        [lazyPageFetcher]="USrefFreedomOptions"
        label="Operator"
        formControlName="selectedReferencevalue"
      ></cc-select>
      <cc-datagrid
        class="my-2"
        *ngIf="
          formGroup.controls['selectedReferenceFor'].value == 'CLEANER' &&
          ((selectedCleanerList$ | async)?.length ?? 0 > 0)
        "
        [columns]="gridCols"
        [data]="selectedCleanerList$ | async"
        [showPaginator]="false"
        [showColumnMenuButton]="true"
        [showColumnMenuHeader]="false"
        [columnMenuButtonIcon]="'settings'"
        [columnMovable]="true"
        [pageOnFront]="false"
      ></cc-datagrid>
      <cc-datagrid
        class="my-2"
        *ngIf="
          formGroup.controls['selectedReferenceFor'].value == 'CONTACT' &&
          ((selectedContactList$ | async)?.length ?? 0 > 0)
        "
        [columns]="gridCols"
        [data]="selectedContactList$ | async"
        [showPaginator]="false"
        [showColumnMenuButton]="true"
        [showColumnMenuHeader]="false"
        [columnMenuButtonIcon]="'settings'"
        [columnMovable]="true"
        [pageOnFront]="false"
      ></cc-datagrid>
      <div class="input-wrapper">
        <cc-input-mask
          *ngIf="formGroup.controls['selectedReferenceFor'].value == 'CLEANER'"
          label="Sales Force Id"
          formControlName="selectedReferencevalue"
          [required]="
            formGroup.controls['selectedReferenceFor'].value == 'CLEANER'
          "
          [minlength]="4"
        ></cc-input-mask>
        <cc-input-mask
          *ngIf="formGroup.controls['selectedReferenceFor'].value == 'CONTACT'"
          label="Sales Force Id"
          formControlName="selectedReferencevalue"
          [required]="
            formGroup.controls['selectedReferenceFor'].value == 'CONTACT'
          "
          [minlength]="4"
        ></cc-input-mask>
        <div
          *ngIf="
            formGroup.controls['selectedReferencevalue'].invalid &&
            formGroup.controls['selectedReferencevalue'].touched &&
            (formGroup.controls['selectedReferenceFor'].value == 'CONTACT' ||
              formGroup.controls['selectedReferenceFor'].value == 'CLEANER')
          "
        >
          <span
            class="error-msg"
            *ngIf="formGroup.controls['selectedReferencevalue'].errors?.['minlength']"
          >
            This field is required
          </span>
        </div>
      </div>
      <cc-input
        *ngIf="
          formGroup.controls['selectedReferenceFor'].value == 'CONTACT' ||
          formGroup.controls['selectedReferenceFor'].value == 'CLEANER'
        "
        label="Name"
        formControlName="salesforceName"
        [required]="
          formGroup.controls['selectedReferenceFor'].value == 'CONTACT' ||
          formGroup.controls['selectedReferenceFor'].value == 'CLEANER'
        "
      ></cc-input>
      <div
        class="d-flex justify-content-end"
        *ngIf="
          formGroup.controls['selectedReferenceFor'].value == 'CONTACT' ||
          formGroup.controls['selectedReferenceFor'].value == 'CLEANER'
        "
      >
        <button class="mb-1" cc-raised-button (click)="add()" style="padding-left: 30px">
          <cc-icon class="icon">add</cc-icon> Add
        </button>
      </div>
      <cc-select
        [lazyPageFetcher]="revenueOptions"
        label="Revenue"
        formControlName="revenue"
        [required]="true"
        [emitFullSelectOption]="true"
      ></cc-select>
      <cc-input
        label="VAT Amount"
        formControlName="vatAmount"
        type="number"
      ></cc-input>
      <cc-select
        *ngIf="
          formGroup.controls['vatAmount'].value &&
          formGroup.controls['vatAmount'].value != 0
        "
        [data]="vatTypeOptions"
        label="VAT Type"
        formControlName="vatType"
        [required]="
          formGroup.controls['vatAmount'].value &&
          formGroup.controls['vatAmount'].value != 0
        "
      ></cc-select>
      <cc-textarea
        label="Description"
        formControlName="description"
        [required]="true"
      ></cc-textarea>
    </form>
    <div class="row justify-content-end">
      <div class="col-md-auto">
        <button cc-raised-button cc-dialog-close>Cancel</button>
      </div>
      <div class="col-md-auto px-3">
        <button
          cc-raised-button
          color="accent"
          [disabled]="formGroup.invalid"
          (click)="addTransaction()"
        >
          Add Transaction
        </button>
      </div>
    </div>
  </cc-dialog-content>
</div>
