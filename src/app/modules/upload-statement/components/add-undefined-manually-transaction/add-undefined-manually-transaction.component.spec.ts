import { ComponentFixture, TestBed } from '@angular/core/testing';

import { AddUndefinedManuallyTransactionComponent } from './add-undefined-manually-transaction.component';

describe('AddUndefinedManuallyTransactionComponent', () => {
  let component: AddUndefinedManuallyTransactionComponent;
  let fixture: ComponentFixture<AddUndefinedManuallyTransactionComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ AddUndefinedManuallyTransactionComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AddUndefinedManuallyTransactionComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
