import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { CCDialogRef } from '@maids/cc-lib/dialog';
import { CCNotificationService } from '@maids/cc-lib/services';
import { UploadStatementService } from '../../services/upload-statement.service';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { PaginationRequest } from '@maids/cc-lib/common';
import { SelectOption } from '@maids/cc-lib/select-input';
import { BehaviorSubject, Observable } from 'rxjs';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { HttpClient } from '@angular/common/http';
import { API } from 'src/environments/api';
import { CCBackendEndpoint } from '@maids/cc-erp-services';
@Component({
  selector: 'app-add-undefined-manually-transaction',
  templateUrl: './add-undefined-manually-transaction.component.html',
  styleUrls: ['./add-undefined-manually-transaction.component.scss'],
})
export class AddUndefinedManuallyTransactionComponent implements OnInit {
  formGroup = this.formBuilder.group({
    id: [''],
    date: [''],
    pnlValueDate: [''],
    amount: [''],
    expense: [''],
    revenue: [''],
    vatAmount: [''],
    vatType: [''],
    description: [''],
    selectedReferenceFor: [''],
    selectedReferencevalue: [''],
    salesforceName: [''],
  });
  readonly revenueOptions = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.uploadStatementService.getRevenueOptions(pageReq.searchString);
  };
  readonly expensesOptions = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.uploadStatementService.getExpensesOptions(pageReq.searchString);
  };
  readonly USrefOfficeStaffOptions = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.uploadStatementService.USrefOfficeStaffOptions(
      pageReq.searchString
    );
  };
  readonly USrefHousemaidOptions = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.uploadStatementService.USrefHousemaidOptions(
      pageReq.searchString
    );
  };
  readonly USrefClientOptions = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.uploadStatementService.USrefClientOptions(pageReq.searchString);
  };
  readonly USrefApplicantOptions = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.uploadStatementService.USrefApplicantOptions(
      pageReq.searchString
    );
  };
  readonly USrefProspectOptions = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.uploadStatementService.USrefProspectOptions(
      pageReq.searchString
    );
  };
  readonly USrefContractOptions = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.uploadStatementService.USrefContractOptions(
      pageReq.searchString
    );
  };
  readonly USrefFreedomOptions = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.uploadStatementService.USrefFreedomOptions(
      pageReq.searchString
    );
  };
  private selectedCleanerSubject = new BehaviorSubject<any[]>([]);
  selectedCleanerList$: Observable<any[]> =
    this.selectedCleanerSubject.asObservable();

  private selectedContactSubject = new BehaviorSubject<any[]>([]);
  selectedContactList$: Observable<any[]> =
    this.selectedContactSubject.asObservable();
  editMode: boolean = false;
  editingIndex: number | null = null;
  referenceForOptions: any[] = [
    { id: 'HOUSEMAID', text: 'Housemaid' },
    { id: 'CLEANER', text: 'Cleaner' },
    { id: 'OFFICE_STAFF', text: 'Office Staff' },
    { id: 'CLIENT', text: 'Maids cc Family' },
    { id: 'PROSPECT', text: 'Maids cc Prospect' },
    { id: 'CONTACT', text: 'Contact' },
    { id: 'CONTRACT', text: 'Contract' },
    { id: 'FREEDOM_OPERATOR', text: 'Operator' },
  ];
  vatTypeOptions: any[] = [
    { id: 'IN', text: ' Input VAT' },
    { id: 'OUT', text: ' Output VAT' },
  ];
  gridCols: CCGridColumn[] = [
    { field: 'salesforceId', header: 'Sales Force Id' },
    { field: 'salesforceName', header: 'Name' },
    {
      field: 'action',
      header: '#',
      type: 'button',
      buttonConfig: {
        mode: 'multiple',
        disabled: false,
        buttons: [
          {
            disabled: false,
            type: 'icon',
            icon: 'delete',
            color: 'accent',
            hidden: (record: any) => {
              return false;
            },
            mode: 'single',
            callback: (row: any, index: any) => {
              this.delete(row, index);
            },
          },
          {
            disabled: false,
            type: 'icon',
            icon: 'edit',
            color: 'primary',
            hidden: (record: any) => {
              return false;
            },
            mode: 'single',
            callback: (row: any, index: any) => {
              this.edit(row, index);
            },
          },
        ],
      },
    },
  ];
  constructor(
    private ccDialogRef: CCDialogRef<AddUndefinedManuallyTransactionComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private formBuilder: FormBuilder,
    private uploadStatementService: UploadStatementService,
    public readonly notifications: CCNotificationService,
    private http: HttpClient,
    @Inject(CCBackendEndpoint) private _api: string
  ) {}

  ngOnInit(): void {
    this.formGroup.controls['selectedReferencevalue'].valueChanges.subscribe(
      (res: any) => {
        if (
          this.formGroup.controls['selectedReferenceFor'].value == 'CLEANER'
        ) {
          if (
            !this.formGroup.controls['selectedReferencevalue'].value.startsWith(
              'a00'
            )
          ) {
            this.formGroup.controls['selectedReferencevalue'].setValue('a00');
          }
        }
        if (
          this.formGroup.controls['selectedReferenceFor'].value == 'CONTACT'
        ) {
          if (
            !this.formGroup.controls['selectedReferencevalue'].value.startsWith(
              '003'
            )
          ) {
            this.formGroup.controls['selectedReferencevalue'].setValue('003');
          }
        }
      }
    );
    this.formGroup.controls['selectedReferenceFor'].valueChanges.subscribe(
      (val) => {
        this.formGroup.controls['selectedReferencevalue'].setValue('');
        this.formGroup.controls['salesforceName'].setValue('');
      }
    );
  }
  addTransaction() {
    let payload: any;
    payload = {
      date: this.formGroup.controls['date'].value + ' 00:00:00',
      pnlValueDate: this.formGroup.controls['pnlValueDate'].value + ' 00:00:00',
      amount: this.formGroup.controls['amount'].value,
      expense: {
        id: this.formGroup.controls['expense'].value.id,
      },
      revenue: {
        id: this.formGroup.controls['revenue'].value.id,
      },
      vatAmount: this.formGroup.controls['vatAmount'].value,
      vatType: this.formGroup.controls['vatType'].value,
      description: this.formGroup.controls['description'].value,
      creationTriggeredAutomatically: false,
      selectedReferenceFor:
        this.formGroup.controls['selectedReferenceFor'].value,
      selectedReferencevalue: parseInt(
        this.formGroup.controls['selectedReferencevalue'].value
      ),
    };
    switch (this.formGroup.controls['selectedReferenceFor'].value) {
      case 'HOUSEMAID':
        let housemaids: any[] = [];
        this.formGroup.controls['selectedReferencevalue'].value.forEach(
          (element: any) => {
            housemaids.push({ housemaid: { id: element.id } });
          }
        );
        payload.housemaids = housemaids;
        break;
      case 'CLEANER':
        payload.sales = this.selectedCleanerSubject.getValue();
        break;
      case 'CONTACT':
        payload.sales = this.selectedContactSubject.getValue();
        break;
      case 'OFFICE_STAFF':
        let officeStaffs: any[] = [];
        this.formGroup.controls['selectedReferencevalue'].value.forEach(
          (element: any) => {
            officeStaffs.push({ officeStaff: { id: element.id } });
          }
        );
        payload.officeStaffs = officeStaffs;
        break;
      case 'CLIENT':
      case 'PROSPECT':
        let clients: any[] = [];
        this.formGroup.controls['selectedReferencevalue'].value.forEach(
          (element: any) => {
            clients.push({ client: { id: element.id } });
          }
        );
        payload.clients = clients;
        break;
      case 'CONTRACT':
        let contracts: any[] = [];
        this.formGroup.controls['selectedReferencevalue'].value.forEach(
          (element: any) => {
            contracts.push({ contract: { id: element.id } });
          }
        );
        payload.contracts = contracts;
        break;
      case 'FREEDOM_OPERATOR':
        let freedomOperators: any[] = [];
        this.formGroup.controls['selectedReferencevalue'].value.forEach(
          (element: any) => {
            freedomOperators.push({ freedomOperator: { id: element.id } });
          }
        );
        payload.freedomOperators = freedomOperators;
        break;
    }
    payload.transactionType =
      this.formGroup.controls['selectedReferenceFor'].value === ''
        ? 'UNKNOWN'
        : this.formGroup.controls['selectedReferenceFor'].value;
    this.http
      .post(
        `${this._api}/accounting/bankStatementFile/createTransaction/${this.data.id}`,
        payload
      )
      .subscribe((res: any) => {
        this.ccDialogRef.close(true);
      });
  }
  add() {
    if (
      this.formGroup.controls['selectedReferencevalue'].valid &&
      this.formGroup.controls['salesforceName'].valid
    ) {
      if (this.formGroup.controls['selectedReferenceFor'].value == 'CONTACT') {
        this.saveContact();
      } else if (
        this.formGroup.controls['selectedReferenceFor'].value == 'CLEANER'
      ) {
        this.saveCleaner();
      }
      this.formGroup.controls['selectedReferencevalue'].markAsUntouched();
      this.formGroup.controls['salesforceName'].markAsUntouched();
    }
  }

  saveContact() {
    const salesforceId =
      this.formGroup.controls['selectedReferencevalue'].value;
    const salesforceName = this.formGroup.controls['salesforceName'].value;
    if (
      this.formGroup.controls['selectedReferencevalue'].valid &&
      this.formGroup.controls['salesforceName'].valid
    ) {
      let currentContacts = this.selectedContactSubject.getValue();
      const contact = { salesforceId, salesforceName };
      currentContacts.push(contact);
      this.selectedContactSubject.next([...currentContacts]);
      this.editMode = false;
      this.editingIndex = null;
      this.formGroup.controls['selectedReferencevalue'].setValue('');
      this.formGroup.controls['salesforceName'].setValue('');
    }
  }

  saveCleaner() {
    const salesforceId =
      this.formGroup.controls['selectedReferencevalue'].value;
    const salesforceName = this.formGroup.controls['salesforceName'].value;
    if (
      this.formGroup.controls['selectedReferencevalue'].valid &&
      this.formGroup.controls['salesforceName'].valid
    ) {
      let currentCleaners = this.selectedCleanerSubject.getValue();
      const cleaner = { salesforceId, salesforceName };
      currentCleaners.push(cleaner);
      this.selectedCleanerSubject.next([...currentCleaners]);
      this.editMode = false;
      this.editingIndex = null;
      this.formGroup.controls['selectedReferencevalue'].setValue('');
      this.formGroup.controls['salesforceName'].setValue('');
    }
  }

  edit(element: any, index: number) {
    if (this.formGroup.controls['selectedReferenceFor'].value == 'CONTACT') {
      this.editContact(element, index);
    } else if (
      this.formGroup.controls['selectedReferenceFor'].value == 'CLEANER'
    ) {
      this.editCleaner(element, index);
    }
  }

  editContact(contact: any, index: number) {
    this.editMode = true;
    this.editingIndex = index;
    let currentContacts = this.selectedContactSubject.getValue();
    currentContacts.splice(index, 1);
    this.selectedContactSubject.next([...currentContacts]);
    this.formGroup.controls['selectedReferencevalue'].setValue(
      contact.salesforceId
    );
    this.formGroup.controls['salesforceName'].setValue(contact.salesforceName);
  }

  editCleaner(cleaner: any, index: number) {
    this.editMode = true;
    this.editingIndex = index;
    let currentCleaners = this.selectedCleanerSubject.getValue();
    currentCleaners.splice(index, 1);
    this.selectedCleanerSubject.next([...currentCleaners]);

    this.formGroup.controls['selectedReferencevalue'].setValue(
      cleaner.salesforceId
    );
    this.formGroup.controls['salesforceName'].setValue(cleaner.salesforceName);
  }

  delete(element: any, index: number) {
    if (this.formGroup.controls['selectedReferenceFor'].value == 'CONTACT') {
      this.deleteContact(index);
    } else if (
      this.formGroup.controls['selectedReferenceFor'].value == 'CLEANER'
    ) {
      this.deleteCleaner(index);
    }
  }

  deleteContact(index: number) {
    const currentContacts = this.selectedContactSubject.value;
    currentContacts.splice(index, 1);
    this.selectedContactSubject.next([...currentContacts]);
  }

  deleteCleaner(index: number) {
    const currentCleaners = this.selectedCleanerSubject.value;
    currentCleaners.splice(index, 1);
    this.selectedCleanerSubject.next([...currentCleaners]);
  }
  preventEnter(event: any) {
    event.preventDefault();
  }
}
