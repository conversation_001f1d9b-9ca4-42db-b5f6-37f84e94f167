import { Component, OnInit, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { UploadStatementService } from '../../services/upload-statement.service';

@Component({
  selector: 'app-bank-statement-transactions-details',
  templateUrl: './bank-statement-transactions-details.component.html',
  styleUrls: ['./bank-statement-transactions-details.component.scss'],
})
export class BankStatementTransactionsDetailsComponent
  implements OnInit, OnDestroy
{
  id: number = 0;
  summary: any;
  profile_tabs: any[] = [];
  selectedTab: string = '';
  private tabsIntervalId: any;
  private processingIntervalId: any;
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private uploadStatementService: UploadStatementService
  ) {}
  ngOnInit(): void {
    this.id = this.route.firstChild?.snapshot.params['id'];
    this.getTabsData();
    this.tabsIntervalId = setInterval(() => {
      this.getTabsData();
    }, 60000);
    this.getProccessingInfo();
    this.processingIntervalId = setInterval(() => {
      this.getProccessingInfo();
    }, 25000);
    this.route.firstChild?.url.subscribe((segments) => {
      const currentTab = segments[0]?.path;
      if (currentTab) {
        this.selectedTab = currentTab;
      }
    });
  }
  ngOnDestroy(): void {
    if (this.tabsIntervalId) {
      clearInterval(this.tabsIntervalId);
    }
    if (this.processingIntervalId) {
      clearInterval(this.processingIntervalId);
    }
  }
  getProccessingInfo() {
    if (this.id) {
      this.uploadStatementService
        .getProccessingInfo(this.id)
        .subscribe((res) => {
          if (res.processing) {
            this.uploadStatementService.processing.next(false);
            this.uploadStatementService.proccessingPercentage.next(res.percent);
          } else {
            this.uploadStatementService.processing.next(true);
          }
        });
    }
  }
  getTabsData() {
    if (this.id) {
      this.uploadStatementService
        .getbankStatementFileSummary(this.id)
        .subscribe((res) => {
          this.summary = res;
          this.setTabs();
          this.uploadStatementService.summaryObject.next(res);
        });
    }
  }
  getUnresolvedCount(page: any) {
    let count = 0;
    switch (page) {
      case 'Direct Debits':
        count =
          parseInt(this.summary.receivedDirectDebit.unresolved) +
          parseInt(this.summary.bouncedDirectDebit.unresolved) +
          parseInt(this.summary.unmatchedDirectDebit.unresolved);
        break;
      case 'Cheque Deposits':
        count =
          parseInt(this.summary.receivedChequeDeposit.unresolved) +
          parseInt(this.summary.bouncedChequeDeposit.unresolved) +
          parseInt(this.summary.unmatchedChequeDeposit.unresolved);
        break;
      case 'Credit Card Payments':
        count = parseInt(this.summary.creditCardPayment);
        break;
      case 'Cash Deposits - Wire Transfer':
        count =
          parseInt(this.summary.wireTransfer.unresolved) +
          parseInt(this.summary.cash.unresolved);
        break;
      case 'PDC payments':
        count = parseInt(this.summary.pdc.unresolved);
        break;
      case 'Expenses':
        count =
          parseInt(this.summary.clientRefundExpenses.unresolved) +
          parseInt(this.summary.unmatchedClientRefundExpenses.unresolved) +
          parseInt(this.summary.unmatchedExpenses.unresolved) +
          parseInt(this.summary.otherExpenses.unresolved) +
          parseInt(this.summary.bucketRefills.unresolved) +
          parseInt(this.summary.maidsAtExpenses.unresolved) +
          parseInt(this.summary.unmatchedMaidsAtExpenses.unresolved);
        break;
      case 'Undefined Transactions':
        count = parseInt(this.summary.undefinedTransaction.unresolved);
        break;
      case 'Payroll Transfers':
        count = parseInt(this.summary.payrollTransfer.unresolved);
        break;
      case 'bankTransferMatchedTransaction':
        count = parseInt(
          this.summary.bankTransferMatchedTransaction.unresolved
        );
        break;
    }
    return count;
  }
  setTabs() {
    this.profile_tabs = [
      {
        label: `Direct Debits (${this.getUnresolvedCount('Direct Debits')})`,
        link: 'direct-debits',
      },
      {
        label: `Cheque Deposits (${this.getUnresolvedCount(
          'Cheque Deposits'
        )})`,
        link: 'cheque-deposits',
      },
      {
        label: `Credit Card Payments (${this.getUnresolvedCount(
          'Credit Card Payments'
        )})`,
        link: 'credit-card-payments',
      },
      {
        label: `Cash Deposits - Wire Transfer (${this.getUnresolvedCount(
          'Cash Deposits - Wire Transfer'
        )})`,
        link: 'wire-transfer',
      },
      {
        label: `PDC payments (${this.getUnresolvedCount('PDC payments')})`,
        link: 'pdc-payments',
      },
      {
        label: `Expenses (${this.getUnresolvedCount('Expenses')})`,
        link: 'expenses',
      },
      {
        label: `Post Money Transfer (${this.getUnresolvedCount(
          'bankTransferMatchedTransaction'
        )})`,
        link: 'post-money-transfer',
      },
      {
        label: `Payroll Transfers (${this.getUnresolvedCount(
          'Payroll Transfers'
        )})`,
        link: 'payroll-transfers',
      },
      {
        label: `Undefined Transactions (${this.getUnresolvedCount(
          'Undefined Transactions'
        )})`,
        link: 'undefined-transactions',
      },
      {
        label: `Summary`,
        link: 'summary',
      },
    ];
  }
  selectTab(tab: string) {
    this.selectedTab = tab;
    this.router.navigateByUrl(
      `/accounting/v2/upload-statements/bank-statement-transactions-details/${tab}/${this.id}`
    );
  }
}
