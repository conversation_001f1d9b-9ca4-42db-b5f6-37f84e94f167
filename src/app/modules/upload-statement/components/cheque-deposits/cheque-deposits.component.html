<app-proccessing-bar
  *ngIf="summary && !summary.isResolved"
></app-proccessing-bar>
<div class="row mb-2 align-items-center" *ngIf="summary && summary.isResolved">
  <div class="col-md-3" style="font-weight: 500">
    <div>
      <span class="pr-1">Total Transactions: </span>
      <span class="bold cc-secondary">{{ summary.ChequeDepositTab.total }}</span>
    </div>
  </div>
  <div class="col-md-3" style="font-weight: 500">
    <div>
      <span class="pr-1">Total Resolved:</span>
      <span class="bold cc-secondary"
        >AED {{ summary.ChequeDepositTab.AED_RESOLVED }}</span
      >
    </div>
  </div>
  <div class="col-md-3" style="font-weight: 500">
    <div>
      <span class="pr-1">Total Unresolved:</span>
      <span class="bold cc-secondary"
        >AED {{ summary.ChequeDepositTab.AED_UNRESOLVED }}</span
      >
    </div>
  </div>
  <div class="col-md-3">
    <button
      cc-raised-button
      color="accent"
      (click)="getAllData()"
      style="padding-left: 33px"
    >
      <cc-icon class="icon">restart_alt </cc-icon>Refresh Transactions
    </button>
  </div>
</div>
<cc-accordion class="w-100">
  <cc-panel>
    <cc-panel-title>
      <b>
        Matched Transactions - Received Cheques ({{
          recordCountMatchedTransReceivedCheques
        }})</b
      >
    </cc-panel-title>

    <cc-panel-body>
      <div class="row mb-2 align-items-center">
        <div class="col-md-3" style="font-weight: 500">
          <div>
            <span class="pr-1">Total Transactions: </span>
            <span class="bold cc-secondary">{{
              recordCountMatchedTransReceivedCheques
            }}</span>
          </div>
        </div>
        <div class="col-md-3" style="font-weight: 500">
          <div>
            <span class="pr-1">Total Amount:</span>
            <span class="bold cc-secondary"
              >AED {{ totalAmountMatchedTransReceivedCheques | number }}</span
            >
          </div>
        </div>
        <div class="col-md-6">
          <div class="row justify-content-end">
            <div class="col-md-auto mt-1">
              <button
                cc-raised-button
                (click)="
                  checkIfSelectedPageReceived()
                    ? unSelectPageReceived()
                    : selectPageReceived(true)
                "
                [disabled]="recordCountMatchedTransReceivedCheques == 0"
              >
                {{ checkIfSelectedPageReceived() ? "Unselect" : "Select" }} Page
              </button>
            </div>
            <div class="col-md-auto mt-1">
              <button cc-raised-button (click)="selectAllReceived()">
                {{ isSelectAllReceived ? "Unselect" : "Select" }} All ({{
                  recordCountMatchedTransReceivedCheques
                }})
              </button>
            </div>
            <div class="col-md-auto mt-1">
              <button
                cc-raised-button
                color="accent"
                (click)="exportMatchedTransactionReceivedCheques()"
              >
                Export
              </button>
            </div>
            <div class="col-md-auto mt-1">
              <button
                cc-raised-button
                (click)="confirmReceivedCheques()"
                [disabled]="getSelectedRowsCountReceived() <= 0"
              >
                Confirm Received Payments
              </button>
            </div>
          </div>
        </div>
      </div>
      <cc-datagrid
        *ngIf="MatchedTransReceivedCheques$ | async as records"
        [data]="records.content"
        [columns]="MTRCCols"
        [length]="records.totalElements"
        [pageOnFront]="false"
        [pageIndex]="records.number"
        [pageSize]="records.size"
        [pageSizeOptions]="[10, 25, 50]"
        (page)="getMTRCNext($event)"
        [stickyHeader]="true"
        [showColumnMenuButton]="true"
        [showColumnMenuHeader]="false"
        [columnMenuButtonIcon]="'settings'"
        [columnMovable]="true"
        [cellTemplate]="{ select: select1, rowNum: rowNum1 }"
      ></cc-datagrid>
      <ng-template #select1 let-row let-index="index" let-col="colDef">
        <cc-checkbox
          [disabled]="row.resolved"
          [(ngModel)]="selectedReceived[row.id]"
        ></cc-checkbox>
      </ng-template>
      <ng-template #rowNum1 let-row let-index="index" let-col="colDef">
        {{ index + currentPageTransReceived * sizePageTransReceived + 1 }}
      </ng-template>
    </cc-panel-body>
  </cc-panel>
</cc-accordion>

<cc-accordion class="w-100">
  <cc-panel>
    <cc-panel-title>
      <b>
        Already Matched Transactions - Received Cheques ({{
          recordCountAlreadyMatchedTransReceivedCheques
        }})</b
      >
    </cc-panel-title>
    <cc-panel-body>
      <div class="row mb-2 align-items-center">
        <div class="col-md-3" style="font-weight: 500">
          <div>
            <span class="pr-1">Total Transactions: </span>
            <span class="bold cc-secondary">{{
              recordCountAlreadyMatchedTransReceivedCheques
            }}</span>
          </div>
        </div>
        <div class="col-md-3" style="font-weight: 500">
          <div>
            <span class="pr-1">Total Amount:</span>
            <span class="bold cc-secondary"
              >AED {{ totalAmountAlreadyMatchedTransReceivedCheques | number }}</span
            >
          </div>
        </div>
        <div class="col-md-6">
          <div class="d-flex justify-content-end">
            <div class="col-md-auto px-0 mt-1">
              <button
                cc-raised-button
                color="accent"
                (click)="exportAlreadyMatchedTransactionReceivedCheques()"
              >
                Export
              </button>
            </div>
          </div>
        </div>
      </div>
      <cc-datagrid
        *ngIf="MatchedTransAlreadyReceivedCheques$ | async as records"
        [data]="records.content"
        [columns]="MTARCCols"
        [length]="records.totalElements"
        [pageOnFront]="false"
        [pageIndex]="records.number"
        [pageSize]="records.size"
        [pageSizeOptions]="[10, 25, 50]"
        (page)="getMTARCNext($event)"
        [stickyHeader]="true"
        [showColumnMenuButton]="true"
        [showColumnMenuHeader]="false"
        [columnMenuButtonIcon]="'settings'"
        [columnMovable]="true"
        [cellTemplate]="{ rowNum: rowNum2 }"
      ></cc-datagrid>
      <ng-template #rowNum2 let-row let-index="index" let-col="colDef">
        {{
          index +
            currentPageTransAlreadyReceived * sizePageTransAlreadyReceived +
            1
        }}
      </ng-template>
    </cc-panel-body></cc-panel
  ></cc-accordion
>
<cc-accordion class="w-100">
  <cc-panel>
    <cc-panel-title>
      <b>
        Unmatched Transactions from Bank Statement - Received Cheques ({{
          recordCountUnmatchedFromBankStatement
        }})</b
      >
    </cc-panel-title>
    <cc-panel-body>
      <div class="row mb-2 align-items-center">
        <div class="col-md-3" style="font-weight: 500">
          <div>
            <span class="pr-1">Total Transactions: </span>
            <span class="bold cc-secondary">{{
              recordCountUnmatchedFromBankStatement
            }}</span>
          </div>
        </div>
        <div class="col-md-3" style="font-weight: 500">
          <div>
            <span class="pr-1">Total Amount:</span>
            <span class="bold cc-secondary"
              >AED {{ totalAmountUnmatchedFromBankStatement | number }}</span
            >
          </div>
        </div>
        <div class="col-md-6">
          <div class="d-flex justify-content-end">
            <div class="col-md-auto px-0 mt-1">
              <button
                cc-raised-button
                color="accent"
                (click)="exportUnmatchedTransactionFromBankStatement()"
              >
                Export
              </button>
            </div>
          </div>
        </div>
      </div>
      <cc-datagrid
        *ngIf="UnmatchedFromBank$ | async as records"
        [data]="records.content"
        [columns]="UFBCols"
        [length]="records.totalElements"
        [pageOnFront]="false"
        [pageIndex]="records.number"
        [pageSize]="records.size"
        [pageSizeOptions]="[10, 25, 50]"
        (page)="getUFBNext($event)"
        [stickyHeader]="true"
        [showColumnMenuButton]="true"
        [showColumnMenuHeader]="false"
        [columnMenuButtonIcon]="'settings'"
        [columnMovable]="true"
        [cellTemplate]="{ rowNum: rowNum3, action: action3 }"
      ></cc-datagrid>
      <ng-template #rowNum3 let-row let-index="index" let-col="colDef">
        {{
          index + currentPageUnmatchedFromBank * sizePageUnmatchedFromBank + 1
        }}
      </ng-template>
      <ng-template #action3 let-row let-index="index" let-col="colDef">
        <button
          cc-raised-button
          color="primary"
          (click)="linkToPayment(row)"
          [disabled]="row.resolved"
        >
          {{ row.resolved ? "Resolved" : "Link To Payment" }}
        </button>
      </ng-template>
    </cc-panel-body>
  </cc-panel>
</cc-accordion>

<cc-accordion class="w-100">
  <cc-panel>
    <cc-panel-title>
      <b>
        Matched Transactions - Bounced Cheques ({{
          recordCountMatchedTransBouncedCheques
        }})</b
      >
    </cc-panel-title>
    <cc-panel-body>
      <div class="row mb-2 align-items-center">
        <div class="col-md-3" style="font-weight: 500">
          <div>
            <span class="pr-1">Total Transactions: </span>
            <span class="bold cc-secondary">{{
              recordCountMatchedTransBouncedCheques
            }}</span>
          </div>
        </div>
        <div class="col-md-3" style="font-weight: 500">
          <div>
            <span class="pr-1">Total Amount:</span>
            <span class="bold cc-secondary"
              >AED {{ totalAmountMatchedTransBouncedCheques | number }}</span
            >
          </div>
        </div>
        <div class="col-md-6">
          <div class="row justify-content-end">
            <div class="col-md-auto mt-1">
              <button
                cc-raised-button
                (click)="
                  checkIfSelectedPageBounced()
                    ? unSelectPageBounced()
                    : selectPageBounced(true)
                "
                [disabled]="recordCountMatchedTransBouncedCheques == 0"
              >
                {{ checkIfSelectedPageBounced() ? "Unselect" : "Select" }} Page
              </button>
            </div>
            <div class="col-md-auto mt-1">
              <button cc-raised-button (click)="selectAllBounced()">
                {{ isSelectAllBounced ? "Unselect" : "Select" }} All ({{
                  recordCountMatchedTransBouncedCheques
                }})
              </button>
            </div>
            <div class="col-md-auto mt-1">
              <button
                cc-raised-button
                color="accent"
                (click)="exportMatchedTransactionBouncedCheques()"
              >
                Export
              </button>
            </div>
            <div class="col-md-auto mt-1">
              <button
                cc-raised-button
                (click)="confirmBouncedCheques()"
                [disabled]="getSelectedRowsCountBounced() <= 0"
              >
                Confirm Bounced Cheques
              </button>
            </div>
          </div>
        </div>
      </div>
      <cc-datagrid
        *ngIf="MatchedTransBouncedCheques$ | async as records"
        [data]="records.content"
        [columns]="MTBCCols"
        [length]="records.totalElements"
        [pageOnFront]="false"
        [pageIndex]="records.number"
        [pageSize]="records.size"
        [pageSizeOptions]="[10, 25, 50]"
        (page)="getMTBCNext($event)"
        [stickyHeader]="true"
        [showColumnMenuButton]="true"
        [showColumnMenuHeader]="false"
        [columnMenuButtonIcon]="'settings'"
        [columnMovable]="true"
        [cellTemplate]="{ select: select4, rowNum: rowNum4 }"
      ></cc-datagrid>
      <ng-template #select4 let-row let-index="index" let-col="colDef">
        <cc-checkbox
          [disabled]="row.resolved"
          [(ngModel)]="selectedBounced[row.id]"
        ></cc-checkbox>
      </ng-template>
      <ng-template #rowNum4 let-row let-index="index" let-col="colDef">
        {{ index + currentPageTransBounced * sizePageTransBounced + 1 }}
      </ng-template>
    </cc-panel-body>
  </cc-panel>
</cc-accordion>
<cc-accordion>
  <cc-panel>
    <cc-panel-title>
      <b
        >Unmatched Transactions - Bounced Cheques ({{
          recordCountUnMatchedTransBouncedCheques
        }})</b
      >
    </cc-panel-title>
    <cc-panel-body>
      <div class="row mb-2 align-items-center">
        <div class="col-md-3" style="font-weight: 500">
          <div>
            <span class="pr-1">Total Transactions: </span>
            <span class="bold cc-secondary">{{
              recordCountUnMatchedTransBouncedCheques
            }}</span>
          </div>
        </div>
        <div class="col-md-3" style="font-weight: 500">
          <div>
            <span class="pr-1">Total Amount:</span>
            <span class="bold cc-secondary"
              >AED {{ totalAmountUnMatchedTransBouncedCheques | number }}</span
            >
          </div>
        </div>
        <div class="col-md-6">
          <div class="d-flex justify-content-end">
            <div class="col-md-auto px-0 mt-1">
              <button
                cc-raised-button
                color="accent"
                (click)="exportUnMatchedTransactionBouncedPayment()"
              >
                Export
              </button>
            </div>
          </div>
        </div>
      </div>
      <cc-datagrid
        *ngIf="UnmatchedBouncedFromBank$ | async as records"
        [data]="records.content"
        [columns]="UBFBCols"
        [length]="records.totalElements"
        [pageOnFront]="false"
        [pageIndex]="records.number"
        [pageSize]="records.size"
        [pageSizeOptions]="[10, 25, 50]"
        (page)="getUBFBNext($event)"
        [stickyHeader]="true"
        [showColumnMenuButton]="true"
        [showColumnMenuHeader]="false"
        [columnMenuButtonIcon]="'settings'"
        [columnMovable]="true"
        [cellTemplate]="{ rowNum: rowNum5, action: action5 }"
      ></cc-datagrid>
      <ng-template #rowNum5 let-row let-index="index" let-col="colDef">
        {{
          index +
            currentPageUnmatchedBouncedFromBank *
              sizePageUnmatchedBouncedFromBank +
            1
        }}
      </ng-template>
      <ng-template #action5 let-row let-index="index" let-col="colDef">
        <button
          cc-raised-button
          color="primary"
          (click)="linkToPayment(row)"
          [disabled]="row.resolved"
        >
          {{ row.resolved ? "Resolved" : "Link To Payment" }}
        </button>
      </ng-template>
    </cc-panel-body></cc-panel
  >
</cc-accordion>

<cc-accordion>
  <cc-panel>
    <cc-panel-title>
      <b
        >Already Matched Transactions - Bounced Cheques ({{
          recordCountAlreadyMatchedTransBouncedCheques
        }})</b
      >
    </cc-panel-title>
    <cc-panel-body>
      <div class="row mb-2 align-items-center">
        <div class="col-md-3" style="font-weight: 500">
          <div>
            <span class="pr-1">Total Transactions: </span>
            <span class="bold cc-secondary">{{
              recordCountAlreadyMatchedTransBouncedCheques
            }}</span>
          </div>
        </div>
        <div class="col-md-3" style="font-weight: 500">
          <div>
            <span class="pr-1">Total Amount:</span>
            <span class="bold cc-secondary"
              >AED {{ totalAmountAlreadyMatchedTransBouncedCheques | number}}</span
            >
          </div>
        </div>
        <div class="col-md-6">
          <div class="d-flex justify-content-end">
            <div class="col-md-auto px-0 mt-1">
              <button
                cc-raised-button
                color="accent"
                (click)="exportAlreadyMatchedTransactionBouncedCheques()"
              >
                Export
              </button>
            </div>
          </div>
        </div>
      </div>
      <cc-datagrid
        *ngIf="MatchedTransAlreadyBouncedCheques$ | async as records"
        [data]="records.content"
        [columns]="MTABCCols"
        [length]="records.totalElements"
        [pageOnFront]="false"
        [pageIndex]="records.number"
        [pageSize]="records.size"
        [pageSizeOptions]="[10, 25, 50]"
        (page)="getMTABCNext($event)"
        [stickyHeader]="true"
        [showColumnMenuButton]="true"
        [showColumnMenuHeader]="false"
        [columnMenuButtonIcon]="'settings'"
        [columnMovable]="true"
        [cellTemplate]="{ rowNum: rowNum6 }"
      ></cc-datagrid>
      <ng-template #rowNum6 let-row let-index="index" let-col="colDef">
        {{
          index +
            currentPageTransAlreadyBounced * sizePageTransAlreadyBounced +
            1
        }}
      </ng-template>
    </cc-panel-body></cc-panel
  ></cc-accordion
>
