import { Component, Inject, Input, OnInit } from '@angular/core';
import { UploadStatementService } from '../../services/upload-statement.service';
import { ActivatedRoute } from '@angular/router';
import { PageableResponseModel } from '@maids/cc-lib/common';
import { BehaviorSubject, Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { CCBackendEndpoint } from '@maids/cc-erp-services';
import { PageEvent } from '@angular/material/paginator';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import * as moment from 'moment/moment';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { LinkToContractPaymentComponent } from '../link-to-contract-payment/link-to-contract-payment.component';
import { LinkToPaymentComponent } from '../link-to-payment/link-to-payment.component';
import { CCDialog } from '@maids/cc-lib/dialog';

@Component({
  selector: 'app-cheque-deposits',
  templateUrl: './cheque-deposits.component.html',
  styleUrls: ['./cheque-deposits.component.scss'],
})
export class ChequeDepositsComponent implements OnInit {
  resolved: number = 0;
  unresolved: number = 0;

  constructor(
    private uploadStatementService: UploadStatementService,
    private route: ActivatedRoute,
    private http: HttpClient,
    @Inject(CCBackendEndpoint) private _api: string,
    private mediaService: MediaService,
    private ccDialog: CCDialog,
    private notification: CCNotificationService
  ) {}

  recordCountMatchedTransReceivedCheques: number = 0;
  totalAmountMatchedTransReceivedCheques: number = 0;
  recordCountAlreadyMatchedTransReceivedCheques: number = 0;
  totalAmountAlreadyMatchedTransReceivedCheques: number = 0;
  recordCountUnmatchedFromBankStatement: number = 0;
  totalAmountUnmatchedFromBankStatement: number = 0;
  recordCountMatchedTransBouncedCheques: number = 0;
  totalAmountMatchedTransBouncedCheques: number = 0;
  recordCountAlreadyMatchedTransBouncedCheques: number = 0;
  totalAmountAlreadyMatchedTransBouncedCheques: number = 0;
  recordCountUnMatchedTransBouncedCheques: number = 0;
  totalAmountUnMatchedTransBouncedCheques: number = 0;
  selectedReceived: any[] = [];
  selectedBounced: any[] = [];
  currentPageTransReceived: number = 0;
  currentPageTransAlreadyReceived: number = 0;
  currentPageTransBounced: number = 0;
  currentPageTransAlreadyBounced: number = 0;
  currentPageUnmatchedFromBank: number = 0;
  currentPageUnmatchedBouncedFromBank: number = 0;
  sizePageTransReceived: number = 50;
  sizePageTransAlreadyReceived: number = 50;
  sizePageTransBounced: number = 50;
  sizePageTransAlreadyBounced: number = 50;
  sizePageUnmatchedFromBank: number = 50;
  sizePageUnmatchedBouncedFromBank: number = 50;
  summary: any;
  isSelectAllReceived: boolean = false;
  isSelectAllBounced: boolean = false;
  MatchedTransReceivedCheques = new BehaviorSubject<PageableResponseModel<any>>(
    {
      content: [],
      number: 0,
      size: 0,
      totalElements: 0,
      totalPages: 0,
    }
  );
  MatchedTransReceivedCheques$: Observable<PageableResponseModel<any>> =
    this.MatchedTransReceivedCheques.asObservable();

  MatchedTransAlreadyReceivedCheques = new BehaviorSubject<
    PageableResponseModel<any>
  >({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  MatchedTransAlreadyReceivedCheques$: Observable<PageableResponseModel<any>> =
    this.MatchedTransAlreadyReceivedCheques.asObservable();

  MatchedTransBouncedCheques = new BehaviorSubject<PageableResponseModel<any>>({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  MatchedTransBouncedCheques$: Observable<PageableResponseModel<any>> =
    this.MatchedTransBouncedCheques.asObservable();

  MatchedTransAlreadyBouncedCheques = new BehaviorSubject<
    PageableResponseModel<any>
  >({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  MatchedTransAlreadyBouncedCheques$: Observable<PageableResponseModel<any>> =
    this.MatchedTransAlreadyBouncedCheques.asObservable();

  UnmatchedFromBank = new BehaviorSubject<PageableResponseModel<any>>({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  UnmatchedFromBank$: Observable<PageableResponseModel<any>> =
    this.UnmatchedFromBank.asObservable();

  UnmatchedBouncedFromBank = new BehaviorSubject<PageableResponseModel<any>>({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  UnmatchedBouncedFromBank$: Observable<PageableResponseModel<any>> =
    this.UnmatchedBouncedFromBank.asObservable();

  ngOnInit(): void {
    this.uploadStatementService.summaryObject.subscribe((res) => {
      this.summary = res;
    });
    this.getAllData();
  }

  getAllData() {
    this.getMatchedTransReceivedChequesData();
    this.getMatchedTransAlreadyReceivedChequesData();
    this.getMatchedTransBouncedChequesData();
    this.getMatchedTransAlreadyBouncedChequesData();
    this.getUnmatchedFromBankData();
    this.getUnmatchedBouncedFromBankData();
  }

  getMatchedTransReceivedChequesData(page: number = 0, size: number = 50) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/chequeDepositTransactions`,
        {
          params: {
            page,
            size,
            gridType: 'matchedTransactionReceivedCheques',
          },
        }
      )
      .subscribe((res: any) => {
        this.MatchedTransReceivedCheques.next(res);
        this.recordCountMatchedTransReceivedCheques = res.totalElements;
        this.totalAmountMatchedTransReceivedCheques = res.totalSum;
        if (this.isSelectAllReceived) {
          this.selectPageReceived();
        }
      });
  }

  getMTRCNext(event: PageEvent) {
    this.getMatchedTransReceivedChequesData(event.pageIndex, event.pageSize);
    this.currentPageTransReceived = event.pageIndex;
    this.sizePageTransReceived = event.pageSize;
  }

  getMatchedTransAlreadyReceivedChequesData(
    page: number = 0,
    size: number = 50
  ) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/chequeDepositTransactions`,
        {
          params: {
            page,
            size,
            gridType: 'alreadyMatchedTransactionReceivedCheques',
          },
        }
      )
      .subscribe((res: any) => {
        this.MatchedTransAlreadyReceivedCheques.next(res);
        this.recordCountAlreadyMatchedTransReceivedCheques = res.totalElements;
        this.totalAmountAlreadyMatchedTransReceivedCheques = res.totalSum;
      });
  }

  getMTARCNext(event: PageEvent) {
    this.getMatchedTransAlreadyReceivedChequesData(
      event.pageIndex,
      event.pageSize
    );
    this.currentPageTransAlreadyReceived = event.pageIndex;
    this.sizePageTransAlreadyReceived = event.pageSize;
  }

  getMatchedTransBouncedChequesData(page: number = 0, size: number = 50) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/chequeDepositTransactions`,
        {
          params: {
            page,
            size,
            gridType: 'matchedTransactionBouncedCheques',
          },
        }
      )
      .subscribe((res: any) => {
        this.MatchedTransBouncedCheques.next(res);
        this.recordCountMatchedTransBouncedCheques = res.totalElements;
        this.totalAmountMatchedTransBouncedCheques = res.totalSum;
        if (this.isSelectAllBounced) {
          this.selectPageBounced();
        }
      });
  }

  getMTBCNext(event: PageEvent) {
    this.getMatchedTransBouncedChequesData(event.pageIndex, event.pageSize);
    this.currentPageTransBounced = event.pageIndex;
    this.sizePageTransBounced = event.pageSize;
  }

  getMatchedTransAlreadyBouncedChequesData(
    page: number = 0,
    size: number = 50
  ) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/chequeDepositTransactions`,
        {
          params: {
            page,
            size,
            gridType: 'alreadyMatchedTransactionBouncedCheques',
          },
        }
      )
      .subscribe((res: any) => {
        this.MatchedTransAlreadyBouncedCheques.next(res);
        this.recordCountAlreadyMatchedTransBouncedCheques = res.totalElements;
        this.totalAmountAlreadyMatchedTransBouncedCheques = res.totalSum;
      });
  }

  getMTABCNext(event: PageEvent) {
    this.getMatchedTransAlreadyBouncedChequesData(
      event.pageIndex,
      event.pageSize
    );
    this.currentPageTransAlreadyBounced = event.pageIndex;
    this.sizePageTransAlreadyBounced = event.pageSize;
  }

  getUnmatchedFromBankData(page: number = 0, size: number = 50) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/chequeDepositTransactions`,
        {
          params: {
            page,
            size,
            gridType: 'unmatchedReceivedTransactionFromBankStatement',
          },
        }
      )
      .subscribe((res: any) => {
        this.UnmatchedFromBank.next(res);
        this.recordCountUnmatchedFromBankStatement = res.totalElements;
        this.totalAmountUnmatchedFromBankStatement = res.totalSum;
      });
  }

  getUFBNext(event: PageEvent) {
    this.getUnmatchedFromBankData(event.pageIndex, event.pageSize);
    this.currentPageUnmatchedFromBank = event.pageIndex;
    this.sizePageUnmatchedFromBank = event.pageSize;
  }

  getUnmatchedBouncedFromBankData(page: number = 0, size: number = 50) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/chequeDepositTransactions`,
        {
          params: {
            page,
            size,
            gridType: 'unmatchedBouncedTransactionFromBankStatement',
          },
        }
      )
      .subscribe((res: any) => {
        this.UnmatchedBouncedFromBank.next(res);
        this.recordCountUnMatchedTransBouncedCheques = res.totalElements;
        this.totalAmountUnMatchedTransBouncedCheques = res.totalSum;
      });
  }

  getUBFBNext(event: PageEvent) {
    this.getUnmatchedBouncedFromBankData(event.pageIndex, event.pageSize);
    this.currentPageUnmatchedBouncedFromBank = event.pageIndex;
    this.sizePageUnmatchedBouncedFromBank = event.pageSize;
  }

  MTRCCols: CCGridColumn[] = [
    { field: 'select', header: 'Select' },
    { field: 'rowNum', header: 'Row Number' },
    {
      field: 'transactionId',
      header: 'Transaction Date',
      formatter: (rowData: any) => {
        let td = moment(rowData.date).format('YYYY-MM-DD');
        return rowData.transaction.id
          ? `<a class="cc-secondary" href="#!/accounting/add-edit-transactions/${rowData.transaction.id}">${td}</a>`
          : td;
      },
    },
    {
      field: 'chequeNumber',
      header: 'Cheque No.',
      formatter: (rowData: any) => {
        if (rowData.contract.client && rowData.chequeNumber) {
          return (
            '<a class="cc-secondary" href="#!/client/payments/edit/' +
            rowData.contract.client.id +
            '/' +
            rowData.contract.id +
            '/' +
            rowData.payment.id +
            '">' +
            rowData.chequeNumber +
            '</a>'
          );
        } else {
          return '-';
        }
      },
    },
    { field: 'chequeName', header: 'Cheque Name' },
    {
      field: 'client',
      header: 'Family Name',
      formatter(rowData, colDef) {
        if (rowData.contract.client)
          return (
            '<a class="cc-secondary" href="#!/client/details/' +
            rowData.contract.client.id +
            '">' +
            rowData.contract.client.name +
            '</a>'
          );
        else return rowData.client;
      },
    },
    { field: 'transactionAmount', header: 'Transaction Amount' },
    {
      field: 'revenue',
      header: 'Revenue',
      formatter(rowData, colDef) {
        return rowData.revenue ? rowData.revenue.code : '';
      },
    },
    { field: 'description', header: 'Description' },
    { field: 'bankTransactionMatchType', header: 'Matched Type' },
    { field: 'paymentDetail', header: 'Payment Details' },
  ];
  MTARCCols: CCGridColumn[] = [
    { field: 'rowNum', header: 'Row Number' },
    {
      field: 'transactionId',
      header: 'Transaction Date',
      formatter: (rowData: any) => {
        let td = moment(rowData.date).format('YYYY-MM-DD');
        return rowData.transaction.id
          ? `<a class="cc-secondary" href="#!/accounting/add-edit-transactions/${rowData.transaction.id}">${td}</a>`
          : td;
      },
    },
    {
      field: 'chequeNumber',
      header: 'Cheque No.',
      formatter: (rowData: any) => {
        if (rowData.contract.client && rowData.chequeNumber) {
          return (
            '<a class="cc-secondary" href="#!/client/payments/edit/' +
            rowData.contract.client.id +
            '/' +
            rowData.contract.id +
            '/' +
            rowData.payment.id +
            '">' +
            rowData.chequeNumber +
            '</a>'
          );
        } else {
          return '-';
        }
      },
    },
    { field: 'chequeName', header: 'Cheque Name' },
    {
      field: 'client',
      header: 'Family Name',
      formatter(rowData, colDef) {
        if (rowData.contract.client)
          return (
            '<a class="cc-secondary" href="#!/client/details/' +
            rowData.contract.client.id +
            '">' +
            rowData.contract.client.name +
            '</a>'
          );
        else return rowData.client;
      },
    },
    { field: 'transactionAmount', header: 'Transaction Amount' },
    {
      field: 'revenue',
      header: 'Revenue',
      formatter(rowData, colDef) {
        return rowData.revenue ? rowData.revenue.code : '';
      },
    },
    { field: 'description', header: 'Description' },
    { field: 'bankTransactionMatchType', header: 'Matched Type' },
    { field: 'paymentDetail', header: 'Payment Details' },
  ];
  MTBCCols: CCGridColumn[] = [
    { field: 'select', header: 'Select' },
    { field: 'rowNum', header: 'Row Number' },
    {
      field: 'transactionId',
      header: 'Transaction Date',
      formatter: (rowData: any) => {
        let td = moment(rowData.date).format('YYYY-MM-DD');
        return rowData.transaction.id
          ? `<a class="cc-secondary" href="#!/accounting/add-edit-transactions/${rowData.transaction.id}">${td}</a>`
          : td;
      },
    },
    { field: 'transactionAmount', header: 'Transaction Amount' },
    {
      field: 'chequeNumber',
      header: 'Cheque No.',
      formatter: (rowData: any) => {
        if (rowData.contract.client && rowData.chequeNumber) {
          return `<a class="cc-secondary" href="#!/client/payments/edit/${rowData.contract.client.id}/${rowData.contract.id}/${rowData.payment.id}">${rowData.chequeNumber}</a>`;
        } else {
          return '-';
        }
      },
    },
    { field: 'chequeName', header: 'Cheque Name' },
    { field: 'paymentDetail', header: 'Payment Details' },
    {
      field: 'applicationId',
      header: 'Bounced Payment Id',
      formatter(rowData, colDef) {
        if (rowData.contract.client && rowData.bouncedPaymentId) {
          return `<a class="cc-secondary" href="#!/client/payments/edit/${rowData.contract.client.id}/${rowData.contract.id}/${rowData.bouncedPaymentId}">Payment-${rowData.bouncedPaymentId}</a>`;
        } else {
          return rowData.applicationId;
        }
      },
    },
    {
      field: 'client',
      header: 'Family Name',
      formatter(rowData, colDef) {
        if (rowData.contract.client)
          return (
            '<a class="cc-secondary" href="#!/client/details/' +
            rowData.contract.client.id +
            '">' +
            rowData.contract.client.name +
            '</a>'
          );
        else return rowData.client;
      },
    },
  ];
  MTABCCols: CCGridColumn[] = [
    { field: 'rowNum', header: 'Row Number' },
    {
      field: 'transactionId',
      header: 'Transaction Date',
      formatter: (rowData: any) => {
        let td = moment(rowData.date).format('YYYY-MM-DD');
        return rowData.transaction.id
          ? `<a class="cc-secondary" href="#!/accounting/add-edit-transactions/${rowData.transaction.id}">${td}</a>`
          : td;
      },
    },
    { field: 'transactionAmount', header: 'Transaction Amount' },
    {
      field: 'chequeNumber',
      header: 'Cheque No.',
      formatter: (rowData: any) => {
        if (rowData.contract.client && rowData.chequeNumber) {
          return `<a class="cc-secondary" href="#!/client/payments/edit/${rowData.contract.client.id}/${rowData.contract.id}/${rowData.payment.id}">${rowData.chequeNumber}</a>`;
        } else {
          return '-';
        }
      },
    },
    { field: 'chequeName', header: 'Cheque Name' },
    { field: 'paymentDetail', header: 'Payment Details' },
    {
      field: 'applicationId',
      header: 'Bounced Payment Id',
      formatter: (rowData: any) => {
        if (rowData.contract.client && rowData.bouncedPaymentId) {
          return `<a class="cc-secondary" href="#!/client/payments/edit/${rowData.contract.client.id}/${rowData.contract.id}/${rowData.bouncedPaymentId}">Payment-${rowData.bouncedPaymentId}</a>`;
        } else {
          return rowData.applicationId;
        }
      },
    },
    {
      field: 'client',
      header: 'Family Name',
      formatter: (rowData: any) => {
        if (rowData.contract.client)
          return (
            '<a class="cc-secondary" href="#!/client/details/' +
            rowData.contract.client.id +
            '">' +
            rowData.contract.client.name +
            '</a>'
          );
        else return rowData.client;
      },
    },
  ];
  UFBCols: CCGridColumn[] = [
    { field: 'rowNum', header: 'Row Number' },
    {
      field: 'date',
      header: 'Transaction',
      formatter(rowData, colDef) {
        return moment(rowData.date).format('YYYY-MM-DD');
      },
    },
    { field: 'transactionAmount', header: 'Transaction Amount' },
    { field: 'chequeNumber', header: 'Cheque No.' },
    { field: 'paymentDetail', header: 'Payment Details' },
    { field: 'reason', header: 'Reason' },
    { field: 'description', header: 'Transaction Description' },
    { field: 'action', header: 'Action' },
  ];
  UBFBCols: CCGridColumn[] = [
    {
      field: 'route',
      header: '',
      type: 'button',
      buttonConfig: {
        mode: 'single',
        disabled: false,
        type: 'raised',
        hidden(record) {
          return record.reason == 'Contract Not Found';
        },
        text: 'Go To Families List',
        callback(record, index) {
          window.open('#!/client/client-list', '_blank');
        },
      },
    },
    { field: 'rowNum', header: 'Row Number' },
    {
      field: 'date',
      header: 'Transaction',
      formatter(rowData, colDef) {
        return moment(rowData.date).format('YYYY-MM-DD');
      },
    },
    { field: 'transactionAmount', header: 'Transaction Amount' },
    { field: 'chequeNumber', header: 'Cheque No.' },
    { field: 'paymentDetail', header: 'Payment Details' },
    { field: 'reason', header: 'Reason' },
    { field: 'actions', header: 'Actions' },
  ];
  checkIfSelectedPageReceived() {
    let selected = true;
    this.MatchedTransReceivedCheques.getValue().content.forEach(
      (element: any) => {
        if (!this.selectedReceived[element.id]) {
          selected = false;
        }
      }
    );
    return selected;
  }
  unSelectPageReceived() {
    this.isSelectAllReceived = false;
    this.MatchedTransReceivedCheques.getValue().content.forEach(
      (element: any) => {
        this.selectedReceived[element.id] = false;
      }
    );
  }

  selectPageReceived(justPage?: any) {
    if (justPage) {
      this.isSelectAllReceived = false;
    }
    this.MatchedTransReceivedCheques.getValue().content.forEach(
      (element: any) => {
        this.selectedReceived[element.id] = true;
      }
    );
  }

  selectAllReceived() {
    if (this.isSelectAllReceived) {
      this.unSelectAllReceived();
      return;
    }
    this.isSelectAllReceived = true;
    this.selectPageReceived();
  }
  unSelectAllReceived() {
    this.isSelectAllReceived = false;
    this.selectedReceived = [];
  }

  getSelectedRowsCountReceived() {
    let len = 0;
    Object.keys(this.selectedReceived).forEach((element: any) => {
      if (this.selectedReceived[element]) {
        len++;
      }
    });
    return len;
  }
  getSelectedMatchedTransReceived() {
    let selected: any[] = [];
    Object.keys(this.selectedReceived).forEach((element: any) => {
      if (this.selectedReceived[element]) {
        selected.push({ id: element });
      }
    });
    return selected;
  }

  checkIfSelectedPageBounced() {
    let selected = true;
    this.MatchedTransBouncedCheques.getValue().content.forEach(
      (element: any) => {
        if (!this.selectedBounced[element.id]) {
          selected = false;
        }
      }
    );
    return selected;
  }
  unSelectPageBounced() {
    this.isSelectAllBounced = false;
    this.MatchedTransBouncedCheques.getValue().content.forEach(
      (element: any) => {
        this.selectedBounced[element.id] = false;
      }
    );
  }
  selectPageBounced(justPage?: any) {
    if (justPage) {
      this.isSelectAllBounced = false;
    }
    this.MatchedTransBouncedCheques.getValue().content.forEach(
      (element: any) => {
        this.selectedBounced[element.id] = true;
      }
    );
  }
  selectAllBounced() {
    if (this.isSelectAllBounced) {
      this.unSelectAllBounced();
      return;
    }
    this.isSelectAllBounced = true;
    this.selectPageBounced();
  }
  unSelectAllBounced() {
    this.isSelectAllBounced = false;
    this.selectedBounced = [];
  }
  getSelectedRowsCountBounced() {
    let len = 0;
    Object.keys(this.selectedBounced).forEach((element: any) => {
      if (this.selectedBounced[element]) {
        len++;
      }
    });
    return len;
  }
  getSelectedMatchedTransBounced() {
    let selected: any[] = [];
    Object.keys(this.selectedBounced).forEach((element: any) => {
      if (this.selectedBounced[element]) {
        selected.push({ id: element });
      }
    });
    return selected;
  }
  confirmBouncedCheques() {
    if (this.isSelectAllBounced) {
      this.uploadStatementService
        .confirmAllTransactions(
          this.route.snapshot.params['id'],
          'matchedTransactionBouncedCheques'
        )
        .subscribe({
          next: (response) => {
            this.notification.notifySuccess(response);
            this.getMatchedTransBouncedChequesData();
          },
        });
      return;
    }

    this.uploadStatementService
      .confirmTransactions(this.getSelectedMatchedTransBounced())
      .subscribe({
        next: (response) => {
          this.notification.notifySuccess(response);
          this.getMatchedTransBouncedChequesData();
        },
      });
  }
  confirmReceivedCheques() {
    if (this.isSelectAllReceived) {
      this.uploadStatementService
        .confirmAllTransactions(
          this.route.snapshot.params['id'],
          'matchedTransactionReceivedCheques'
        )
        .subscribe({
          next: (response) => {
            this.notification.notifySuccess(response);
            this.getMatchedTransReceivedChequesData();
          },
        });
      return;
    }
    console.log(this.getSelectedMatchedTransReceived());

    // this.uploadStatementService
    //   .confirmTransactions(this.getSelectedMatchedTransReceived())
    //   .subscribe({
    //     next: (response) => {
    //       this.notification.notifySuccess(response);
    //       this.getMatchedTransReceivedChequesData();
    //     },
    //   });
  }
  exportMatchedTransactionReceivedCheques() {
    let url = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/chequeDepositTransactionsCSV?gridType=matchedTransactionReceivedCheques`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }
  exportAlreadyMatchedTransactionReceivedCheques() {
    let url = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/chequeDepositTransactionsCSV?gridType=alreadyMatchedTransactionReceivedCheques`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }
  exportMatchedTransactionBouncedCheques() {
    let url = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/chequeDepositTransactionsCSV?gridType=matchedTransactionBouncedCheques`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }
  exportAlreadyMatchedTransactionBouncedCheques() {
    let url = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/chequeDepositTransactionsCSV?gridType=alreadyMatchedTransactionBouncedCheques`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }
  exportUnmatchedTransactionFromBankStatement() {
    let url = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/chequeDepositTransactionsCSV?gridType=unmatchedReceivedTransactionFromBankStatement`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }
  exportUnMatchedTransactionBouncedPayment() {
    let url = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/chequeDepositTransactionsCSV?gridType=unmatchedBouncedTransactionFromBankStatement`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }
  linkToPayment(element: any) {
    this.ccDialog
      .originalOpen(LinkToContractPaymentComponent, {
        data: {
          id: element.id,
          contract: element.contract,
        },
      })
      .afterClosed()
      .subscribe((res: any) => {
        if (res) {
          this.getAllData();
        }
      });
  }
}
