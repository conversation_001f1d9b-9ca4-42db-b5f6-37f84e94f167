<cc-dialog-header>
  <h1 cc-dialog-title>Confirm Expense</h1>
  <a
    role="button"
    type="button"
    cc-icon-button
    cc-dialog-close-button
    cc-dialog-close
  ></a
></cc-dialog-header>
<cc-dialog-content>
  <h5>Expense Details</h5>
  <cc-input
    [(ngModel)]="data.transaction.expense.label"
    label="Expense"
    [disabled]="true"
  ></cc-input>
  <cc-input
    [(ngModel)]="data.expense.amount"
    label="Amount"
    [disabled]="true"
  ></cc-input>
  <cc-input
    [(ngModel)]="data.expense.date"
    label="Date"
    [disabled]="true"
  ></cc-input>
  <cc-input
    [(ngModel)]="data.expense.requester"
    label="Requester"
    [disabled]="true"
  ></cc-input>
  <cc-input
    [(ngModel)]="data.expense.description"
    label="Description"
    [disabled]="true"
  ></cc-input>
  <cc-input
    [(ngModel)]="data.expense.notes"
    label="Notes"
    [disabled]="true"
  ></cc-input>
  <cc-input
    [(ngModel)]="data.expense.invoiceNumber"
    label="Invoice Number"
    [disabled]="true"
  ></cc-input>
  <div class="row aling-items-center">
    <label class="col-4">Invoice File</label>
    <div *ngIf="data.expense.invoiceFile && data.expense.invoiceFile.uuid" class="col-8">
      <a class="cc-secondary" (click)="downloadFile(data.expense.invoiceFile.uuid)">{{
        data.expense.invoiceFile.name
      }}</a>
    </div>
  </div>
  <div class="row aling-items-center">
    <label class="col-4">Vat Invoice File</label>
    <div *ngIf="data.expense.vatInvoiceFile && data.expense.vatInvoiceFile.uuid" class="col-8">
      <a class="cc-secondary" (click)="downloadFile(data.expense.vatInvoiceFile.uuid)">{{
        data.expense.vatInvoiceFile.name
      }}</a>
    </div>
  </div>
  <cc-input
    [(ngModel)]="data.expense.beneficiary"
    label="Beneficiary"
    [disabled]="true"
  ></cc-input>
  <cc-input
    [(ngModel)]="data.expense.relatedTo"
    label="Related To"
    [disabled]="true"
  ></cc-input>
  <h5>Transaction Details</h5>
  <cc-input
    [(ngModel)]="data.transaction.fromBucket"
    label="From Bucket"
    [disabled]="true"
  ></cc-input>
  <cc-input
    [(ngModel)]="data.transaction.expense.label"
    label="Expense"
    [disabled]="true"
  ></cc-input>
  <cc-input
    [(ngModel)]="data.transaction.license.label"
    label="License"
    [disabled]="true"
  ></cc-input>
  <cc-input
    [(ngModel)]="data.transaction.transactionAmount"
    label="Amount"
    [disabled]="true"
  ></cc-input>
  <cc-input
    [(ngModel)]="data.transaction.vatType"
    label="Vat Type"
    [disabled]="true"
  ></cc-input>
  <cc-input
    [(ngModel)]="data.transaction.vatAmount"
    label="Vat Amount"
    [disabled]="true"
  ></cc-input>
  <cc-input
    [(ngModel)]="data.transaction.date"
    label="Date"
    [disabled]="true"
  ></cc-input>
  <cc-textarea
    [(ngModel)]="data.transaction.description"
    label="Description"
  ></cc-textarea>
  <cc-input
    [(ngModel)]="data.transaction.expensePaymentType.label"
    label="Payment Type"
    [disabled]="true"
  ></cc-input>
  <div class="row align-items-center">
    <label class="col-4">No Vat Attachment</label>
    <div class="col-8">
      <a
        class="cc-secondary"
        *ngFor="let item of data.transaction.noVatAttachments"
        (click)="downloadFile(item.uuid)"
        >{{ item.name }}</a
      >
    </div>
  </div>
  <hr />
  <div class="row align-items-center">
    <label class="col-4">Vat Attachment</label>
    <div class="col-8">
      <a
        class="cc-secondary"
        *ngFor="let item of data.transaction.vatAttachments"
        (click)="downloadFile(item.uuid)"
        >{{ item.name }}</a
      >
    </div>
  </div>
  <hr />
  <div class="row align-items-center">
    <label class="col-4">Missing Tax Invoice</label>
    <div class="col-8">
      {{ data.transaction.missingVatInvoice ? "Yes" : "No" }}
    </div>
  </div>
</cc-dialog-content>
<cc-dialog-actions>
  <button cc-raised-button cc-dialog-close>Do Nothing</button>
  <button cc-raised-button (click)="confirm()" color="accent">
    Confirm Expense
  </button>
</cc-dialog-actions>
