<cc-dialog-header>
  <h1 cc-dialog-title>Confirm Replenishment</h1>
  <a
    role="button"
    type="button"
    cc-icon-button
    cc-dialog-close-button
    cc-dialog-close
  ></a>
</cc-dialog-header>

<cc-dialog-content>
  <h5>Replenishment Details</h5>
  <cc-input
    [(ngModel)]="data.replenishment.amount"
    label="Amount"
    [disabled]="true"
  ></cc-input>
  <cc-input [(ngModel)]="data.date" label="Date" [disabled]="true"></cc-input>
  <h5>Post to Accounting</h5>
  <cc-input
    [(ngModel)]="data.transaction.fromBucket"
    label="From Bucket"
    [disabled]="true"
  ></cc-input>
  <cc-input
    [(ngModel)]="data.transaction.toBucket"
    label="To Bucket"
    [disabled]="true"
  ></cc-input>
  <cc-input
    [(ngModel)]="data.transaction.license.label"
    label="License"
    [disabled]="true"
  ></cc-input>
  <cc-input
    [(ngModel)]="data.transaction.transactionAmount"
    label="Amount"
    [disabled]="true"
  ></cc-input>
  <cc-input
    [(ngModel)]="data.transaction.date"
    label="Date"
    [disabled]="true"
  ></cc-input>
  <cc-textarea
    [(ngModel)]="data.transaction.description"
    label="Description"
    [disabled]="true"
  ></cc-textarea>
  <cc-input
    [(ngModel)]="data.transaction.expensePaymentType.label"
    label="Type of Payment"
    [disabled]="true"
  ></cc-input>
</cc-dialog-content>

<cc-dialog-actions>
  <button cc-raised-button cc-dialog-close>Do Nothing</button>
  <button cc-raised-button (click)="confirm()" color="accent">
    Confirm Replenishment
  </button>
</cc-dialog-actions>
