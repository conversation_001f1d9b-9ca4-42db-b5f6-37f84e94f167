import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { CCDialogRef } from '@maids/cc-lib/dialog';
import { UploadStatementService } from '../../services/upload-statement.service';
import { HttpClient } from '@angular/common/http';
import { CCBackendEndpoint } from '@maids/cc-erp-services';
import { API } from 'src/environments/api';
@Component({
  selector: 'app-confirm-replenishment',
  templateUrl: './confirm-replenishment.component.html',
  styleUrls: ['./confirm-replenishment.component.scss'],
})
export class ConfirmReplenishmentComponent implements OnInit {
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private uploadStatementService: UploadStatementService,
    private ccDialogRef: CCDialogRef<ConfirmReplenishmentComponent>,
    private http: HttpClient,
    @Inject(CCBackendEndpoint) private _api: string
  ) {}

  ngOnInit(): void {}
  confirm() {
    this.http
      .post(`${this._api}/${API.confirmTransactions}`, [
        {
          id: this.data.id,
          description: this.data.replenishment.description,
        },
      ])
      .subscribe((res) => {
        this.ccDialogRef.close(true);
      });
  }
}
