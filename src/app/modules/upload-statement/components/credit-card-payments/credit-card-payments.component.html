<div class="row my-2" *ngIf="creditCardAmountData$ | async as creditCardAmountData">
  <div class="col-md-12">
    <span style="font-weight: 500"
      >Total Pos Settlement Amount From Bank Statement: AED
      {{ creditCardAmountData.totalPosSettlementAmountFromBankStatement | number  }}</span
    >
  </div>
  <div class="col-md-6">
    <span style="font-weight: 500"
      >Total Tadbeer transactions count:
      {{ creditCardAmountData.tadbeerTransactionCount | number }}</span
    >
  </div>
  <div class="col-md-6">
    <span style="font-weight: 500"
      >Total Tadbeer transactions Amount: AED
      {{ creditCardAmountData.tadbeerTransactionAmount | number }}</span
    >
  </div>
  <div class="col-md-6">
    <span style="font-weight: 500"
      >Total maids.cc/maids.visa transactions count :
      {{
        creditCardAmountData.totalMaidsTransactionsCountFromCreditCardStatement | number
      }}</span
    >
  </div>
  <div class="col-md-6">
    <span style="font-weight: 500"
      >Total maids.cc/maids.visa transactions amount: AED
      {{
        creditCardAmountData.totalMaidsTransactionsAmountFromCreditCardStatement | number
      }}</span
    >
  </div>
</div>
<cc-accordion>
  <cc-panel>
    <cc-panel-title> <b> Credit Card Commision</b></cc-panel-title>
    <cc-panel-body>
      <cc-datagrid
        *ngIf="creditCardCommissionData$ | async as records"
        [data]="records"
        [columns]="commisionCols"
        [pageOnFront]="false"
        [stickyHeader]="true"
        [showPaginator]="false"
        [showColumnMenuButton]="true"
        [showColumnMenuHeader]="false"
        [columnMenuButtonIcon]="'settings'"
        [columnMovable]="true"
      ></cc-datagrid>
    </cc-panel-body>
  </cc-panel>
</cc-accordion>
<cc-accordion>
  <cc-panel>
    <cc-panel-title><b>Tadbeer Transactions</b></cc-panel-title>
    <cc-panel-body>
      <div class="d-flex mb-2 justify-content-between">
        <div class="col-md-auto px-0">
          <span style="font-weight: 500"
            class="pr-1"
            >Total Amount:
            <span class="cc-secondary"> AED {{ totalAmountTadbeer | number }}</span></span
          >
        </div>
        <div class="col-md-auto px-0">
          <button
            cc-raised-button
            (click)="exportTadbeerTransactions()"
            color="accent"
          >
            Export
          </button>
        </div>
      </div>
      <cc-datagrid
        *ngIf="TadbeerData$ | async as records"
        [data]="records.content"
        [columns]="tadbeerCols"
        [length]="records.totalElements"
        [pageOnFront]="false"
        [pageIndex]="records.number"
        [pageSize]="records.size"
        [pageSizeOptions]="[10, 25, 50]"
        (page)="getNextTadbeerData($event)"
        [stickyHeader]="true"
        [showColumnMenuButton]="true"
        [showColumnMenuHeader]="false"
        [columnMenuButtonIcon]="'settings'"
        [columnMovable]="true"
      ></cc-datagrid>
      <div class="d-flex justify-content-between my-2">
        <div class="col-md-auto d-flex px-0">
          <div class="col-md-auto px-0">
            <button cc-raised-button (click)="addTransaction()">
              Add Transaction
            </button>
          </div>
          <div class="col-md-auto ">
            <button
              cc-raised-button
              color="accent"
              (click)="confirmTransactions()"
            >
              Confirm and Save Transactions
            </button>
          </div>
        </div>
        <div class="col-md-auto px-0">
          <button
            cc-raised-button
            color="accent"
            (click)="exportAddedTadbeerTransactions()"
          >
            Export
          </button>
        </div>
      </div>
      <cc-datagrid
        *ngIf="tadbeerTransactions$ | async as tadbeerTransactions"
        [data]="tadbeerTransactions"
        [columns]="tadbeerTrsCols"
        [pageOnFront]="false"
        [stickyHeader]="true"
        [showPaginator]="false"
        [showColumnMenuButton]="true"
        [showColumnMenuHeader]="false"
        [columnMenuButtonIcon]="'settings'"
        [columnMovable]="true"
        [cellTemplate]="{ transactionID: transactionID }"
        [noResultTemplate]="noResult"
      ></cc-datagrid>
      <ng-template #transactionID let-row let-index="index" let-col="colDef">
        <a
          *ngIf="row.saved"
          target="_blank"
          [href]="'#!/accounting/add-edit-transactions/' + row.transactionID"
          >trans-{{ row.transactionID }}</a
        >
      </ng-template>
      <ng-template #noResult> No records found </ng-template>
    </cc-panel-body>
  </cc-panel>
</cc-accordion>
