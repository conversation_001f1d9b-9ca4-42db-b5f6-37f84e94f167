import { Component, Inject, OnInit } from '@angular/core';
import { UploadStatementService } from '../../services/upload-statement.service';
import { ActivatedRoute } from '@angular/router';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { HttpClient } from '@angular/common/http';
import { CCBackendEndpoint } from '@maids/cc-erp-services';
import { BehaviorSubject, Observable, tap } from 'rxjs';
import { PageableResponseModel } from '@maids/cc-lib/common';
import { PageEvent } from '@angular/material/paginator';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import * as moment from 'moment';
import { CCDialog } from '@maids/cc-lib/dialog';
import { AddTransactionComponent } from '../add-transaction/add-transaction.component';
@Component({
  selector: 'app-credit-card-payments',
  templateUrl: './credit-card-payments.component.html',
  styleUrls: ['./credit-card-payments.component.scss'],
})
export class CreditCardPaymentsComponent implements OnInit {
  constructor(
    private uploadStatementService: UploadStatementService,
    private route: ActivatedRoute,
    private http: HttpClient,
    @Inject(CCBackendEndpoint) private _api: string,
    private mediaService: MediaService,
    public readonly notifications: CCNotificationService,
    private ccDialog: CCDialog
  ) {}
  totalAmountTadbeer: number = 0;
  currentPageTadbeer: number = 0;
  sizePageTadbeer: number = 50;
  totalAdded: number = 0;
  minDateOfTadbeerTransaction: any;
  TadbeerData = new BehaviorSubject<PageableResponseModel<any>>({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  TadbeerData$: Observable<PageableResponseModel<any>> =
    this.TadbeerData.asObservable();
  creditCardCommissionData = new BehaviorSubject<any>(null);
  creditCardCommissionData$: Observable<any> =
    this.creditCardCommissionData.asObservable();
  creditCardAmountData = new BehaviorSubject<any>(null);
  creditCardAmountData$: Observable<any> =
    this.creditCardAmountData.asObservable();
  tadbeerPreTransactions: any[] = [];
  tadbeerTransactions = new BehaviorSubject<any[]>([]);
  tadbeerTransactions$: Observable<any[]> =
    this.tadbeerTransactions.asObservable();
  tadbeerNewTransactions: any[] = [];
  commisionCols: CCGridColumn[] = [
    { field: 'totalTransactions', header: 'Total Transactions' },
    { field: 'totalAmount', header: 'Total Transaction Amount' },
    { field: 'totalCommission', header: 'Total Commission' },
    { field: 'totalVatOnCommission', header: 'Total Vat on Commission' },
    { field: 'commissionPerTransaction', header: 'Commission per Transaction' },
  ];
  tadbeerCols: CCGridColumn[] = [
    { field: 'rowNum', header: 'Row Number' },
    {
      field: 'transactionDate',
      header: 'Transaction Date',
      formatter(rowData, colDef) {
        return moment(rowData.transactionDate).format('YYYY-MM-DD');
      },
    },
    { field: 'terminalId', header: 'Terminal ID' },
    { field: 'sequenceNumber', header: 'Sequence Number' },
    { field: 'authCode', header: 'Auth Code' },
    {
      field: 'salesAmount',
      header: 'Amount',
      formatter(rowData, colDef) {
        return rowData.salesAmount ? rowData.salesAmount.toFixed(2) : 0;
      },
    },
  ];
  tadbeerTrsCols: CCGridColumn[] = [
    {
      field: 'amount',
      header: 'Amount',
      formatter(rowData, colDef) {
        return rowData.amount ? rowData.amount.toFixed(2) : 0;
      },
    },
    { field: 'revenue.code', header: 'Revenue Code' },
    { field: 'vatAmount', header: 'Vat Amount' },
    { field: 'vatType', header: 'Vat Type' },
    { field: 'description', header: 'Description' },
    { field: 'transactionID', header: 'Transaction' },
  ];
  ngOnInit(): void {
    this.getAllData();
  }
  getAllData() {
    this.getTadbeerData();
    this.getCreditCardCommissionData();
    this.getCreditCardAmountData();
    this.getPreTadbeerTrs();
  }
  getTadbeerData(page: number = 0, size: number = 50) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/creditCardTransactions`,
        {
          params: { page, size, gridType: 'tadbeerTransactions' },
        }
      )
      .subscribe((res: any) => {
        this.totalAmountTadbeer = res.totalSum;
        this.TadbeerData.next(res);
      });
  }
  getNextTadbeerData(event: PageEvent) {
    this.getTadbeerData(event.pageIndex, event.pageSize);
    this.currentPageTadbeer = event.pageIndex;
    this.sizePageTadbeer = event.pageSize;
  }
  getCreditCardCommissionData() {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/creditCardCommission`
      )
      .subscribe((res: any) => {
        this.creditCardCommissionData.next([res]);
      });
  }
  getCreditCardAmountData() {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/creditCardAmount`
      )
      .subscribe((res: any) => {
        this.creditCardAmountData.next(res);
        if (res.minDateOfTadbeerTransaction) {
          this.minDateOfTadbeerTransaction = moment(
            res.minDateOfTadbeerTransaction
          ).format('YYYY-MM-DD');
        } else {
          this.minDateOfTadbeerTransaction = moment(new Date()).format(
            'YYYY-MM-DD'
          );
        }
      });
  }
  getPreTadbeerTrs() {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/getAddedTadbeerTransactions`
      )
      .pipe(
        tap((res: any) => {
          res.forEach((element: any) => {
            element.saved = true;
            element.transactionID = element.id;
            this.tadbeerPreTransactions.push(element);
            this.totalAdded = this.totalAdded + element.amount;
          });
          this.tadbeerTransactions.next(
            this.tadbeerTransactions
              .getValue()
              .concat(this.tadbeerPreTransactions)
          );
        })
      )
      .subscribe();
  }
  confirmTransactions(): void {
    let trans: any[] = this.tadbeerNewTransactions.map((item: any) => {
      if (!item.saved) {
        return { id: item };
      }
      return;
    });
    if (trans.length == 0) {
      this.notifications.notifyError('All transactions saved');
      return;
    }
    this.http
      .post(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/confirmAndSaveTransactions`,
        trans
      )
      .subscribe((res: any) => {
        this.notifications.notifySuccess(res);
        let newlyItemIndex: number = 0;
        this.tadbeerNewTransactions.forEach((element: any, index: number) => {
          if (!element.saved) {
            this.tadbeerNewTransactions[index].transactionID =
              res[newlyItemIndex++];
            this.tadbeerNewTransactions[index].saved = true;
          }
        });
      });
  }
  exportStorageTransactions() {
    let url = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/creditCardTransactionsCSV?gridType=storageTransactions`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }
  exportTadbeerTransactions() {
    let url = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/creditCardTransactionsCSV?gridType=tadbeerTransactions`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }
  exportAddedTadbeerTransactions() {
    let url = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/getAddedTadbeerTransactionsCSV`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }
  addTransaction() {
    this.ccDialog
      .originalOpen(AddTransactionComponent, {
        data: {
          totalAdded: this.totalAdded,
          minDateOfTadbeerTransaction: this.minDateOfTadbeerTransaction,
          totalAmountTadbeer: this.totalAmountTadbeer,
        },
      })
      .afterClosed()
      .subscribe((data: any) => {
        if (data) {
          let currentTadbeerTransactions = this.tadbeerTransactions.getValue();
          currentTadbeerTransactions.push(data);
          this.tadbeerNewTransactions.push(data);
          this.tadbeerTransactions.next([...currentTadbeerTransactions]);
        }
      });
  }
}
