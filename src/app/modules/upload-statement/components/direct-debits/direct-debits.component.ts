import { Component, Inject, Input, OnInit } from '@angular/core';
import { UploadStatementService } from '../../services/upload-statement.service';
import { HttpClient } from '@angular/common/http';
import { CCBackendEndpoint } from '@maids/cc-erp-services';
import { BehaviorSubject, Observable } from 'rxjs';
import { PageableResponseModel } from '@maids/cc-lib/common';
import { PageEvent } from '@angular/material/paginator';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import * as moment from 'moment';
import { ActivatedRoute } from '@angular/router';
import { API } from 'src/environments/api';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { CCDialog } from '@maids/cc-lib/dialog';
import { LinkToContractPaymentComponent } from '../link-to-contract-payment/link-to-contract-payment.component';
import { LinkToPaymentComponent } from '../link-to-payment/link-to-payment.component';
import { AddNoteComponent } from '../add-note/add-note.component';

@Component({
  selector: 'app-direct-debits',
  templateUrl: './direct-debits.component.html',
  styleUrls: ['./direct-debits.component.scss'],
})
export class DirectDebitsComponent implements OnInit {
  recordCountMatchedTransReceivedPayments: number = 0;
  totalAmountMatchedTransReceivedPayments: number = 0;
  totalMatchedTransReceived: number = 0;
  recordCountAlreadyMatchedTransReceivedPayments: number = 0;
  totalAmountAlreadyMatchedTransReceivedPayments: number = 0;
  recordCountUnmatchedFromERP: number = 0;
  totalAmountUnmatchedFromERP: number = 0;
  recordCountUnmatchedFromBankStatement: number = 0;
  totalAmountUnmatchedFromBankStatement: number = 0;
  recordCountMatchedTransBouncedPayments: number = 0;
  totalMatchedTransBounced: number = 0;
  totalAmountMatchedTransBouncedPayments: number = 0;
  recordCountAlreadyMatchedTransBouncedPayments: number = 0;
  totalAmountAlreadyMatchedTransBouncedPayments: number = 0;
  recordCountUnmatchedTransactionsBouncedPayments: number = 0;
  totalAmountUnmatchedTransactionsBouncedPayments: number = 0;
  currentPageTransReceived: number = 0;
  sizePageTransReceived: number = 50;
  currentPageTransAlreadyReceived: number = 0;
  sizePageTransAlreadyReceived: number = 50;
  currentPageTransBounced: number = 0;
  sizePageTransBounced: number = 50;
  currentPageTransAlreadyBounced: number = 0;
  sizePageTransAlreadyBounced: number = 50;
  currentPageUnmatchedFromBank: number = 0;
  sizePageUnmatchedFromBank: number = 50;
  currentPageUnmatchedBouncedFromBank: number = 0;
  sizePageUnmatchedBouncedFromBank: number = 50;
  currentPageUnmatchedFromERP: number = 0;
  sizePageUnmatchedFromERP: number = 50;
  isSelectAllReceived: boolean = false;
  isSelectAllBounced: boolean = false;
  summary: any;
  selectedReceived: any[] = [];
  selectedBounced: any[] = [];
  private MatchedTransReceivedPayments = new BehaviorSubject<
    PageableResponseModel<any>
  >({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  MatchedTransReceivedPayments$: Observable<PageableResponseModel<any>> =
    this.MatchedTransReceivedPayments.asObservable();
  private MatchedTransAlreadyReceivedPayments = new BehaviorSubject<
    PageableResponseModel<any>
  >({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  MatchedTransAlreadyReceivedPayments$: Observable<PageableResponseModel<any>> =
    this.MatchedTransAlreadyReceivedPayments.asObservable();
  private MatchedTransBouncedPayments = new BehaviorSubject<
    PageableResponseModel<any>
  >({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  MatchedTransBouncedPayments$: Observable<PageableResponseModel<any>> =
    this.MatchedTransBouncedPayments.asObservable();
  private MatchedTransAlreadyBouncedPayments = new BehaviorSubject<
    PageableResponseModel<any>
  >({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  MatchedTransAlreadyBouncedPayments$: Observable<PageableResponseModel<any>> =
    this.MatchedTransAlreadyBouncedPayments.asObservable();
  private UnmatchedFromBank = new BehaviorSubject<PageableResponseModel<any>>({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  UnmatchedFromBank$: Observable<PageableResponseModel<any>> =
    this.UnmatchedFromBank.asObservable();
  private UnmatchedBouncedFromBank = new BehaviorSubject<
    PageableResponseModel<any>
  >({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  UnmatchedBouncedFromBank$: Observable<PageableResponseModel<any>> =
    this.UnmatchedBouncedFromBank.asObservable();
  private UnmatchedFromERP = new BehaviorSubject<PageableResponseModel<any>>({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  UnmatchedFromERP$: Observable<PageableResponseModel<any>> =
    this.UnmatchedFromERP.asObservable();

  constructor(
    private uploadStatementService: UploadStatementService,
    private http: HttpClient,
    @Inject(CCBackendEndpoint) private _api: string,
    private route: ActivatedRoute,
    public readonly notifications: CCNotificationService,
    private mediaService: MediaService,
    private ccDialog: CCDialog
  ) {}

  ngOnInit(): void {
    this.uploadStatementService.summaryObject.subscribe((res) => {
      this.summary = res;
    });
    this.getAllData();
  }
  getAllData() {
    this.getMatchedTransReceivedPaymentsData();
    this.getMatchedTransAlreadyReceivedPaymentsData();
    this.getMatchedTransBouncedPaymentsData();
    this.getUnmatchedFromBankData();
    this.getMatchedTransAlreadyBouncedPaymentsData();
    this.getUnmatchedBouncedFromBankData();
    this.getUnmatchedFromERPData();
  }
  getMatchedTransReceivedPaymentsData(page: number = 0, size: number = 50) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/directDebitTransactions`,
        {
          params: { gridType: 'matchedTransactionReceivedPayment', page, size },
        }
      )
      .subscribe((res: any) => {
        this.MatchedTransReceivedPayments.next(res);
        this.recordCountMatchedTransReceivedPayments = res.totalElements;
        this.totalAmountMatchedTransReceivedPayments = res.totalSum;
        this.totalMatchedTransReceived = res.total;
        if (this.isSelectAllReceived) {
          this.selectPageReceived();
        }
      });
  }

  getMTRPNext(event: PageEvent) {
    this.getMatchedTransReceivedPaymentsData(event.pageIndex, event.pageSize);
    this.currentPageTransReceived = event.pageIndex;
    this.sizePageTransReceived = event.pageSize;
  }

  getMatchedTransAlreadyReceivedPaymentsData(
    page: number = 0,
    size: number = 50
  ) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/directDebitTransactions`,
        {
          params: {
            gridType: 'alreadyMatchedTransactionReceivedPayment',
            page,
            size,
          },
        }
      )
      .subscribe((res: any) => {
        this.MatchedTransAlreadyReceivedPayments.next(res);
        this.recordCountAlreadyMatchedTransReceivedPayments = res.totalElements;
        this.totalAmountAlreadyMatchedTransReceivedPayments = res.totalSum;
      });
  }

  getMTARPNext(event: PageEvent) {
    this.getMatchedTransAlreadyReceivedPaymentsData(
      event.pageIndex,
      event.pageSize
    );
    this.currentPageTransAlreadyReceived = event.pageIndex;
    this.sizePageTransAlreadyReceived = event.pageSize;
  }

  getMatchedTransBouncedPaymentsData(page: number = 0, size: number = 50) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/directDebitTransactions`,
        {
          params: { gridType: 'matchedTransactionBouncedPayment', page, size },
        }
      )
      .subscribe((res: any) => {
        this.MatchedTransBouncedPayments.next(res);
        this.recordCountMatchedTransBouncedPayments = res.totalElements;
        this.totalMatchedTransBounced = res.total;
        this.totalAmountMatchedTransBouncedPayments = res.totalSum;
        if (this.isSelectAllBounced) {
          this.selectPageBounced();
        }
      });
  }

  getMTBPNext(event: PageEvent) {
    this.getMatchedTransBouncedPaymentsData(event.pageIndex, event.pageSize);
    this.currentPageTransBounced = event.pageIndex;
    this.sizePageTransBounced = event.pageSize;
  }

  getMatchedTransAlreadyBouncedPaymentsData(
    page: number = 0,
    size: number = 50
  ) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/directDebitTransactions`,
        {
          params: {
            gridType: 'alreadyMatchedTransactionBouncedPayment',
            page,
            size,
          },
        }
      )
      .subscribe((res: any) => {
        this.MatchedTransAlreadyBouncedPayments.next(res);
        this.recordCountAlreadyMatchedTransBouncedPayments = res.totalElements;
        this.totalAmountAlreadyMatchedTransBouncedPayments = res.totalSum;
      });
  }

  getMTABPNext(event: PageEvent) {
    this.getMatchedTransAlreadyBouncedPaymentsData(
      event.pageIndex,
      event.pageSize
    );
    this.currentPageTransAlreadyBounced = event.pageIndex;
    this.sizePageTransAlreadyBounced = event.pageSize;
  }

  getUnmatchedFromBankData(page: number = 0, size: number = 50) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/directDebitTransactions`,
        {
          params: {
            gridType: 'unmatchedReceivedTransactionFromBankStatement',
            page,
            size,
          },
        }
      )
      .subscribe((res: any) => {
        this.UnmatchedFromBank.next(res);
        this.recordCountUnmatchedFromBankStatement = res.totalElements;
        this.totalAmountUnmatchedFromBankStatement = res.totalSum;
      });
  }

  getUFBNext(event: PageEvent) {
    this.getUnmatchedFromBankData(event.pageIndex, event.pageSize);
    this.currentPageUnmatchedFromBank = event.pageIndex;
    this.sizePageUnmatchedFromBank = event.pageSize;
  }

  getUnmatchedBouncedFromBankData(page: number = 0, size: number = 50) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/directDebitTransactions`,
        {
          params: {
            gridType: 'unmatchedBouncedTransactionFromBankStatement',
            page,
            size,
          },
        }
      )
      .subscribe((res: any) => {
        this.UnmatchedBouncedFromBank.next(res);
        this.recordCountUnmatchedTransactionsBouncedPayments =
          res.totalElements;
        this.totalAmountUnmatchedTransactionsBouncedPayments = res.totalSum;
      });
  }

  getUBFBNext(event: PageEvent) {
    this.getUnmatchedBouncedFromBankData(event.pageIndex, event.pageSize);
    this.currentPageUnmatchedBouncedFromBank = event.pageIndex;
    this.sizePageUnmatchedBouncedFromBank = event.pageSize;
  }

  getUnmatchedFromERPData(page: number = 0, size: number = 50) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/unMatchedDirectDebitTransactionsFromErp`,
        {
          params: {
            page,
            size,
          },
        }
      )
      .subscribe((res: any) => {
        this.UnmatchedFromERP.next(res);
        this.recordCountUnmatchedFromERP = res.totalElements;
        this.totalAmountUnmatchedFromERP = res.totalSum;
      });
  }

  getUFENext(event: PageEvent) {
    this.getUnmatchedFromERPData(event.pageIndex, event.pageSize);
    this.currentPageUnmatchedFromERP = event.pageIndex;
    this.sizePageUnmatchedFromERP = event.pageSize;
  }

  MTRPCols: CCGridColumn[] = [
    { field: 'select', header: 'Select' },
    {
      field: 'rowNum',
      header: 'Row Number',
    },
    {
      field: 'transaction',
      header: 'Transaction Date',
      formatter: (rowData: any) => {
        let td = moment(rowData.date).format('YYYY-MM-DD');
        if (
          rowData.linkedTransactionIds &&
          rowData.linkedTransactionIds.length > 0
        ) {
          return `<a class="cc-secondary" (click)="openLinkedTransactionsFor(${rowData.id})" >${td}</a>`;
        }
        return rowData.transaction.id
          ? `<a class="cc-secondary" href="#!/accounting/add-edit-transactions/${rowData.transaction.id}">${td}</a>`
          : td;
      },
    },
    {
      field: 'transactionAmount',
      header: 'Amount',
      formatter(rowData, colDef) {
        return rowData.transactionAmount ? rowData.transactionAmount.toFixed(2) : 0;
      },
    },
    {
      field: 'applicationId',
      header: 'DD Application',
      formatter: (rowData: any) => {
        if (rowData.directDebitFile.id) {
          return (
            '<a class="cc-secondary" href="#!/accounting/contract-payments-files/' +
            rowData.contract.id +
            '">' +
            rowData.applicationId +
            '</a>'
          );
        }
        return rowData.applicationId;
      },
    },
    {
      field: 'client',
      header: 'Family Name',
      formatter: (rowData: any) => {
        if (rowData.contract.client) {
          return (
            '<a class="cc-secondary" href="#!/client/details/' +
            rowData.contract.client.id +
            '">' +
            rowData.contract.client.name +
            '</a>'
          );
        } else return rowData.client;
      },
    },
    {
      field: 'revenue',
      header: 'Revenue',
      formatter: (rowData: any) => {
        return rowData.revenue ? rowData.revenue.code : '';
      },
    },
    {
      field: 'paymentStatus',
      header: 'Payment Status',
      formatter: (rowData: any) => {
        return rowData.paymentStatus ? rowData.paymentStatus.label : '';
      },
    },
    { field: 'description', header: 'Transaction Description' },
    { field: 'bankTransactionMatchType', header: 'Matched Type' },
    { field: 'paymentDetail', header: 'Payment Details' },
  ];
  MTARPCols: CCGridColumn[] = [
    {
      field: 'rowNum',
      header: 'Row Number',
    },
    {
      field: 'transaction',
      header: 'Transaction Date',
      formatter: (rowData: any) => {
        let td = moment(rowData.date).format('YYYY-MM-DD');
        return rowData.transaction.id
          ? `<a class="cc-secondary" href="#!/accounting/add-edit-transactions/${rowData.transaction.id}">${td}</a>`
          : td;
      },
    },
    { field: 'transactionAmount', header: 'Amount' },
    {
      field: 'applicationId',
      header: 'DD Application',
      formatter: (rowData: any) => {
        if (rowData.directDebitFile.id) {
          return (
            '<a class="cc-secondary" href="#!/accounting/contract-payments-files/' +
            rowData.contract.id +
            '">' +
            rowData.applicationId +
            '</a>'
          );
        }
        return rowData.applicationId;
      },
    },
    {
      field: 'client',
      header: 'Family Name',
      formatter: (rowData: any) => {
        if (rowData.contract.client) {
          return (
            '<a class="cc-secondary" href="#!/client/details/' +
            rowData.contract.client.id +
            '">' +
            rowData.contract.client.name +
            '</a>'
          );
        } else return rowData.client;
      },
    },
    {
      field: 'revenue',
      header: 'Revenue',
      formatter: (rowData: any) => {
        return rowData.revenue ? rowData.revenue.code : '';
      },
    },
    {
      field: 'paymentStatus',
      header: 'Payment Status',
      formatter: (rowData: any) => {
        return rowData.paymentStatus ? rowData.paymentStatus.label : '';
      },
    },
    { field: 'description', header: 'Transaction Description' },
    { field: 'paymentDetail', header: 'Payment Details' },
  ];
  MTBPCols: CCGridColumn[] = [
    { field: 'select', header: 'Select' },
    {
      field: 'rowNum',
      header: 'Row Number',
    },
    {
      field: 'transaction',
      header: 'Transaction Date',
      formatter: (rowData: any) => {
        let td = moment(rowData.date).format('YYYY-MM-DD');
        return rowData.transaction.id
          ? `<a class="cc-secondary" href="#!/accounting/add-edit-transactions/${rowData.transaction.id}">${td}</a>`
          : td;
      },
    },
    { field: 'transactionAmount', header: 'Transaction Amount' },
    {
      field: 'applicationId',
      header: 'DD Application',
      formatter: (rowData: any) => {
        if (rowData.directDebitFile.id) {
          return (
            '<a class="cc-secondary" href="#!/accounting/contract-payments-files/' +
            rowData.contract.id +
            '">' +
            rowData.applicationId +
            '</a>'
          );
        }
        return rowData.applicationId;
      },
    },
    { field: 'paymentDetail', header: 'Payment Details' },
    {
      field: 'bouncedPaymentId',
      header: 'Bounced Payment Id',
      formatter: (rowData: any, colDef: any) => {
        if (rowData.contract.client && rowData.bouncedPaymentId) {
          return (
            '<a class="cc-secondary" href="#!/client/payments/edit/' +
            rowData.contract.client.id +
            '/' +
            rowData.contract.id +
            '/' +
            rowData.bouncedPaymentId +
            '">Payment-' +
            rowData.bouncedPaymentId +
            '</a>'
          );
        } else return rowData.applicationId;
      },
    },
    {
      field: 'paymentStatus',
      header: 'Payment Status',
      formatter(rowData, colDef) {
        return rowData.paymentStatus ? rowData.paymentStatus.label : '';
      },
    },
    {
      field: 'client',
      header: 'Family Name',
      formatter: (rowData: any) => {
        if (rowData.contract.client) {
          return (
            '<a class="cc-secondary" href="#!/client/details/' +
            rowData.contract.client.id +
            '">' +
            rowData.contract.client.name +
            '</a>'
          );
        } else return rowData.client;
      },
    },
  ];
  MTABPCols: CCGridColumn[] = [
    {
      field: 'rowNum',
      header: 'Row Number',
    },
    {
      field: 'transaction',
      header: 'Transaction Date',
      formatter: (rowData: any) => {
        let td = moment(rowData.date).format('YYYY-MM-DD');
        return rowData.transaction.id
          ? `<a class="cc-secondary" href="#!/accounting/add-edit-transactions/${rowData.transaction.id}">${td}</a>`
          : td;
      },
    },
    { field: 'transactionAmount', header: 'Transaction Amount' },
    {
      field: 'applicationId',
      header: 'DD Application',
      formatter: (rowData: any) => {
        if (rowData.directDebitFile.id) {
          return (
            '<a class="cc-secondary" href="#!/accounting/contract-payments-files/' +
            rowData.contract.id +
            '">' +
            rowData.applicationId +
            '</a>'
          );
        }
        return rowData.applicationId;
      },
    },
    { field: 'paymentDetail', header: 'Payment Details' },
    {
      field: 'bouncedPaymentId',
      header: 'Bounced Payment Id',
      formatter: (rowData: any, colDef: any) => {
        if (rowData.contract.client && rowData.bouncedPaymentId) {
          return (
            '<a class="cc-secondary" href="#!/client/payments/edit/' +
            rowData.contract.client.id +
            '/' +
            rowData.contract.id +
            '/' +
            rowData.bouncedPaymentId +
            '">Payment-' +
            rowData.bouncedPaymentId +
            '</a>'
          );
        } else return rowData.applicationId;
      },
    },
    {
      field: 'client',
      header: 'Family Name',
      formatter: (rowData: any) => {
        if (rowData.contract.client) {
          return (
            '<a class="cc-secondary" href="#!/client/details/' +
            rowData.contract.client.id +
            '">' +
            rowData.contract.client.name +
            '</a>'
          );
        } else return rowData.client;
      },
    },
  ];
  UFBCols: CCGridColumn[] = [
    {
      field: 'route',
      header: '',
      type: 'button',
      buttonConfig: {
        mode: 'multiple',
        disabled: false,
        type: 'raised',
        buttons: [
          {
            text: 'Go To Families List',
            type: 'raised',
            disabled: false,
            hidden(record) {
              return record.reason !== 'Contract Not Found';
            },
            callback: (row: any) => {
              window.open('#!/client/client-list', '_blank');
            },
          },
          {
            text: "Go To Family's Payments",
            type: 'raised',
            disabled: false,
            hidden(record) {
              return record.reason !== 'Payment Not Found';
            },
            callback: (row: any) => {
              window.open(
                '#!/client/payments/' + row.contract.client.id,
                '_blank'
              );
            },
          },
        ],
      },
    },
    {
      field: 'rowNum',
      header: 'Row Number',
    },
    {
      field: 'date',
      header: 'Transaction',
      formatter(rowData, colDef) {
        return moment(rowData.date).format('YYYY-MM-DD');
      },
    },
    { field: 'transactionAmount', header: 'Transaction Amount' },
    { field: 'paymentDetail', header: 'Payment Details' },
    { field: 'reason', header: 'Reason' },
    { field: 'description', header: 'Transaction Description' },
    { field: 'note', header: 'Note' },
    { field: 'actions', header: 'Actions', width: 'max-content' },
  ];
  UBFBCols: CCGridColumn[] = [
    {
      field: 'route',
      header: '',
      type: 'button',
      buttonConfig: {
        mode: 'multiple',
        disabled: false,
        type: 'raised',
        buttons: [
          {
            text: 'Go To Families List',
            type: 'raised',
            disabled: false,
            hidden(record) {
              return record.reason !== 'Contract Not Found';
            },
            callback: (row: any) => {
              window.open('#!/client/client-list', '_blank');
            },
          },
          {
            text: "Go To Family's Payments",
            type: 'raised',
            disabled: false,
            hidden(record) {
              return record.reason !== 'Payment Not Found';
            },
            callback: (row: any) => {
              window.open(
                '#!/client/payments/' + row.contract.client.id,
                '_blank'
              );
            },
          },
        ],
      },
    },
    {
      field: 'rowNum',
      header: 'Row Number',
    },
    {
      field: 'date',
      header: 'Transaction',
      formatter(rowData, colDef) {
        return moment(rowData.date).format('YYYY-MM-DD');
      },
    },
    { field: 'transactionAmount', header: 'Transaction Amount' },
    { field: 'paymentDetail', header: 'Payment Details' },
    { field: 'reason', header: 'Reason' },
    { field: 'description', header: 'Transaction Description' },
    { field: 'note', header: 'Note' },
    { field: 'actions', header: 'Actions' },
  ];
  UFECols: CCGridColumn[] = [
    {
      field: 'route',
      header: '',
      type: 'button',
      buttonConfig: {
        mode: 'single',
        type: 'raised',
        disabled: false,
        text: "Go To Family's Payments",
        callback: (row: any) => {
          window.open('#!/client/payments/' + row.clientId, '_blank');
        },
        hidden(record) {
          return false;
        },
      },
    },
    {
      field: 'rowNum',
      header: 'Row Number',
    },
    {
      field: 'date',
      header: 'Payment Due Date',
      formatter(rowData, colDef) {
        return moment(rowData.date).format('YYYY-MM-DD');
      },
    },
    { field: 'amountOfPayment', header: 'Payment Amount' },
    { field: 'clientName', header: 'Family Name' },
    {
      field: 'contractId',
      header: 'Contract Id',
      formatter(rowData, colDef) {
        return rowData.contractId ? 'CONTX-' + rowData.contractId : '';
      },
    },
  ];

  checkIfSelectedPageReceived() {
    let selected = true;
    this.MatchedTransReceivedPayments.getValue().content.forEach(
      (element: any) => {
        if (!this.selectedReceived[element.id]) {
          selected = false;
        }
      }
    );
    return selected;
  }

  unSelectPageReceived() {
    this.isSelectAllReceived = false;
    this.MatchedTransReceivedPayments.getValue().content.forEach(
      (element: any) => {
        this.selectedReceived[element.id] = false;
      }
    );
  }

  selectPageReceived(justPage?: any) {
    if (justPage) {
      this.isSelectAllReceived = false;
    }
    this.MatchedTransReceivedPayments.getValue().content.forEach(
      (element: any) => {
        this.selectedReceived[element.id] = true;
      }
    );
  }

  selectAllReceived() {
    if (this.isSelectAllReceived) {
      this.unSelectAllReceived();
      return;
    }
    this.isSelectAllReceived = true;
    this.selectPageReceived();
  }

  confirmReceivedPayments() {
    if (this.isSelectAllReceived) {
      this.confirmReceivedPaymentsAll();
      return;
    }
    this.http
      .post(
        `${this._api}/${API.confirmTransactions}`,
        this.getSelectedMatchedTransReceived()
      )
      .subscribe((res: any) => {
        this.notifications.notifySuccess(res);
        this.getMatchedTransReceivedPaymentsData();
        this.unSelectAllReceived();
      });
  }

  confirmReceivedPaymentsAll() {
    this.http
      .get(
        `${this._api}/${API.confirmAllTransactions}/${this.route.snapshot.params['id']}/directDebitMatchedTransactionReceivedPayment`
      )
      .subscribe((res: any) => {
        this.notifications.notifySuccess(res);
        this.getMatchedTransReceivedPaymentsData();
      });
  }

  unSelectAllReceived() {
    this.isSelectAllReceived = false;
    this.selectedReceived = [];
  }

  getSelectedRowsCountReceived() {
    let len = 0;
    Object.keys(this.selectedReceived).forEach((element: any) => {
      if (this.selectedReceived[element]) {
        len++;
      }
    });
    return len;
  }

  getSelectedMatchedTransReceived() {
    let selected: any[] = [];
    Object.keys(this.selectedReceived).forEach((element: any) => {
      if (this.selectedReceived[element]) {
        selected.push({ id: element });
      }
    });
    return selected;
  }

  checkIfSelectedPageBounced() {
    let selected = true;
    this.MatchedTransBouncedPayments.getValue().content.forEach(
      (element: any) => {
        if (!this.selectedBounced[element.id]) {
          selected = false;
        }
      }
    );
    return selected;
  }

  unSelectPageBounced() {
    this.isSelectAllBounced = false;
    this.MatchedTransBouncedPayments.getValue().content.forEach(
      (element: any) => {
        this.selectedBounced[element.id] = false;
      }
    );
  }

  selectPageBounced(justPage?: any) {
    if (justPage) {
      this.isSelectAllBounced = false;
    }
    this.MatchedTransBouncedPayments.getValue().content.forEach(
      (element: any) => {
        this.selectedBounced[element.id] = true;
      }
    );
  }

  selectAllBounced() {
    if (this.isSelectAllBounced) {
      this.unSelectAllBounced();
      return;
    }
    this.isSelectAllBounced = true;
    this.selectPageBounced();
  }

  unSelectAllBounced() {
    this.isSelectAllBounced = false;
    this.selectedBounced = [];
  }

  getSelectedRowsCountBounced() {
    let len = 0;
    Object.keys(this.selectedBounced).forEach((element: any) => {
      if (this.selectedBounced[element]) {
        len++;
      }
    });
    return len;
  }

  getSelectedMatchedTransBounced() {
    let selected: any[] = [];
    Object.keys(this.selectedBounced).forEach((element: any) => {
      if (this.selectedBounced[element]) {
        selected.push({ id: element });
      }
    });
    return selected;
  }

  confirmBouncedPayments() {
    if (this.isSelectAllBounced) {
      this.confirmBouncedPaymentsAll();
      return;
    }
    this.http
      .post(
        `${this._api}/${API.confirmTransactions}`,
        this.getSelectedMatchedTransBounced()
      )
      .subscribe((res: any) => {
        this.notifications.notifySuccess(res);
        this.getMatchedTransBouncedPaymentsData();
        this.unSelectAllBounced();
      });
  }

  confirmBouncedPaymentsAll() {
    this.http
      .get(
        `${this._api}/${API.confirmAllTransactions}/${this.route.snapshot.params['id']}/directDebitMatchedTransactionBouncedPayment`
      )
      .subscribe((res: any) => {});
  }

  exportMatchedReceivedPayments() {
    let url: string = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/directDebitTransactionsCSV?gridType=matchedTransactionReceivedPayment`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }

  exportAlreadyMatchedTransactionReceivedPayment() {
    let url: string = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/directDebitTransactionsCSV?gridType=alreadyMatchedTransactionReceivedPayment`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }

  exportUnMatchedFromBankStatement() {
    let url: string = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/directDebitTransactionsCSV?gridType=unmatchedReceivedTransactionFromBankStatement`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }

  exportUnMatchedDirectDebitTransactionsFromErpCSV() {
    let url: string = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/unMatchedDirectDebitTransactionsFromErpCSV`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }

  exportMatchedTransactionBouncedPayment() {
    let url: string = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/directDebitTransactionsCSV?gridType=matchedTransactionBouncedPayment`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }

  exportAlreadyMatchedTransactionBouncedPayment() {
    let url: string = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/directDebitTransactionsCSV?gridType=alreadyMatchedTransactionBouncedPayment`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }

  exportUnMatchedTransactionBouncedPayment() {
    let url: string = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/directDebitTransactionsCSV?gridType=unmatchedBouncedTransactionFromBankStatement`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }

  linkToPayment(element: any) {
    if (element.reason === 'Payment Not Found') {
      this.ccDialog
        .originalOpen(LinkToPaymentComponent, {
          data: {
            id: element.id,
            contract: element.contract,
          },
        })
        .afterClosed()
        .subscribe((res: any) => {
          if (res) {
            this.getAllData();
          }
        });
    } else if (element.reason === 'Contract Not Found') {
      this.ccDialog
        .originalOpen(LinkToContractPaymentComponent, {
          data: {
            id: element.id,
            contract: element.contract,
          },
        })
        .afterClosed()
        .subscribe((res: any) => {
          if (res) {
            this.getAllData();
          }
        });
    }
  }

  addNote(element: any) {
    this.ccDialog
      .originalOpen(AddNoteComponent, {
        data: {
          id: element.id,
          note: element.note,
        },
      })
      .afterClosed()
      .subscribe((res: any) => {
        if (res) {
          this.getAllData();
        }
      });
  }
  openLinkedTransactionsFor(transactionId: number): void {
    const payment = this.MatchedTransReceivedPayments.getValue().content.find(
      (item) => item.id === transactionId
    );
    if (!payment) return;
    const getTransactionLink = (transactionId: number): string =>
      `/accounting/add-edit-transactions/${transactionId}`;
    const transactionsList = [
      payment.transaction.id,
      ...payment.linkedTransactionIds,
    ];
    transactionsList.forEach((transId) => {
      const url = getTransactionLink(transId);
      window.open(url, '_blank');
    });
  }
}
