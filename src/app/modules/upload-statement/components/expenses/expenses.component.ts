import { Component, Inject, Input, OnInit } from '@angular/core';
import { UploadStatementService } from '../../services/upload-statement.service';
import { BehaviorSubject, Observable } from 'rxjs';
import { PageableResponseModel } from '@maids/cc-lib/common';
import { HttpClient } from '@angular/common/http';
import { ActivatedRoute } from '@angular/router';
import { CCBackendEndpoint } from '@maids/cc-erp-services';
import { CCDialog } from '@maids/cc-lib/dialog';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { PageEvent } from '@angular/material/paginator';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import * as moment from 'moment';
import { LinkToContractPaymentComponent } from '../link-to-contract-payment/link-to-contract-payment.component';
import { LinkToPaymentComponent } from '../link-to-payment/link-to-payment.component';
import { AddNoteComponent } from '../add-note/add-note.component';
import { AddUndefinedManuallyTransactionComponent } from '../add-undefined-manually-transaction/add-undefined-manually-transaction.component';
@Component({
  selector: 'app-expenses',
  templateUrl: './expenses.component.html',
  styleUrls: ['./expenses.component.scss'],
})
export class ExpensesComponent implements OnInit {
  resolved: number = 0;
  unresolved: number = 0;
  summary: any;
  MatchedRefundsPage: number = 0;
  MatchedRefundsAlreadyPage: number = 0;
  BucketPage: number = 0;
  BucketAlreadyPage: number = 0;
  OtherPage: number = 0;
  OtherAlreadyPage: number = 0;
  UnMatchedRefundsPage: number = 0;
  UnmatchedPage: number = 0;
  ManuallyMatchedTransactionsPage: number = 0;
  MaidsAtExpensePage: number = 0;
  AlreadyMatchedMaidsAtExpensePage: number = 0;
  UnmatchedMaidsAtExpensePage: number = 0;

  MatchedRefundsSize: number = 50;
  MatchedRefundsAlreadySize: number = 50;
  BucketSize: number = 50;
  BucketAlreadySize: number = 50;
  OtherSize: number = 50;
  OtherAlreadySize: number = 50;
  UnMatchedRefundsSize: number = 50;
  UnmatchedSize: number = 50;
  ManuallyMatchedTransactionsSize: number = 50;
  MaidsAtExpenseSize: number = 50;
  AlreadyMatchedMaidsAtExpenseSize: number = 50;
  UnmatchedMaidsAtExpenseSize: number = 50;

  recordCountMatchedRefunds: number = 0;
  recordCountAlreadyMatchedRefunds: number = 0;
  recordCountOther: number = 0;
  recordCountAlreadyOther: number = 0;
  recordCountBucket: number = 0;
  recordCountAlreadyBucket: number = 0;
  recordCountUnMatched: number = 0;
  recordCountUnMatchedRefunds: number = 0;
  recordCountManuallyMatchedTransactions: number = 0;
  recordCountMaidsAtExpense: number = 0;
  recordCountAlreadyMatchedMaidsAtExpense: number = 0;
  recordCountUnmatchedMaidsAtExpense: number = 0;

  totalAmountMatchedRefunds: number = 0;
  totalAmountAlreadyMatchedRefunds: number = 0;
  totalAmountUnMatched: number = 0;
  totalAmountOther: number = 0;
  totalAmountAlreadyOther: number = 0;
  totalAmountBucket: number = 0;
  totalAmountAlreadyBucket: number = 0;
  totalAmountUnMatchedRefunds: number = 0;
  totalAmountManuallyMatchedTransactions: number = 0;
  totalAmountMaidsAtExpense: number = 0;
  totalAmountAlreadyMatchedMaidsAtExpense: number = 0;
  totalAmountUnmatchedMaidsAtExpense: number = 0;

  selectedMatchedRefunds: any[] = [];
  isSelectAllRefunds: boolean = false;
  selectedOther: any[] = [];
  isSelectAllOther: boolean = false;
  selectedBucket: any[] = [];
  isSelectAllBucket: boolean = false;
  selectedMaidsAtExpense: any[] = [];
  isSelectAllMaidsAtExpense: boolean = false;
  MatchedRefunds = new BehaviorSubject<PageableResponseModel<any>>({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  MatchedRefunds$: Observable<PageableResponseModel<any>> =
    this.MatchedRefunds.asObservable();
  MatchedRefundsAlready = new BehaviorSubject<PageableResponseModel<any>>({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  MatchedRefundsAlready$: Observable<PageableResponseModel<any>> =
    this.MatchedRefundsAlready.asObservable();
  UnMatchedRefunds = new BehaviorSubject<PageableResponseModel<any>>({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  UnMatchedRefunds$: Observable<PageableResponseModel<any>> =
    this.UnMatchedRefunds.asObservable();
  Other = new BehaviorSubject<PageableResponseModel<any>>({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  Other$: Observable<PageableResponseModel<any>> = this.Other.asObservable();
  OtherAlready = new BehaviorSubject<PageableResponseModel<any>>({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  OtherAlready$: Observable<PageableResponseModel<any>> =
    this.OtherAlready.asObservable();
  Bucket = new BehaviorSubject<PageableResponseModel<any>>({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  Bucket$: Observable<PageableResponseModel<any>> = this.Bucket.asObservable();
  BucketAlready = new BehaviorSubject<PageableResponseModel<any>>({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  BucketAlready$: Observable<PageableResponseModel<any>> =
    this.BucketAlready.asObservable();
  Unmatched = new BehaviorSubject<PageableResponseModel<any>>({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  Unmatched$: Observable<PageableResponseModel<any>> =
    this.Unmatched.asObservable();
  ManuallyMatchedTransactions = new BehaviorSubject<PageableResponseModel<any>>(
    {
      content: [],
      number: 0,
      size: 0,
      totalElements: 0,
      totalPages: 0,
    }
  );
  ManuallyMatchedTransactions$: Observable<PageableResponseModel<any>> =
    this.ManuallyMatchedTransactions.asObservable();
  MaidsAtExpense = new BehaviorSubject<PageableResponseModel<any>>({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  MaidsAtExpense$: Observable<PageableResponseModel<any>> =
    this.MaidsAtExpense.asObservable();
  AlreadyMatchedMaidsAtExpense = new BehaviorSubject<
    PageableResponseModel<any>
  >({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  AlreadyMatchedMaidsAtExpense$: Observable<PageableResponseModel<any>> =
    this.AlreadyMatchedMaidsAtExpense.asObservable();
  UnmatchedMaidsAtExpenseExpenses = new BehaviorSubject<
    PageableResponseModel<any>
  >({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  UnmatchedMaidsAtExpenseExpenses$: Observable<PageableResponseModel<any>> =
    this.UnmatchedMaidsAtExpenseExpenses.asObservable();
  constructor(
    private uploadStatementService: UploadStatementService,
    private http: HttpClient,
    @Inject(CCBackendEndpoint) private _api: string,
    private route: ActivatedRoute,
    public readonly notifications: CCNotificationService,
    private mediaService: MediaService,
    private ccDialog: CCDialog
  ) {}
  ngOnInit(): void {
    this.uploadStatementService.summaryObject.subscribe((res) => {
      this.summary = res;
    });
    this.getAllData();
  }

  getAllData() {
    this.getTableMatchedRefunds();
    this.getTableMatchedRefundsAlready();
    this.getTableUnMatchedRefunds();
    this.getTableOther();
    this.getTableOtherAlready();
    this.getTableBucket();
    this.getTableBucketAlready();
    this.getTableUnmatched();
    this.getTableManuallyMatchedTransactions();
    this.getTableMaidsAtExpense();
    this.getTableAlreadyMatchedMaidsAtExpense();
    this.getTableUnmatchedMaidsAtExpenseExpenses();
  }
  getTableMatchedRefunds(page: number = 0, size: number = 50) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/expensesTransactions`,
        {
          params: { gridType: 'matchedTransactionsClientRefunds', page, size },
        }
      )
      .subscribe((res: any) => {
        this.MatchedRefunds.next(res);
        this.recordCountMatchedRefunds = res.totalElements;
        this.totalAmountMatchedRefunds = res.totalSum;
        if (this.isSelectAllRefunds) {
          this.selectPageRefunds();
        }
      });
  }
  getNextMR(event: PageEvent) {
    this.getTableMatchedRefunds(event.pageIndex, event.pageSize);
    this.MatchedRefundsPage = event.pageIndex;
    this.MatchedRefundsSize = event.pageSize;
  }
  getTableMatchedRefundsAlready(page: number = 0, size: number = 50) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/expensesTransactions`,
        {
          params: {
            gridType: 'alreadyMatchedTransactionsClientRefunds',
            page,
            size,
          },
        }
      )
      .subscribe((res: any) => {
        this.MatchedRefundsAlready.next(res);
        this.recordCountAlreadyMatchedRefunds = res.totalElements;
        this.totalAmountAlreadyMatchedRefunds = res.totalSum;
      });
  }
  getNextMRA(event: PageEvent) {
    this.getTableMatchedRefundsAlready(event.pageIndex, event.pageSize);
    this.MatchedRefundsAlreadyPage = event.pageIndex;
    this.MatchedRefundsAlreadySize = event.pageSize;
  }
  getTableUnMatchedRefunds(page: number = 0, size: number = 50) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/expensesTransactions`,
        {
          params: {
            gridType: 'unmatchedTransactionsClientRefunds',
            page,
            size,
          },
        }
      )
      .subscribe((res: any) => {
        this.UnMatchedRefunds.next(res);
        this.recordCountUnMatchedRefunds = res.totalElements;
        this.totalAmountUnMatchedRefunds = res.totalSum;
      });
  }
  getNextUMR(event: PageEvent) {
    this.getTableUnMatchedRefunds(event.pageIndex, event.pageSize);
    this.UnMatchedRefundsPage = event.pageIndex;
    this.UnMatchedRefundsSize = event.pageSize;
  }
  getTableOther(page: number = 0, size: number = 50) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/expensesTransactions`,
        {
          params: { gridType: 'otherExpenses', page, size },
        }
      )
      .subscribe((res: any) => {
        this.Other.next(res);
        this.recordCountOther = res.totalElements;
        this.totalAmountOther = res.totalSum;
        if (this.isSelectAllOther) {
          this.selectPageOther();
        }
      });
  }
  getNextO(event: PageEvent) {
    this.getTableOther(event.pageIndex, event.pageSize);
    this.OtherPage = event.pageIndex;
    this.OtherSize = event.pageSize;
  }
  getTableOtherAlready(page: number = 0, size: number = 50) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/expensesTransactions`,
        {
          params: { gridType: 'alreadyMatchedOtherExpenses', page, size },
        }
      )
      .subscribe((res: any) => {
        this.OtherAlready.next(res);
        this.recordCountAlreadyOther = res.totalElements;
        this.totalAmountAlreadyOther = res.totalSum;
      });
  }
  getNextOA(event: PageEvent) {
    this.getTableOtherAlready(event.pageIndex, event.pageSize);
    this.OtherAlreadyPage = event.pageIndex;
    this.OtherAlreadySize = event.pageSize;
  }
  getTableBucket(page: number = 0, size: number = 50) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/expensesTransactions`,
        {
          params: { gridType: 'bucketRefills', page, size },
        }
      )
      .subscribe((res: any) => {
        this.Bucket.next(res);
        this.recordCountBucket = res.totalElements;
        this.totalAmountBucket = res.totalSum;
      });
  }
  getNextB(event: PageEvent) {
    this.getTableBucket(event.pageIndex, event.pageSize);
    this.BucketPage = event.pageIndex;
    this.BucketSize = event.pageSize;
    if (this.isSelectAllBucket) {
      this.selectPageBucket();
    }
  }
  getTableBucketAlready(page: number = 0, size: number = 50) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/expensesTransactions`,
        {
          params: { gridType: 'alreadyMatchedBucketRefills', page, size },
        }
      )
      .subscribe((res: any) => {
        this.BucketAlready.next(res);
        this.recordCountAlreadyBucket = res.totalElements;
        this.totalAmountAlreadyBucket = res.totalSum;
      });
  }
  getNextBA(event: PageEvent) {
    this.getTableBucketAlready(event.pageIndex, event.pageSize);
    this.BucketAlreadyPage = event.pageIndex;
    this.BucketAlreadySize = event.pageSize;
  }
  getTableUnmatched(page: number = 0, size: number = 50) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/expensesTransactions`,
        {
          params: { gridType: 'unmatchedTransactions', page, size },
        }
      )
      .subscribe((res: any) => {
        this.Unmatched.next(res);
        this.recordCountUnMatched = res.totalElements;
        this.totalAmountUnMatched = res.totalSum;
      });
  }
  getNextU(event: PageEvent) {
    this.getTableUnmatched(event.pageIndex, event.pageSize);
    this.UnmatchedPage = event.pageIndex;
    this.UnmatchedSize = event.pageSize;
  }
  getTableManuallyMatchedTransactions(page: number = 0, size: number = 50) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/expensesTransactions`,
        {
          params: { gridType: 'manuallyMatchedTransactions', page, size },
        }
      )
      .subscribe((res: any) => {
        this.ManuallyMatchedTransactions.next(res);
        this.recordCountManuallyMatchedTransactions = res.totalElements;
        this.totalAmountManuallyMatchedTransactions = res.totalSum;
      });
  }
  getNextMM(event: PageEvent) {
    this.getTableManuallyMatchedTransactions(event.pageIndex, event.pageSize);
    this.ManuallyMatchedTransactionsPage = event.pageIndex;
    this.ManuallyMatchedTransactionsSize = event.pageSize;
  }
  getTableMaidsAtExpense(page: number = 0, size: number = 50) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/expensesTransactions`,
        {
          params: { gridType: 'maidsAtExpense', page, size },
        }
      )
      .subscribe((res: any) => {
        this.MaidsAtExpense.next(res);
        this.recordCountMaidsAtExpense = res.totalElements;
        this.totalAmountMaidsAtExpense = res.totalSum;
        if (this.isSelectAllMaidsAtExpense) {
          this.selectPageMaidsAtExpense();
        }
      });
  }
  getNextMAE(event: PageEvent) {
    this.getTableMaidsAtExpense(event.pageIndex, event.pageSize);
    this.MaidsAtExpensePage = event.pageIndex;
    this.MaidsAtExpenseSize = event.pageSize;
  }
  getTableAlreadyMatchedMaidsAtExpense(page: number = 0, size: number = 50) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/expensesTransactions`,
        {
          params: { gridType: 'alreadyMatchedMaidsAtExpense', page, size },
        }
      )
      .subscribe((res: any) => {
        this.AlreadyMatchedMaidsAtExpense.next(res);
        this.recordCountAlreadyMatchedMaidsAtExpense = res.totalElements;
        this.totalAmountAlreadyMatchedMaidsAtExpense = res.totalSum;
      });
  }
  getNextAMMAE(event: PageEvent) {
    this.getTableAlreadyMatchedMaidsAtExpense(event.pageIndex, event.pageSize);
    this.AlreadyMatchedMaidsAtExpensePage = event.pageIndex;
    this.AlreadyMatchedMaidsAtExpenseSize = event.pageSize;
  }
  getTableUnmatchedMaidsAtExpenseExpenses(page: number = 0, size: number = 50) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/expensesTransactions`,
        {
          params: { gridType: 'unmatchedMaidsAtExpense', page, size },
        }
      )
      .subscribe((res: any) => {
        this.UnmatchedMaidsAtExpenseExpenses.next(res);
        this.recordCountUnmatchedMaidsAtExpense = res.totalElements;
        this.totalAmountUnmatchedMaidsAtExpense = res.totalSum;
      });
  }
  getNextUMAEE(event: PageEvent) {
    this.getTableUnmatchedMaidsAtExpenseExpenses(
      event.pageIndex,
      event.pageSize
    );
    this.UnmatchedMaidsAtExpensePage = event.pageIndex;
    this.UnmatchedMaidsAtExpenseSize = event.pageSize;
  }
  gridColsMR: CCGridColumn[] = [
    { field: 'select', header: 'Select' },
    { field: 'rowNum', header: 'Row Number' },
    {
      field: 'transaction',
      header: 'Transaction Date',
      formatter(rowData, colDef) {
        let td = moment(rowData.date).format('YYYY-MM-DD');
        return rowData.transaction.id
          ? `<a class="cc-secondary" href="#!/accounting/add-edit-transactions/${rowData.transaction.id}">${td}</a>`
          : td;
      },
    },
    { field: 'transactionAmount', header: 'Transaction Amount' },
    { field: 'vatAmount', header: 'VAT Amount' },
    { field: 'vatType', header: 'VAT Type' },
    { field: 'expenseCode', header: 'Expense Code' },
    { field: 'description', header: 'Description' },
    {
      field: 'paymentId',
      header: 'Payment Id',
      formatter(rowData, colDef) {
        if (rowData.payment.id) {
          return `<a class="cc-secondary" href="#!/client/payments/edit/${rowData.contract.client.id}/${rowData.contract.id}/${rowData.payment.id}">Payment-${rowData.payment.id}</a>`;
        } else {
          return '-';
        }
      },
    },
    {
      field: 'client',
      header: 'Family Name',
      formatter(rowData, colDef) {
        if (rowData.contract.client)
          return (
            '<a class="cc-secondary" href="#!/client/details/' +
            rowData.contract.client.id +
            '">' +
            rowData.contract.client.name +
            '</a>'
          );
        else return rowData.client;
      },
    },
    { field: 'paymentDetail', header: 'Payment Details' },
  ];

  gridColsMRA: CCGridColumn[] = [
    { field: 'rowNum', header: 'Row Number' },
    {
      field: 'transaction',
      header: 'Transaction Date',
      formatter(rowData, colDef) {
        let td = moment(rowData.date).format('YYYY-MM-DD');
        return rowData.transaction.id
          ? `<a class="cc-secondary" href="#!/accounting/add-edit-transactions/${rowData.transaction.id}">${td}</a>`
          : td;
      },
    },
    { field: 'transactionAmount', header: 'Transaction Amount' },
    { field: 'vatAmount', header: 'VAT Amount' },
    { field: 'vatType', header: 'VAT Type' },
    { field: 'expenseCode', header: 'Expense Code' },
    { field: 'description', header: 'Description' },
    { field: 'paymentDetail', header: 'Payment Details' },
    {
      field: 'paymentId',
      header: 'Payment Id',
      formatter(rowData, colDef) {
        if (rowData.payment.id) {
          return `<a class="cc-secondary" href="#!/client/payments/edit/${rowData.contract.client.id}/${rowData.contract.id}/${rowData.payment.id}">Payment-${rowData.payment.id}</a>`;
        } else {
          return '-';
        }
      },
    },
    {
      field: 'client',
      header: 'Family Name',
      formatter(rowData, colDef) {
        if (rowData.contract.client)
          return (
            '<a class="cc-secondary" href="#!/client/details/' +
            rowData.contract.client.id +
            '">' +
            rowData.contract.client.name +
            '</a>'
          );
        else return rowData.client;
      },
    },
  ];
  gridColsUMR: CCGridColumn[] = [
    {
      field: 'route',
      header: '',
      type: 'button',
      buttonConfig: {
        mode: 'multiple',
        disabled: false,
        type: 'raised',
        buttons: [
          {
            text: 'Go To Families List',
            type: 'raised',
            disabled: false,
            hidden(record) {
              return record.contract ? true : false;
            },
            callback: (row: any) => {
              window.open('#!/client/client-list', '_blank');
            },
          },
          {
            text: "Go To Family's Payments",
            type: 'raised',
            disabled: false,
            hidden(record) {
              return record.reason !== 'Payment Not Found';
            },
            callback: (row: any) => {
              window.open(
                '#!/client/payments/' + row.contract.client.id,
                '_blank'
              );
            },
          },
        ],
      },
    },
    { field: 'rowNum', header: 'Row Number' },
    { field: 'date', header: 'Transaction Date' },
    { field: 'transactionAmount', header: 'Transaction Amount' },
    { field: 'vatAmount', header: 'VAT Amount' },
    { field: 'vatType', header: 'VAT Type' },
    { field: 'description', header: 'Description' },
    { field: 'reason', header: 'Reason' },
    { field: 'paymentDetail', header: 'Payment Details' },
    { field: 'action', header: 'Action' },
  ];
  gridColsO: CCGridColumn[] = [
    { field: 'select', header: 'Select' },
    { field: 'rowNum', header: 'Row Number' },
    {
      field: 'transactionDate',
      header: 'Transaction Date',
      formatter(rowData, colDef) {
        let td = moment(rowData.transactionDate).format('YYYY-MM-DD');
        return rowData.transaction.id
          ? `<a class="cc-secondary" href="#!/accounting/add-edit-transactions/${rowData.transaction.id}">${td}</a>`
          : td;
      },
    },
    { field: 'transactionAmount', header: 'Transaction Amount' },
    { field: 'vatAmount', header: 'VAT Amount' },
    { field: 'vatType', header: 'VAT Type' },
    {
      field: 'forName',
      header: 'For',
      formatter(rowData, colDef) {
        let id = rowData.erpObjectId.replace('ERP:', '');
        if (rowData.forEntity == 'MAID') {
          return `<a class="cc-secondary" href="#!/housemaid/details/${id}">${rowData.forName}</a>`;
        } else if (rowData.forEntity == 'Operator') {
          return `<a class="cc-secondary" href="#!/freedom-operator/payments">${rowData.forName}</a>`;
        } else if (rowData.forEntity == 'Client') {
          return `<a class="cc-secondary" href="#!/client/details/${id}">${rowData.forName}</a>`;
        } else {
          return '-';
        }
      },
    },
    { field: 'expenseCode', header: 'Expense Code' },
    { field: 'description', header: 'Description' },
    { field: 'paymentDetail', header: 'Payment Details' },
  ];
  gridColsOA: CCGridColumn[] = [
    { field: 'rowNum', header: 'Row Number' },
    {
      field: 'transactionDate',
      header: 'Transaction Date',
      formatter(rowData, colDef) {
        let td = moment(rowData.transactionDate).format('YYYY-MM-DD');
        return rowData.transaction.id
          ? `<a class="cc-secondary" href="#!/accounting/add-edit-transactions/${rowData.transaction.id}">${td}</a>`
          : td;
      },
    },
    { field: 'transactionAmount', header: 'Transaction Amount' },
    { field: 'vatAmount', header: 'VAT Amount' },
    { field: 'vatType', header: 'VAT Type' },
    {
      field: 'forName',
      header: 'For',
      formatter(rowData, colDef) {
        let id = rowData.erpObjectId.replace('ERP:', '');
        if (rowData.forEntity == 'MAID') {
          return `<a class="cc-secondary" href="#!/housemaid/details/${id}">${rowData.forName}</a>`;
        } else if (rowData.forEntity == 'Operator') {
          return `<a class="cc-secondary" href="#!/freedom-operator/payments">${rowData.forName}</a>`;
        } else if (rowData.forEntity == 'Client') {
          return `<a class="cc-secondary" href="#!/client/details/${id}">${rowData.forName}</a>`;
        } else {
          return '-';
        }
      },
    },
    { field: 'expenseCode', header: 'Expense Code' },
    { field: 'description', header: 'Description' },
    { field: 'paymentDetail', header: 'Payment Details' },
  ];
  gridColsB: CCGridColumn[] = [
    { field: 'select', header: 'Select' },
    { field: 'rowNum', header: 'Row Number' },
    {
      field: 'transactionDate',
      header: 'Transaction Date',
      formatter(rowData, colDef) {
        let td = moment(rowData.transactionDate).format('YYYY-MM-DD');
        return rowData.transaction.id
          ? `<a class="cc-secondary" href="#!/accounting/add-edit-transactions/${rowData.transaction.id}">${td}</a>`
          : td;
      },
    },
    { field: 'transactionAmount', header: 'Transaction Amount' },
    { field: 'fromBucket', header: 'From Bucket' },
    { field: 'toBucket', header: 'To Bucket' },
    { field: 'vatAmount', header: 'VAT Amount' },
    { field: 'vatType', header: 'VAT Type' },
    { field: 'paymentDetail', header: 'Payment Details' },
  ];
  gridColsBA: CCGridColumn[] = [
    { field: 'rowNum', header: 'Row Number' },
    {
      field: 'transactionDate',
      header: 'Transaction Date',
      formatter(rowData, colDef) {
        let td = moment(rowData.transactionDate).format('YYYY-MM-DD');
        return rowData.transaction.id
          ? `<a class="cc-secondary" href="#!/accounting/add-edit-transactions/${rowData.transaction.id}">${td}</a>`
          : td;
      },
    },
    { field: 'transactionAmount', header: 'Transaction Amount' },
    { field: 'fromBucket', header: 'From Bucket' },
    { field: 'toBucket', header: 'To Bucket' },
    { field: 'vatAmount', header: 'VAT Amount' },
    { field: 'vatType', header: 'VAT Type' },
    { field: 'paymentDetail', header: 'Payment Details' },
  ];
  gridColsU: CCGridColumn[] = [
    { field: 'rowNum', header: 'Row Number' },
    { field: 'date', header: 'Transaction Date' },
    { field: 'transactionAmount', header: 'Transaction Amount' },
    { field: 'paymentDetail', header: 'Payment Details' },
    { field: 'note', header: 'Note' },
    { field: 'actions', header: 'Actions' },
  ];
  gridColsMM: CCGridColumn[] = [
    { field: 'rowNum', header: 'Row Number' },
    { field: 'transactionAmount', header: 'Transaction Amount' },
    { field: 'expenseCode', header: 'Expense Code' },
    { field: 'vatAmount', header: 'VAT Amount' },
    { field: 'vatType', header: 'VAT Type' },
    { field: 'description', header: 'Description' },
    { field: 'paymentDetail', header: 'Payment Details' },
  ];
  gridColsMAE: CCGridColumn[] = [
    { field: 'select', header: 'Select' },
    { field: 'rowNum', header: 'Row Number' },
    {
      field: 'transactionDate',
      header: 'Transaction Date',
      formatter(rowData, colDef) {
        let td = moment(rowData.transactionDate).format('YYYY-MM-DD');
        return rowData.transaction.id
          ? `<a href="#!/accounting/add-edit-transactions/${rowData.transaction.id}">${td}</a>`
          : td;
      },
    },
    { field: 'transactionAmount', header: 'Transaction Amount' },
    { field: 'vatAmount', header: 'VAT Amount' },
    { field: 'vatType', header: 'VAT Type' },
    {
      field: 'forName',
      header: 'For',
      formatter(rowData, colDef) {
        if (rowData.forEntity == 'MAID') {
          return `<a class="cc-secondary" href="#!/housemaid/details/${rowData.forId}">${rowData.forName}</a>`;
        } else if (rowData.forEntity == 'Operator') {
          return `<a class="cc-secondary" href="#!/freedom-operator/payments">${rowData.forName}</a>`;
        } else if (rowData.forEntity == 'Client') {
          return `<a href="#!/client/details/${rowData.forId}">${rowData.forName}</a>`;
        } else {
          return '-';
        }
      },
    },
    { field: 'expenseCode', header: 'Expense Code' },
    { field: 'description', header: 'Description' },
    { field: 'paymentDetail', header: 'Payment Details' },
  ];
  gridColsAMMAE: CCGridColumn[] = [
    { field: 'rowNum', header: 'Row Number' },
    {
      field: 'transactionDate',
      header: 'Transaction Date',
      formatter(rowData, colDef) {
        let td = moment(rowData.transactionDate).format('YYYY-MM-DD');
        return rowData.transaction.id
          ? `<a href="#!/accounting/add-edit-transactions/${rowData.transaction.id}">${td}</a>`
          : td;
      },
    },
    { field: 'transactionAmount', header: 'Transaction Amount' },
    { field: 'vatAmount', header: 'VAT Amount' },
    { field: 'vatType', header: 'VAT Type' },
    {
      field: 'forName',
      header: 'For',
      formatter(rowData, colDef) {
        if (rowData.forEntity == 'MAID') {
          return `<a href="#!/housemaid/details/${rowData.forId}">${rowData.forName}</a>`;
        } else if (rowData.forEntity == 'Operator') {
          return `<a href="#!/freedom-operator/payments">${rowData.forName}</a>`;
        } else if (rowData.forEntity == 'Client') {
          return `<a class="cc-secondary" href="#!/client/details/${rowData.forId}">${rowData.forName}</a>`;
        } else {
          return '-';
        }
      },
    },
    { field: 'expenseCode', header: 'Expense Code' },
    { field: 'description', header: 'Description' },
    { field: 'paymentDetail', header: 'Payment Details' },
  ];
  gridColsUMAEE: CCGridColumn[] = [
    { field: 'rowNum', header: 'Row Number' },
    { field: 'date', header: 'Transaction Date' },
    { field: 'transactionAmount', header: 'Transaction Amount' },
    { field: 'paymentDetail', header: 'Payment Details' },
    { field: 'note', header: 'Note' },
    { field: 'actions', header: 'Actions' },
  ];
  checkIfAllRefunds() {
    Object.keys(this.selectedMatchedRefunds).forEach((key: any) => {
      if (!this.selectedMatchedRefunds[key]) {
        this.isSelectAllRefunds = false;
      }
    });
  }
  checkIfSelectedPageRefunds() {
    let selected: boolean = true;
    this.MatchedRefunds.getValue().content.forEach((element: any) => {
      if (!this.selectedMatchedRefunds[element.id]) {
        selected = false;
      }
    });
    return selected;
  }
  selectAllRefunds() {
    if (this.isSelectAllRefunds) {
      this.unSelectAllRefunds();
      return;
    }
    this.isSelectAllRefunds = true;
    this.selectPageRefunds();
  }
  unSelectAllRefunds() {
    this.isSelectAllRefunds = false;
    this.selectedMatchedRefunds = [];
  }
  selectPageRefunds(justPage?: any) {
    if (justPage) {
      this.isSelectAllRefunds = false;
    }
    this.MatchedRefunds.getValue().content.forEach((element: any) => {
      this.selectedMatchedRefunds[element.id] = true;
    });
  }
  unSelectPageRefunds() {
    this.isSelectAllRefunds = false;
    this.MatchedRefunds.getValue().content.forEach((element: any) => {
      this.selectedMatchedRefunds[element.id] = false;
    });
  }
  getSelectedMatchedRefunds() {
    let selected: any[] = [];
    Object.keys(this.selectedMatchedRefunds).forEach((element: any) => {
      if (this.selectedMatchedRefunds[element]) {
        selected.push({ id: element });
      }
    });
    return selected;
  }
  getSelectedRowsCountMatchedRefunds() {
    let len: number = 0;
    Object.keys(this.selectedMatchedRefunds).forEach((element: any) => {
      if (this.selectedMatchedRefunds[element]) {
        len++;
      }
    });
    return len;
  }
  confirmRefunds() {
    if (this.isSelectAllRefunds) {
      this.uploadStatementService
        .confirmAllTransactions(
          this.route.snapshot.params['id'],
          'matchedTransactionsClientRefunds'
        )
        .subscribe({
          next: (response) => {
            this.notifications.notifySuccess(response);
            this.getTableMatchedRefunds();
          },
        });
      return;
    }
    this.uploadStatementService
      .confirmTransactions(this.getSelectedMatchedRefunds())
      .subscribe({
        next: (response) => {
          this.notifications.notifySuccess(response);
          this.getTableMatchedRefunds();
        },
      });
  }
  checkIfAllOther() {
    Object.keys(this.selectedOther).forEach((key: any) => {
      if (!this.selectedOther[key]) {
        this.isSelectAllOther = false;
      }
    });
  }
  checkIfSelectedPageOther() {
    let selected: boolean = true;
    this.Other.getValue().content.forEach((element: any) => {
      if (!this.selectedOther[element.id]) {
        selected = false;
      }
    });
    return selected;
  }
  selectAllOther() {
    if (this.isSelectAllOther) {
      this.unSelectAllOther();
      return;
    }
    this.isSelectAllOther = true;
    this.selectPageOther();
  }
  unSelectAllOther() {
    this.isSelectAllOther = false;
    this.selectedOther = [];
  }
  selectPageOther(justPage?: any) {
    if (justPage) {
      this.isSelectAllOther = false;
    }
    this.Other.getValue().content.forEach((element: any) => {
      this.selectedOther[element.id] = true;
    });
  }
  unSelectPageOther() {
    this.isSelectAllOther = false;
    this.Other.getValue().content.forEach((element: any) => {
      this.selectedOther[element.id] = false;
    });
  }
  getSelectedMatchedTransOther() {
    let selected: any[] = [];
    Object.keys(this.selectedOther).forEach((element: any) => {
      if (this.selectedOther[element]) {
        selected.push({ id: element });
      }
    });
    return selected;
  }
  getSelectedRowsCountOther() {
    let len: number = 0;
    Object.keys(this.selectedOther).forEach((element: any) => {
      if (this.selectedOther[element]) {
        len++;
      }
    });
    return len;
  }
  confirmOther() {
    if (this.isSelectAllOther) {
      this.uploadStatementService
        .confirmAllTransactions(
          this.route.snapshot.params['id'],
          'otherExpenses'
        )
        .subscribe({
          next: (response) => {
            this.notifications.notifySuccess(response);
            this.getTableOther();
          },
        });
      return;
    }
    this.uploadStatementService
      .confirmTransactions(this.getSelectedMatchedTransOther())
      .subscribe({
        next: (response) => {
          this.notifications.notifySuccess(response);
          this.getTableOther();
        },
      });
  }
  checkIfAllBucket() {
    Object.keys(this.selectedBucket).forEach((key: any) => {
      if (!this.selectedBucket[key]) {
        this.isSelectAllBucket = false;
      }
    });
  }
  checkIfSelectedPageBucket() {
    let selected: boolean = true;
    this.Bucket.getValue().content.forEach((element: any) => {
      if (!this.selectedBucket[element.id]) {
        selected = false;
      }
    });
    return selected;
  }
  selectAllBucket() {
    if (this.isSelectAllBucket) {
      this.unSelectAllBucket();
      return;
    }
    this.isSelectAllBucket = true;
    this.selectPageBucket();
  }
  unSelectAllBucket() {
    this.isSelectAllBucket = false;
    this.selectedBucket = [];
  }
  selectPageBucket(justPage?: any) {
    if (justPage) {
      this.isSelectAllBucket = false;
    }
    this.Bucket.getValue().content.forEach((element: any) => {
      this.selectedBucket[element.id] = true;
    });
  }
  unSelectPageBucket() {
    this.isSelectAllBucket = false;
    this.Bucket.getValue().content.forEach((element: any) => {
      this.selectedBucket[element.id] = false;
    });
  }
  getSelectedMatchedTransBucket() {
    let selected: any[] = [];
    Object.keys(this.selectedBucket).forEach((element: any) => {
      if (this.selectedBucket[element]) {
        selected.push({ id: element });
      }
    });
    return selected;
  }
  getSelectedRowsCountBucket() {
    let len: number = 0;
    Object.keys(this.selectedBucket).forEach((element: any) => {
      if (this.selectedBucket[element]) {
        len++;
      }
    });
    return len;
  }
  confirmBucket() {
    if (this.isSelectAllBucket) {
      this.uploadStatementService
        .confirmAllTransactions(
          this.route.snapshot.params['id'],
          'bucketRefills'
        )
        .subscribe({
          next: (response) => {
            this.notifications.notifySuccess(response);
            this.getTableBucket();
          },
        });
      return;
    }
    this.uploadStatementService
      .confirmTransactions(this.getSelectedMatchedTransBucket())
      .subscribe({
        next: (response) => {
          this.notifications.notifySuccess(response);
          this.getTableBucket();
        },
      });
  }
  checkIfAllMaidsAtExpense() {
    Object.keys(this.selectedMaidsAtExpense).forEach((key: any) => {
      if (!this.selectedMaidsAtExpense[key]) {
        this.isSelectAllMaidsAtExpense = false;
      }
    });
  }
  checkIfSelectedPageMaidsAtExpense() {
    let selected: boolean = true;
    this.MaidsAtExpense.getValue().content.forEach((element: any) => {
      if (!this.selectedMaidsAtExpense[element.id]) {
        selected = false;
      }
    });
    return selected;
  }
  selectAllMaidsAtExpense() {
    if (this.isSelectAllMaidsAtExpense) {
      this.unSelectAllMaidsAtExpense();
      return;
    }
    this.isSelectAllMaidsAtExpense = true;
    this.selectPageMaidsAtExpense();
  }
  unSelectAllMaidsAtExpense() {
    this.isSelectAllMaidsAtExpense = false;
    this.selectedMaidsAtExpense = [];
  }
  selectPageMaidsAtExpense(justPage?: any) {
    if (justPage) {
      this.isSelectAllMaidsAtExpense = false;
    }
    this.MaidsAtExpense.getValue().content.forEach((element: any) => {
      this.selectedMaidsAtExpense[element.id] = true;
    });
  }
  unSelectPageMaidsAtExpense() {
    this.isSelectAllMaidsAtExpense = false;
    this.MaidsAtExpense.getValue().content.forEach((element: any) => {
      this.selectedMaidsAtExpense[element.id] = false;
    });
  }
  getSelectedMatchedTransMaidsAtExpense() {
    let selected: any[] = [];
    Object.keys(this.selectedMaidsAtExpense).forEach((element: any) => {
      if (this.selectedMaidsAtExpense[element]) {
        selected.push({ id: element });
      }
    });
    return selected;
  }
  getSelectedRowsCountMaidsAtExpense() {
    let len: number = 0;
    Object.keys(this.selectedMaidsAtExpense).forEach((element: any) => {
      if (this.selectedMaidsAtExpense[element]) {
        len++;
      }
    });
    return len;
  }
  confirmMaidsAtExpense() {
    if (this.isSelectAllMaidsAtExpense) {
      this.uploadStatementService
        .confirmAllTransactions(
          this.route.snapshot.params['id'],
          'maidsAtExpense'
        )
        .subscribe({
          next: (response) => {
            this.notifications.notifySuccess(response);
            this.getTableMaidsAtExpense();
          },
        });
      return;
    }
    this.uploadStatementService
      .confirmTransactions(this.getSelectedMatchedTransMaidsAtExpense())
      .subscribe({
        next: (response) => {
          this.notifications.notifySuccess(response);
          this.getTableMaidsAtExpense();
        },
      });
  }
  exportMatchedTransactionsClientRefunds() {
    let url = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/expensesTransactionsCSV?gridType=matchedTransactionsClientRefunds`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }
  exportAlreadyMatchedTransactionsClientRefunds() {
    let url = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/expensesTransactionsCSV?gridType=alreadyMatchedTransactionsClientRefunds`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }
  exportUnmatchedTransactionsClientRefunds() {
    let url = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/expensesTransactionsCSV?gridType=unmatchedTransactionsClientRefunds`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }
  exportOtherExpenses() {
    let url = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/expensesTransactionsCSV?gridType=otherExpenses`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }
  exportAlreadyMatchedOtherExpenses() {
    let url = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/expensesTransactionsCSV?gridType=alreadyMatchedOtherExpenses`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }
  exportBucketRefills() {
    let url = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/expensesTransactionsCSV?gridType=bucketRefills`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }
  exportAlreadyMatchedBucketRefills() {
    let url = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/expensesTransactionsCSV?gridType=alreadyMatchedBucketRefills`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }
  exportUnmatchedTransactions() {
    let url = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/expensesTransactionsCSV?gridType=unmatchedTransactions`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }
  exportManuallyMatchedTransactions() {
    let url = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/expensesTransactionsCSV?gridType=manuallyMatchedTransactions`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }
  exportMaidsAtExpenseExpenses() {
    let url = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/expensesTransactionsCSV?gridType=maidsAtExpense`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }
  exportAlreadyMatchedMaidsAtExpenseExpenses() {
    let url = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/expensesTransactionsCSV?gridType=alreadyMatchedMaidsAtExpense`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }
  exportUnmatchedMaidsAtExpenseExpenses() {
    let url = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/expensesTransactionsCSV?gridType=unmatchedMaidsAtExpense`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }
  linkToPayment(element: any) {
    if (element.reason === 'Payment Not Found') {
      this.ccDialog
        .originalOpen(LinkToPaymentComponent, {
          data: {
            id: element.id,
            contract: element.contract,
          },
        })
        .afterClosed()
        .subscribe((res: any) => {
          if (res) {
            this.getAllData();
          }
        });
    } else if (element.reason === 'Contract Not Found') {
      this.ccDialog
        .originalOpen(LinkToContractPaymentComponent, {
          data: {
            id: element.id,
            contract: element.contract,
          },
        })
        .afterClosed()
        .subscribe((res: any) => {
          if (res) {
            this.getAllData();
          }
        });
    }
  }
  addNote(element: any) {
    this.ccDialog
      .originalOpen(AddNoteComponent, {
        data: {
          id: element.id,
          note: element.note,
        },
      })
      .afterClosed()
      .subscribe((res: any) => {
        if (res) {
          this.getAllData();
        }
      });
  }
  addTransaction(id: number) {
    this.ccDialog
      .originalOpen(AddUndefinedManuallyTransactionComponent, {
        data: { id },
      })
      .afterClosed()
      .subscribe((res: any) => {
        if (res) {
          this.getAllData();
        }
      });
  }
}
