<div class="acc-8152">
  <cc-dialog-header>
    <h1 cc-dialog-title>Link To Payment</h1>
    <a
      role="button"
      type="button"
      cc-icon-button
      cc-dialog-close-button
      cc-dialog-close
    ></a
  ></cc-dialog-header>
  <cc-dialog-content>
    <form [formGroup]="formGroup">
      <cc-select
        [lazyPageFetcher]="clientsOptions"
        label="Family"
        formControlName="client"
        [required]="true"
      ></cc-select>
      <cc-select
        [data]="contractsOptions"
        label="Contract"
        formControlName="contract"
        [required]="true"
      ></cc-select>
    </form>
    <div *ngIf="payments?.length > 0">
      <cc-radio-group [(ngModel)]="selectedPayment">
        <cc-datagrid
          [data]="payments"
          [columns]="gridCols"
          [cellTemplate]="{ pay: pay }"
        ></cc-datagrid>
        <ng-template #pay let-row let-index="index" let-col="colDef">
          <cc-radio-button [value]="row.id"></cc-radio-button>
        </ng-template>
      </cc-radio-group>
    </div>
  </cc-dialog-content>
  <cc-dialog-actions>
    <button cc-raised-button cc-dialog-close>Close</button>
    <button
      cc-raised-button
      color="accent"
      (click)="linkToPayment()"
      [disabled]="!formGroup.valid || !selectedPayment"
    >
      Submit
    </button>
  </cc-dialog-actions>
</div>
