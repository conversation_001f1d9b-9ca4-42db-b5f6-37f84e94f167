import { Component, Inject, OnInit } from '@angular/core';
import { CCDialogRef } from '@maids/cc-lib/dialog';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { PaginationRequest } from '@maids/cc-lib/common';
import { Observable, tap } from 'rxjs';
import { SelectOption } from '@maids/cc-lib/select-input';
import { UploadStatementService } from '../../services/upload-statement.service';
import { FormBuilder } from '@angular/forms';
import { CCGridColumn } from '@maids/cc-lib/datagrid';

@Component({
  selector: 'app-link-to-contract-payment',
  templateUrl: './link-to-contract-payment.component.html',
  styleUrls: ['./link-to-contract-payment.component.scss'],
})
export class LinkToContractPaymentComponent implements OnInit {
  formGroup = this.formBuilder.group({
    client: [null],
    contract: [null],
  });
  payments: any | null = null;
  selectedPayment: any | null = null;
  gridCols: CCGridColumn[] = [
    { field: 'pay', header: '' },
    { field: 'dateOfPayment', header: 'Payment Date' },
    { field: 'amountOfPayment', header: 'Payment Amount' },
    { field: 'id', header: 'Payment ID' },
    { field: 'directDebitFile.applicationId', header: 'DD Application Id' },
  ];
  readonly clientsOptions = (
    pageReq: PaginationRequest
  ): Observable<SelectOption[]> => {
    return this.uploadStatementService.USclientsOptions(pageReq.searchString);
  };
  contractsOptions: any[] = [];

  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private ccDialogRef: CCDialogRef<LinkToContractPaymentComponent>,
    private uploadStatementService: UploadStatementService,
    private formBuilder: FormBuilder
  ) {}

  ngOnInit(): void {
    this.formGroup.controls['client'].valueChanges.subscribe((val) => {
      if (val) {
        this.uploadStatementService
          .UScontractsOptions(val)
          .subscribe((res: any) => {
            this.contractsOptions = res;
          });
      }
    });
    this.formGroup.controls['contract'].valueChanges.subscribe((val) => {
      if (val) {
        this.uploadStatementService
          .USpdcByContract(val)
          .subscribe((res: any) => {
            this.payments = res;
          });
      }
    });
  }

  linkToPayment() {
    this.uploadStatementService
      .matchPaymentToTransaction(this.data.id, this.selectedPayment)
      .subscribe((res: any) => {
        this.ccDialogRef.close(true);
      });
  }
}
