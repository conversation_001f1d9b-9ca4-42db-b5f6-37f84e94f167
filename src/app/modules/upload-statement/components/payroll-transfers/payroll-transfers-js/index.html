<style>
    .opacity2 {
        opacity: 0.7;
        cursor: not-allowed;
    }
    .vertical-align-middle{
        vertical-align: middle !important;
    }
</style>
<div class="row w3-margin-0" ng-if="processingInfo.processing && processingInfo.pageName == 'PayrollTransfers'">
    <div class="col-md-8 col-md-offset-2">
        <h4>Processing...</h4>
        <div class="progress" style="height: 20px; border-radius: 20px;">
            <div ng-style="myStyle" class="progress-bar progress-bar-striped bg-info" role="progressbar" aria-valuenow="{{processingInfo.processedRecords}}" aria-valuemin="0" aria-valuemax="{{processingInfo.totalRecords}}">{{ (processingInfo.processedRecords / processingInfo.totalRecords)*100 | number: 1}} %</div>
        </div>
    </div>
</div>

<div class="panel-group">
    <div class="row mb-2">
        <div class="col-md-3" style="font-weight: 500;">
            <div>
                <span>Total Transactions: </span>
                <span class="bold w3-text-red">{{summary.payrollTransferTab.total }}</span>
            </div>
        </div>
        <div class="col-md-3" style="font-weight: 500;">
            <div>
                <span>Total Resolved:</span>
                <span class="bold w3-text-red">AED {{summary.payrollTransferTab.AED_RESOLVED }}</span>
            </div>
        </div>
        <div class="col-md-3" style="font-weight: 500;">
            <div>
                <span>Total Unresolved:</span>
                <span class="bold w3-text-red">AED {{summary.payrollTransferTab.AED_UNRESOLVED }}</span>
            </div>
        </div>
    </div>
    <div class="tabs">
        <div class="tab">
            <input class="acc" id="matched-transactions" type="checkbox">
            <label ng-click="toggleReport('matched-transactions')" class="tab-label dark-green"
                   for="matched-transactions">Matched Transactions ({{model.recordCountMatchedTransactions}})</label>
            <div class="tab-content">
                <div class="panel panel-default light_grey">
                    <div class="panel-body">
                        <div class="container-fluid add-content">
                            <div class="row">
                                <div class="col-md-3" style="font-weight: 500;padding-top: 16px">
                                    <div>
                                        <span>Total Transactions: </span>
                                        <span class="bold w3-text-red">{{model.recordCountMatchedTransactions}}</span>
                                    </div>
                                </div>
                                <div class="col-md-3" style="font-weight: 500;padding-top: 16px">
                                    <div>
                                        <span>Total Amount:</span>
                                        <span class="bold w3-text-red">AED {{model.totalAmountMatchedTransactions}}</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="pull-right">
                                        <button class="btn btn-primary btn-raised" ng-disabled="model.recordCountMatchedTransactions == 0" ng-click="selectAllTransactions()">
                                            {{isSelectAllTransactions ? 'Unselect All' : 'Select All'}}
                                        </button>
                                        <button class="btn btn-success btn-raised" ng-disabled="model.recordCountMatchedTransactions == 0" ng-click="checkIfSelectedPage()?unSelectPageTransactions():selectPageTransactions(true)">
                                            {{checkIfSelectedPage() ? 'Unselect' : 'Select'}} Page 
                                        </button>
                                        <button class="btn btn-primary btn-raised" ng-click="confirmSelectedTransactions()"
                                        ng-disabled="getSelectedRowsCount()<=0">
                                        Confirm Selected ({{getSelectedRowsCount()}})
                                        </button>
                                        <button class="btn btn-success btn-raised"
                                                ng-click="exportMatchedTransactions()" ng-disabled="getSelectedRowsCount()<=0">
                                            Export
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class='row w3-margin-0'>
                                <magna-data-grid config="matchedTransactionsGrid"></magna-data-grid>
                                <magna-pagination
                                        config="matchedTransactionsGridPagination"></magna-pagination>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="tab">
            <input class="acc" checked id="already-matched-transactions" type="checkbox">
            <label ng-click="toggleReport('already-matched-transactions')" class="tab-label dark-green"
                   for="already-matched-transactions">Already Matched Transactions ({{model.recordCountAlreadyMatchedTransactions}})</label>
            <div class="tab-content">
                <div class="panel panel-default light_grey">
                    <div class="panel-body">
                        <div class="container-fluid add-content">
                            <div class="row">
                                <div class="col-md-3" style="font-weight: 500;padding-top: 16px">
                                    <div>
                                        <span>Total Transactions: </span>
                                        <span class="bold w3-text-red">{{model.recordCountAlreadyMatchedTransactions}}</span>
                                    </div>
                                </div>
                                <div class="col-md-3" style="font-weight: 500;padding-top: 16px">
                                    <div>
                                        <span>Total Amount:</span>
                                        <span class="bold w3-text-red">AED {{model.totalAmountAlreadyMatchedTransactions}}</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="pull-right">
                                        <button class="btn btn-success btn-raised"
                                                ng-click="exportAlreadyMatchedTransactions()">
                                            Export
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class='row w3-margin-0'>
                                <magna-data-grid config="alreadyMatchedTransactionsGrid"></magna-data-grid>
                                <magna-pagination
                                        config="alreadyMatchedTransactionsGridPagination"></magna-pagination>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="tab">
            <input class="acc" id="unmatched-transactions" type="checkbox">
            <label ng-click="toggleReport('unmatched-transactions')" class="tab-label dark-green"
                   for="unmatched-transactions">Unmatched Transactions ({{model.recordCountUnmatchedTransactions}})</label>
            <div class="tab-content">
                <div class="panel panel-default light_grey">
                    <div class="panel-body">
                        <div class="container-fluid add-content">
                            <div class="row">
                                <div class="col-md-3" style="font-weight: 500;padding-top: 16px">
                                    <div>
                                        <span>Total Transactions: </span>
                                        <span class="bold w3-text-red">{{model.recordCountUnmatchedTransactions}}</span>
                                    </div>
                                </div>
                                <div class="col-md-3" style="font-weight: 500;padding-top: 16px">
                                    <div>
                                        <span>Total Amount:</span>
                                        <span class="bold w3-text-red">AED {{model.totalAmountUnmatchedTransactions}}</span>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="pull-right">
                                        <button class="btn btn-success btn-raised"
                                                ng-click="exportUnmatchedTransactions()">
                                            Export
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class='row w3-margin-0'>
                                <magna-data-grid config="unmatchedTransactionsGrid"></magna-data-grid>
                                <magna-pagination
                                        config="unmatchedTransactionsGridPagination"></magna-pagination>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="add-note-modal">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button aria-hidden="true" class="close" data-dismiss="modal" type="button">×</button>
                <h3 class="text-center">Add Note</h3>
            </div>
            <div class="modal-body add-content">
                <form class="form-horizontal">
                    <div class="form-group">
                        <label class="control-label col-md-3 required-label">Note:</label>
                        <div class="col-md-9">
                            <textarea class="form-control" ng-model="addNotePopup.note" rows="5"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-default" data-dismiss="modal" type="button">Cancel</button>
                <button class="btn btn-primary" ng-click="addNotePopup.submit()" type="button">Save</button>
            </div>
        </div>
    </div>
</div>
