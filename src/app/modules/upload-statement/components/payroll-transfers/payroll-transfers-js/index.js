mainApp.registerCtrl(
  "profile-tab-ctrl",
  function (
    $scope,
    $route,
    $rootScope,
    $routeParams,
    $location,
    $filter,
    $window,
    $timeout,
    magnaMainService,
    magnaHttpService,
    __env,
    maidccService,
    magnaValidationService,
    $compile
  ) {
    $scope.maidccService = maidccService;
    $scope.bankStatment = {};
    $scope.bankStatment.id = $routeParams.id;
    $scope.page_options = $route.current.$$route.page_options;
    $scope.pageUrl = $scope.page_options.page_url;
    $scope.model = {
      recordCountMatchedTransactions: 0,
      totalAmountMatchedTransactions: 0,
      recordCountAlreadyMatchedTransactions: 0,
      totalAmountAlreadyMatchedTransactions: 0,
      recordCountUnmatchedTransactions: 0,
      totalAmountUnmatchedTransactions: 0,
      selectedTransactions: [],
    };

    $scope.currentPageMatched = 0;
    $scope.currentPageAlreadyMatched = 0;
    $scope.currentPageUnmatched = 0;
    $scope.isSelectAllTransactions = false;

    $scope.processingInfo = {
      processing: false,
      pageName: "",
      processedRecords: 0,
      totalRecords: 0,
    };
    $scope.$watch("summary.isResolved", function (newVal) {
      if (newVal) {
        $scope.getTableDataMatched(0);
        $scope.getTableDataAlreadyMatched(0);
        $scope.getTableDataUnmatched(0);
        magnaMainService.Timing.interval(
          $scope,
          function () {
            $scope.refresh = true;
          },
          60000
        );
      }
    });
    $scope.$watch("refresh", function (newVal) {
      if (newVal) {
        $scope.getTableDataMatched(0);
        $scope.getTableDataAlreadyMatched(0);
        $scope.getTableDataUnmatched(0);
        $scope.getProcessingInfo();
      }
    });

    $scope.myStyle = {
      width: "0%",
    };

    $scope.$watch("processingInfo.processedRecords", function (newVal, oldVal) {
      if (newVal && $scope.processingInfo.totalRecords) {
        $scope.myStyle.width =
          (newVal / $scope.processingInfo.totalRecords) * 100 + "%";
      }
    });
    $scope.getProcessingInfo = function () {
      magnaHttpService.HttpWrapper(
        {
          method: "GET",
          url:
            __env.ACCOUNTING +
            `bankStatementFile/${$scope.bankStatment.id}/getProcessSummary`,
        },
        function (response) {
          console.log("progressing info", response);
          let tempProcessing = $scope.processingInfo.processing;
          $scope.processingInfo = response;
          $scope.processingInfo.pageName = "PayrollTransfers";
          $scope.myStyle = {
            "min-width":
              $scope.processingInfo.processedRecords === 0 ? "2em" : "",
            width:
              ($scope.processingInfo.processedRecords /
                $scope.processingInfo.totalRecords) *
                100 +
              "%",
          };
          if (
            tempProcessing &&
            $scope.processingInfo.totalRecords ===
              $scope.processingInfo.processedRecords
          ) {
            $scope.processingInfo.processing = true;
            $scope.myStyle = {
              "min-width":
                $scope.processingInfo.processedRecords === 0 ? "2em" : "",
              width: "100%",
            };
            $scope.processingInfo.totalRecords = 100;
            $scope.processingInfo.processedRecords = 100;
            setTimeout(() => {
              $scope.processingInfo.processing = false;
              $scope.processingInfo.totalRecords = 0;
              $scope.processingInfo.processedRecords = 0;
              $scope.myStyle = {
                "min-width":
                  $scope.processingInfo.processedRecords === 0 ? "2em" : "",
                width: "0%",
              };
            }, 5000);
          }
          $(".progress-bar").css(
            "width",
            ($scope.processingInfo.processedRecords /
              $scope.processingInfo.totalRecords) *
              100 +
              "%"
          );
        },
        { needs_loading_icon: false }
      );
    };
    // Matched Transactions
    $scope.getTableDataMatched = function (pageNo) {
      $scope.currentPageMatched = pageNo;
      magnaHttpService.HttpWrapper(
        {
          method: "GET",
          url:
            __env.ACCOUNTING +
            "bankStatementFile/" +
            $scope.bankStatment.id +
            "/getPayrollTransfers?gridType=matched&page=" +
            pageNo +
            "&size=50",
        },
        function (response) {
          $scope.matchedTransactionsGrid.data = response.content;
          $scope.matchedTransactionsGridPagination.paginationInfo = response;
          $scope.model.recordCountMatchedTransactions = response.totalElements;
          $scope.model.totalAmountMatchedTransactions =
            maidccService.getAmountLabel(response.totalSum);

          if ($scope.isSelectAllTransactions) {
            $scope.selectPageTransactions();
          }

          $timeout(function () {
            $.material.checkbox();
          });
        },
        { needs_loading_icon: true }
      );
    };

    $scope.matchedTransactionsGrid = {
      columns: [
        {
          label: "Select",
          type: "html",
          valueExp: function ($data, $index) {
            var html =
              `<div class="checkbox">
                            <label>
                                <input type="checkbox" ng-change="checkIfAllTransactions()" ng-model="model.selectedTransactions['id_` +
              $data.id +
              `']" ng-disabled="` +
              $data.resolved +
              `" >
                                <span class="checkbox-material"></span>
                            </label>
                        </div>`;
            return $compile(html)($scope);
          },
        },
        {
          label: "#",
          type: "html",
          valueExp: function ($data, index) {
            return index + $scope.currentPageMatched * 50;
          },
        },
        {
          label: "Amount",
          type: "text",
          valueExp: function ($data) {
            return maidccService.getAmountLabel($data.transactionAmount);
          },
        },
        {
          label: "Employee Name",
          type: "text",
          valueExp: function ($data) {
            return $data.employeeName || "";
          },
        },
        {
          label: "Expense",
          type: "text",
          valueExp: function ($data) {
            return $data.expenseType || "";
          },
        },
        {
          label: "From Bucket",
          type: "text",
          valueExp: function ($data) {
            return $data.fromBucket || "";
          },
        },
        {
          label: "Transaction Description",
          type: "text",
          valueExp: function ($data) {
            return $data.description || "";
          },
        },
        {
          label: "Payment Details",
          type: "text",
          valueExp: function ($data) {
            return $data.paymentDetail || "";
          },
        },
      ],
      data: [],
    };

    $scope.matchedTransactionsGridPagination = {
      paginationInfo: {},
      submitFunction: function (pageNo) {
        $scope.getTableDataMatched(pageNo);
      },
    };

    // Already Matched Transactions
    $scope.getTableDataAlreadyMatched = function (pageNo) {
      $scope.currentPageAlreadyMatched = pageNo;
      magnaHttpService.HttpWrapper(
        {
          method: "GET",
          url:
            __env.ACCOUNTING +
            "bankStatementFile/" +
            $scope.bankStatment.id +
            "/getPayrollTransfers?gridType=alreadyMatched&page=" +
            pageNo +
            "&size=50",
        },
        function (response) {
          $scope.alreadyMatchedTransactionsGrid.data = response.content;
          $scope.alreadyMatchedTransactionsGridPagination.paginationInfo =
            response;
          $scope.model.recordCountAlreadyMatchedTransactions =
            response.totalElements;
          $scope.model.totalAmountAlreadyMatchedTransactions =
            maidccService.getAmountLabel(response.totalSum);

          $timeout(function () {
            $.material.checkbox();
          });
        },
        { needs_loading_icon: true }
      );
    };

    $scope.alreadyMatchedTransactionsGrid = {
      columns: [
        {
          label: "#",
          type: "html",
          valueExp: function ($data, index) {
            return index + $scope.currentPageAlreadyMatched * 50;
          },
        },
        {
          label: "Transaction Date",
          type: "text",
          valueExp: function ($data) {
            return $data.date.split(" ")[0];
          },
        },
        {
          label: "Amount",
          type: "text",
          valueExp: function ($data) {
            return maidccService.getAmountLabel($data.transactionAmount);
          },
        },
        {
          label: "Employee Name",
          type: "text",
          valueExp: function ($data) {
            return $data.employeeName || "";
          },
        },
        {
          label: "Expense",
          type: "text",
          valueExp: function ($data) {
            return $data.expenseType || "";
          },
        },
        {
          label: "From Bucket",
          type: "text",
          valueExp: function ($data) {
            return $data.fromBucket || "";
          },
        },
        {
          label: "Transaction Description",
          type: "text",
          valueExp: function ($data) {
            return $data.description || "";
          },
        },
        {
          label: "Payment Details",
          type: "text",
          valueExp: function ($data) {
            return $data.paymentDetail || "";
          },
        },
        {
          label: "Matched Type",
          type: "text",
          valueExp: function ($data) {
            return $data.bankTransactionMatchType || "";
          },
        },
      ],
      data: [],
    };

    $scope.alreadyMatchedTransactionsGridPagination = {
      paginationInfo: {},
      submitFunction: function (pageNo) {
        $scope.getTableDataAlreadyMatched(pageNo);
      },
    };

    // Unmatched Transactions
    $scope.getTableDataUnmatched = function (pageNo) {
      $scope.currentPageUnmatched = pageNo;
      magnaHttpService.HttpWrapper(
        {
          method: "GET",
          url:
            __env.ACCOUNTING +
            "bankStatementFile/" +
            $scope.bankStatment.id +
            "/getPayrollTransfers?gridType=unMatched&page=" +
            pageNo +
            "&size=50",
        },
        function (response) {
          $scope.unmatchedTransactionsGrid.data = response.content;
          $scope.unmatchedTransactionsGridPagination.paginationInfo = response;
          $scope.model.recordCountUnmatchedTransactions =
            response.totalElements;
          $scope.model.totalAmountUnmatchedTransactions =
            maidccService.getAmountLabel(response.totalSum);

          $timeout(function () {
            $.material.checkbox();
          });
        },
        { needs_loading_icon: true }
      );
    };

    $scope.unmatchedTransactionsGrid = {
      columns: [
        {
          label: "#",
          type: "html",
          valueExp: function ($data, index) {
            return index + $scope.currentPageUnmatched * 50;
          },
        },
        {
          label: "Amount",
          type: "text",
          valueExp: function ($data) {
            return maidccService.getAmountLabel($data.transactionAmount);
          },
        },
        {
          label: "Beneficiary Name",
          type: "text",
          valueExp: function ($data) {
            return $data.beneficiaryName || "";
          },
        },
        {
          label: "Unmatched Reason",
          type: "text",
          valueExp: function ($data) {
            return $data.reason || "";
          },
        },
        {
          label: "Transaction Description",
          type: "text",
          valueExp: function ($data) {
            return $data.description || "";
          },
        },
        {
          label: "Payment Details",
          type: "text",
          valueExp: function ($data) {
            return $data.paymentDetail || "";
          },
        },
        {
          label: "Notes",
          type: "text",
          valueExp: function ($data) {
            return $data.note || "";
          },
        },
        {
          label: "Actions",
          type: "html",
          valueExp: function ($data) {
            // Escape single quotes in the note to prevent AngularJS parsing errors
            var escapedNote = $data.note ? $data.note.replace(/'/g, "\\'") : "";
            var html = `<button class="btn btn-default btn-raised mx-2" ng-click="addNote('${$data.id}','${escapedNote}')" > Add Note </button>`;
            html += `<button class="btn btn-primary btn-raised mx-2" ng-click="addTransaction('${$data.id}')" > Add Transaction </button>`;
            return $compile(html)($scope);
          },
        },
      ],
      data: [],
    };

    $scope.unmatchedTransactionsGridPagination = {
      paginationInfo: {},
      submitFunction: function (pageNo) {
        $scope.getTableDataUnmatched(pageNo);
      },
    };

    // Export functions
    $scope.exportMatchedTransactions = function () {
      let url =
        __env.ACCOUNTING +
        "/bankStatementFile/" +
        $scope.bankStatment.id +
        "/getPayrollTransfersCSV?gridType=matched";
      if ($scope.isSelectAllTransactions) {
        magnaHttpService.downloadFile(url, { method: "POST" });
        return;
      }
      let selectedTransactions = $scope
        .getSelectedTransactions()
        .map(function (item) {
          return item.id;
        });
      magnaHttpService.downloadFile(url, {
        method: "POST",
        data: selectedTransactions,
      });
    };

    $scope.exportAlreadyMatchedTransactions = function () {
      let url =
        __env.ACCOUNTING +
        "/bankStatementFile/" +
        $scope.bankStatment.id +
        "/getPayrollTransfersCSV?gridType=alreadyMatched";
      magnaHttpService.downloadFile(url, { method: "POST" });
    };

    $scope.exportUnmatchedTransactions = function () {
      let url =
        __env.ACCOUNTING +
        "/bankStatementFile/" +
        $scope.bankStatment.id +
        "/getPayrollTransfersCSV?gridType=unMatched";
      magnaHttpService.downloadFile(url, { method: "POST" });
    };

    // Selection functions for matched transactions
    $scope.getSelectedTransactions = function () {
      var selected = [];
      var keys = Object.keys($scope.model.selectedTransactions);
      angular.forEach(keys, function (item) {
        if ($scope.model.selectedTransactions[item]) {
          selected.push({ id: item.replace("id_", "") });
        }
      });
      return selected;
    };
    $scope.getSelectedTransactionsToExport = function () {
      var selected = [];
      var keys = Object.keys($scope.model.selectedTransactions);
      angular.forEach(keys, function (item) {
        if ($scope.model.selectedTransactions[item]) {
          selected.push(item.replace("id_", ""));
        }
      });
      return selected;
    };
    $scope.getSelectedRowsCount = function () {
      if ($scope.isSelectAllTransactions) {
        return $scope.model.recordCountMatchedTransactions;
      } else {
        var len = 0;
        var keys = Object.keys($scope.model.selectedTransactions);
        angular.forEach(keys, function (item) {
          if ($scope.model.selectedTransactions[item]) {
            len++;
          }
        });
        return len;
      }
    };

    $scope.checkIfAllTransactions = function () {
      var keys = Object.keys($scope.model.selectedTransactions);
      angular.forEach(keys, function (item) {
        if (!$scope.model.selectedTransactions[item]) {
          $scope.isSelectAllTransactions = false;
        }
      });
    };

    $scope.checkIfSelectedPage = function () {
      var selected = true;
      $.each($scope.matchedTransactionsGrid.data, function (index, item) {
        if (!$scope.model.selectedTransactions["id_" + item.id])
          selected = false;
      });
      return selected;
    };

    $scope.unSelectPageTransactions = function () {
      $scope.isSelectAllTransactions = false;
      $.each($scope.matchedTransactionsGrid.data, function (index, item) {
        $scope.model.selectedTransactions["id_" + item.id] = false;
      });
      $timeout(function () {
        $.material.checkbox();
      });
    };

    $scope.selectPageTransactions = function (justPage) {
      if (justPage) {
        $scope.isSelectAllTransactions = false;
      }
      $.each($scope.matchedTransactionsGrid.data, function (index, item) {
        if (!item.resolved) {
          $scope.model.selectedTransactions["id_" + item.id] = true;
        }
      });
      $timeout(function () {
        $.material.checkbox();
      });
    };

    $scope.selectAllTransactions = function () {
      if ($scope.isSelectAllTransactions) {
        $scope.unSelectAllTransactions();
        return;
      }
      $scope.isSelectAllTransactions = true;
      $scope.selectPageTransactions();
    };

    $scope.unSelectAllTransactions = function () {
      $scope.isSelectAllTransactions = false;
      $scope.model.selectedTransactions = [];
      $timeout(function () {
        $.material.checkbox();
      });
    };

    $scope.confirmSelectedTransactions = function () {
      var selectedTransactions = $scope.getSelectedTransactions();

      if (selectedTransactions.length === 0) {
        magnaMainService.DialogBox.showWarningMsg(
          "Please select at least one transaction."
        );
        return;
      }
      if ($scope.isSelectAllTransactions) {
        $scope.confirmAllTransactions();
        return;
      }
      magnaMainService.DialogBox.showWarningMsg(
        "You have selected " +
          selectedTransactions.length +
          ' transactions for confirmation. Once confirmed, these transactions will be automatically added to the ERP and moved to the "Already Matched Transactions - Overseas" grid. Are you sure you want to proceed?',
        function () {
          magnaHttpService.HttpWrapper(
            {
              method: "POST",
              url: __env.ACCOUNTING + "bankStatementFile/confirmTransactions",
              data: selectedTransactions,
            },
            function (response) {
              magnaMainService.DialogBox.showSuccessMsg(response);
              $scope.unSelectAllTransactions();
              $scope.getTableDataMatched($scope.currentPageMatched);
              $scope.getTableDataAlreadyMatched(0);
              $scope.refresh = true;
            },
            { needs_loading_icon: true }
          );
        }
      );
    };
    $scope.confirmAllTransactions = function () {
      magnaMainService.DialogBox.showWarningMsg(
        "You have selected All transactions for confirmation. Once confirmed, these transactions will be automatically added to the ERP and moved to the Already Matched Transactions - Overseas grid. Are you sure you want to proceed?",
        function () {
          magnaHttpService.HttpWrapper(
            {
              method: "GET",
              url:
                __env.ACCOUNTING +
                "bankStatementFile/confirmAllTransactions/" +
                $scope.bankStatment.id +
                "/matchedPayrollTransfersTransactions",
            },
            function (response) {
              magnaMainService.DialogBox.showSuccessMsg(response);
              $scope.unSelectAllTransactions();
              $scope.getTableDataMatched($scope.currentPageMatched);
              $scope.getTableDataAlreadyMatched(0);
              $scope.refresh = true;
            },
            { needs_loading_icon: true }
          );
        }
      );
    };
    $scope.addTransaction = function (id) {
      var selectedRow = $scope.unmatchedTransactionsGrid.data.find(
        (item) => item.id == id
      );
      $window.localStorage.setItem(
        "payroll-transfer",
        JSON.stringify({ ...selectedRow, fileId: $scope.bankStatment.id })
      );
      $location.path("/accounting/add-edit-transactions");
    };
    $scope.addNote = function (id, note) {
      // Unescape the note before passing it to the modal
      var unescapedNote = note ? note.replace(/\\'/g, "'") : "";
      $scope.addNotePopup.openModal(id, unescapedNote);
    };
    $scope.addNotePopup = {
      note: "",
      transaction: "",
      reset: function () {
        this.note = "";
        this.transaction = "";
      },
      openModal: function (transaction, note) {
        this.reset();
        this.transaction = transaction;
        this.note = note;
        magnaMainService.DialogBox.showModal($(`#add-note-modal`));
      },
      submit: function () {
        $scope.refresh = false;
        // Encode the note to handle special characters in the URL
        var encodedNote = encodeURIComponent(this.note);
        magnaHttpService.HttpWrapper(
          {
            method: "POST",
            url:
              __env.ACCOUNTING +
              "bankStatementFile/addNoteToTransaction?transactionId=" +
              this.transaction +
              "&note=" +
              encodedNote,
          },
          function (response) {
            $(`#add-note-modal`).modal("hide");
            $scope.refresh = true;
          },
          { needs_loading_icon: true }
        );
      },
    };
    $scope.$on("$includeContentLoaded", function () {
      $scope.getProcessingInfo();
    });
  }
);
