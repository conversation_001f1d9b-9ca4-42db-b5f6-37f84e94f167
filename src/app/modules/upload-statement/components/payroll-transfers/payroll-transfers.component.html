<app-proccessing-bar
  *ngIf="summary && !summary.isResolved"
></app-proccessing-bar>

<div class="row mb-2 align-items-center" *ngIf="summary && summary.isResolved">
  <div class="col-md-3" style="font-weight: 500">
    <div>
      <span class="pr-1">Total Transactions: </span>
      <span class="bold cc-secondary">{{ summary.payrollTransferTab?.total | number }}</span>
    </div>
  </div>
  <div class="col-md-3" style="font-weight: 500">
    <div>
      <span class="pr-1">Total Resolved:</span>
      <span class="bold cc-secondary"
        >AED {{ summary.payrollTransferTab?.AED_RESOLVED }}</span
      >
    </div>
  </div>
  <div class="col-md-3" style="font-weight: 500">
    <div>
      <span class="pr-1">Total Unresolved:</span>
      <span class="bold cc-secondary"
        >AED {{ summary.payrollTransferTab?.AED_UNRESOLVED }}</span
      >
    </div>
  </div>
  <div class="col-md-3">
    <button
      cc-raised-button
      color="accent"
      (click)="getAllData()"
      style="padding-left: 33px"
    >
      <cc-icon class="icon">restart_alt </cc-icon>Refresh Transactions
    </button>
  </div>
</div>

<div class="row my-2">
  <cc-accordion class="w-100">
    <cc-panel>
      <cc-panel-title>
        <b>
          Matched Transactions ({{ recordCountMatchedTransactions | number }})</b
        ></cc-panel-title
      >
      <cc-panel-body>
        <div class="row mb-2 align-items-center">
          <div class="col-md-3" style="font-weight: 500">
            <div>
              <span class="pr-1">Total Transactions: </span>
              <span class="bold cc-secondary">{{
                recordCountMatchedTransactions | number
              }}</span>
            </div>
          </div>
          <div class="col-md-3" style="font-weight: 500">
            <div>
              <span class="pr-1">Total Amount:</span>
              <span class="bold cc-secondary"
                >AED {{ totalAmountMatchedTransactions | number }}</span
              >
            </div>
          </div>
          <div class="col-md-6">
            <div class="row justify-content-end">
              <div class="col-md-auto mt-1">
                <button
                  cc-raised-button
                  (click)="
                    checkIfSelectedPageTransactions()
                      ? unSelectPageTransactions()
                      : selectPageTransactions(true)
                  "
                  [disabled]="recordCountMatchedTransactions == 0"
                >
                  {{ checkIfSelectedPageTransactions() ? "Unselect" : "Select" }}
                  Page
                </button>
              </div>
              <div class="col-md-auto mt-1">
                <button
                  cc-raised-button
                  (click)="selectAllTransactions()"
                  [disabled]="recordCountMatchedTransactions == 0"
                >
                  {{ isSelectAllTransactions ? "Unselect" : "Select" }} All ({{
                    recordCountMatchedTransactions
                  }})
                </button>
              </div>
              <div class="col-md-auto mt-1">
                <button
                  cc-raised-button
                  color="accent"
                  [disabled]="getSelectedRowsCount() <= 0"
                  (click)="exportMatchedTransactions()"
                >
                  Export
                </button>
              </div>
              <div class="col-md-auto mt-1">
                <button
                  cc-raised-button
                  (click)="confirmSelectedTransactions()"
                  [disabled]="getSelectedRowsCount() <= 0"
                >
                  Confirm Selected ({{ getSelectedRowsCount() }})
                </button>
              </div>
            </div>
          </div>
        </div>
        <cc-datagrid
          *ngIf="MatchedTransactions$ | async as records"
          [data]="records.content"
          [columns]="matchedTransactionsColumns"
          [length]="records.totalElements"
          [pageOnFront]="false"
          [pageIndex]="records.number"
          [pageSize]="records.size"
          [pageSizeOptions]="[10, 25, 50]"
          (page)="getNextMatched($event)"
          [stickyHeader]="true"
          [showColumnMenuButton]="true"
          [showColumnMenuHeader]="false"
          [columnMenuButtonIcon]="'settings'"
          [columnMovable]="true"
          [cellTemplate]="{ select: selectMatched, rowNum: rowNumMatched }"
        ></cc-datagrid>
        <ng-template #selectMatched let-row let-index="index" let-col="colDef">
          <cc-checkbox
            [disabled]="row.resolved"
            [ngModel]="selectedMatchedTransactions[row.id]"
            (ngModelChange)="onCheckboxChange(row.id, $event)"
          ></cc-checkbox>
        </ng-template>
        <ng-template #rowNumMatched let-row let-index="index" let-col="colDef">
          {{ index + currentPageMatched * matchedSize + 1 }}
        </ng-template>
      </cc-panel-body>
    </cc-panel>
  </cc-accordion>

  <cc-accordion class="w-100">
    <cc-panel>
      <cc-panel-title>
        <b>
          Already Matched Transactions ({{
            recordCountAlreadyMatchedTransactions | number
          }})</b
        ></cc-panel-title
      >
      <cc-panel-body>
        <div class="row mb-2 align-items-center">
          <div class="col-md-3" style="font-weight: 500">
            <div>
              <span class="pr-1">Total Transactions: </span>
              <span class="bold cc-secondary">{{
                recordCountAlreadyMatchedTransactions | number
              }}</span>
            </div>
          </div>
          <div class="col-md-3" style="font-weight: 500">
            <div>
              <span class="pr-1">Total Amount:</span>
              <span class="bold cc-secondary"
                >AED {{ totalAmountAlreadyMatchedTransactions | number }}</span
              >
            </div>
          </div>
          <div class="col-md-6">
            <div class="d-flex justify-content-end">
              <div class="col-md-auto px-0 mt-1">
                <button
                  cc-raised-button
                  color="accent"
                  (click)="exportAlreadyMatchedTransactions()"
                >
                  Export
                </button>
              </div>
            </div>
          </div>
        </div>
        <cc-datagrid
          *ngIf="AlreadyMatchedTransactions$ | async as records"
          [data]="records.content"
          [columns]="alreadyMatchedTransactionsColumns"
          [length]="records.totalElements"
          [pageOnFront]="false"
          [pageIndex]="records.number"
          [pageSize]="records.size"
          [pageSizeOptions]="[10, 25, 50]"
          (page)="getNextAlreadyMatched($event)"
          [stickyHeader]="true"
          [showColumnMenuButton]="true"
          [showColumnMenuHeader]="false"
          [columnMenuButtonIcon]="'settings'"
          [columnMovable]="true"
          [cellTemplate]="{ rowNum: rowNumAlreadyMatched }"
        ></cc-datagrid>
        <ng-template #rowNumAlreadyMatched let-row let-index="index" let-col="colDef">
          {{ index + currentPageAlreadyMatched * alreadyMatchedSize + 1 }}
        </ng-template>
      </cc-panel-body>
    </cc-panel>
  </cc-accordion>

  <cc-accordion class="w-100">
    <cc-panel>
      <cc-panel-title>
        <b>
          Unmatched Transactions ({{ recordCountUnmatchedTransactions | number }})</b
        ></cc-panel-title
      >
      <cc-panel-body>
        <div class="row mb-2 align-items-center">
          <div class="col-md-3" style="font-weight: 500">
            <div>
              <span class="pr-1">Total Transactions: </span>
              <span class="bold cc-secondary">{{
                recordCountUnmatchedTransactions | number
              }}</span>
            </div>
          </div>
          <div class="col-md-3" style="font-weight: 500">
            <div>
              <span class="pr-1">Total Amount:</span>
              <span class="bold cc-secondary"
                >AED {{ totalAmountUnmatchedTransactions | number }}</span
              >
            </div>
          </div>
          <div class="col-md-6">
            <div class="d-flex justify-content-end">
              <div class="col-md-auto px-0 mt-1">
                <button
                  cc-raised-button
                  color="accent"
                  (click)="exportUnmatchedTransactions()"
                >
                  Export
                </button>
              </div>
            </div>
          </div>
        </div>
        <cc-datagrid
          *ngIf="UnmatchedTransactions$ | async as records"
          [data]="records.content"
          [columns]="unmatchedTransactionsColumns"
          [length]="records.totalElements"
          [pageOnFront]="false"
          [pageIndex]="records.number"
          [pageSize]="records.size"
          [pageSizeOptions]="[10, 25, 50]"
          (page)="getNextUnmatched($event)"
          [stickyHeader]="true"
          [showColumnMenuButton]="true"
          [showColumnMenuHeader]="false"
          [columnMenuButtonIcon]="'settings'"
          [columnMovable]="true"
          [cellTemplate]="{ rowNum: rowNumUnmatched, actions: actionsUnmatched }"
        ></cc-datagrid>
        <ng-template #rowNumUnmatched let-row let-index="index" let-col="colDef">
          {{ index + currentPageUnmatched * unmatchedSize + 1 }}
        </ng-template>
        <ng-template #actionsUnmatched let-row let-index="index" let-col="colDef">
          <div class="d-flex justify-content-center">
            <div class="col-md-auto">
              <button cc-raised-button (click)="addNote(row)">
                Add Note
              </button>
            </div>
            <div class="col-md-auto">
              <button cc-raised-button (click)="addTransaction(row.id)">
                Add Transaction
              </button>
            </div>
          </div>
        </ng-template>
      </cc-panel-body>
    </cc-panel>
  </cc-accordion>
</div>
