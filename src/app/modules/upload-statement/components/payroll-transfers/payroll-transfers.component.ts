import { Component, OnInit } from '@angular/core';
import { UploadStatementService } from '../../services/upload-statement.service';
import { BehaviorSubject, Observable } from 'rxjs';
import { PageableResponseModel } from '@maids/cc-lib/common';
import { ActivatedRoute, Router } from '@angular/router';
import { CCDialog } from '@maids/cc-lib/dialog';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { PageEvent } from '@angular/material/paginator';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { AddNoteComponent } from '../add-note/add-note.component';

@Component({
  selector: 'app-payroll-transfers',
  templateUrl: './payroll-transfers.component.html',
  styleUrls: ['./payroll-transfers.component.scss'],
})
export class PayrollTransfersComponent implements OnInit {
  summary: any;

  // Pagination properties
  currentPageMatched: number = 0;
  currentPageAlreadyMatched: number = 0;
  currentPageUnmatched: number = 0;

  matchedSize: number =50;
  alreadyMatchedSize: number = 50;
  unmatchedSize: number = 50;

  // Record counts and totals
  recordCountMatchedTransactions: number = 0;
  totalAmountMatchedTransactions: number = 0;
  recordCountAlreadyMatchedTransactions: number = 0;
  totalAmountAlreadyMatchedTransactions: number = 0;
  recordCountUnmatchedTransactions: number = 0;
  totalAmountUnmatchedTransactions: number = 0;

  // Selection properties
  selectedMatchedTransactions: any[] = [];
  isSelectAllTransactions: boolean = false;

  // Observable data streams
  private MatchedTransactions = new BehaviorSubject<PageableResponseModel<any>>(
    {
      content: [],
      number: 0,
      size: 0,
      totalElements: 0,
      totalPages: 0,
    }
  );
  MatchedTransactions$: Observable<PageableResponseModel<any>> =
    this.MatchedTransactions.asObservable();

  private AlreadyMatchedTransactions = new BehaviorSubject<
    PageableResponseModel<any>
  >({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  AlreadyMatchedTransactions$: Observable<PageableResponseModel<any>> =
    this.AlreadyMatchedTransactions.asObservable();

  private UnmatchedTransactions = new BehaviorSubject<
    PageableResponseModel<any>
  >({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  UnmatchedTransactions$: Observable<PageableResponseModel<any>> =
    this.UnmatchedTransactions.asObservable();

  constructor(
    private uploadStatementService: UploadStatementService,
    private route: ActivatedRoute,
    private router: Router,
    public readonly notifications: CCNotificationService,
    private mediaService: MediaService,
    private ccDialog: CCDialog
  ) {}

  ngOnInit(): void {
    this.uploadStatementService.summaryObject.subscribe((res) => {
      this.summary = res;
    });
    this.getAllData();
  }

  getAllData() {
    this.getTableDataMatched();
    this.getTableDataAlreadyMatched();
    this.getTableDataUnmatched();
  }

  // Matched Transactions
  getTableDataMatched(page: number = 0, size: number = 50) {
    this.uploadStatementService
      .getPayrollTransfers(
        this.route.snapshot.params['id'],
        'matched',
        page,
        size
      )
      .subscribe((res: any) => {
        this.MatchedTransactions.next(res);
        this.recordCountMatchedTransactions = res.totalElements;
        this.totalAmountMatchedTransactions = res.totalSum;

        if (this.isSelectAllTransactions) {
          this.selectPageTransactions();
        }
      });
  }

  getNextMatched(event: PageEvent) {
    this.getTableDataMatched(event.pageIndex, event.pageSize);
    this.currentPageMatched = event.pageIndex;
    this.matchedSize = event.pageSize;
  }

  // Already Matched Transactions
  getTableDataAlreadyMatched(page: number = 0, size: number = 50) {
    this.uploadStatementService
      .getPayrollTransfers(
        this.route.snapshot.params['id'],
        'alreadyMatched',
        page,
        size
      )
      .subscribe((res: any) => {
        this.AlreadyMatchedTransactions.next(res);
        this.recordCountAlreadyMatchedTransactions = res.totalElements;
        this.totalAmountAlreadyMatchedTransactions = res.totalSum;
      });
  }

  getNextAlreadyMatched(event: PageEvent) {
    this.getTableDataAlreadyMatched(event.pageIndex, event.pageSize);
    this.currentPageAlreadyMatched = event.pageIndex;
    this.alreadyMatchedSize = event.pageSize;
  }

  // Unmatched Transactions
  getTableDataUnmatched(page: number = 0, size: number = 50) {
    this.uploadStatementService
      .getPayrollTransfers(
        this.route.snapshot.params['id'],
        'unMatched',
        page,
        size
      )
      .subscribe((res: any) => {
        this.UnmatchedTransactions.next(res);
        this.recordCountUnmatchedTransactions = res.totalElements;
        this.totalAmountUnmatchedTransactions = res.totalSum;
      });
  }

  getNextUnmatched(event: PageEvent) {
    this.getTableDataUnmatched(event.pageIndex, event.pageSize);
    this.currentPageUnmatched = event.pageIndex;
    this.unmatchedSize = event.pageSize;
  }

  // Grid column definitions
  matchedTransactionsColumns: CCGridColumn[] = [
    { field: 'select', header: 'Select' },
    { field: 'rowNum', header: '#' },
    {
      field: 'transactionAmount',
      header: 'Amount',
      formatter: (rowData) => rowData.transactionAmount?.toFixed(2) || '0.00',
    },
    { field: 'employeeName', header: 'Employee Name' },
    { field: 'expenseType', header: 'Expense' },
    { field: 'fromBucket', header: 'From Bucket' },
    { field: 'description', header: 'Transaction Description' },
    { field: 'paymentDetail', header: 'Payment Details' },
  ];

  alreadyMatchedTransactionsColumns: CCGridColumn[] = [
    { field: 'rowNum', header: '#' },
    {
      field: 'date',
      header: 'Transaction Date',
      formatter: (rowData) => rowData.date?.split(' ')[0],
    },
    {
      field: 'transactionAmount',
      header: 'Amount',
      formatter: (rowData) => rowData.transactionAmount?.toFixed(2) || '0.00',
    },
    { field: 'employeeName', header: 'Employee Name' },
    { field: 'expenseType', header: 'Expense' },
    { field: 'fromBucket', header: 'From Bucket' },
    { field: 'description', header: 'Transaction Description' },
    { field: 'paymentDetail', header: 'Payment Details' },
    { field: 'bankTransactionMatchType', header: 'Matched Type' },
  ];

  unmatchedTransactionsColumns: CCGridColumn[] = [
    { field: 'rowNum', header: '#' },
    {
      field: 'transactionAmount',
      header: 'Amount',
      formatter: (rowData) => rowData.transactionAmount?.toFixed(2) || '0.00',
    },
    { field: 'beneficiaryName', header: 'Beneficiary Name' },
    { field: 'reason', header: 'Unmatched Reason' },
    { field: 'description', header: 'Transaction Description' },
    { field: 'paymentDetail', header: 'Payment Details' },
    { field: 'note', header: 'Notes' },
    { field: 'actions', header: 'Actions' },
  ];

  // Selection methods for matched transactions
  checkIfSelectedPageTransactions() {
    // If "Select All" is active, the page button should always show "Select Page"
    if (this.isSelectAllTransactions) {
      return false;
    }
    
    let selected = true;
    this.MatchedTransactions.getValue().content.forEach((element: any) => {
      if (!element.resolved && !this.selectedMatchedTransactions[element.id]) {
        selected = false;
      }
    });
    return selected;
  }

  selectPageTransactions(justPage?: boolean) {
    if (justPage) {
      // Clear all previous selections when selecting just the page
      this.isSelectAllTransactions = false;
      this.selectedMatchedTransactions = [];
    }
    this.MatchedTransactions.getValue().content.forEach((element: any) => {
      if (!element.resolved) {
        this.selectedMatchedTransactions[element.id] = true;
      }
    });
  }

  unSelectPageTransactions() {
    // If we're unselecting items while "Select All" is active, clear the select all state
    if (this.isSelectAllTransactions) {
      this.isSelectAllTransactions = false;
    }
    this.MatchedTransactions.getValue().content.forEach((element: any) => {
      this.selectedMatchedTransactions[element.id] = false;
    });
  }

  selectAllTransactions() {
    if (this.isSelectAllTransactions) {
      this.unSelectAllTransactions();
      return;
    }
    // Clear any previous page selections when selecting all
    this.selectedMatchedTransactions = [];
    this.isSelectAllTransactions = true;
    this.selectPageTransactions();
  }

  unSelectAllTransactions() {
    this.isSelectAllTransactions = false;
    this.selectedMatchedTransactions = [];
  }

  getSelectedRowsCount() {
    if (this.isSelectAllTransactions) {
      return this.recordCountMatchedTransactions;
    } else {
      let len = 0;
      Object.keys(this.selectedMatchedTransactions).forEach((element: any) => {
        if (this.selectedMatchedTransactions[element]) {
          len++;
        }
      });
      return len;
    }
  }

  getSelectedTransactions() {
    let selected: any[] = [];
    Object.keys(this.selectedMatchedTransactions).forEach((element: any) => {
      if (this.selectedMatchedTransactions[element]) {
        selected.push({ id: element });
      }
    });
    return selected;
  }

  // Handle individual checkbox changes
  onCheckboxChange(transactionId: any, isChecked: boolean) {
    this.selectedMatchedTransactions[transactionId] = isChecked;
    
    // If "Select All" is active and user unchecks an item, turn off "Select All"
    if (this.isSelectAllTransactions && !isChecked) {
      this.isSelectAllTransactions = false;
    }
  }

  // Confirmation methods
  confirmSelectedTransactions() {
    const selectedTransactions = this.getSelectedTransactions();

    if (selectedTransactions.length === 0 && !this.isSelectAllTransactions) {
      this.notifications.notifyError('Please select at least one transaction.');
      return;
    }

    if (this.isSelectAllTransactions) {
      this.confirmAllTransactions();
      return;
    }
    this.ccDialog.confirm(
      '',
      `You have selected ${selectedTransactions.length} transactions for confirmation. Once confirmed, these transactions will be automatically added to the ERP and moved to the "Already Matched Transactions" grid. Are you sure you want to proceed?`,
      () => {
        this.uploadStatementService
          .confirmTransactions(selectedTransactions)
          .subscribe({
            next: (response) => {
              this.notifications.notifySuccess(response);
              this.unSelectAllTransactions();
              this.getTableDataMatched(this.currentPageMatched);
              this.getTableDataAlreadyMatched(0);
            },
          });
      }
    );
  }

  confirmAllTransactions() {
    this.ccDialog.confirm(
      '',
      `You have selected All transactions for confirmation. Once confirmed, these transactions will be automatically added to the ERP and moved to the "Already Matched Transactions" grid. Are you sure you want to proceed?`,
      () => {
        this.uploadStatementService
          .confirmAllTransactions(
            this.route.snapshot.params['id'],
            'matchedPayrollTransfersTransactions'
          )
          .subscribe({
            next: (response) => {
              this.notifications.notifySuccess(response);
              this.unSelectAllTransactions();
              this.getTableDataMatched(this.currentPageMatched);
              this.getTableDataAlreadyMatched(0);
            },
          });
      }
    );
  }

  // Export methods
  exportMatchedTransactions() {
    let url = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/getPayrollTransfersCSV?gridType=matched`;
    if (this.isSelectAllTransactions) {
      this.mediaService.downloadFile(url, '', { method: 'POST' });
      return;
    }
    let selected = this.getSelectedTransactions().map((item) => item.id);
    this.mediaService.downloadFile(url, '', { method: 'POST', body: selected });
  }

  exportAlreadyMatchedTransactions() {
    let url = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/getPayrollTransfersCSV?gridType=alreadyMatched`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }

  exportUnmatchedTransactions() {
    let url = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/getPayrollTransfersCSV?gridType=unMatched`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }

  // Action methods for unmatched transactions
  addNote(element: any) {
    this.ccDialog
      .originalOpen(AddNoteComponent, {
        data: {
          id: element.id,
          note: element.note,
        },
      })
      .afterClosed()
      .subscribe((res: any) => {
        if (res) {
          this.getAllData();
        }
      });
  }

  addTransaction(id: number) {
    // Store transaction data in localStorage as per original implementation
    const selectedRow = this.UnmatchedTransactions.getValue().content.find(
      (item) => item.id == id
    );

    if (selectedRow) {
      localStorage.setItem(
        'payroll-transfer',
        JSON.stringify({
          ...selectedRow,
          fileId: this.route.snapshot.params['id'],
        })
      );
      this.router.navigate(['/accounting/v2/manage-transactions/add-edit-transactions']);
    }
  }
}
