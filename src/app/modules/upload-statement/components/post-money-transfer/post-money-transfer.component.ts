import { Component, Inject, Input, OnInit } from '@angular/core';
import { UploadStatementService } from '../../services/upload-statement.service';
import { HttpClient } from '@angular/common/http';
import { ActivatedRoute } from '@angular/router';
import { CCBackendEndpoint } from '@maids/cc-erp-services';
import { CCDialog } from '@maids/cc-lib/dialog';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { BehaviorSubject, Observable } from 'rxjs';
import { PageableResponseModel } from '@maids/cc-lib/common';
import { PageEvent } from '@angular/material/paginator';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import * as moment from 'moment';
import { ConfirmReplenishmentComponent } from '../confirm-replenishment/confirm-replenishment.component';
import { ConfirmExpenseComponent } from '../confirm-expense/confirm-expense.component';
import { TitleCasePipe } from '@angular/common';
@Component({
  selector: 'app-post-money-transfer',
  templateUrl: './post-money-transfer.component.html',
  styleUrls: ['./post-money-transfer.component.scss'],
  providers: [TitleCasePipe],
})
export class PostMoneyTransferComponent implements OnInit {
  summary: any;
  Transactions = new BehaviorSubject<PageableResponseModel<any>>({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  Transactions$: Observable<PageableResponseModel<any>> =
    this.Transactions.asObservable();
  constructor(
    private uploadStatementService: UploadStatementService,
    private http: HttpClient,
    @Inject(CCBackendEndpoint) private _api: string,
    private route: ActivatedRoute,
    public readonly notifications: CCNotificationService,
    private mediaService: MediaService,
    private ccDialog: CCDialog,
    private titleCasePipe: TitleCasePipe
  ) {}

  ngOnInit(): void {
    this.uploadStatementService.summaryObject.subscribe((res) => {
      this.summary = res;
    });
    this.getTransactions();
  }
  getTransactions(page: number = 0, size: number = 50) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/bankTransferTransactions`,
        {
          params: {
            gridType: 'bankTransferMatchedTransaction',
            page,
            size,
          },
        }
      )
      .subscribe((res: any) => {
        this.Transactions.next(res);
      });
  }
  getNextPage(event: PageEvent) {
    this.getTransactions(event.pageIndex, event.pageSize);
  }
  gridCols: CCGridColumn[] = [
    {
      field: 'transactionDate',
      header: 'Date',
      formatter(rowData, colDef) {
        let td = moment(rowData.date).format('YYYY-MM-DD');
        return rowData.transaction.id
          ? `<a class="cc-secondary" href="#!/accounting/add-edit-transactions/${rowData.transaction.id}">${td}</a>`
          : td;
      },
      width:'150px'
    },
    {
      field: 'transactionAmount',
      header: 'Amount',
      formatter(rowData, colDef) {
        return rowData.transactionAmount ? rowData.transactionAmount.toFixed(2) : 0;
      },
    },
    { field: 'description', header: 'Description' },
    {
      field: 'expense',
      header: 'Expense',
      formatter(rowData, colDef) {
        return rowData.expense ? rowData.expense.label : '';
      },
    },
    {
      field: 'expensePaymentDate',
      header: 'Payment Date',
      formatter(rowData, colDef) {
        return rowData.expensePayment ? rowData.expensePayment.date : '';
      },
    },
    {
      field: 'expensePaymentBeneficiary',
      header: 'Beneficiary',
      formatter(rowData, colDef) {
        return rowData.expensePayment ? rowData.expensePayment.beneficiary : '';
      },
    },
    {
      field: 'bankTransactionType',
      header: 'Bank Transaction Type',
      formatter(rowData, colDef) {
        return rowData.bankTransactionType.split('_')[0].toUpperCase();
      },
    },
    {
      field: 'bankTransactionMatchType',
      header: 'Match Type'
    },
  ];
  reviewToConfirm(element: any) {
    let transaction: any;
    transaction = element;
    transaction.transactionAmount = element.transactionAmount;
    transaction.date = transaction.date
      ? moment(transaction.date).format('YYYY-MM-DD')
      : '';
    if (
      element.bankTransactionType &&
      element.bankTransactionType.split('_')[0] === 'REPLENISHMENT'
    ) {
      let replenishment: any;
      replenishment = element.expensePayment;
      replenishment.date = replenishment.date
        ? moment(replenishment.date).format('YYYY-MM-DD')
        : '';
      this.ccDialog
        .originalOpen(ConfirmReplenishmentComponent, {
          width: 'auto',
          data: {
            id: element.id,
            transaction,
            replenishment,
          },
        })
        .afterClosed()
        .subscribe((res: any) => {
          if (res) {
            this.getTransactions();
          }
        });
    } else if (
      element.bankTransactionType &&
      element.bankTransactionType.split('_')[0] === 'EXPENSE'
    ) {
      let expense: any;
      expense = element.expensePayment;
      expense.vatInvoiceFile = expense.attachments.find(
        (item: any) => item.tag === 'EXPENSE_PAYMENT_VAT_INVOICE'
      );
      if (expense.vatInvoiceFile == null) {
        expense.vatInvoiceFile = expense.attachments.find(
          (item: any) => item.tag === 'EXPENSE_REQUEST_VAT_INVOICE'
        );
      }
      expense.invoiceFile = expense.attachments.find(
        (item: any) => item.tag === 'EXPENSE_PAYMENT_INVOICE'
      );
      if (expense.invoiceFile == null) {
        expense.invoiceFile = expense.attachments.find(
          (item: any) => item.tag === 'EXPENSE_REQUEST_INVOICE'
        );
      }
      expense.date = expense.date
        ? moment(expense.date).format('YYYY-MM-DD')
        : '';
      this.ccDialog
        .originalOpen(ConfirmExpenseComponent, {
          width: 'auto',
          data: {
            id: element.id,
            transaction,
            expense,
          },
        })
        .afterClosed()
        .subscribe((res: any) => {
          if (res) {
            this.getTransactions();
          }
        });
    }
  }
}
