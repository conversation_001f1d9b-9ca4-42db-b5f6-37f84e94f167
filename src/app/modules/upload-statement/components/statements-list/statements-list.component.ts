import {
  AfterViewChecked,
  ChangeDetectorRef,
  Component,
  OnInit,
} from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { UploadStatementService } from '../../services/upload-statement.service';
import { BehaviorSubject, Observable } from 'rxjs';
import { PageableResponseModel } from '@maids/cc-lib/common';
import { PageEvent } from '@angular/material/paginator';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import * as moment from 'moment';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { Router } from '@angular/router';
import { CCDialog } from '@maids/cc-lib/dialog';
@Component({
  selector: 'app-statements-list',
  templateUrl: './statements-list.component.html',
  styleUrls: ['./statements-list.component.scss'],
})
export class StatementsListComponent implements OnInit, AfterViewChecked {
  uploadForm = this.formBuilder.group({
    bankStatement: [[]],
    creditCardStatement: [[]],
  });
  filterForm = this.formBuilder.group({
    uploadedFromDate: [''],
    uploadedToDate: [''],
    showDeletedFiles: [''],
  });
  constructor(
    private formBuilder: FormBuilder,
    private uploadStatementService: UploadStatementService,
    public readonly notifications: CCNotificationService,
    private mediaService: MediaService,
    private router: Router,
    private ccDialog: CCDialog,
    private cdr: ChangeDetectorRef
  ) {}

  private recordsSubject = new BehaviorSubject<PageableResponseModel<any>>({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });

  records$: Observable<PageableResponseModel<any>> =
    this.recordsSubject.asObservable();
  gridCols: CCGridColumn[] = [
    {
      field: 'creationDate',
      header: 'Upload Date',
      formatter(rowData, colDef) {
        return moment(rowData.creationDate).format('YYYY-MM-DD');
      },
      width: '15%',
    },
    {
      field: 'totalTransactions',
      header: 'Total Transactions',
      width: '12%',
    },
    {
      field: 'totalResolvedTransactions',
      header: 'Total Resolved Transactions',
      width: '12%',
    },
    {
      field: 'totalUnresolvedTransactions',
      header: 'Total Unresolved Transactions',
      width: '12%',
    },
    { field: 'fileName', header: 'File Name', width: '15%', },
  ];
  ngOnInit(): void {
    this.getFileStatement();
  }
  ngAfterViewChecked(): void {
    this.cdr.detectChanges();
  }
  getFileStatement(page: number = 0, size: number = 20) {
    let params: any;
    params = {
      sort: 'creationDate,desc',
      fromDate: this.filterForm.controls['uploadedFromDate'].value,
      toDate: this.filterForm.controls['uploadedToDate'].value,
      showDeletedFiles: this.filterForm.controls['showDeletedFiles'].value,
      page: page,
      size: size,
    };
    this.uploadStatementService.getFileStatement(params).subscribe((res) => {
      this.recordsSubject.next(res);
    });
  }
  getNextPage(event: PageEvent) {
    this.getFileStatement(event.pageIndex, event.pageSize);
  }
  delete(id: any) {
    this.ccDialog.confirm(
      '',
      'Are you sure you want to delete this statement?',
      () => {
        this.uploadStatementService
          .deleteFileStatement(id)
          .subscribe((res: any) => {
            this.notifications.notifySuccess('File Deleted Successfully!');
            this.getFileStatement();
          });
      }
    );
  }
  results(id: any) {
    this.router.navigateByUrl(
      `/accounting/v2/upload-statements/bank-statement-transactions-details/direct-debits/${id}`
    );
  }
  downloadAttachment(element: any) {
    if (element.attachments[0].uuid) {
      this.mediaService.downloadFile(
        `public/download/${element.attachments[0].uuid}`
      );
    }
  }
  proccess() {
    let attach: any[] = [];
    if (this.uploadForm.controls['bankStatement'].value.length > 0) {
      attach.push({
        id: this.uploadForm.controls['bankStatement'].value[0].id,
      });
    }
    if (this.uploadForm.controls['creditCardStatement'].value.length > 0) {
      attach.push({
        id: this.uploadForm.controls['creditCardStatement'].value[0].id,
      });
    }
    this.uploadStatementService
      .createFileStatement({
        attachments: attach,
        reportDate: moment().format('YYYY-MM-DD h:mm:ss'),
        resolved: false,
      })
      .subscribe((res: any) => {
        this.router.navigateByUrl(
          `/accounting/v2/upload-statements/bank-statement-transactions-details/direct-debits/${res}`
        );
      });
  }
}
