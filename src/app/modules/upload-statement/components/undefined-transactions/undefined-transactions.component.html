<div class="row mb-2 align-items-center" *ngIf="summary && summary.isResolved">
  <div class="col-md-3" style="font-weight: 500">
    <div>
      <span class="pr-1">Total Transactions: </span>
      <span class="bold cc-secondary">{{
        summary.UndefinedTransactionTab.total | number
      }}</span>
    </div>
  </div>
  <div class="col-md-3" style="font-weight: 500">
    <div>
      <span class="pr-1">Total Resolved:</span>
      <span class="bold cc-secondary"
        >AED {{ summary.UndefinedTransactionTab.AED_RESOLVED }}</span
      >
    </div>
  </div>
  <div class="col-md-3" style="font-weight: 500">
    <div>
      <span class="pr-1">Total Unresolved:</span>
      <span class="bold cc-secondary"
        >AED {{ summary.UndefinedTransactionTab.AED_UNRESOLVED }}</span
      >
    </div>
  </div>
  <div class="col-md-3">
    <button
      cc-raised-button
      color="accent"
      (click)="getAllData()"
      style="padding-left: 33px"
    >
      <cc-icon class="icon">restart_alt </cc-icon>Refresh Transactions
    </button>
  </div>
</div>
<cc-accordion class="w-100">
  <cc-panel>
    <cc-panel-title>
      <b>
        Unknown Cash Deposits (Credit Transactions) ({{
          recordCountCreditTransactions | number
        }})</b
      ></cc-panel-title
    >
    <cc-panel-body>
      <div class="row mb-2 align-items-center">
        <div class="col-md-3" style="font-weight: 500">
          <div>
            <span class="pr-1">Total Transactions: </span>
            <span class="bold cc-secondary">{{
              recordCountCreditTransactions | number
            }}</span>
          </div>
        </div>
        <div class="col-md-3" style="font-weight: 500">
          <div>
            <span class="pr-1">Total Amount:</span>
            <span class="bold cc-secondary"
              >AED {{ totalAmountCreditTransactions | number }}</span
            >
          </div>
        </div>
        <div class="col-md-6">
          <div class="d-flex justify-content-end">
            <div class="col-md-auto px-0 mt-1">
              <button
                cc-raised-button
                color="accent"
                (click)="exportCreditTransactionsCSV()"
              >
                Export
              </button>
            </div>
          </div>
        </div>
      </div>
      <cc-datagrid
        *ngIf="CreditTransactions$ | async as records"
        [data]="records.content"
        [columns]="creditCols"
        [length]="records.totalElements"
        [pageOnFront]="false"
        [pageIndex]="records.number"
        [pageSize]="records.size"
        [pageSizeOptions]="[10, 25, 50]"
        (page)="getNextCredit($event)"
        [stickyHeader]="true"
        [showColumnMenuButton]="true"
        [showColumnMenuHeader]="false"
        [columnMenuButtonIcon]="'settings'"
        [columnMovable]="true"
        [cellTemplate]="{ rowNum: rowNum1, action: action1 }"
      ></cc-datagrid>
      <ng-template #rowNum1 let-row let-index="index" let-col="colDef">
        {{ index + currentCreditPage * currentCreditSize + 1 }}
      </ng-template>
      <ng-template #action1 let-row let-index="index" let-col="colDef">
        <button cc-raised-button (click)="addTransaction(row.id)">
          Create Transaction
        </button>
      </ng-template>
    </cc-panel-body>
  </cc-panel>
</cc-accordion>
<cc-accordion>
  <cc-panel>
    <cc-panel-title>
      <b
        >Unknown Cash Deposits (Debit Transactions) ({{
          recordCountDebitTransactions | number
        }})</b
      >
    </cc-panel-title>
    <cc-panel-body>
      <div class="row mb-2 align-items-center">
        <div class="col-md-3" style="font-weight: 500">
          <div>
            <span class="pr-1">Total Transactions: </span>
            <span class="bold cc-secondary">{{
              recordCountDebitTransactions | number
            }}</span>
          </div>
        </div>
        <div class="col-md-3" style="font-weight: 500">
          <div>
            <span class="pr-1">Total Amount:</span>
            <span class="bold cc-secondary"
              >AED {{ totalAmountDebitTransactions | number }}</span
            >
          </div>
        </div>
        <div class="col-md-6">
          <div class="d-flex justify-content-end">
            <div class="col-md-auto px-0 mt-1">
              <button
                cc-raised-button
                color="accent"
                (click)="exportDebitTransactionsCSV()"
              >
                Export
              </button>
            </div>
          </div>
        </div>
      </div>
      <cc-datagrid
        *ngIf="DebitTransactions$ | async as records"
        [data]="records.content"
        [columns]="debitCols"
        [length]="records.totalElements"
        [pageOnFront]="false"
        [pageIndex]="records.number"
        [pageSize]="records.size"
        [pageSizeOptions]="[10, 25, 50]"
        (page)="getNextDebit($event)"
        [stickyHeader]="true"
        [showColumnMenuButton]="true"
        [showColumnMenuHeader]="false"
        [columnMenuButtonIcon]="'settings'"
        [columnMovable]="true"
        [cellTemplate]="{ rowNum: rowNum2, action: action2 }"
      ></cc-datagrid>
      <ng-template #rowNum2 let-row let-index="index" let-col="colDef">
        {{ index + currentDebitPage * currentDebitSize + 1 }}
      </ng-template>
      <ng-template #action2 let-row let-index="index" let-col="colDef">
        <button cc-raised-button (click)="addTransaction(row.id)">
          Create Transaction
        </button>
      </ng-template>
    </cc-panel-body>
  </cc-panel>
</cc-accordion>
<cc-accordion>
  <cc-panel>
    <cc-panel-title>
      <b>
        Manually Matched Transactions ({{
          recordCountManuallyMatchedTransactions | number
        }})</b
      ></cc-panel-title
    >
    <cc-panel-body>
      <div class="row mb-2 align-items-center">
        <div class="col-md-3" style="font-weight: 500">
          <div>
            <span class="pr-1">Total Transactions: </span>
            <span class="bold cc-secondary">{{
              recordCountManuallyMatchedTransactions | number
            }}</span>
          </div>
        </div>
        <div class="col-md-3" style="font-weight: 500">
          <div>
            <span class="pr-1">Total Amount:</span>
            <span class="bold cc-secondary"
              >AED {{ totalAmountManuallyMatchedTransactions | number }}</span
            >
          </div>
        </div>
        <div class="col-md-6">
          <div class="d-flex justify-content-end">
            <div class="col-md-auto px-0 mt-1">
              <button
                cc-raised-button
                color="accent"
                (click)="exportManuallyMatchedTransactionsCSV()"
              >
                Export
              </button>
            </div>
          </div>
        </div>
      </div>
      <cc-datagrid
        *ngIf="ManuallyMatchedTransactions$ | async as records"
        [data]="records.content"
        [columns]="manuallyMatchedCols"
        [length]="records.totalElements"
        [pageOnFront]="false"
        [pageIndex]="records.number"
        [pageSize]="records.size"
        [pageSizeOptions]="[10, 25, 50]"
        (page)="getNextManuallyMatched($event)"
        [stickyHeader]="true"
        [showColumnMenuButton]="true"
        [showColumnMenuHeader]="false"
        [columnMenuButtonIcon]="'settings'"
        [columnMovable]="true"
        [cellTemplate]="{ rowNum: rowNum3 }"
      ></cc-datagrid>
      <ng-template #rowNum3 let-row let-index="index" let-col="colDef">
        {{
          index + currentManuallyMatchedPage * currentManuallyMatchedSize + 1
        }}
      </ng-template>
    </cc-panel-body></cc-panel
  >
</cc-accordion>
