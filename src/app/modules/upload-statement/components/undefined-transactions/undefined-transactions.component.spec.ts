import { ComponentFixture, TestBed } from '@angular/core/testing';

import { UndefinedTransactionsComponent } from './undefined-transactions.component';

describe('UndefinedTransactionsComponent', () => {
  let component: UndefinedTransactionsComponent;
  let fixture: ComponentFixture<UndefinedTransactionsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ UndefinedTransactionsComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(UndefinedTransactionsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
