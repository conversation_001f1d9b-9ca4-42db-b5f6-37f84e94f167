import { Component, Inject, Input, OnInit } from '@angular/core';
import { UploadStatementService } from '../../services/upload-statement.service';
import { HttpClient } from '@angular/common/http';
import { ActivatedRoute } from '@angular/router';
import { CCBackendEndpoint } from '@maids/cc-erp-services';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { PageableResponseModel } from '@maids/cc-lib/common';
import { BehaviorSubject, Observable } from 'rxjs';
import { PageEvent } from '@angular/material/paginator';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { CCDialog } from '@maids/cc-lib/dialog';
import { AddUndefinedManuallyTransactionComponent } from '../add-undefined-manually-transaction/add-undefined-manually-transaction.component';
@Component({
  selector: 'app-undefined-transactions',
  templateUrl: './undefined-transactions.component.html',
  styleUrls: ['./undefined-transactions.component.scss'],
})
export class UndefinedTransactionsComponent implements OnInit {
  resolved: number = 0;
  unresolved: number = 0;
  summary: any | null = null;
  recordCountCreditTransactions: number = 0;
  totalAmountCreditTransactions: number = 0;
  recordCountDebitTransactions: number = 0;
  totalAmountDebitTransactions: number = 0;
  recordCountManuallyMatchedTransactions: number = 0;
  totalAmountManuallyMatchedTransactions: number = 0;
  currentCreditPage: number = 0;
  currentDebitPage: number = 0;
  currentManuallyMatchedPage: number = 0;
  currentCreditSize: number = 50;
  currentDebitSize: number = 50;
  currentManuallyMatchedSize: number = 50;
  CreditTransactions = new BehaviorSubject<PageableResponseModel<any>>({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  CreditTransactions$: Observable<PageableResponseModel<any>> =
    this.CreditTransactions.asObservable();
  DebitTransactions = new BehaviorSubject<PageableResponseModel<any>>({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  DebitTransactions$: Observable<PageableResponseModel<any>> =
    this.DebitTransactions.asObservable();
  ManuallyMatchedTransactions = new BehaviorSubject<PageableResponseModel<any>>(
    {
      content: [],
      number: 0,
      size: 0,
      totalElements: 0,
      totalPages: 0,
    }
  );
  ManuallyMatchedTransactions$: Observable<PageableResponseModel<any>> =
    this.ManuallyMatchedTransactions.asObservable();

  constructor(
    private uploadStatementService: UploadStatementService,
    private http: HttpClient,
    @Inject(CCBackendEndpoint) private _api: string,
    private route: ActivatedRoute,
    public readonly notifications: CCNotificationService,
    private mediaService: MediaService,
    private ccDialog: CCDialog
  ) {}

  ngOnInit(): void {
    this.uploadStatementService.summaryObject.subscribe((res: any) => {
      this.summary = res;
    });
    this.getAllData();
  }
  getAllData() {
    this.getCreditTransactions();
    this.getDebitTransactions();
    this.getManuallyMatchedTransactions();
  }
  getCreditTransactions(page: number = 0, size: number = 50) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/undefinedTransactions`,
        {
          params: {
            gridType: 'creditTransactions',
            page,
            size,
          },
        }
      )
      .subscribe((res: any) => {
        this.CreditTransactions.next(res);
        this.recordCountCreditTransactions = res.totalElements;
        this.totalAmountCreditTransactions = res.totalSum;
      });
  }
  getNextCredit(event: PageEvent) {
    this.currentCreditPage = event.pageIndex;
    this.currentCreditSize = event.pageSize;
    this.getCreditTransactions(this.currentCreditPage, this.currentCreditSize);
  }
  getDebitTransactions(page: number = 0, size: number = 50) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/undefinedTransactions`,
        {
          params: {
            gridType: 'debitTransactions',
            page,
            size,
          },
        }
      )
      .subscribe((res: any) => {
        this.DebitTransactions.next(res);
        this.recordCountDebitTransactions = res.totalElements;
        this.totalAmountDebitTransactions = res.totalSum;
      });
  }
  getNextDebit(event: PageEvent) {
    this.currentDebitPage = event.pageIndex;
    this.currentDebitSize = event.pageSize;
    this.getDebitTransactions(this.currentDebitPage, this.currentDebitSize);
  }
  getManuallyMatchedTransactions(page: number = 0, size: number = 50) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/undefinedTransactions`,
        {
          params: {
            gridType: 'manuallyMatchedTransactions',
            page,
            size,
          },
        }
      )
      .subscribe((res: any) => {
        this.ManuallyMatchedTransactions.next(res);
        this.recordCountManuallyMatchedTransactions = res.totalElements;
        this.totalAmountManuallyMatchedTransactions = res.totalSum;
      });
  }
  getNextManuallyMatched(event: PageEvent) {
    this.currentManuallyMatchedPage = event.pageIndex;
    this.currentManuallyMatchedSize = event.pageSize;
    this.getManuallyMatchedTransactions(
      this.currentManuallyMatchedPage,
      this.currentManuallyMatchedSize
    );
  }
  creditCols: CCGridColumn[] = [
    { field: 'rowNum', header: 'Row Number' },
    { field: 'date', header: 'Transaction Date' },
    { field: 'transactionAmount', header: 'Transaction Amount' },
    { field: 'paymentDetail', header: 'Payment Details' },
    { field: 'action', header: 'Action' },
  ];
  debitCols: CCGridColumn[] = [
    { field: 'rowNum', header: 'Row Number' },
    { field: 'date', header: 'Transaction Date' },
    { field: 'transactionAmount', header: 'Transaction Amount' },
    { field: 'paymentDetail', header: 'Payment Details' },
    { field: 'action', header: 'Action' },
  ];
  manuallyMatchedCols: CCGridColumn[] = [
    { field: 'rowNum', header: 'Row Number' },
    {
      field: 'transactionAmount',
      header: 'Amount',
      formatter(rowData, colDef) {
        return rowData.transactionAmount ? rowData.transactionAmount.toFixed(2) : 0;
      },
    },
    { field: 'expense', header: 'Expense Code' },
    { field: 'vatAmount', header: 'Vat Amount' },
    { field: 'vatType', header: 'Vat Type' },
    { field: 'description', header: 'Description' },
    { field: 'paymentDetail', header: 'Payment Details' },
  ];
  exportCreditTransactionsCSV() {
    let url = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/undefinedTransactionsCSV?gridType=creditTransactions`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }
  exportDebitTransactionsCSV() {
    let url = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/undefinedTransactionsCSV?gridType=debitTransactions`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }
  exportManuallyMatchedTransactionsCSV() {
    let url = `accounting/bankStatementFile/${this.route.snapshot.params['id']}/undefinedTransactionsCSV?gridType=manuallyMatchedTransactions`;
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }
  addTransaction(id: number) {
    this.ccDialog
      .originalOpen(AddUndefinedManuallyTransactionComponent, {
        data: { id },
      })
      .afterClosed()
      .subscribe((res: any) => {
        if (res) {
          this.getAllData();
        }
      });
  }
}
