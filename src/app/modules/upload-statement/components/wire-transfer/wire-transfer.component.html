<app-proccessing-bar
  *ngIf="summary && !summary.isResolved"
></app-proccessing-bar>
<div class="row mb-2 align-items-center" *ngIf="summary && summary.isResolved">
  <div class="col-md-3" style="font-weight: 500">
    <div>
      <span class="pr-1">Total Transactions: </span>
      <span class="bold cc-secondary">{{
        summary.CashDepositTab.total | number
      }}</span>
    </div>
  </div>
  <div class="col-md-3" style="font-weight: 500">
    <div>
      <span class="pr-1">Total Resolved:</span>
      <span class="bold cc-secondary"
        >AED {{ summary.CashDepositTab.AED_RESOLVED }}</span
      >
    </div>
  </div>
  <div class="col-md-3" style="font-weight: 500">
    <div>
      <span class="pr-1">Total Unresolved:</span>
      <span class="bold cc-secondary"
        >AED {{ summary.CashDepositTab.AED_UNRESOLVED }}</span
      >
    </div>
  </div>
  <div class="col-md-3">
    <button
      cc-raised-button
      color="accent"
      (click)="getAllData()"
      style="padding-left: 33px"
    >
      <cc-icon class="icon">restart_alt </cc-icon>Refresh Transactions
    </button>
  </div>
</div>
<cc-accordion>
  <cc-panel>
    <cc-panel-title>
      <b> Transactions - Unknown Cash Deposits ({{ recordCountCash }}) </b>
    </cc-panel-title>
    <cc-panel-body>
      <div class="row mb-2 align-items-center">
        <div class="col-md-3" style="font-weight: 500">
          <div>
            <span class="pr-1">Total Transactions: </span>
            <span class="bold cc-secondary">{{
              recordCountCash | number
            }}</span>
          </div>
        </div>
        <div class="col-md-3" style="font-weight: 500">
          <div>
            <span class="pr-1">Total Amount:</span>
            <span class="bold cc-secondary"
              >AED {{ totalAmountCash | number }}</span
            >
          </div>
        </div>
        <div class="col-md-6 px-0">
          <div class="d-flex justify-content-end">
            <div class="col-md-auto mt-1">
              <button
                cc-raised-button
                (click)="
                  checkIfSelectedPageCash()
                    ? unSelectPageCash()
                    : selectPageCash(true)
                "
                [disabled]="recordCountCash == 0"
              >
                {{ checkIfSelectedPageCash() ? "Unselect" : "Select" }}
                Page
              </button>
            </div>
            <div class="col-md-auto mt-1">
              <button
                cc-raised-button
                (click)="selectAllCash()"
                [disabled]="recordCountCash == 0"
              >
                {{ isSelectAllCash ? "Unselect" : "Select" }} All ({{
                  recordCountCash
                }})
              </button>
            </div>
            <div class="col-md-auto mt-1">
              <button
                cc-raised-button
                color="accent"
                (click)="exportTransactionsUnknownCashDeposits()"
              >
                Export
              </button>
            </div>
            <div class="col-md-auto mt-1">
              <button
                cc-raised-button
                (click)="confirmCash()"
                [disabled]="getSelectedRowsCountCash() <= 0"
              >
                Confirm Transactions
              </button>
            </div>
          </div>
        </div>
      </div>
      <cc-datagrid
        *ngIf="CashObj$ | async as records"
        [data]="records.content"
        [columns]="cashCols"
        [length]="records.totalElements"
        [pageOnFront]="false"
        [pageIndex]="records.number"
        [pageSize]="records.size"
        [pageSizeOptions]="[10, 25, 50]"
        (page)="getNextCash($event)"
        [stickyHeader]="true"
        [showColumnMenuButton]="true"
        [showColumnMenuHeader]="false"
        [columnMenuButtonIcon]="'settings'"
        [columnMovable]="true"
        [cellTemplate]="{ select: select1, rowNum: rowNum1 }"
      ></cc-datagrid>
      <ng-template #select1 let-row let-index="index" let-col="colDef">
        <cc-checkbox [(ngModel)]="selectedCash[row.id]"></cc-checkbox>
      </ng-template>
      <ng-template #rowNum1 let-row let-index="index" let-col="colDef">
        {{ index + currentPageCash * sizePageCash + 1 }}
      </ng-template>
    </cc-panel-body>
  </cc-panel>
</cc-accordion>
<cc-accordion>
  <cc-panel>
    <cc-panel-title>
      <b> Transactions - Unknown Wire Transfers ({{ recordCountWire }}) </b>
    </cc-panel-title>
    <cc-panel-body>
      <cc-panel-body>
        <div class="row mb-2 align-items-center">
          <div class="col-md-3" style="font-weight: 500">
            <div>
              <span class="pr-1">Total Transactions: </span>
              <span class="bold cc-secondary">{{
                recordCountWire | number
              }}</span>
            </div>
          </div>
          <div class="col-md-3" style="font-weight: 500">
            <div>
              <span class="pr-1">Total Amount:</span>
              <span class="bold cc-secondary"
                >AED {{ totalAmountWire | number }}</span
              >
            </div>
          </div>
          <div class="col-md-6 px-0">
            <div class="d-flex justify-content-end">
              <div class="col-md-auto mt-1">
                <button
                  cc-raised-button
                  (click)="
                    checkIfSelectedPageWire()
                      ? unSelectPageWire()
                      : selectPageWire(true)
                  "
                  [disabled]="recordCountWire == 0"
                >
                  {{ checkIfSelectedPageWire() ? "Unselect" : "Select" }}
                  Page
                </button>
              </div>
              <div class="col-md-auto mt-1">
                <button
                  cc-raised-button
                  (click)="selectAllWire()"
                  [disabled]="recordCountWire == 0"
                >
                  {{ isSelectAllWire ? "Unselect" : "Select" }} All ({{
                    recordCountWire
                  }})
                </button>
              </div>
              <div class="col-md-auto mt-1">
                <button
                  cc-raised-button
                  color="accent"
                  (click)="exportTransactionsUnknownWireTransfers()"
                >
                  Export
                </button>
              </div>
              <div class="col-md-auto mt-1">
                <button
                  cc-raised-button
                  (click)="confirmWire()"
                  [disabled]="getSelectedRowsCountWire() <= 0"
                >
                  Confirm Transactions
                </button>
              </div>
            </div>
          </div>
        </div>
        <cc-datagrid
          *ngIf="WireObj$ | async as records"
          [data]="records.content"
          [columns]="wireCols"
          [length]="records.totalElements"
          [pageOnFront]="false"
          [pageIndex]="records.number"
          [pageSize]="records.size"
          [pageSizeOptions]="[10, 25, 50]"
          (page)="getNextWire($event)"
          [stickyHeader]="true"
          [showColumnMenuButton]="true"
          [showColumnMenuHeader]="false"
          [columnMenuButtonIcon]="'settings'"
          [columnMovable]="true"
          [cellTemplate]="{ select: select2, rowNum: rowNum2 }"
        ></cc-datagrid>
        <ng-template #select2 let-row let-index="index" let-col="colDef">
          <cc-checkbox [(ngModel)]="selectedWire[row.id]"></cc-checkbox>
        </ng-template>
        <ng-template #rowNum2 let-row let-index="index" let-col="colDef">
          {{ index + currentPageWire * sizePageWire + 1 }}
        </ng-template>
      </cc-panel-body>
    </cc-panel-body>
  </cc-panel>
</cc-accordion>
<cc-accordion>
  <cc-panel>
    <cc-panel-title>
      <b>
        Already Matched transactions ({{
          alreadyMatchedTransactionsUnknownWireCount | number
        }})</b
      ></cc-panel-title
    >
    <cc-panel-body>
      <div class="row mb-2 align-items-center">
        <div class="col-md-3" style="font-weight: 500">
          <div>
            <span class="pr-1">Total Transactions: </span>
            <span class="bold cc-secondary">{{
              alreadyMatchedTransactionsUnknownWireCount | number
            }}</span>
          </div>
        </div>
        <div class="col-md-3" style="font-weight: 500">
          <div>
            <span class="pr-1">Total Amount:</span>
            <span class="bold cc-secondary"
              >AED
              {{ alreadyMatchedTransactionsUnknownWireAmount | number }}</span
            >
          </div>
        </div>
        <div class="col-md-6 px-0">
          <div class="d-flex justify-content-end">
            <div class="col-md-auto mt-1">
              <button
                cc-raised-button
                color="accent"
                (click)="exportAlreadyMatchedTransactionsUnknownWireTransfers()"
              >
                Export
              </button>
            </div>
          </div>
        </div>
      </div>
      <cc-datagrid
        *ngIf="AlreadyObj$ | async as records"
        [data]="records.content"
        [columns]="alreadyCols"
        [length]="records.totalElements"
        [pageOnFront]="false"
        [pageIndex]="records.number"
        [pageSize]="records.size"
        [pageSizeOptions]="[10, 25, 50]"
        (page)="getNextAlready($event)"
        [stickyHeader]="true"
        [showColumnMenuButton]="true"
        [showColumnMenuHeader]="false"
        [columnMenuButtonIcon]="'settings'"
        [columnMovable]="true"
        [cellTemplate]="{ rowNum: rowNum3 }"
      ></cc-datagrid>
      <ng-template #rowNum3 let-row let-index="index" let-col="colDef">
        {{ index + currentPageAlready * sizePageAlready + 1 }}
      </ng-template>
    </cc-panel-body>
    <cc-panel-body> </cc-panel-body
  ></cc-panel>
</cc-accordion>
