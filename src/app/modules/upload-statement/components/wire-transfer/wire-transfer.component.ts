import { Component, Inject, Input, OnInit } from '@angular/core';
import { UploadStatementService } from '../../services/upload-statement.service';
import { HttpClient } from '@angular/common/http';
import { ActivatedRoute } from '@angular/router';
import { CCBackendEndpoint } from '@maids/cc-erp-services';
import { CCDialog } from '@maids/cc-lib/dialog';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { BehaviorSubject, Observable } from 'rxjs';
import { PageableResponseModel } from '@maids/cc-lib/common';
import { PageEvent } from '@angular/material/paginator';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import * as moment from 'moment';
@Component({
  selector: 'app-wire-transfer',
  templateUrl: './wire-transfer.component.html',
  styleUrls: ['./wire-transfer.component.scss'],
})
export class WireTransferComponent implements OnInit {
  constructor(
    private uploadStatementService: UploadStatementService,
    private http: HttpClient,
    @Inject(CCBackendEndpoint) private _api: string,
    private route: ActivatedRoute,
    public readonly notifications: CCNotificationService,
    private mediaService: MediaService,
    private ccDialog: CCDialog
  ) {}
  resolved: number = 0;
  unresolved: number = 0;
  summary: any | null = null;
  CashObj = new BehaviorSubject<PageableResponseModel<any>>({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  CashObj$: Observable<PageableResponseModel<any>> =
    this.CashObj.asObservable();

  WireObj = new BehaviorSubject<PageableResponseModel<any>>({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  WireObj$: Observable<PageableResponseModel<any>> =
    this.WireObj.asObservable();

  AlreadyObj = new BehaviorSubject<PageableResponseModel<any>>({
    content: [],
    number: 0,
    size: 0,
    totalElements: 0,
    totalPages: 0,
  });
  AlreadyObj$: Observable<PageableResponseModel<any>> =
    this.AlreadyObj.asObservable();
  recordCountCash: number = 0;
  recordCountWire: number = 0;
  totalAmountCash: number = 0;
  totalAmountWire: number = 0;
  alreadyMatchedTransactionsUnknownWireCount: number = 0;
  alreadyMatchedTransactionsUnknownWireAmount: number = 0;
  selectedCash: any[] = [];
  selectedWire: any[] = [];
  currentPageCash: number = 0;
  currentPageWire: number = 0;
  currentPageAlready: number = 0;
  sizePageCash: number = 50;
  sizePageWire: number = 50;
  sizePageAlready: number = 50;
  isSelectAllCash: boolean = false;
  isSelectAllWire: boolean = false;
  ngOnInit(): void {
    this.uploadStatementService.summaryObject.subscribe((res) => {
      this.summary = res;
    });
    this.getAllData();
  }
  getAllData() {
    this.getTableCash();
    this.getTableWire();
    this.getTableAlready();
  }
  getTableCash(page: number = 0, size: number = 50) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/cashAndWireTransactions`,
        {
          params: {
            page,
            size,
            gridType: 'transactionsUnknownCashDeposits',
          },
        }
      )
      .subscribe((res: any) => {
        this.CashObj.next(res);
        this.recordCountCash = res.totalElements;
        this.totalAmountCash = res.totalSum;
        if (this.isSelectAllCash) {
          this.selectPageCash();
        }
      });
  }
  getNextCash(event: PageEvent) {
    this.getTableCash(event.pageIndex, event.pageSize);
  }
  getTableWire(page: number = 0, size: number = 50) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/cashAndWireTransactions`,
        {
          params: {
            page,
            size,
            gridType: 'transactionsUnknownWireTransfers',
          },
        }
      )
      .subscribe((res: any) => {
        this.WireObj.next(res);
        this.recordCountWire = res.totalElements;
        this.totalAmountWire = res.totalSum;
        if (this.isSelectAllWire) {
          this.selectPageWire();
        }
      });
  }
  getNextWire(event: PageEvent) {
    this.getTableWire(event.pageIndex, event.pageSize);
  }
  getTableAlready(page: number = 0, size: number = 50) {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/${this.route.snapshot.params['id']}/cashAndWireTransactions`,
        {
          params: {
            page,
            size,
            gridType: 'alreadyMatchedTransactionsUnknownWireTransfers',
          },
        }
      )
      .subscribe((res: any) => {
        this.AlreadyObj.next(res);
        this.alreadyMatchedTransactionsUnknownWireCount = res.totalElements;
        this.alreadyMatchedTransactionsUnknownWireAmount = res.totalSum;
      });
  }
  getNextAlready(event: PageEvent) {
    this.getTableAlready(event.pageIndex, event.pageSize);
  }
  cashCols: CCGridColumn[] = [
    { field: 'select', header: 'Select' },
    { field: 'rowNum', header: 'Row Number' },
    {
      field: 'transactionDate',
      header: 'Transaction Date',
      formatter(rowData, colDef) {
        let td = moment(rowData.date).format('YYYY-MM-DD');
        return rowData.transaction.id
          ? `<a class="cc-secondary" href="#!/accounting/add-edit-transactions/${rowData.transaction.id}">${td}</a>`
          : td;
      },
    },
    {
      field: 'transactionAmount',
      header: 'Amount',
      formatter(rowData, colDef) {
        return rowData.transactionAmount ? rowData.transactionAmount.toFixed(2) : 0;
      },
    },
    {
      field: 'revenue',
      header: 'Revenue',
      formatter(rowData, colDef) {
        return rowData.revenue ? rowData.revenue.code : '';
      },
    },
    { field: 'description', header: 'Description' },
    { field: 'paymentDetail', header: 'Payment Details' },
  ];
  wireCols: CCGridColumn[] = [
    { field: 'select', header: 'Select' },
    { field: 'rowNum', header: 'Row Number' },
    {
      field: 'transactionDate',
      header: 'Transaction Date',
      formatter(rowData, colDef) {
        let td = moment(rowData.date).format('YYYY-MM-DD');
        return rowData.transaction.id
          ? `<a class="cc-secondary"  href="#!/accounting/add-edit-transactions/${rowData.transaction.id}">${td}</a>`
          : td;
      },
    },
    {
      field: 'transactionAmount',
      header: 'Amount',
      formatter(rowData, colDef) {
        return rowData.transactionAmount ? rowData.transactionAmount.toFixed(2) : 0;
      },
    },
    {
      field: 'revenue',
      header: 'Revenue',
      formatter(rowData, colDef) {
        return rowData.revenue ? rowData.revenue.code : '';
      },
    },
    { field: 'description', header: 'Description' },
    { field: 'paymentDetail', header: 'Payment Details' },
  ];
  alreadyCols: CCGridColumn[] = [
    { field: 'rowNum', header: 'Row Number' },
    { field: 'bankTransactionType', header: 'Type' },
    {
      field: 'transactionDate',
      header: 'Transaction Date',
      formatter(rowData, colDef) {
        let td = moment(rowData.date).format('YYYY-MM-DD');
        return rowData.transaction.id
          ? `<a class="cc-secondary" href="#!/accounting/add-edit-transactions/${rowData.transaction.id}">${td}</a>`
          : td;
      },
    },
    {
      field: 'transactionAmount',
      header: 'Amount',
      formatter(rowData, colDef) {
        return rowData.transactionAmount ? rowData.transactionAmount.toFixed(2) : 0;
      },
    },
    {
      field: 'revenue',
      header: 'Revenue',
      formatter(rowData, colDef) {
        return rowData.revenue ? rowData.revenue.code : '';
      },
    },
    { field: 'description', header: 'Description' },
    { field: 'paymentDetail', header: 'Payment Details' },
  ];
  checkIfSelectedPageCash() {
    let selected = true;
    this.CashObj.getValue().content.forEach((element: any) => {
      if (!this.selectedCash[element.id]) {
        selected = false;
      }
    });
    return selected;
  }
  unSelectPageCash() {
    this.isSelectAllCash = false;
    this.CashObj.getValue().content.forEach((element: any) => {
      this.selectedCash[element.id] = false;
    });
  }
  selectPageCash(justPage?: any) {
    if (justPage) {
      this.isSelectAllCash = false;
    }
    this.CashObj.getValue().content.forEach((element: any) => {
      this.selectedCash[element.id] = true;
    });
  }
  selectAllCash() {
    if (this.isSelectAllCash) {
      this.unSelectAllCash();
      return;
    }
    this.isSelectAllCash = true;
    this.selectPageCash();
  }
  unSelectAllCash() {
    this.isSelectAllCash = false;
    this.selectedCash = [];
  }
  getSelectedRowsCountCash() {
    let len = 0;
    Object.keys(this.selectedCash).forEach((element: any) => {
      if (this.selectedCash[element]) {
        len++;
      }
    });
    return len;
  }
  confirmCash() {
    if (this.isSelectAllCash) {
      this.confirmCashPaymentsAll();
      return;
    }
    this.http
      .post(
        `${this._api}/accounting/bankStatementFile/confirmTransactions`,
        this.getSelectedCash()
      )
      .subscribe((res: any) => {
        this.notifications.notifySuccess(res);
        this.unSelectAllCash();
        this.getTableCash();
      });
  }
  confirmCashPaymentsAll() {
    this.http
      .get(
        `${this._api}/accounting/bankStatementFile/confirmAllTransactions/${this.route.snapshot.params['id']}/transactionsUnknownCashDeposits`
      )
      .subscribe((res: any) => {
        this.notifications.notifySuccess(res);
        this.unSelectAllCash();
        this.getTableCash();
      });
  }
  getSelectedCash() {
    let selected: any[] = [];
    Object.keys(this.selectedCash).forEach((element: any) => {
      if (this.selectedCash[element]) {
        selected.push({ id: element });
      }
    });
    return selected;
  }
  /////////////////////////////////////
  checkIfSelectedPageWire() {
    let selected = true;
    this.WireObj.getValue().content.forEach((element: any) => {
      if (!this.selectedWire[element.id]) {
        selected = false;
      }
    });
    return selected;
  }
  unSelectPageWire() {
    this.isSelectAllWire = false;
    this.WireObj.getValue().content.forEach((element: any) => {
      this.selectedWire[element.id] = false;
    });
  }
  selectPageWire(justPage?: any) {
    if (justPage) {
      this.isSelectAllWire = false;
    }
    this.WireObj.getValue().content.forEach((element: any) => {
      this.selectedWire[element.id] = true;
    });
  }
  selectAllWire() {
    if (this.isSelectAllWire) {
      this.unSelectAllWire();
      return;
    }
    this.isSelectAllWire = true;
    this.selectPageWire();
  }
  unSelectAllWire() {
    this.isSelectAllWire = false;
    this.selectedWire = [];
  }
  getSelectedRowsCountWire() {
    let len = 0;
    Object.keys(this.selectedWire).forEach((element: any) => {
      if (this.selectedWire[element]) {
        len++;
      }
    });
    return len;
  }
  getSelectedWire() {
    let selected: any[] = [];
    Object.keys(this.selectedWire).forEach((element: any) => {
      if (this.selectedWire[element]) {
        selected.push({ id: element });
      }
    });
    return selected;
  }
  confirmWire() {
    if (this.isSelectAllWire) {
      this.confirmWirePaymentsAll();
      return;
    }

    this.http
      .post(
        `${this._api}/accounting/bankStatementFile/confirmTransactions`,
        this.getSelectedWire()
      )
      .subscribe((res: any) => {
        this.notifications.notifySuccess(res);
        this.unSelectAllWire();
        this.getTableWire();
      });
  }
  confirmWirePaymentsAll() {
    this.http
      .get(
        `${this._api}/bankStatementFile/confirmAllTransactions/"+$scope.bankStatment.id+ "/transactionsUnknownWireTransfers`
      )
      .subscribe((res: any) => {
        this.notifications.notifySuccess(res);
        this.unSelectAllWire();
        this.getTableWire();
      });
  }
  exportTransactionsUnknownCashDeposits() {
    let url =
      'accounting/bankStatementFile/' +
      this.route.snapshot.params['id'] +
      '/cashAndWireTransactionsCSV?gridType=transactionsUnknownCashDeposits';
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }
  exportTransactionsUnknownWireTransfers() {
    let url =
      'accounting/bankStatementFile/' +
      this.route.snapshot.params['id'] +
      '/cashAndWireTransactionsCSV?gridType=transactionsUnknownWireTransfers';
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }
  exportAlreadyMatchedTransactionsUnknownWireTransfers() {
    let url =
      'accounting/bankStatementFile/' +
      this.route.snapshot.params['id'] +
      '/cashAndWireTransactionsCSV?gridType=alreadyMatchedTransactionsUnknownWireTransfers';
    this.mediaService.downloadFile(url, '', { method: 'POST' });
  }
}
