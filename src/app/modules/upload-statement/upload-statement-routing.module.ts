import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { StatementsListComponent } from './components/statements-list/statements-list.component';
const routes: Routes = [
  {
    path: '',
    component: StatementsListComponent,
  },
  {
    path: 'bank-statement-transactions-details',
    loadChildren: () =>
      import(
        '../upload-statement/components/bank-statement-transactions-details/bank-statement-transactions-details.module'
      ).then((m) => m.BankStatementTransactionsDetailsModule),
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class UploadStatementRoutingModule {}
