import { Component, Inject, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { CCDialogRef } from '@maids/cc-lib/dialog';
import { VisaExpensesService } from '../../services/visa-expenses.service';
import { CCNotificationService } from '@maids/cc-lib/services';
@Component({
  selector: 'app-add-edit-expense-config',
  templateUrl: './add-edit-expense-config.component.html',
  styleUrls: ['./add-edit-expense-config.component.scss'],
})
export class AddEditExpenseConfigComponent implements OnInit {
  expenseForm = this.formBuilder.group({
    selectedEmployeeType: [null, [Validators.required]],
    selectedProcess: [null, [Validators.required]],
    selectedExpenseType: [null, [Validators.required]],
    selectedExpense: [null, [Validators.required]],
    selectedPaymentType: [null, [Validators.required]],
  });
  mode: string = '';
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private ccDialogRef: CCDialogRef<AddEditExpenseConfigComponent>,
    private formBuilder: FormBuilder,
    private visaExpensesService: VisaExpensesService,
    public readonly notifications: CCNotificationService
  ) {}
  ngOnInit(): void {
    if (this.data.element) {
      this.mode = 'edit';
      this.expenseForm.controls['selectedEmployeeType'].setValue(
        this.data.element.employeeType.value
      );
      this.expenseForm.controls['selectedProcess'].setValue(
        this.data.element.newEmployee ? 'new' : 'renew'
      );
      this.expenseForm.controls['selectedExpenseType'].setValue(
        this.data.element.expensePurpose
      );
      this.expenseForm.controls['selectedExpense'].setValue(
        this.data.element.expense.id
      );
      this.expenseForm.controls['selectedPaymentType'].setValue(
        this.data.element.paymentType
      );
    } else {
      this.mode = 'add';
    }
  }
  save() {
    if (this.mode == 'add') {
      let payload: any;
      payload = {
        employeeType: this.expenseForm.controls['selectedEmployeeType'].value,
        newEmployee:
          this.expenseForm.controls['selectedProcess'].value == 'new'
            ? true
            : false,
        expensePurpose: this.expenseForm.controls['selectedExpenseType'].value,
        expense: { id: this.expenseForm.controls['selectedExpense'].value },
        paymentType: this.expenseForm.controls['selectedPaymentType'].value,
      };
      this.visaExpensesService.addVisaExpenseConfiguration(payload).subscribe({
        next: (res: any) => {
          this.notifications.notifySuccess(
            'Expense configuration added successfully'
          );
          this.ccDialogRef.close(true);
        },
        error: (err: any) => {
          this.notifications.notifyError(err.error.message);
        },
      });
    } else {
      let payload: any;
      payload = {
        id: this.data.element.id,
        employeeType: this.expenseForm.controls['selectedEmployeeType'].value,
        newEmployee:
          this.expenseForm.controls['selectedProcess'].value == 'new'
            ? true
            : false,
        expensePurpose: this.expenseForm.controls['selectedExpenseType'].value,
        expense: { id: this.expenseForm.controls['selectedExpense'].value },
        paymentType: this.expenseForm.controls['selectedPaymentType'].value,
      };
      this.visaExpensesService
        .updateVisaExpenseConfiguration(payload)
        .subscribe({
          next: (res: any) => {
            this.notifications.notifySuccess(
              'Expense configuration updated successfully'
            );
            this.ccDialogRef.close(true);
          },
          error: (err: any) => {
            this.notifications.notifyError(err.error.message);
          },
        });
    }
  }
}
