<div class="acc-7912">
  <cc-dialog-header>
    <h1 cc-dialog-title>Upload New Statement</h1>
    <a
      role="button"
      type="button"
      cc-icon-button
      cc-dialog-close-button
      cc-dialog-close
    ></a>
  </cc-dialog-header>
  <cc-dialog-content>
    <form [formGroup]="statementForm">
      <div class="row align-items-center">
        <cc-select
          class="w-100"
          label="Select Bucket"
          formControlName="type"
          [data]="uploadBucketOptions"
          [required]="true"
        ></cc-select>
      </div>
      <div class="row align-items-center">
        <cc-file-uploader
          label="Statement"
          class="w-100"
          formControlName="attachments"
          tag="NEW_VISA_MATCH_STATEMENT"
          [required]="true"
          [dropzoneConfig]="config"
        ></cc-file-uploader>
      </div>
    </form>
  </cc-dialog-content>
  <cc-dialog-actions>
    <div class="row">
      <div class="col-md-auto">
        <button cc-raised-button cc-dialog-close color="accent">Close</button>
      </div>
      <button
        cc-raised-button
        (click)="save()"
        color="primary"
        [disabled]="!statementForm.valid"
      >
        Start Parsing
      </button>
    </div>
  </cc-dialog-actions>
</div>
