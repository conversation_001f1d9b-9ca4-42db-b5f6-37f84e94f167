import { Component, OnInit } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { VisaExpensesService } from '../../services/visa-expenses.service';
import { CCNotificationService } from '@maids/cc-lib/services';
import { Router } from '@angular/router';
import { CCFileUploaderConfig } from '@maids/cc-lib/file-uploader';
import { CCDialogRef } from '@maids/cc-lib/dialog';

@Component({
  selector: 'app-add-new-statement',
  templateUrl: './add-new-statement.component.html',
  styleUrls: ['./add-new-statement.component.scss'],
})
export class AddNewStatementComponent implements OnInit {
  statementForm = this.formBuilder.group({
    type: [null, [Validators.required]],
    attachments: [null, [Validators.required]],
  });
  config:CCFileUploaderConfig = {
    maxFiles: 1,
    maxFilesize: 10,
    acceptedFiles: 'xlsx',
  };
  uploadBucketOptions = [
    {
      id: 'Noqodi maids.cc',
      text: 'Noqodi maids.cc',
    },
    {
      id: 'Amwal wallet',
      text: 'Amwal wallet',
    },
  ];
  constructor(
    private formBuilder: FormBuilder,
    private visaExpensesService: VisaExpensesService,
    public readonly notifications: CCNotificationService,
    private router: Router,
    private ccDialog: CCDialogRef<AddNewStatementComponent>
  ) {}

  ngOnInit(): void {}
  save() {
    let payload: any;
    payload = {
      type: this.statementForm.controls['type'].value,
      attachments: [
        {
          id: this.statementForm.controls['attachments'].value[0].id,
        },
      ],
    };
    this.visaExpensesService.uploadStatement(payload).subscribe({
      next: (res: any) => {
        this.ccDialog.close();
        this.notifications.notifySuccess('New statement uploaded successfully');
        this.router.navigateByUrl(
          '/accounting/v2/visa-expenses/statements/statement-matching-result/' + res.id
        );
      },
    });
  }
}
