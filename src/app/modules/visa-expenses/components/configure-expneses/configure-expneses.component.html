<div class="acc-7912">
  <cc-dialog-header
    ><h1 cc-dialog-title>Visa Expenses Configuration</h1>
    <a
      role="button"
      type="button"
      cc-icon-button
      cc-dialog-close-button
      cc-dialog-close
    ></a
  ></cc-dialog-header>
  <cc-accordion>
    <cc-panel>
      <cc-panel-title> <cc-icon>filter_alt</cc-icon> Filter</cc-panel-title>
      <cc-panel-body>
        <form class="row justify-content-around mt-2" [formGroup]="searchForm">
          <div class="col-md-6">
            <cc-select
              label="Employee Type"
              formControlName="selectedEmployeeType"
              [data]="employeeTypeOptions"
            ></cc-select>
          </div>
          <div class="col-md-6">
            <cc-select
              label="Visa Expense Type"
              formControlName="selectedExpenseType"
              [data]="expenseTypeOptions"
            ></cc-select>
          </div>
          <div class="col-md-6">
            <cc-select
              label="Expense Process"
              formControlName="selectedProcess"
              [data]="ProcessOptions"
            ></cc-select>
          </div>
          <div class="col-md-6">
            <cc-select
              label="Expense Name"
              formControlName="selectedExpense"
              [data]="expensesOptions"
            ></cc-select>
          </div>
          <div class="col-md-6">
            <cc-select
              label="Payment Type"
              formControlName="selectedPaymentType"
              [data]="paymentTypeOptions"
            ></cc-select>
          </div>
          <div class="col-md-6"></div>
        </form>
        <div class="d-flex justify-content-center">
          <button
            cc-raised-button
            (click)="search()"
            style="padding-left: 30px"
            color="primary"
          >
            <cc-icon class="icon">search</cc-icon> Search
          </button>
        </div>
      </cc-panel-body>
    </cc-panel>
  </cc-accordion>
  <div class="d-flex justify-content-end my-2">
    <button cc-raised-button (click)="addConfiguration()" color="primary">
      Add
    </button>
  </div>
  <cc-datagrid
    class="my-2"
    [data]="configs?.content ?? []"
    [columns]="gridCols"
    [length]="configs?.totalElements ?? 0"
    [pageOnFront]="false"
    [pageIndex]="configs?.number ?? 0"
    [pageSize]="configs?.size ?? 0"
    [pageSizeOptions]="[20]"
    (page)="getNextPage($event)"
    [stickyHeader]="true"
    [showColumnMenuButton]="true"
    [showColumnMenuHeader]="false"
    [columnMenuButtonIcon]="'settings'"
    [columnMovable]="true"
    [cellTemplate]="{ newEmployee: newEmployee }"
  >
    <cc-grid-actions-list
      *ccActionData="let ctx of configs?.content; row as row"
      [renderedActionsCount]="2"
      style="width: fit-content; gap: 8px"
    >
      <button
        *cc-action
        cc-raised-button
        color="primary"
        (click)="edit(row)"
        class="ml-1"
      >
        Edit
      </button>
      <button *cc-action cc-raised-button (click)="delete(row)">Delete</button>
    </cc-grid-actions-list>
  </cc-datagrid>
  <ng-template #newEmployee let-row let-index="index" let-col="colDef">
    <span>{{ row.newEmployee ? "New" : "Renew" }}</span>
  </ng-template>
</div>
