<cc-dialog-header>
  <h1 cc-dialog-title>Payment type charges</h1>
  <a
    role="button"
    type="button"
    cc-icon-button
    cc-dialog-close-button
    cc-dialog-close
  ></a>
</cc-dialog-header>
<cc-dialog-content>
  <div class="d-flex justify-content-between">
    <span>Lastest update on ({{ chargesLastUpdated }})</span>
    <button cc-raised-button color="primary" (click)="addNew()">Add New</button>
  </div>
  <cc-datagrid
    class="my-2"
    [data]="charges$ | async"
    [columns]="gridCols"
    [pageOnFront]="false"
    [showPaginator]="false"
    [stickyHeader]="true"
    [showColumnMenuButton]="true"
    [showColumnMenuHeader]="false"
    [columnMenuButtonIcon]="'settings'"
    [columnMovable]="true"
    [cellTemplate]="{
      paymentType: paymentType,
      expensePurpose: expensePurpose,
      charge: charge,
      vatChargePercentage: vatChargePercentage,
      serviceChargeOfExpense: serviceChargeOfExpense,
      vatChargeOfExpense: vatChargeOfExpense,
      typeOfServiceChargeOfExpense: typeOfServiceChargeOfExpense
    }"
  >
    <cc-grid-actions-list
      *ccActionData="let ctx of charges$ | async; row as row; index as index"
      [renderedActionsCount]="1"
      style="width: fit-content"
    >
      <button
        *cc-action
        cc-raised-button
        color="accent"
        (click)="delete(row, index)"
      >
        Delete
      </button>
    </cc-grid-actions-list>
  </cc-datagrid>
  <ng-template #paymentType let-row let-index="index" let-col="colDef">
    <cc-select
      *ngIf="row.newRecord"
      [(ngModel)]="row.paymentType"
      [data]="paymentTypeOptions"
    ></cc-select>
    <span *ngIf="row.paymentType && !row.newRecord">{{ row.paymentType }}</span>
  </ng-template>

  <ng-template #expensePurpose let-row let-index="index" let-col="colDef">
    <cc-select
      *ngIf="row.newRecord"
      [(ngModel)]="row.expensePurpose"
      [data]="expensesOptions"
      [emitFullSelectOption]="true"
    ></cc-select>
    <span *ngIf="row.expensePurpose && !row.newRecord">{{
      row.expensePurpose
    }}</span>
  </ng-template>

  <ng-template #charge let-row let-index="index" let-col="colDef">
    <div class="row align-items-center justify-content-center">
      <div class="col-md-auto w-50">
        <cc-input [(ngModel)]="row.charge"></cc-input>
      </div>
    </div>
  </ng-template>

  <ng-template #vatChargePercentage let-row let-index="index" let-col="colDef">
    <div class="row align-items-center justify-content-center">
      <div class="col-md-auto w-50">
        <cc-input [(ngModel)]="row.vatChargePercentage"></cc-input>
      </div>
      <div class="col-md-auto">%</div>
    </div>
  </ng-template>

  <ng-template
    #serviceChargeOfExpense
    let-row
    let-index="index"
    let-col="colDef"
  >
    <div class="row align-items-center justify-content-center">
      <div class="col-md-auto w-50">
        <cc-input [(ngModel)]="row.serviceChargeOfExpense"></cc-input>
      </div>
    </div>
  </ng-template>

  <ng-template #vatChargeOfExpense let-row let-index="index" let-col="colDef">
    <div class="row align-items-center justify-content-center">
      <div class="col-md-auto w-50">
        <cc-input [(ngModel)]="row.vatChargeOfExpense"></cc-input>
      </div>
      <div class="col-md-auto">%</div>
    </div>
  </ng-template>

  <ng-template
    #typeOfServiceChargeOfExpense
    let-row
    let-index="index"
    let-col="colDef"
  >
    <cc-select
      [(ngModel)]="row.typeOfServiceChargeOfExpense"
      [data]="serviceChargeOfExpenseTypeOptions"
    ></cc-select>
  </ng-template>
</cc-dialog-content>
<cc-dialog-actions>
  <button cc-raised-button cc-dialog-close>Close</button>
  <button cc-raised-button color="primary" (click)="save()">Save</button>
</cc-dialog-actions>
