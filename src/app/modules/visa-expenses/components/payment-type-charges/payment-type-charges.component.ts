import { ChangeDetectorRef, Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { CCDialog, CCDialogRef } from '@maids/cc-lib/dialog';
import { BehaviorSubject, forkJoin, map, of } from 'rxjs';
import { VisaExpensesService } from '../../services/visa-expenses.service';
import * as moment from 'moment';
import { CCNotificationService } from '@maids/cc-lib/services';
@Component({
  selector: 'app-payment-type-charges',
  templateUrl: './payment-type-charges.component.html',
  styleUrls: ['./payment-type-charges.component.scss'],
})
export class PaymentTypeChargesComponent implements OnInit {
  private chargesSubject = new BehaviorSubject<any[]>([]);
  paymentTypeOptions: any[] = [];
  expensesOptions: any[] = [];
  chargesLastUpdated: string = '';
  serviceChargeOfExpenseTypeOptions: any[] = [
    { id: 'STATIC_AMOUNT', text: 'Static Amount' },
    { id: 'PERCENTAGE', text: 'Percentage %' },
  ];
  charges$ = this.chargesSubject.asObservable();
  constructor(
    @Inject(MAT_DIALOG_DATA) public data: any,
    private ccDialogRef: CCDialogRef<PaymentTypeChargesComponent>,
    private ccDialog: CCDialog,
    private cdr: ChangeDetectorRef,
    private visaExpensesService: VisaExpensesService,
    public readonly notifications: CCNotificationService
  ) {}
  gridCols: CCGridColumn[] = [
    { field: 'paymentType', header: 'Payment type' },
    { field: 'charge', header: 'Charge Method Type' },
    { field: 'vatChargePercentage', header: 'VAT Charge Payment' },
    { field: 'expensePurpose', header: 'Expense type' },
    { field: 'serviceChargeOfExpense', header: 'Service Charge of Expense' },
    { field: 'vatChargeOfExpense', header: 'VAT Charge of Expense' },
    {
      field: 'typeOfServiceChargeOfExpense',
      header: 'Type of Service Charge of Expense',
    },
  ];
  ngOnInit(): void {
    console.log(this.data);
    this.chargesSubject.next(this.data.data);
    this.visaExpensesService.getSearchInfo().subscribe((res: any) => {
      this.paymentTypeOptions = res.paymentTypes.map((opt: any) => {
        return { id: opt, text: opt };
      });
      this.expensesOptions = Object.entries(res.expensePurposes).map((row) => {
        return { id: row[1], text: row[1] };
      });
    });
    this.chargesLastUpdated = moment(this.data.last_updated).format(
      'DD/MM/YYYY'
    );
  }
  delete(element: any, index: number) {
    if (element.newRecord) {
      const currentCharges = this.chargesSubject.getValue();
      const updatedCharges = currentCharges.filter(
        (charge: any) => charge !== element
      );
      this.chargesSubject.next(updatedCharges);
    } else {
      this.ccDialog.confirm(
        '',
        'Are you sure you want to delete this charge?',
        () => {
          this.visaExpensesService.deleteCharge(element.id).subscribe({
            next: () => {
              this.notifications.notifySuccess('Deleted successfully');
              this.getCharges();
            },
          });
        }
      );
    }
  }
  getCharges() {
    this.visaExpensesService.getPaymentTypeCharges().subscribe((res: any) => {
      this.chargesSubject.next(res.data);
      this.chargesLastUpdated = moment(res.last_updated).format('DD/MM/YYYY');
    });
  }
  addNew() {
    let newRow = {
      newRecord: true,
      expensePurpose: null,
      paymentType: null,
      charge: 0,
      vatChargePercentage: 0,
      serviceChargeOfExpense: 0,
      vatChargeOfExpense: 0,
    };
    const currentCharges = this.chargesSubject.getValue();
    const updatedCharges = [newRow, ...currentCharges];
    this.chargesSubject.next(updatedCharges);
  }
  save() {
    let chargesData = this.chargesSubject.getValue();
    let data: any[] = [];
    let addDefArr = chargesData
      .map((charge: any) => {
        if (charge.newRecord) {
          return this.visaExpensesService
            .createCharge({
              paymentType: charge.paymentType,
              charge: charge.charge,
              vatChargePercentage: charge.vatChargePercentage,
              expensePurpose: charge.expensePurpose.text,
              serviceChargeOfExpense: charge.serviceChargeOfExpense,
              vatChargeOfExpense: charge.vatChargeOfExpense,
              typeOfServiceChargeOfExpense: charge.typeOfServiceChargeOfExpense,
            })
            .pipe(
              map((res: any) => ({
                id: res.id,
                charge: res.charge,
                serviceChargeOfExpense: res.serviceChargeOfExpense,
                vatChargeOfExpense: res.vatChargeOfExpense,
                vatChargePercentage: res.vatChargePercentage,
                typeOfServiceChargeOfExpense: res.typeOfServiceChargeOfExpense,
              }))
            );
        } else {
          return of(charge);
        }
      })
      .filter((obs: any) => !!obs);
    forkJoin(addDefArr).subscribe((results: any[]) => {
      chargesData = chargesData.map((charge: any, index: number) => {
        if (charge.newRecord) {
          const updatedCharge = results[index];
          return {
            ...charge,
            id: updatedCharge.id,
            charge: updatedCharge.charge,
            serviceChargeOfExpense: updatedCharge.serviceChargeOfExpense,
            vatChargeOfExpense: updatedCharge.vatChargeOfExpense,
            vatChargePercentage: updatedCharge.vatChargePercentage,
            typeOfServiceChargeOfExpense:
              updatedCharge.typeOfServiceChargeOfExpense,
            newRecord: false,
          };
        } else {
          console.log(charge);

          return charge;
        }
      });
      data = chargesData.map((charge: any) => {
        return {
          id: charge.id,
          charge: charge.charge,
          vatChargePercentage: charge.vatChargePercentage,
          serviceChargeOfExpense: charge.serviceChargeOfExpense,
          vatChargeOfExpense: charge.vatChargeOfExpense,
          typeOfServiceChargeOfExpense: charge.typeOfServiceChargeOfExpense?.id || charge.typeOfServiceChargeOfExpense.value,
        };
      });
      this.visaExpensesService.updateCharges(data).subscribe({
        next: () => {
          this.notifications.notifySuccess('Updated successfully');
          this.ccDialogRef.close();
        },
      });
    });
  }
}
