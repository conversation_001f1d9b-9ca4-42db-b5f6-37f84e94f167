import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CCDialog } from '@maids/cc-lib/dialog';
import { VisaExpensesService } from '../../../services/visa-expenses.service';
import { CCGridColumn } from '@maids/cc-lib/datagrid';

@Component({
  selector: 'app-missing-statement',
  templateUrl: './missing-statement.component.html',
  styleUrls: ['./missing-statement.component.scss'],
})
export class MissingStatementComponent implements OnInit {
  records: any | null = null;
  selectedMissingStatements: number[] = [];
  gridCols: CCGridColumn[] = [
    { field: 'select', header: 'Select' },
    {
      field: 'expenseCreationDate',
      header: 'Creation Date',
      formatter(rowData) {
        return rowData.expenseCreationDate.split(' ')[0];
      },
      class: 'max',
    },
    { field: 'type', header: 'Name' },
    { field: 'amount', header: 'ERP Amount' },
    { field: 'referenceNumber', header: 'Reference Number' },
    { field: 'description', header: 'Description' },
    { field: 'fromBucket.name', header: 'From Bucket', class: 'max' },
    { field: 'expense.label', header: 'Expense Name' },
    { field: 'contractType', header: 'Contract Type' },
    { field: 'employeeType', header: 'Employee Type' },
  ];
  constructor(
    private visaExpensesService: VisaExpensesService,
    private route: ActivatedRoute,
    private router: Router,
    private ccDialog: CCDialog
  ) {}

  ngOnInit(): void {
    this.visaExpensesService.refresh$.subscribe(() => {
      this.getMissingFromStatementTransaction();
    });
    this.getMissingFromStatementTransaction();
  }
  getMissingFromStatementTransaction(page: number = 0, size: number = 20) {
    this.visaExpensesService
      .getMissingFromStatementTransaction(this.route.snapshot.params['id'], {
        page,
        size,
      })
      .subscribe((response: any) => {
        this.records = response;
      });
  }
  getNextPage(event: any) {
    this.getMissingFromStatementTransaction(event.pageIndex, event.pageSize);
  }
  dismissAll() {
    const ids: any[] = [];
    this.ccDialog.confirm('', 'Are you sure?', () => {
      this.selectedMissingStatements.forEach((element, index) => {
        let item = this.records.content.find(
          (item: any) => (item.visaRequestExpenseID = index)
        );
        if (element) {
          ids.push({
            id: item.visaRequestExpenseID,
            type: item.visaExpenseType,
          });
        }
      });
      this.dismissStatementTransactions(ids);
    });
  }
  dismissSingle(element: any) {
    this.ccDialog.confirm('', 'Are you sure?', () => {
      this.dismissStatementTransactions([
        { id: element.visaRequestExpenseID, type: element.visaExpenseType },
      ]);
    });
  }
  dismissStatementTransactions(ids: any[]) {
    this.visaExpensesService
      .dismissMissingStatementTransactions(
        this.route.snapshot.params['id'],
        ids
      )
      .subscribe((response: any) => {
        this.selectedMissingStatements = [];
        this.visaExpensesService.triggerRefresh();
      });
  }
  getSelectedRowsCount() {
    let len = 0;
    if (this.selectedMissingStatements) {
      this.selectedMissingStatements.forEach((element) => {
        if (element) {
          len++;
        }
      });
    }
    return len;
  }
}
