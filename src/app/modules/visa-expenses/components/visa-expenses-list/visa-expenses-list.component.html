<div class="acc-7912">
  <cc-accordion class="mt-2">
    <cc-panel>
      <cc-panel-title>
        <cc-icon class="filter_icon">filter_alt</cc-icon>
        <span style="margin-left: 25px">Filters</span></cc-panel-title
      >
      <cc-panel-body>
        <div
          class="row align-items-center justify-content-center"
          [formGroup]="filterForm"
        >
          <div class="col-4">
            <cc-input label="Name" formControlName="searchName"></cc-input>
          </div>
          <div class="col-4">
            <cc-select
              label="Status"
              [data]="statusOptions"
              formControlName="selectedStatus"
              [required]="true"
            ></cc-select>
          </div>
          <div class="col-md-auto mb-3">
            <button
              cc-raised-button
              [disabled]="filterForm.invalid"
              (click)="search()"
              color="primary"
              style="padding-left: 30px"
            >
              <cc-icon class="icon">search</cc-icon> Search
            </button>
          </div>
        </div>
      </cc-panel-body>
    </cc-panel>
  </cc-accordion>
  <form class="mt-2" [formGroup]="advancedSearchForm">
    <cc-advanced-search
      mode="developer"
      [metaApi]="VisaExpenseMetaSearch"
      (onFilter)="getVisaExpenses()"
      formControlName="search"
      [cardTitle]="'Advanced Search'"
      (onReset)="resetSearch()"
      [fieldOverrides]="fieldOverrides"
    ></cc-advanced-search>
  </form>
  <div class="row py-2">
    <div class="col-md-auto">
      <button
        cc-raised-button
        [disabled]="getSelectedRowsCount() <= 0"
        color="primary"
        (click)="addMultiTransaction()"
      >
        Add Selected
      </button>
    </div>
    <div class="col-md-auto">
      <button
        cc-raised-button
        [disabled]="getSelectedRowsCount() <= 0"
        color="accent"
        (click)="dismissMultiTransaction()"
      >
        Dismiss Selected
      </button>
    </div>
  </div>
  <div class="d-flex justify-content-between">
    <div class="col align-self-end">
      <div class="row">
        <span class="bold">Payments Sum :</span>
        <span class="bold cc-secondary">{{ totalRecordSum }}</span>
        ,
        <span class="bold">Record Count:</span
        ><span class="bold cc-secondary">{{ totalRecordCount }}</span>
      </div>
      <div class="row">
        <span class="bold">Selected Sum:</span
        ><span class="bold cc-secondary">{{ selectedRecordSum }}</span>
        ,
        <span class="bold">Selected Count:</span
        ><span class="bold cc-secondary">{{ getSelectedRowsCount() }}</span>
      </div>
      <div class="row">
        <cc-checkbox
          [(ngModel)]="checkAllOption"
          (change)="selectAll()"
          color="accent"
          >Select all</cc-checkbox
        >
      </div>
    </div>
    <div class="offset-md-auto">
      <div class="row justify-content-end p-2">
        <div class="col-md-auto">
          <button cc-raised-button color="primary" (click)="auditStatements()">
            Audit Statements
          </button>
        </div>
        <div class="col-md-auto">
          <button cc-raised-button (click)="exportExcel()">Export Excel</button>
        </div>
      </div>
      <div class="row justify-content-end p-2">
        <div class="col-md-auto">
          <button cc-raised-button (click)="paymentTypeCharges()">
            Payment Type Charges
          </button>
        </div>
        <div class="col-md-auto">
          <button cc-raised-button (click)="configureExpenses()">
            Configure Expenses
          </button>
        </div>
      </div>
    </div>
  </div>
  <cc-datagrid
    class="my-2"
    [data]="visaExpenses?.content ?? []"
    [columns]="gridCols"
    [length]="visaExpenses?.totalElements ?? 0"
    [pageOnFront]="false"
    [pageIndex]="visaExpenses?.number ?? 0"
    [pageSize]="visaExpenses?.size ?? 0"
    [pageSizeOptions]="[50]"
    (page)="getNextPage($event)"
    [stickyHeader]="true"
    [showColumnMenuButton]="true"
    [showColumnMenuHeader]="false"
    [columnMenuButtonIcon]="'settings'"
    [columnMovable]="true"
    [cellTemplate]="{
      select: select,
      labelName: labelName,
      description: description,
      amount: amount,
      bucketFrom: bucketFrom,
      expense: expense
    }"
  >
    <cc-grid-actions-list
      *ccActionData="
        let ctx of visaExpenses?.content;
        row as row;
        index as index
      "
      [renderedActionsCount]="2"
      style="width: fit-content; gap: 8px"
    >
      <ng-container *ngIf="row.status == 'Pending'">
        <button
          *cc-action
          cc-raised-button
          (click)="addTransaction(index)"
          color="primary"
        >
          Add Transaction
        </button>
      </ng-container>
      <ng-container *ngIf="row.status == 'Pending'">
        <button
          *cc-action
          cc-raised-button
          (click)="dismissTrans(index)"
          class="ml-1"
          color="accent"
        >
          Dismiss
        </button>
      </ng-container>
    </cc-grid-actions-list>
  </cc-datagrid>
  <ng-template #select let-row let-index="index" let-col="colDef">
    <cc-checkbox
      [disabled]="row.status !== 'Pending'"
      [(ngModel)]="selectedRecords[index + 1]"
      (change)="selectRow(index)"
      color="accent"
    ></cc-checkbox>
  </ng-template>
  <ng-template #labelName let-row let-index="index" let-col="colDef">
    <span>{{
      row.housemaid ? row.housemaid.label : row.officeStaff.label
    }}</span>
  </ng-template>
  <ng-template #description let-row let-index="index" let-col="colDef">
    <div style="width: 300px">
      <cc-textarea [(ngModel)]="row.description"></cc-textarea>
    </div>
  </ng-template>
  <ng-template #amount let-row let-index="index" let-col="colDef">
    <div class="row justify-content-center" style="width: max-content">
      <div class="col-md-auto px-1">
        <span>{{ row.amount }}</span>
      </div>
      <div class="col-md-auto px-0">
        <cc-icon [ccTooltip]="row.equation">error</cc-icon>
      </div>
    </div>
  </ng-template>
  <ng-template #bucketFrom let-row let-index="index" let-col="colDef">
    <cc-select
      [(ngModel)]="row.fromBucket"
      [lazyPageFetcher]="getBuckets"
    ></cc-select>
  </ng-template>
  <ng-template #expense let-row let-index="index" let-col="colDef">
    <cc-select
      [(ngModel)]="row.expense"
      [lazyPageFetcher]="getExpenses"
    ></cc-select>
  </ng-template>
</div>
