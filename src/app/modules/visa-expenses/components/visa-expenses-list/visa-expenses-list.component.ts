import {
  <PERSON>mpo<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  OnC<PERSON><PERSON>,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { FormB<PERSON>er, Validators } from '@angular/forms';
import { API } from 'src/environments/api';
import { PageEvent } from '@angular/material/paginator';
import { CCGridColumn } from '@maids/cc-lib/datagrid';
import { PageableResponseModel, PaginationRequest } from '@maids/cc-lib/common';
import { VisaExpensesService } from '../../services/visa-expenses.service';
import { map, Observable } from 'rxjs';
import { SelectOption } from '@maids/cc-lib/select-input';
import { Router } from '@angular/router';
import * as moment from 'moment';
import { CCNotificationService, MediaService } from '@maids/cc-lib/services';
import { CCDialog } from '@maids/cc-lib/dialog';
import { ConfigureExpnesesComponent } from '../configure-expneses/configure-expneses.component';
import { PaymentTypeChargesComponent } from '../payment-type-charges/payment-type-charges.component';
import { AdvancedSearchFieldOverrides } from '@maids/cc-lib/advanced-search';
@Component({
  selector: 'app-visa-expenses-list',
  templateUrl: './visa-expenses-list.component.html',
  styleUrls: ['./visa-expenses-list.component.scss'],
})
export class VisaExpensesListComponent implements OnInit {
  visaExpenses: any;
  selectedRecords: any[] = [];
  filterForm = this.formBuilder.group({
    searchName: [''],
    selectedStatus: ['Pending', Validators.required],
  });
  advancedSearchForm = this.formBuilder.group({
    search: [null],
  });
  totalRecordCount: number = 0;
  totalRecordSum: number = 0;
  selectedRecordSum: number = 0;
  selectedRecordCount: number = 0;
  VisaExpenseMetaSearch = API.visaRequestExpenseMeta;
  checkAllOption: boolean = false;
  currentPage: number = 0;
  currentSize: number = 50;
  statusOptions = [
    {
      id: 'Pending',
      text: 'Pending',
    },
    {
      id: 'Added',
      text: 'Added',
    },
    {
      id: 'Dismissed',
      text: 'Dismissed',
    },
  ];
  fieldOverrides: AdvancedSearchFieldOverrides = {
    employeeType: {
      emitFullSelectOption: true,
    },
    maidContractType: {
      emitFullSelectOption: true,
    },
    paymentType: {
      emitFullSelectOption: true,
    },
    status: {
      emitFullSelectOption: true,
    },
  };
  ignoreAdvanceSearch: boolean = true;
  getBuckets = (pageReq: PaginationRequest): Observable<SelectOption[]> => {
    return this.visaExpensesService
      .searchBuckets(pageReq.page, pageReq.size, pageReq.searchString)
      .pipe(
        map((val: any) => {
          return val.content.map((opt: any) => ({
            id: opt.id,
            text: opt.name,
          }));
        })
      );
  };
  getExpenses = (pageReq: PaginationRequest): Observable<SelectOption[]> => {
    return this.visaExpensesService
      .searchExpenses(pageReq.page, pageReq.size, pageReq.searchString)
      .pipe(
        map((val: any) => {
          return val.content.map((opt: any) => ({
            id: opt.id,
            text: opt.name,
          }));
        })
      );
  };
  gridCols: CCGridColumn[] = [
    { field: 'select', header: 'Select' },
    { field: 'name', header: 'Expense Name' },
    {
      field: 'creationDate',
      header: 'Creation Date',
      type: 'date',
      width: '150px',
    },
    { field: 'employeeType', header: 'Employee Type' },
    {
      field: 'maidVisaAEContract',
      header: 'Contract Type',
      formatter(rowData, colDef) {
        return rowData.maidVisaAEContract ? 'MaidVisa' : 'Maid.cc';
      },
    },
    { field: 'labelName', header: 'Name' },
    { field: 'paymentType', header: 'Type of Payment' },
    { field: 'description', header: 'Description' },
    { field: 'amount', header: 'Amount' },
    { field: 'referenceNumber', header: 'Reference Number' },
    { field: 'bucketFrom', header: 'From Bucket' },
    { field: 'expense', header: 'Expense' },
  ];
  constructor(
    private formBuilder: FormBuilder,
    private visaExpensesService: VisaExpensesService,
    private router: Router,
    public readonly notifications: CCNotificationService,
    private ccDialog: CCDialog,
    private mediaService: MediaService
  ) {}

  ngOnInit(): void {
    this.getVisaExpenses();
  }
  search() {
    this.selectedRecords = [];
    this.checkAllOption = false;
    this.getVisaExpenses();
  }
  getVisaExpenses(page: number = 0, size: number = 50) {
    if (this.advancedSearchForm.controls['search'].value != undefined) {
      this.visaExpensesService
        .getVisaExpensesListAdvancedSearch(
          { page, size },
          this.advancedSearchForm.controls['search'].value
        )
        .subscribe((response: any) => {
          this.visaExpenses = response;
          this.totalRecordSum = response?.totalSum;
          this.totalRecordCount = response.totalElements;
        });
    } else {
      this.visaExpensesService
        .getVisaExpensesList({
          page,
          size,
          searchname: this.filterForm.value.searchName,
          searchstatus: this.filterForm.value.selectedStatus,
        })
        .subscribe((response: any) => {
          this.visaExpenses = response;
          this.totalRecordSum = response?.totalSum;
          this.totalRecordCount = response.totalElements;
        });
    }
  }
  resetSearch() {
    this.advancedSearchForm.reset();
    this.getVisaExpenses();
  }
  getNextPage(event: PageEvent) {
    this.currentPage = event.pageIndex;
    this.currentSize = event.pageSize;
    this.getVisaExpenses(event.pageIndex, event.pageSize);
    this.selectedRecords = [];
    this.checkAllOption = false;
  }
  selectAll() {
    if (this.checkAllOption) {
      let i = 1;
      this.visaExpenses.content.forEach((element: any) => {
        if (element.status == 'Pending') {
          this.selectedRecords[i] = this.checkAllOption;
        }
        i++;
      });
    } else {
      this.selectedRecords = [];
      this.checkAllOption = false;
    }
    this.updateSelectedRecords();
  }
  selectRow(index: number) {
    if (this.selectedRecords[index + 1]) {
      this.selectedRecords[index + 1] = true;
    } else {
      this.selectedRecords[index + 1] = false;
    }
    this.updateSelectedRecords();
  }
  updateSelectedRecords() {
    this.selectedRecordSum = this.visaExpenses.content
      .filter((_: any, index: any) => this.selectedRecords[index + 1])
      .reduce((sum: any, record: any) => sum + (record.amount || 0), 0);
    this.getSelectedRowsCount();
  }
  getSelectedRowsCount() {
    let len = 0;
    if (this.selectedRecords) {
      this.selectedRecords.forEach((element) => {
        if (element) {
          len++;
        }
      });
    }
    return len;
  }
  addTransaction(itemIndex: any) {
    let payload: any;
    payload = {
      date: moment().format('YYYY-MM-DD') + ' 00:00:00',
      description: this.visaExpenses.content[itemIndex].description,
      amount: this.visaExpenses.content[itemIndex].amount,
      paymentType: 'CARD',
    };
    let element = this.visaExpenses.content[itemIndex];
    if (typeof element.fromBucket == 'object' && element.fromBucket.id) {
      payload.fromBucket = { id: element.fromBucket.id };
    } else if (typeof element.fromBucket == 'number' && element.fromBucket) {
      payload.fromBucket = { id: element.fromBucket };
    } else {
      payload.fromBucket = null;
    }

    if (typeof element.expense == 'object' && element.expense.id) {
      payload.expense = { id: element.expense.id };
    } else if (typeof element.expense == 'number' && element.expense) {
      payload.expense = { id: element.expense };
    } else {
      payload.expense = null;
    }
    if (this.visaExpenses.content[itemIndex].housemaid) {
      payload.transactionType = 'HOUSEMAID';
      payload.housemaids = [
        {
          housemaid: { id: this.visaExpenses.content[itemIndex].housemaid.id },
        },
      ];
    } else {
      payload.transactionType = 'OFFICE_STAFF';
      payload.officeStaffs = [
        {
          officeStaff: {
            id: this.visaExpenses.content[itemIndex].officeStaff.id,
          },
        },
      ];
    }
    let params: any;
    params = {
      id: this.visaExpenses.content[itemIndex].visaRequestExpenseID,
      type: this.visaExpenses.content[itemIndex].visaExpenseType,
    };
    this.visaExpensesService.addTrans(params, payload).subscribe({
      next: (res: any) => {
        this.notifications.notifySuccess('Transaction added successfully');
        this.getVisaExpenses(this.currentPage, this.currentSize);
        this.selectedRecords = [];
        this.checkAllOption = false;
        this.updateSelectedRecords();
      },
    });
  }
  addMultiTransaction() {
    let records: any[] = [];
    this.selectedRecords.forEach((element, index) => {
      if (element) {
        records.push(this.visaExpenses.content[index - 1]);
      }
    });
    let requestData: any[] = [];
    records.forEach((element) => {
      let obj: any = {
        visaRequestExpenseID: element.visaRequestExpenseID,
        visaRequestExpenseType: element.visaExpenseType,
        transaction: {
          date: moment().format('YYYY-MM-DD') + ' 00:00:00',
          description: element.description,
          amount: element.amount,
          paymentType: 'CARD',
        },
      };
      if (typeof element.fromBucket == 'object' && element.fromBucket.id) {
        obj.transaction.fromBucket = { id: element.fromBucket.id };
      } else if (typeof element.fromBucket == 'number' && element.fromBucket) {
        obj.transaction.fromBucket = { id: element.fromBucket };
      } else {
        obj.transaction.fromBucket = null;
      }

      if (typeof element.expense == 'object' && element.expense.id) {
        obj.transaction.expense = { id: element.expense.id };
      } else if (typeof element.expense == 'number' && element.expense) {
        obj.transaction.expense = { id: element.expense };
      } else {
        obj.transaction.expense = null;
      }
      if (element.housemaid) {
        obj.transactionType = 'HOUSEMAID';
        obj.housemaid = { id: element.housemaid.id };
      } else {
        obj.transactionType = 'OFFICE_STAFF';
        obj.officeStaff = { id: element.officeStaff.id };
      }
      requestData.push(obj);
    });
    this.visaExpensesService.addTransactions(requestData).subscribe({
      next: (res: any) => {
        this.notifications.notifySuccess('Transactions added successfully');
        this.selectedRecords = [];
        this.checkAllOption = false;
        this.updateSelectedRecords();
        this.getVisaExpenses(this.currentPage, this.currentSize);
      },
    });
  }
  dismissTrans(indexItem: any) {
    let params: any;
    params = {
      id: this.visaExpenses.content[indexItem].visaRequestExpenseID,
      type: this.visaExpenses.content[indexItem].visaExpenseType,
    };
    this.visaExpensesService.dismissExpense(params).subscribe({
      next: (res: any) => {
        this.notifications.notifySuccess('Expense dismissed successfully');
        this.getVisaExpenses(this.currentPage, this.currentSize);
        this.selectedRecords = [];
        this.checkAllOption = false;
        this.updateSelectedRecords();
      },
    });
  }
  dismissMultiTransaction() {
    this.ccDialog.confirm(
      '',
      'Are you sure you want to dismiss these item?',
      () => {
        let records: any[] = [];
        this.selectedRecords.forEach((element, index) => {
          if (element) {
            records.push(this.visaExpenses.content[index - 1]);
          }
        });
        let requestData: any[] = [];
        records.forEach((element) => {
          requestData.push({
            visaRequestExpenseID: element.visaRequestExpenseID,
            visaRequestExpenseType: element.visaExpenseType,
          });
        });
        this.visaExpensesService.dismissExpenses(requestData).subscribe({
          next: (res: any) => {
            this.notifications.notifySuccess('Expenses dismissed successfully');
            this.selectedRecords = [];
            this.checkAllOption = false;
            this.updateSelectedRecords();
            this.getVisaExpenses(this.currentPage, this.currentSize);
          },
        });
      }
    );
  }
  auditStatements() {
    this.router.navigateByUrl('/accounting/v2/visa-expenses/statements');
  }
  configureExpenses() {
    this.ccDialog.originalOpen(ConfigureExpnesesComponent, {});
  }
  paymentTypeCharges() {
    this.visaExpensesService.getPaymentTypeCharges().subscribe((res: any) => {
      this.ccDialog.originalOpen(PaymentTypeChargesComponent, { data: res });
    });
  }
  exportExcel() {
    if (this.advancedSearchForm.controls['search'].value != undefined) {
      this.mediaService.downloadFile(
        'accounting/visarequestexpense/advanceSearch/csv',
        '',
        {
          method: 'GET',
          headers: {
            searchFilter: this.advancedSearchForm.controls['search'].value,
          },
        }
      );
    } else {
      this.mediaService.downloadFile(
        'accounting/visarequestexpense/csv/search',
        '',
        {
          params: {
            searchname: this.filterForm.controls['searchName'].value,
            searchstatus: this.filterForm.controls['selectedStatus'].value,
          },
        }
      );
    }
  }
  downloadFile(data: Blob, filename: string) {
    const url = window.URL.createObjectURL(data);
    const a = document.createElement('a');
    document.body.appendChild(a);
    a.setAttribute('style', 'display: none');
    a.href = url;
    a.download = filename;
    a.click();
    window.URL.revokeObjectURL(url);
    a.remove();
  }
}
