import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { VisaExpensesListComponent } from './components/visa-expenses-list/visa-expenses-list.component';
import { StatementsComponent } from './components/statements/statements.component';

const routes: Routes = [
  {
    path: '',
    component: VisaExpensesListComponent,
    data: { pageCode: 'ACCOUNTING__visaExpensesStatements' },
  },
  {
    path: 'statements',
    children: [
      {
        path: '',
        component: StatementsComponent,
        data: {
          pageCode: 'ACCOUNTING__visaExpensesStatements',
        },
      },
      {
        path: 'statement-matching-result/:id',
        loadChildren: () =>
          import(
            './components/statement-matching-result/statement-matching-result.module'
          ).then((m) => m.StatementMatchingResultModule),
        data: {
          label: 'Visa Expenses Matching Statements Result',
          pageCode: 'ACCOUNTING__visaExpensesStatementMatchingResult',
        },
      },
    ],
    data: {
      label: 'Visa Expenses Statements',
    },
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class VisaExpensesRoutingModule {}
