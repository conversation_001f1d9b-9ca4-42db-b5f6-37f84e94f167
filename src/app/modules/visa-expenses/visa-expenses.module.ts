import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { VisaExpensesRoutingModule } from './visa-expenses-routing.module';
import { VisaExpensesListComponent } from './components/visa-expenses-list/visa-expenses-list.component';
import { ConfigureExpnesesComponent } from './components/configure-expneses/configure-expneses.component';
import { PaymentTypeChargesComponent } from './components/payment-type-charges/payment-type-charges.component';
import { StatementsComponent } from './components/statements/statements.component';
import { AddNewStatementComponent } from './components/add-new-statement/add-new-statement.component';
import { CCButtonModule } from '@maids/cc-lib/button';
import { CCAccordionModule } from '@maids/cc-lib/accordion';
import { CCCheckboxModule } from '@maids/cc-lib/checkbox';
import { CCSelectInputModule } from '@maids/cc-lib/select-input';
import { CCInputModule } from '@maids/cc-lib/input';
import { CCDatagridModule } from '@maids/cc-lib/datagrid';
import { CCDialogModule } from '@maids/cc-lib/dialog';
import { CCAdvancedSearchModule } from '@maids/cc-lib/advanced-search';
import { CCTextareaModule } from '@maids/cc-lib/textarea';
import { CCIconModule } from '@maids/cc-lib/icon';
import { CCMenuModule } from '@maids/cc-lib/menu';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CCTooltipModule } from '@maids/cc-lib/tooltip';
import { AddEditExpenseConfigComponent } from './components/add-edit-expense-config/add-edit-expense-config.component';
import { CCDaterangePickerModule } from '@maids/cc-lib/date';
import { CCFileUploaderModule } from '@maids/cc-lib/file-uploader';

@NgModule({
  declarations: [
    VisaExpensesListComponent,
    ConfigureExpnesesComponent,
    PaymentTypeChargesComponent,
    StatementsComponent,
    AddNewStatementComponent,
    AddEditExpenseConfigComponent,
  ],
  imports: [
    CommonModule,
    VisaExpensesRoutingModule,
    CCButtonModule,
    CCAccordionModule,
    CCCheckboxModule,
    CCSelectInputModule,
    CCInputModule,
    CCDatagridModule,
    CCDialogModule,
    CCAdvancedSearchModule,
    CCTextareaModule,
    CCIconModule,
    CCTooltipModule,
    CCMenuModule,
    CCDaterangePickerModule,
    ReactiveFormsModule,
    CCFileUploaderModule.forChild({}),
    FormsModule,
  ],
})
export class VisaExpensesModule {}
