import { environment } from "./environment";

export const API = {
  base: [environment.apiBase].join('/'),
  adminMeta: [environment.apiBase, 'admin', 'sms', 'meta', 'page'].join('/'),
  public: [environment.apiBase, 'public'].join('/'),
  upload: [environment.apiBase, 'public', 'upload'].join('/'),
  download: [environment.apiBase, 'public', 'download'].join('/'),
  login: [environment.apiBase, 'public', 'login'].join('/'),
  //region upload statement
  createFileStatement:['accounting','bankStatementFile','create'].join('/'),
  deleteFileStatement:['accounting','bankStatementFile','deleteFileStatement'].join('/'),
  getFileStatement:['accounting','bankStatementFile','filterStatement'].join('/'),
  getbankStatementFileSummary:['accounting','bankStatementFile'].join('/'),
  confirmTransactions:['accounting','bankStatementFile','confirmTransactions'].join('/'),
  confirmAllTransactions:['accounting','bankStatementFile','confirmAllTransactions'].join('/'),
  USclientsOptions:['accounting','clients','clients','name'].join('/'),
  UScontractsOptions:['accounting','clients','clientcontracts'].join('/'),
  USpdcByContract:['accounting','payments','pdcByContract'].join('/'),
  matchPaymentToTransaction:['accounting','bankStatementFile','matchPaymentToTransaction'].join('/'),
  addNoteToTransaction:['accounting','bankStatementFile','addNoteToTransaction'].join('/'),
  USgetRevenueOptions:['accounting','revenues','page','searchRevenues'].join('/'),
  USgetExpensesOptions:['accounting','expenses','page','searchExpenses'].join('/'),
  USrefOfficeStaffOptions:['staffmgmt','officestaff','page'].join('/'),
  USrefHousemaidOptions:['accounting','housemaid','searchHousemaid'].join('/'),
  USrefClientOptions:['staffmgmt','client','status','client'].join('/'),
  USrefApplicantOptions:['recruitment','maidsAtCandidateWA','getCandidateByName'].join('/'),
  USrefProspectOptions:['staffmgmt','client','status','prospect'].join('/'),
  USrefContractOptions:['accounting','contract','searchContract'].join('/'),
  USrefFreedomOptions:['accounting','freedomoperator','search','page'].join('/'),
  getPayrollTransfers:['accounting','bankStatementFile'].join('/'),
  getPayrollTransfersCSV:['accounting','bankStatementFile'].join('/'),
  //endregion
}
