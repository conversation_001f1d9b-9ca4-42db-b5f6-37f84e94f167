import { environment } from './environment';
export const API = {
  base: [environment.apiBase].join('/'),
  adminMeta: [environment.apiBase, 'admin', 'sms', 'meta', 'page'].join('/'),
  public: [environment.apiBase, 'public'].join('/'),
  upload: [environment.apiBase, 'public', 'upload'].join('/'),
  download: [environment.apiBase, 'public', 'download'].join('/'),
  login: [environment.apiBase, 'public', 'login'].join('/'),
  getUsers: ['public', 'user', 'page'].join('/'),
  //#deposits region
  //region deposits
  getDeposits: ['accounting', 'deposits', 'page'].join('/'),
  updateDeposit: ['accounting', 'deposits', 'update'].join('/'),
  createDeposit: ['accounting', 'deposits', 'create'].join('/'),
  deleteDeposit: ['accounting', 'deposits', 'delete'].join('/'),
  paymentMethods: ['accounting', 'payments', 'getallpaymentmethods'].join('/'),
  getDepositFile:['accounting','deposits','file'].join('/'),
  deleteDepositFile:['accounting','deposits','delete','file'].join('/'),
  //#region companies
  getCompanies:['accounting','companies','page'].join('/'),
  createCompany:['accounting','companies','create'].join('/'),
  updateCompany:['accounting','companies','update'].join('/'),
  deleteCompany:['accounting','companies','delete'].join('/'),
  //#endregion
  //region failed dd generation
  getfailedddsgeneration: [
    'accounting',
    'DirectDebitGenerationPlan',
    'getfailedddsgeneration',
  ].join('/'),
  generateDDs: [
    'accounting',
    'DirectDebitGenerationPlan',
    'generatepostponedds',
  ].join('/'),
  dismissDD: ['accounting', 'DirectDebitGenerationPlan', 'delete'].join('/'),
  //endregion
  getEventsConfig: ['accounting', 'flowProcessor', 'page'].join('/'),
  subEventConfigs: ['accounting', 'flowProcessor', 'subEventConfigs'].join('/'),
  updateEventConfig: ['accounting', 'flowProcessor', 'update'].join('/'),
  ReasonOfTermination: [
    'admin',
    'picklist',
    'items',
    'ReasonOfTermination',
  ].join('/'),
  getTags: ['admin', 'tag', 'page'].join('/'),
  createTag: ['admin', 'tag', 'create'].join('/'),
  deleteSubEvent: ['accounting', 'flowSubEvents', 'delete'].join('/'),
  updateSubEvent: ['accounting', 'flowSubEvents', 'update'].join('/'),
  requiredActions: ['accounting', 'flowSubEvents', 'requiredActions'].join('/'),
  requiredDocuments: ['accounting', 'flowSubEvents', 'requiredDocuments'].join(
    '/'
  ),
  savePeriods:['accounting', 'flowProcessor', 'savePeriods'].join('/'),
  //region multiple dd configuration
  getMultipleDDConfiguration: ['accounting', 'ddfexportingconfig', 'page'].join(
    '/'
  ),
  updateMultipleDDConfiguration: [
    'accounting',
    'ddfexportingconfig',
    'update',
  ].join('/'),
  getBankddConfig: ['accounting', 'direct-debit-config'].join('/'),
  updateAllBanksDDsConfig: [
    'accounting',
    'direct-debit-config',
    'update-all',
  ].join('/'),
  //endregion
  //#region send manual dd to bank
  getSendManualDDToBank: ['accounting', 'manualddftosend', 'notsent'].join('/'),
  confirmSentToBankBatch: ['accounting', 'manualddftosend', 'confirmsenttobank'].join('/'),
  //#endregion
  //#region threshold-table
  getPaymentrequestPurposes:['accounting','paymentrequestpurposes','page'].join('/'),
  createPaymentrequestPurposes:['accounting','paymentrequestpurposes','create'].join('/'),
  updatePaymentrequestPurposes:['accounting','paymentrequestpurposes','update'].join('/'),
  csvPaymentRequestPurposes:['accounting','paymentrequestpurposes','csv'].join('/'),
  getLoanTypes:['accounting','expenses','getLoanTypes'].join('/'),
  //#endregion
  //#region RiskManagementDocuments
  getRiskManagementDocumentsList: ['accounting', 'riskDocumentsManagement', 'page'].join('/'),
  createRiskManagementDocuments: ['accounting', 'riskDocumentsManagement', 'create'].join('/'),
  updateRiskManagementDocuments: ['accounting', 'riskDocumentsManagement', 'update'].join('/'),
  deleteRiskManagementDocuments: ['accounting', 'riskDocumentsManagement', 'delete'].join('/'),
  getRiskManagementDocuments: ['accounting', 'riskDocumentsManagement', 'get'].join('/'),
  changeRiskManagementDocumentStatus: ['accounting', 'riskDocumentsManagement', 'changeStatus'].join('/'),
  searchRiskManagementDocumentsList: ['accounting', 'riskDocumentsManagement', 'getDocuments'].join('/'),
  changeStatus: ['accounting', 'riskDocumentsManagement', 'changeStatus'].join('/'),
  updateCurrentPendingVisitInfo: ['accounting', 'riskDocumentsManagement', 'updateCurrentPendingVisitInfo'].join('/'),
  getDocumentVisitHistory: ['accounting', 'riskDocumentsManagement', 'getDocumentVisitHistory'].join('/'),
  getDocumentHistory: ['accounting', 'riskDocumentsManagement', 'getDocumentHistory'].join('/'),

  getRiskManagementDocumentRolesList: ['accounting', 'riskDocumentsManagementRole', 'getActiveRoles'].join('/'),
  getRiskManagementDocumentAllRolesList: ['accounting', 'riskDocumentsManagementRole', 'page'].join('/'),
  createRiskManagementDocumentRole: ['accounting', 'riskDocumentsManagementRole', 'create'].join('/'),
  updateRiskManagementDocumentRole: ['accounting', 'riskDocumentsManagementRole', 'update'].join('/'),
  deleteRiskManagementDocumentRole: ['accounting', 'riskDocumentsManagementRole', 'deleteRole'].join('/'),
  getRiskManagementDocumentRole: ['accounting', 'riskDocumentsManagementRole', 'get'].join('/'),

  getRiskManagementToDoList: ['accounting', 'riskDocumentsManagementToDo', 'searchToDos'].join('/'),
  updateRiskDocumentsManagementTodoStatus: ['accounting', 'riskDocumentsManagementToDo', 'updateRiskDocumentsManagementTodoStatus'].join('/'),
  createRiskManagementToDo: ['accounting', 'riskDocumentsManagementToDo', 'create'].join('/'),
  updateRiskManagementToDo: ['accounting', 'riskDocumentsManagementToDo', 'update'].join('/'),
  deleteRiskManagementToDo: ['accounting', 'riskDocumentsManagementToDo', 'delete'].join('/'),
  getRiskManagementToDo: ['accounting', 'riskDocumentsManagementToDo', 'get'].join('/'),
  getRiskManagementToDoById: ['accounting', 'riskDocumentsManagementToDo'].join('/'),
  visitDone: ['accounting', 'riskDocumentsManagementToDo', 'visitDone'].join('/'),
  doneRenewal: ['accounting', 'riskDocumentsManagementToDo', 'doneRenewal'].join('/'),
  doneRegistration: ['accounting', 'riskDocumentsManagementToDo', 'doneRegistration'].join('/'),
  //#endregion
    //region Dewa Report
  getDewaReportList: ['accounting', 'dewa', 'getDEWA'].join('/'),
  //endregion
  //region insurance agreement
  insuranceAgreementsList: ['accounting', 'insuranceagreement', 'page'].join('/'),
  createInsuranceAgreement: ['accounting', 'insuranceagreement','create'].join('/'),
  updateInsuranceAgreement: ['accounting', 'insuranceagreement','update'].join('/'),
  //endregion
  //#region send dd to bank
  getDDTable:['accounting','directdebitfile','advanceSearchForSendDDToBankAcc8544'].join('/'),
  getTypeOfPayments:['public','picklist','items','TypeOfPayment'].join('/'),
  getBankNames:['accounting','directdebitfile','getBankNames'].join('/'),
  sentDDToBank:['accounting','directdebitfile','send'].join('/'),
  sendToBankInBatchByRpa:['accounting','directdebitfile','generatebatch','rpa'].join('/'),
  sendmultipleddfs:['accounting','directdebitfile','exportmultipleddfs'].join('/'),
  sendallddfs:['accounting','directdebitfile','exportallddfs'].join('/'),
  confirmSentDDToBankBatch:['accounting','directdebitfile','sendtobank'].join('/'),
  exportddfs:['accounting','directdebitfile','advancesearch','csv','sublist'].join('/'),
  sendExportallddfs:['accounting','directdebitfile','advancesearch','csv','alllist','send'].join('/'),
  downloadExportallddfs:['accounting','directdebitfile','advancesearch','csv','alllist','download'].join('/'),
  //#endregion
  // #Region
  getBuckets: ['accounting', 'buckets', 'page', 'searchBuckets'].join('/'),
  getRevenues: ['accounting', 'revenues', 'page', 'searchRevenues'].join('/'),
  getExpensesList: ['accounting', 'expenses', 'page', 'searchExpenses'].join('/'),
  getStatementOfAccountData: ['accounting', 'account-balance', 'get-balance-report'].join('/'),
  // #EndRegion
  //#region payment-plans
  submitContractInfo: ['accounting', 'contract', 'submitContractInfo'].join('/'),
  getContractInfo: ['accounting', 'contract', 'contractsForInfo'].join('/'),
  contractPaymentPlan: ['accounting', 'contract', 'contractPaymentPlan'].join('/'),
  //#end region
  //region Client Refund
  getClientRefundSetup:['accounting','clientRefundSetup','list'].join('/'),
  getClientRefundSetupData:['accounting','clientRefundSetup'].join('/'),
  getCliendRefundUsers:['public','user','page'].join('/'),
  getallclientspurpose:['accounting','paymentrequestpurposes','getallclientspurpose'].join('/'),
  getPaymentMethodPicklistClientRefund:['public','picklist','items','Partial_Refund_For_Cancellation_Payments_Method'].join('/'),
  getClientRefundParameter:['public','parameter'].join('/'),
  getComplaintsClientRefundTypes:['complaints','category','list','withTypes'].join('/'),
  getClientRefundBanksList:['public','picklist','items','BankName'].join('/'),
  getClientRefundPurposeCategory:['public','picklist','items','REFUND_PURPOSE_CATEGORY'].join('/'),
  createClientRefundSetup:['accounting','clientRefundSetup','create'].join('/'),
  updateClientRefundSetup:['accounting','clientRefundSetup','update'].join('/'),
  //endregion
  //#region purchase order history
  getPurchaseOrder:['accounting','purchase-order','page'].join('/'),
  getSupplierList:['accounting','supplier','get-supplier-list'].join('/'),
  getCategoryOptions:['accounting','category','list'].join('/'),
  getPurshaseOrderPage:['accounting','purchase-order','page-order'].join('/'),
  getPendingPurchaseRequestList:['accounting','purchase-order','get-pending-purchase-request-list'].join('/'),
  getItemsOfOrder:['accounting','purchase-order','get-items-of-order'].join('/'),
  //#end region
  //#region expense items
  getCategory: ['accounting', 'category'].join('/'),
  getCategories: ['accounting', 'category', 'page'].join('/'),
  getItemsWithSearch: ['accounting', 'item', 'page-with-search'].join('/'),
  calculateInitialCycleInventory: [
    'accounting',
    'item',
    'calculate-initial-cycle-inventory',
  ].join('/'),
  updateItem: ['accounting', 'item', 'update'].join('/'),
  updateSalesBinderData:['accounting','supplier','update-sales-binder-data'].join('/'),
  //#endregion
  //#region expense category
  ECgetCategories: ['accounting', 'category', 'page'].join('/'),
  updateData: ['accounting', 'supplier', 'update-sales-binder-data'].join('/'),
  searchExpensesPageable: ['accounting', 'expenses', 'page', 'searchExpenses'].join('/'),
  getCategoryOrderCycle: [
    'public',
    'picklist',
    'items',
    'category_order_cycle',
  ].join('/'),
  updateCategory: ['accounting', 'category', 'update'].join('/'),
  //#endregion
  //#region Reccurring CC Payments Issues Management
  getTheErrorCodeNotSelectedAndTheSubFlow:['accounting','recurringCreditCardPaymentsIssue','getTheErrorCodeNotSelectedAndTheSubFlow'].join('/'),
  getRecurringCreditCardPaymentsIssue:['accounting','recurringCreditCardPaymentsIssue','page'].join('/'),
  updateRecurringCreditCardPaymentsIssue:['accounting','recurringCreditCardPaymentsIssue','update'].join('/'),
  createRecurringCreditCardPaymentsIssue:['accounting','recurringCreditCardPaymentsIssue','create'].join('/'),
  deleteRecurringCreditCardPaymentsIssue:['accounting','recurringCreditCardPaymentsIssue','delete'].join('/'),
  createAllRecurringCreditCardPaymentsIssue:['accounting','recurringCreditCardPaymentsIssue','createAll'].join('/'),
  updateAllRecurringCreditCardPaymentsIssue:['accounting','recurringCreditCardPaymentsIssue','updateAll'].join('/'),
  //#end region
  //#region credit card holder
  getPendingCreditCardPayments: ['accounting','expense-payment','get-pending-credit-card-payments'].join('/'),
  getCreditCardPaymentInfo: ['accounting', 'expense-payment'].join('/'),
  getCreditCardBuckets: ['accounting','expense-payment','get-credit-card-buckets'].join('/'),
  payCreditCard: ['accounting','expense-payment','pay-credit-card-payment'].join('/'),
  //#endregion
  //region cancel dd from bank
  getDirectdebitcancelationtodos:['accounting','directdebitcancelationtodos','getAll'].join('/'),
  updateDirectdebitcancelationtodos:['accounting','directdebitfile','update'].join('/'),
  generateDDCancellation:['accounting','directdebitcancelationtodos','generateDDCancellation'].join('/'),
  sentToBank:['accounting','directdebitcancelationtodos','sentToBank'].join('/'),
  allSentToBank:['accounting','directdebitcancelationtodos','allSentToBank'].join('/'),
  //endregion
  //region contract payments
  CPgetDefaultProvider:['public','parameter'].join('/'),
  getContractPaymentConfirmation:['accounting','contract-payment-confirmation','search'].join('/'),
  CPcancelOperation:['accounting','contract-payment-confirmation','deletedirectdebitpayments'].join('/'),
  CPcancelAllOperation:['accounting','contract-payment-confirmation','deleteAll'].join('/'),
  CPSentToBank:['accounting','contractpayments','send'].join('/'),
  CPconfirmPayment:['accounting','contract-payment-confirmation','confirm'].join('/'),
  exportExcelNotDD:['accounting','contract-payment-confirmation','search','csv'].join('/'),
  onlineCardStatementFileSearch:['accounting','onlineCardStatementFile','onlineCardStatementFileSearch'].join('/'),
  deleteOnlineCardStatementFile:['accounting','onlineCardStatementFile','deleteOnlineCardStatementFile'].join('/'),
  createOnlineCardStatementFile:['accounting','onlineCardStatementFile','create'].join('/'),
  CPpaymentTypeOptions:['public','picklist','items','typeOfPayment'].join('/'),
  getOnlineCardStatementFile:['accounting','onlineCardStatementFile'].join('/'),
  CPmatchWithWithoutUpdate:['accounting','onlineCardStatementRecord','confirmTodoManually'].join('/'),
  CPviewResult:['accounting','onlineCardStatementRecord','viewResult'].join('/'),
  CPsearchByGrid:['accounting','onlineCardStatementRecord','searchByGrid'].join('/'),
  getTodosForMatchOnlineStatement:['accounting','onlineCardStatementRecord','getTodosForMatchOnlineStatement'].join('/'),
  getTodosForMatchOnlineStatementForRefundPayment:['accounting','onlineCardStatementRecord','getTodosForMatchOnlineStatementForRefundPayment'].join('/'),
  CPgenerateCsv:['accounting','onlineCardStatementRecord','generateCsv'].join('/'),
  createPosTransaction:['accounting','onlineCardStatementFile','createPosTransaction'].join('/'),
  //endregion
  //#region Expense refund history
  getSearchRefund:['accounting','expenseRequestTodo','searchRefund'].join('/'),
  officeStaffOptions:['accounting','officestaff','search','page'].join('/'),
  housemaidsOptions:['accounting','housemaid','searchHousemaid'].join('/'),
  applicantsOptions:['recruitment','maidsAtCandidateWA','getCandidateByName'].join('/'),
  suppliersOptions:['accounting','supplier','get-supplier-list'].join('/'),
  exportRefundsAsCsv:['accounting','expenseRequestTodo','exportRefundsAsCsv'].join('/'),
  updateRefund :['accounting','expenseRequestTodo','update'].join('/'),
  //#end region
  //#region Telecom Management
  getTelecomList: ['accounting', 'telecomphone', 'page', 'search'].join('/'),
  getCSVFile: ['accounting', 'telecomphone', 'search', 'csv'].join('/'),
  getexpensesList: ['accounting', 'expenses', 'list'].join('/'),
  addPhone: ['accounting', 'telecomphone', 'create'].join('/'),
  deletePhone: ['accounting', 'telecomphone', 'delete'].join('/'),
  updatePhone: ['accounting', 'telecomphone', 'update'].join('/'),
  getPhone: ['accounting', 'telecomphone'].join('/'),
  //#endregion
  //#region TransactionPostingSetup
    getTransactionPostingSetupList: [
      'accounting',
      'transactionpostingrules',
      'advancesearch',
      'page',
    ].join('/'),

    TransactionPostingSetupMetaSearch: [
      'accounting',
      'transactionpostingrules',
      'meta',
      'transactionpostingrules_management',
    ].join('/'),

    disableTransactionPostingSetup: [
      'accounting',
      'transactionpostingrules',
      'deactivate-rule'].join('/'),

    enableTransactionPostingSetup: [
      'accounting',
      'transactionpostingrules',
      'activate-rule'].join('/'),

    getProspectType: ['public', 'picklist', 'items', 'ProspectType'].join('/'),
    getMethodOfPayment: ['accounting', 'payments', 'getallpaymentmethods'].join('/'),
    getTypeOfPayment: ['public', 'picklist', 'items', 'TypeOfPayment'].join('/'),
    getRevenueOptions: ['accounting', 'revenues', 'page', 'searchRevenues'].join('/'),
    getBucketOptions: ['accounting', 'buckets', 'page', 'searchBuckets'].join('/'),
    getTransactionLicense: ['public','picklist', 'items', 'transaction_license'].join('/'),
    getPurposeOfPaymentOptions: ['accounting', 'paymentrequestpurposes', 'getallhousemaidspurpose'].join('/'),
    getExpenseOptions: ['accounting', 'expenses', 'page', 'searchExpenses'].join('/'),

    createTransactionPostingSetup: [
      'accounting',
      'transactionpostingrules',
      'create',
    ].join('/'),

    updateTransactionPostingSetup: [
      'accounting',
      'transactionpostingrules',
      'update',
    ].join('/'),

    deleteTransactionPostingSetup: [
      'accounting',
      'transactionpostingrules',
      'delete',
    ].join('/'),

    getTransactionPostingSetup: [
      'accounting',
      'transactionpostingrules',
    ].join('/'),

    //#endregion
  //#region Maid Expenses Summary
  searchExpenses: ['accounting', 'expenses', 'search'].join('/'),
  getExpenses: ['accounting', 'expenseRequestTodo', 'search'].join('/'),
  expensePayment: ['accounting', 'expense-payment'].join('/'),
  getExpenseFile: ['accounting', 'expenseRequestTodo', 'file'].join('/'),
  //#endregion
   //region Purchase Requests
   getAuditorRequestsList:['accounting','purchasing-auditor','get-auditor-requests-list'].join('/'),
   getPurchaseOrdersOrderCycleName:['accounting','purchasing-manager','get-purchase-orders-order-cycle-name'].join('/'),
   getPurchase:['accounting','purchasing'].join('/'),
   completePurchaseRequest:['accounting','purchasing','complete'].join('/'),
   cancelPurchaseItem:['accounting','purchase-item','delete'].join('/'),
   updateConsumptionRate:['accounting','purchasing-auditor','update-consumption-rate'].join('/'),
   getConfirmRequestItems:['accounting','purchasing-auditor','get-confirm-request-items'].join('/'),
   getMaintenanceRequest:['accounting','maintenance-request'].join('/'),
   completeMaintenanceRequest:['accounting','maintenance-request','complete'].join('/'),
   getConfirmPricesSupplierGrids:['accounting','purchasing-auditor','get-confirm-prices-supplier-grids'].join('/'),
   getConfirmPricesReviewGrids:['accounting','purchasing-auditor','get-confirm-prices-review-grids'].join('/'),
   confirmPriceActionCancel:['accounting','purchasing-auditor','confirm-price-action-cancel'].join('/'),
   confirmPriceActionApprove:['accounting','purchasing-auditor','confirm-price-action-approve'].join('/'),
   confirmPriceActionGetBetterPrices:['accounting','purchasing-auditor','confirm-price-action-get-better-prices'].join('/'),
   confirmPriceActionUndo:['accounting','purchasing-auditor','confirm-price-action-undo'].join('/'),
  //endregion
  //#region purchasing manager
  getManagerRequestsList:['accounting','purchasing-manager','get-manager-requests-list'].join('/'),
  PMgetPurchaseOrdersOrderCycleName:['accounting','purchasing-manager','get-purchase-orders-order-cycle-name'].join('/'),
  getSuppliersRequestItems:['accounting','purchasing-manager','get-suppliers-request-items'].join('/'),
  getSalesbinderSupplierList:['accounting','supplier','get-salesbinder-supplier-list'].join('/'),
  approveBestSupplierStep:['accounting','purchasing-manager','approve-best-supplier-step'].join('/'),
  approveGetBestSupplierStep:['accounting','purchasing-manager','approve-get-better-supplier-step'].join('/'),
  getPurchaseOrdersToPurchaseStep:['accounting','purchasing-manager','get-purchase-orders-to-purchase-step'].join('/'),
  getRecheckSuppliersRequestItems:['accounting','purchasing-manager','get-recheck-suppliers-request-items'].join('/'),
  cancelItemFormPurchaseOrderInPurchaseStep:['accounting','purchasing-manager','cancel-item-form-purchase-order-in-purchase-step'].join('/'),
  getBillInfoForPurchase:['accounting','purchasing-manager','get-bill-info-for-purchase'].join('/'),
  purchaseInOneBill:['accounting','purchasing-manager','purchase-in-one-bill'].join('/'),
  PMgetMaintenance:['accounting','maintenance-request'].join('/'),
  PMgetExpenses:['accounting','expenses'].join('/'),
  PMsendFormApproval:['accounting','maintenance-request','complete'].join('/'),
  //#end region
  //#region Expenses
  getRequestedFromOpts:['public','picklist','items','PICKLIST_EXPENSE_REQUESTED_FROM'].join('/'),
  EgetUsers:['public','user','page/'].join('/'),
  EsearchExpensesNew:['accounting','expenses','page','searchExpensesNew'].join('/'),
  EdeleteExpense:['accounting','expenses','deleteExpense'].join('/'),
  enableDisableExpense:['accounting','expenses','enableDisableExpense'].join('/'),
  createExpense:['accounting','expenses','create'].join('/'),
  updateExpense:['accounting','expenses','update'].join('/'),
  EgetLoanTypes:['accounting','expenses','getLoanTypes'].join('/'),
  EgetParameter:['public','parameter'].join('/'),
  EgetExpenses:(id:string) => ['accounting','expenses',id].join('/'),
  EgetOfficeStaffTeam:['public','picklist','items','OfficestaffTeam'].join('/'),
  EsearchBuckets:['accounting','buckets','page','searchBuckets'].join('/'),
  getActiveSuppliers:['accounting','supplier','getActiveSuppliers'].join('/'),
  AdditionReasons:['public','picklist','items','AdditionReasons/'].join('/'),
  getSubExpenseCode:(id:string) => ['accounting','expenses','getSubExpenseCode',id].join('/'),
  //#endregion
  getAuditTodosList: ['accounting', 'bucket-replenishment', 'getRequests'].join(
    '/'
  ),
  getReplenishmentRequests: [
    'accounting',
    'bucket-replenishment',
    'searchRequests',
  ].join('/'),
  searchBuckets: ['accounting', 'buckets', 'page', 'searchBuckets'].join('/'),
  approveRequest: ['accounting', 'bucket-replenishment', 'approveRequest'].join(
    '/'
  ),
  rejectRequest: ['accounting', 'bucket-replenishment', 'rejectRequest'].join(
    '/'
  ),
  missingTaxInvoice: ['accounting', 'reconciliator', 'missingTaxInvoice'].join(
    '/'
  ),
  missingInvoiceDismissed: [
    'accounting',
    'reconciliator',
    'missingInvoiceDismissed',
  ].join('/'),
  getDoneByReconciliator: [
    'accounting',
    'reconciliator',
    'getDoneByReconciliator',
  ].join('/'),
  addApprovedRequest: [
    'accounting',
    'bucket-replenishment',
    'addApprovedRequest',
  ].join('/'),
  //#region insurance invoicing
  debitnotelist:['accounting','insuranceagreement','debitnotelist'].join('/'),
  creditnotelist:['accounting','insuranceagreement','creditnotelist'].join('/'),
  //#end region
  //region Add Maid Refund
  AMRgetHouseMaidOptions:['accounting','housemaid','searchActiveHousemaid'].join('/'),
  AMRgetReferredMaidOptions:['accounting','housemaid','getActiveMaids'].join('/'),
  getCheckSpentThirtyDays:['recruitment','housemaid','calculate-passed-days-with-client'].join('/'),
  getMaidPaymentValidation:['payroll','ManagerNotes','getAdditionsForMaid'].join('/'),
  getPurposesPicklist:['public','picklist','items'].join('/'),
  addMaidRefund:['accounting','expenseRequestTodo','create'].join('/'),
  AMRgetExpenseForPage:['accounting','expenseRequestTodo','expensesavailableforpage'].join('/'),
  AMRgetPaymentsMethodsByExpense:['accounting','expenses'].join('/'),
  AMRgetPublicParameterByCode:['public','parameter'].join('/'),
  checkToCreateVacationRequest:['accounting','housemaid','checktocreatevacationrequest'].join('/'),
  gethousemaidvacationsinfo:['staffmgmt','HousemaidVacations','gethousemaidvacationsinfo'].join('/'),
  //endregion
  //#region acountant todo
  findOpenedTodos:['accounting','accountantTodo','findOpenedTodos'].join('/'),
  cancelOpenedTodo:(id:any)=>['accounting','accountantTodo','cancelOpenedTodo',id].join('/'),
  accountantAction:['accounting','accountantTodo','accountantAction'].join('/'),
  bulkAccountantActionByIds:['accounting','accountantTodo','bulkAccountantActionByIds'].join('/'),
  bulkAccountantActionAllTodos:['accounting','accountantTodo','bulkAccountantActionAllTodos'].join('/'),
  exportCSVAccountantTodoMail:['accountantTodo','findOpenedTodos','csv','mail'].join('/'),
  //#end region
  //#region stock keeper todo
  getStockKeeperTasks:['accounting','stock-keeper','get-stock-keeper-tasks'].join('/'),
  getBillInfoForMaintenanceRequest:['accounting','stock-keeper','get-bill-info-for-maintenance-request'].join('/'),
  getOrderObject:['accounting','stock-keeper','get-order-object'].join('/'),
  confirmReceiveOrder:['accounting','stock-keeper','confirm-receive-order'].join('/'),
  SKTpurchaseInOneBill:['accounting','stock-keeper','purchase-in-one-bill'].join('/'),
  doneMaintenanceRequest:['accounting','stock-keeper','done-maintenance-request'].join('/'),
  //#endregion
  //region unknown wire transfer
  getUnknownWireTransfer:['accounting','transactions','page','advancesearch'].join('/'),
  generateCSVFileAdvanced:['accounting','transactions','csv','advancesearch','mail'].join('/'),
  UWTclientsOptions:['accounting','clients','clients','name'].join('/'),
  UWTtoBucketOptions:['accounting','buckets','page','searchBuckets'].join('/'),
  UWTrevenueOptions:['accounting','revenues','page','searchRevenues'].join('/'),
  UWTtypeOfPaymentOptions:['public','picklist','items','typeOfPayment'].join('/'),
  UWTclientcontracts:['accounting','clients','clientcontracts'].join('/'),
  UWTmatchingParameter:['public','parameter'].join('/'),
  getunreplacedbouncedpayments:['clientmgmt','contract','getunreplacedbouncedpayments'].join('/'),
  getWireTransferTempPayment:['accounting','wireTransferTempPayment','getPayments'].join('/'),
  DeleteWireTransferTempTransaction:['accounting','wireTransferTempTransaction','delete'].join('/'),
  updateWireTransferTempTransaction:['accounting','wireTransferTempTransaction','update'].join('/'),
  getWireTransferTempTransaction:['accounting','wireTransferTempTransaction','getTransactions'].join('/'),
  confirmWireTransferTempTransaction:['accounting','wireTransferTempPayment','confirmTransactions'].join('/'),
  UWTsavePayments:['accounting','wireTransferTempPayment','savePayments'].join('/'),
  UWTTransactionLicense:['public','picklist','items','transaction_license'].join('/'),
  //endregion
  //#Region Client Refund Summary
  getPurposes: ['accounting', 'paymentrequestpurposes', 'getallclientspurposewithsetup'].join('/'),
  getCategoriesPicklist: ['public', 'picklist', 'items', 'REFUND_PURPOSE_CATEGORY'].join('/'),
  getComplaintTypes: ['complaints', 'category', 'list', 'withTypes'].join('/'),
  getPaymentMethods: ['accounting', 'clientRefundTodo', 'getRefundMethods'].join('/'),
  searchClientRefunds: ['accounting', 'clientRefundTodo', 'search', 'page'].join('/'),
  exportClientRefunds: ['accounting', 'clientRefundTodo', 'downloadSearchAttachment','csv'].join('/'),
  //#endregion
  //region tenancy contracts
  gettenancyContracts: ['accounting','tenancyContracts','advancedSearch','page'].join('/'),
  deletetenancyContracts: ['accounting','tenancyContracts','delete'].join("/"),
  exporttenancytoCSV:['accounting','tenancyContracts','exportToCsv'].join('/'),
  updatetenancyContracts: ['accounting','tenancyContracts','update'].join("/"),
  getTypesOfNotifyDate:['accounting','tenancyContracts','getTypesOfNotifyDate'].join('/'),
  getAdminUsers:['admin','user','page','active'].join('/'),
  createTenancyContract: ['accounting', 'tenancyContracts', 'create'].join('/'),
  nonclientpdcTanancy:['accounting','nonclientpdc','byTenancyContract'].join('/'),
  tenancyContractsList:['accounting','tenancyContracts','list'].join("/"),
  createNonclientpdc:['accounting','nonclientpdc','create'].join("/"),
  //endregion
  getCashiersOptions:['accounting','expense-payment','cashiers'].join('/'),
  //region manage suppliers
  getSuppliers:['accounting','supplier','page/'].join('/'),
  MSupdateSalesBinderData:['accounting','supplier','update-sales-binder-data'].join('/'),
  supplierChangeStatus:['accounting','supplier','update'].join('/'),
  getSupplierData:['accounting','supplier'].join('/'),
  createSupplier:['accounting','supplier','create'].join('/'),
  updateSupplier:['accounting','supplier','update'].join('/'),
  MSgetHolderOptions:['public','user','page'].join('/'),
  MScheckPosition:['public','parameter'].join('/'),
  MSvalidateSwiftCode:['accounting','supplier','checkSwift'].join('/'),
  MSvalidateIBAN:['accounting','contractpaymentterm','checkiban'].join('/'),
  MSgetExpensesByCode:['accounting','expenses','getExpenseRelatedToSupplier'].join('/'),
  //end region
  //region request expenses
  expensesavailableforpage:['accounting','expenseRequestTodo','expensesavailableforpage'].join('/'),
  //endregion
  //region Telecom expense request
  getTelecomUsages:['accounting','expenseRequestTodo','getAvailableTelecomPhonesUsages'].join('/'),
  getAvailableTelecomPhonesExpenses:['accounting','expenseRequestTodo','getAvailableTelecomPhonesExpenses'].join('/'),
  createTelecomExpense:['accounting','expenseRequestTodo','create'].join('/'),
  //endregion
  //region Insurance Expenses
  getInsuranceExpenseName:['public','parameter'].join('/'),
  getInsuranceExpenseByCode:['accounting','expenses','getByCode'].join('/'),
  getInsuranceSupplier:['accounting','supplier'].join('/'),
  createInsuranceExpense:['accounting','expenseRequestTodo','create'].join('/'),
  //endregion
  //region Ticketing expenses
  getTicketingExpenses:['accounting','expenseRequestTodo','expensesavailableforpage'].join('/'),
  bookAvailableTicket:['recruitment','booker','ticket-available'].join('/'),
  createTicketRequest:['accounting','expenseRequestTodo','create'].join('/'),
  getTicketingUsers:['public','user','page'].join('/'),
  getTeams:['public','picklist','items','OfficestaffTeam'].join('/'),
  getApplicants:['recruitment','maidsAtCandidateWA','getCandidateByName'].join('/'),
  getHousemaids:['accounting','housemaid','searchHousemaid'].join('/'),
  getOfficeStaffs:['payroll','officestaff','asPicklist'].join('/'),
  getCountries:['public','picklist','items','countries'].join('/'),
  getAirports:['public','picklist','items','airports'].join('/'),
  getCurrencies:['public','picklist','items','EXPENSE_CURRENCY'].join('/'),
  getTicketingExpense:['accounting','expenses'].join('/'),
  ticketingAddSupplier:['accounting','expenses','add-supplier'].join('/'),
  ticketingValidateAddIBAN:['accounting','contractpaymentterm','checkiban'].join('/'),
  //endregion
  //region vip expense
  vipExpensesHolders: ['public', 'user', 'page'].join('/'),
  vipBuckets:['accounting','buckets','page','searchBuckets'].join('/'),
  vipOfficeStaffTeam:['public','picklist','items','OfficestaffTeam'].join('/'),
  vipApplicants:['recruitment','maidsAtCandidateWA','getCandidateByName'].join('/'),
  vipSuppliers:['accounting','supplier','search','page'].join('/'),
  vipHouseMaids:['accounting','housemaid','searchHousemaid'].join('/'),
  vipOfficeStaffs:['payroll','officestaff','asPicklist'].join('/'),
  vipExpenses:['accounting','expenses'].join('/'),
  vipExpenseForPage:['accounting','expenseRequestTodo','expensesavailableforpage'].join('/'),
  vipCreateRequest:['accounting','expenseRequestTodo','create'].join('/'),
  vipValidateAddIBAN:['accounting','contractpaymentterm','checkiban'].join('/'),
  vipGetCurrencies:['public','picklist','items','EXPENSE_CURRENCY'].join('/'),
  vipAddSupplier:['accounting','expenses','add-supplier'].join('/'),
  //endregion
  //region transportation expense
  transportationHolders:['public','user','page'].join('/'),
  transportationExpenses:['public','parameter'].join('/'),
  transportationGetExpenses:['accounting','expenses','getByCode'].join('/'),
  transportationExpenseRequestNeedsInvoice:['accounting','expenseRequestTodo','transportationexpenserequestneedsinvoice'].join('/'),
  transportationAuditInvoice:['accounting','expenseRequestTodo','parsetransportationexpensefile'].join('/'),
  transportationCreateExpense:['accounting','expenseRequestTodo','create'].join('/'),
  transportationGetSupplierInfo:['accounting','supplier'].join('/'),
  transportationAddSupplier:['accounting','expenses','add-supplier'].join('/'),
  transportationValidateIban:['accounting','contractpaymentterm','checkiban'].join('/'),
  //endregion
  //region DEWA Expense
  getDewaUsage:['accounting','expenseRequestTodo','getAvailableDewaPhones'].join('/'),
  createDewaUsage:['accounting','expenseRequestTodo','create'].join('/'),
  //endregion
  //region add expense
  ADDholderOptionsExpense:['public','user','page'].join('/'),
  ADDteamOptionsExpense:['public','picklist','items','OfficestaffTeam'].join('/'),
  ADDapplicantOptionsExpense:['recruitment','maidsAtCandidateWA','getCandidateByName'].join('/'),
  ADDsuppliersAjaxOptionsExpense:['accounting','supplier','search','page'].join('/'),
  ADDhousemaidOptionsExpense:['accounting','housemaid','searchHousemaid'].join('/'),
  ADDofficeStaffOptionsExpense:['payroll','officestaff','asPicklist'].join('/'),
  ADDgetExpenseDetails:['accounting','expenses'].join('/'),
  ADDgetSupplierDetails:['accounting','supplier'].join('/'),
  ADDgetExpenses:['accounting','expenses','page','searchExpenses'].join('/'),
  ADDgetExpenseForPage:['accounting','expenseRequestTodo','expensesavailableforpage'].join('/'),
  ADDcreateExpense:['accounting','expenseRequestTodo','create'].join('/'),
  ADDvalidateIBAN:['accounting','contractpaymentterm','checkiban'].join('/'),
  ADDgetCurrencies:['public','picklist','items','EXPENSE_CURRENCY'].join('/'),
  ADDaddSupplier:['accounting','expenses','add-supplier'].join('/'),
  //endregion
  //region maintenance request
  MRgetSuppliers:['accounting','supplier','page/'].join('/'),
  MRgetExpenseForPage:['accounting','expenseRequestTodo','expensesavailableforpage'].join('/'),
  MRgetExpenses:['accounting','expenses'].join('/'),
  MRcreateExpense:['accounting','maintenance-request','create'].join('/'),
  //end region
  //region one time request
  OTRgetSuppliers:['accounting','supplier','page'].join('/'),
  OTRteamOptions:['public','picklist','items','OfficestaffTeam'].join('/'),
  OTRapplicantOptions:['recruitment','maidsAtCandidateWA','getCandidateByName'].join('/'),
  OTRhousemaidOptions:['accounting','housemaid','searchHousemaid'].join('/'),
  OTRofficeStaffOptions:['payroll','officestaff','asPicklist'].join('/'),
  OTRgetExpenses:['accounting','expenses'].join('/'),
  OTRgetCurrencies:['public','picklist','items','EXPENSE_CURRENCY'].join('/'),
  OTRrequestorOptions:['public','user','page'].join('/'),
  OTRexpenseOptions:['accounting','expenseRequestTodo','expensesavailableforpage'].join('/'),
  OTRcreateExpense:['accounting','expenseRequestTodo','create'].join('/'),
  //endregion
  getPendingCashierPayments: [
    'accounting',
    'expense-payment',
    'get-pending-cashier-payments',
  ].join('/'),
  getPedingInvoicePayment: [
    'accounting',
    'expense-payment',
    'get-pending-invoice-payments',
  ].join('/'),
  getFinalSettlementPayments: [
    'accounting',
    'paymentOrders',
    'getFinalSettlementPayments',
  ].join('/'),
  paymentDetails: ['accounting', 'expense-payment'].join('/'),
  getFile: ['accounting', 'expense-payment', 'file'].join('/'),
  cashierPayMoney: ['accounting', 'expense-payment', 'cashier-pay-money'].join(
    '/'
  ),
  cashierCollectInvoice: [
    'accounting',
    'expense-payment',
    'cashier-collect-invoice',
  ].join('/'),
  cashierCollectPayment: ['accounting', 'paymentOrders', 'collect'].join('/'),
  uploadFile: ['public', 'upload'].join('/'),
  updateCashBox: ['accounting', 'cash-box', 'update-cash-box'].join('/'),
  getCurrentBalance:['accounting','expense-payment','get-current-balance'].join('/'),
  cashierCollectMoney:['accounting','expense-payment','cashier-collect-money'].join('/'),
  reassignCashier:['accounting','expense-payment','reassign-cashier'].join('/'),
  dismissTodo:['accounting','expense-payment','dismiss-payment'].join('/'),
  cashierRejectAbscondingRemoval:['accounting','paymentOrders','reject'].join('/'),
  // coo questions
  cooQuestionGetQuestion: [environment.apiBase, 'accounting', 'coo-questions'].join('/'),
  cooQuestionAnswerQuestion: [environment.apiBase, 'accounting', 'coo-questions', 'answer-question'].join('/'),
  // coo questions end
  //region import new dd file
  hideDDFile:['accounting','bddactivationfiles','update'].join('/'),
  getDDProjectedList:['accounting','bddactivationfiles','projectedlist'].join('/'),
  createDDFile:['accounting','bddactivationfiles','parse-file'].join('/'),
  INDFgetAdvancedFilter:['accounting','bddactivationfiles','meta','importing_bank_direct_debit_activation_file'].join('/'),
  INDFgetDDfileRecords:['accounting','bddactivationfiles','getrecords'].join('/'),
  INDFDDConfirmChanges:['accounting','bddactivationfiles','confirmdd'].join('/'),
  INDFGetMatchToCurrentDD:['accounting','directdebitfile','advancesearch2','page'].join('/'),
  INDFSaveMatchToCurrentDD:['accounting','bddactivationfiles','matchdirectdebitfile'].join('/'),
  INDFgetProcessingInfo:['accounting','bddactivationfiles'].join('/'),
  INDFgetProcessSummary:['accounting','bddactivationfiles'].join('/'),
  //endregion
  //#region pdc
  getNonclientpdc:['accounting','nonclientpdc','filter'].join("/"),
  getSumpdc:['accounting','nonclientpdc','filtersumpdcs'].join("/"),
  updateNonclientpdc:['accounting','nonclientpdc','update'].join("/"),
  deleteNonclientpdc:['accounting','nonclientpdc','delete'].join("/"),
  withdrawNonclientpdc:['accounting','nonclientpdc','withdrawpdc'].join("/"),
  bounceNonclientpdc:['accounting','nonclientpdc','bouncepdc'].join("/"),
  nonclientpdcById:['accounting','nonclientpdc'].join('/'),
  //#endregion
  //region dd data entry
  getddDataEntry:['accounting','directdebitfile','advanceSearchForDDDataEntryAcc8544'].join('/'),
  getCCnotapprovedapps:['sales','ccserviceapplication','getnotapprovedapps'].join('/'),
  getVisanotapprovedapps:['sales','visaserviceapplication','getnotapprovedapps'].join('/'),
  updateNote:['accounting','directDebit','updateDataEntryNotes'].join('/'),
  getNotes:['sales','ddcnote','getnotes'].join('/'),
  createddcnote:['sales','ddcnote','createddcnote'].join('/'),
  deleteddnote:['sales','ddcnote','deleteddcnote'].join('/'),
  editddcnote:['sales','ddcnote','editddcnote'].join('/'),
  getclientnumbers:['sales','mobilenumber','getclientnumbers'].join('/'),
  //endregion
  // # confirmPage Form Region
  confirmPage: [environment.apiBase, 'accounting','directdebitfile','dd-data-entry'].join('/'),
  confirmPageValidate: [environment.apiBase, 'accounting','directdebitfile','dd-data-entry-validateClient'].join('/'),
  confirmPageFileSource: [environment.apiBase, 'public','download'].join('/'),
  accountNameRejectionReason: [environment.apiBase, 'public','picklist','items','account_name_accountant_rejection_reason'].join('/'),
  ibanRejectionReason: [environment.apiBase, 'public','picklist','items','iban_accountant_rejection_reason'].join('/'),
  eidRejectionReason: [environment.apiBase, 'public','picklist','items','eid_accountant_rejection_reason'].join('/'),
  bankName: [environment.apiBase, 'public','picklist','items','BankName'].join('/'),
  title: [environment.apiBase, 'public','picklist','items','PersonTitle'].join('/'),
  nationality: [environment.apiBase, 'public','picklist','items','nationalities'].join('/'),
  ibanValidate: [environment.apiBase, 'accounting','contractpaymentterm','checkiban'].join('/'),
  removeWrongPhotos: [environment.apiBase, 'accounting','directDebit','removeddbankphoto'].join('/'),
  saveChanges: [environment.apiBase, 'accounting','directDebit','confirmddbankinfo'].join('/'),
  swapBankInfoAttachments: [environment.apiBase, 'accounting','contractpaymentterm','swapBankInfoAttachments'].join('/'),
  // # End Form Region
  //#region pay-invoices
  getPayInvoicesList: ['accounting','expense-payment','get-pay-invoice-list'].join('/'),
  getPayInvoice:['accounting','expense-payment','get-pay-invoice'].join("/"),
  exportToCsv:['accounting','expense-payment','pay-invoice-exportToCsv'].join('/'),
  payInvoice:['accounting','expense-payment','pay-invoice'].join('/'),
  //#endregion
  //#region pay-statement
  invoiceStatementCalculations: ['accounting','invoiceStatement','calculations'].join('/'),
  invoiceStatementSearch: ['accounting','invoiceStatement','search'].join('/'),
  invoiceStatementExportToCSV: ['accounting','invoiceStatement','exportToCSV'].join('/'),
  invoiceStatementPayAllTransactions: ['accounting','invoiceStatement','pay-all-transactions'].join('/'),
  invoiceStatementPayTransactions: ['accounting','invoiceStatement','pay-transactions'].join('/'),
  invoiceStatementRematch: ['accounting','invoiceStatement','rematch'].join('/'),
  invoiceStatementUpdatePendingMatchedAmount: ['accounting','invoiceStatement','updatePendingMatchedTransactionAmount'].join('/'),
  invoiceStatementUpdateTransactionAmount: ['accounting','invoiceStatement','updateTransactionAmount'].join('/'),
  invoiceStatementDeleteTransaction: ['accounting','invoiceStatement','deleteTransaction'].join('/'),
  expenseRequestTodoUpdate: ['accounting','expenseRequestTodo','update'].join('/'),
  //#endregion
  //region transaction management
  TMAdvancedSearchMeta:['accounting','transactions','meta','transaction_management_New'].join('/'),
  TMgetTransactions:['accounting','transactions','page','searchNew'].join('/'),
  TMgetTransactionsBasic:['accounting','transactions','page','advancesearchNew'].join('/'),
  TMgetTransactionsAdvanced:['accounting','transactions','page','advancesearch2New'].join('/'),
  TMlicenseOptions:['public','picklist','items','transaction_license'].join('/'),
  TMgetBuckets:['accounting','buckets','page','searchBuckets'].join('/'),
  TMrevenueValueOptions:['accounting','revenues','page','searchRevenues'].join('/'),
  TMexpenseValueOptions:['accounting','expenses','page','searchExpenses'].join('/'),
  TMrefOfficeStaffOptions:['staffmgmt','officestaff','page'].join('/'),
  TMrefHousemaidOptions:['accounting','housemaid','searchHousemaid'].join('/'),
  TMrefClientOptions:['staffmgmt','client','status','client'].join('/'),
  TMrefApplicantOptions:['recruitment','maidsAtCandidateWA','getCandidateByName'].join('/'),
  TMrefProspectOptions:['staffmgmt','client','status','prospect'].join('/'),
  TMrefContractOptions:['accounting','contract','searchContract'].join('/'),
  TMrefFreedomOptions:['accounting','freedomoperator','search','page'].join('/'),
  deleteTransaction:['accounting','transactions','delete'].join('/'),
  TMgetDDBankInfoAttachments:['accounting','transactions','getDDBankInfoAttachments'].join('/'),
  TMgetTransactionDetails:['accounting','transactions'].join('/'),
  TM_SearchGenerateCSVmail:['accounting','transactions','csv','search','mail'].join('/'),
  TM_SearchGenerateCSVsearch:['accounting','transactions','csv','search'].join('/'),
  TM_BasicGenerateCSVmail:['accounting','transactions','csv','advancesearch','mail'].join('/'),
  TM_BasicGenerateCSVsearch:['accounting','transactions','csv','advancesearch'].join('/'),
  TM_AdvGenerateCSVmail:['accounting','transactions','csv','advancesearch2','mail'].join('/'),
  TM_AdvGenerateCSVsearch:['accounting','transactions','csv','advancesearch2'].join('/'),
  TMgetPaymentTypes:['accounting','payments','getallpaymentmethods'].join('/'),
  addTransaction:['accounting','transactions','create'].join('/'),
  updateTransaction:['accounting','transactions','update'].join('/'),
  deleteTransactionFile:['accounting','transactions','delete','file'].join('/'),
  TM_getExpenseByCode:['accounting','expenses','getByCode'].join('/'),
  TM_createTransaction:['accounting','bankStatementFile','createTransaction'].join('/'),
  //end region
  //#region ManageBuckets
  getBucketsList: ['accounting', 'buckets', 'page', 'searchBuckets'].join('/'),
  getLastCalculateBucketAmountJobRun: ['accounting','buckets','getLastCalculateBucketAmountJobRun'].join('/'),
  createManageBuckets: ['accounting', 'buckets', 'create'].join('/'),
  updateManageBuckets: ['accounting', 'buckets', 'update'].join('/'),
  deleteManageBuckets: ['accounting', 'buckets', 'delete'].join('/'),
  getManageBucket: ['accounting', 'buckets'].join('/'),
  ManageBucketsMetaSearch: ['accounting', 'meta', 'page'].join('/'),
  getRefillList: ['accounting', 'buckets', 'getRefillFrom'].join('/'),
  downloadCSV: ['accounting', 'buckets', 'page', 'searchBuckets','csv'].join('/'),
  //#endregion
  //#region reconciliator
  RgetBucketOptions:['accounting','buckets','page','searchBuckets'].join('/'),
  RgetexpenseOptions:['accounting','expenses','page','searchExpenses'].join('/'),
  getCashSummaryTableData:['accounting','reconciliator','get-today-cash-box'].join('/'),
  getCompleteExpensesPendingTableData:['accounting','reconciliator','get-complete-expenses-pending-confirmation'].join('/'),
  getExpensesPendingInvoicesTableData:['accounting','reconciliator','get-expenses-pending-invoices'].join('/'),
  getByCreditCard:['accounting','CreditCardReconciliation','getByCreditCard'].join('/'),
  RdeleteStatement:['accounting','CreditCardReconciliation','deleteStatement'].join('/'),
  RuploadStatement:['accounting','CreditCardReconciliation','uploadStatement'].join('/'),
  Radvancesearch3:['accounting','transactions','page','advancesearch3'].join('/'),
  RgetExpensePayment:['accounting','expense-payment'].join('/'),
  RgetCurrencies:['public','picklist','items','EXPENSE_CURRENCY'].join('/'),
  selectAsInvoice:['accounting','reconciliator','select-as-invoice'].join('/'),
  selectAsVatInvoice:['accounting','reconciliator','select-as-vat-invoice'].join('/'),
  addInvoice:['accounting','reconciliator','add-invoice'].join('/'),
  deleteInvoice:['accounting','reconciliator','delete-invoice'].join('/'),
  RshowSupplierDetails:['accounting','supplier'].join('/'),
  saveExpensePaymentTask:['accounting','expense-payment','saveTask'].join('/'),
  completeExpensePayment:['accounting','expense-payment','complete'].join('/'),

  createManualTransaction:(id:any)=>['accounting','CreditCardReconciliation','create-manual-transaction',id].join('/'),
  searchExpenseTodo: ['accounting', 'expenseRequestTodo', 'search'].join('/'),
  RconfirmStatement: (id: any) => ['accounting', 'CreditCardReconciliation', 'confirm-statement', id].join('/'),
  transaction_license: ['public', 'picklist', 'items', 'transaction_license'].join('/'),
  RsearchExpenses: ['accounting', 'expenses', 'page', 'searchExpenses'].join('/'),
  linkedTransactionIdOptions: (id: any) =>['accounting', 'CreditCardReconciliation','details',id, 'alreadyMatchedTransactions'].join('/'),
  getTransactions:(id:any)=>['accounting','CreditCardReconciliation','getDetails',id].join('/'),
  getStatementData:(id:any)=>['accounting','CreditCardReconciliation',id].join('/'),
  wrongMatch:(id:any)=>['accounting','CreditCardReconciliation','wrongMatch',id].join('/'),
  confirmRefundAction:(id:any)=>['accounting','CreditCardReconciliation','confirmRefund',id].join('/'),
  confirmExpenseAction:(id:any)=>['accounting','CreditCardReconciliation','confirmExpense',id].join('/'),
  confirmReplenishmentAction:(id:any)=>['accounting','CreditCardReconciliation','confirmReplenishment',id].join('/'),
  RgetParameter:['public','parameter'].join('/'),
  getReviewDetailsBucket:(id:any)=>['accounting','bucket-replenishment',id].join('/'),
  getReviewDetailsExpense:(id:any)=>['accounting','expenseRequestTodo',id].join('/'),
  expenseRequestTodo:(id:any)=>['accounting','expenseRequestTodo',id].join('/'),
  reconciliationTransactionUpdate:['accounting','reconciliation-transaction','update'].join('/'),
  reconciliationTransactionDelete:(id:any)=>['accounting','reconciliation-transaction','delete','file',id].join('/'),
  matchWithExpense:(id:any)=>['accounting','CreditCardReconciliation','matchWithExpense',id].join('/'),
  confirmAlreadyMatched:(details_id:any)=>['accounting','CreditCardReconciliation','details',details_id,'confirmAlreadyMatched'].join('/'),
  //#end region
  //#region Bank Direct Debit Cancellation File
  getBDDcancelationfiles:['accounting','bddcancelationfiles','projectedlist'].join('/'),
  deleteBDDcancelationfiles:['accounting','bddcancelationfiles','update'].join('/'),
  createBDDcancelationfiles:['accounting','bddcancelationfiles','create'].join('/'),
  getRecordsBDDcancelationfiles:['accounting','bddcancelationfiles','getrecords'].join('/'),
  confirmdd:['accounting','bddcancelationfiles','confirmdd'].join('/'),
  confirmAlldd:['accounting','bddcancelationfiles','confirmAlldd'].join('/'),
  exportDDCancelationFile:['accounting','bddcancelationfiles','getrecords','csv'].join('/'),
  bulkdelete:['accounting','bddcancelationrecords','bulkdelete'].join('/'),
  dismissAlldd:['accounting','bddcancelationfiles','dismissAlldd'].join('/'),
  approveRejectionByBankForDDs:['accounting','bddcancelationrecords','approveRejectionByBankForDDs'].join('/'),
  approveAllRejectionByBankForDDs:['accounting','bddcancelationrecords','approveAllRejectionByBankForDDs'].join('/'),
  //#endregion
  //#region visa expenses
  visaRequestExpenseMeta:['accounting','visarequestexpense','meta','visa-expenses'].join('/'),
  getVisaExpensesList:['accounting','visarequestexpense','page','search'].join('/'),
  getVisaExpensesListAdvancedSearch:['accounting','visarequestexpense','advanceSearch','page'].join('/'),
  dismissVisaRequestExpense:['accounting','visarequestexpense','dismiss'].join('/'),
  dismissVisaRequestExpenseList:['accounting','visarequestexpense','list','dismiss'].join('/'),
  VisaStatementaddTransaction:['accounting','visarequestexpense','addtransaction'].join('/'),
  addTransactionList:['accounting','visarequestexpense','list','addtransaction'].join('/'),
  paymentTypesCharges:['accounting','visaExpensePaymentTypeDetails','list'].join('/'),
  getVisaSearchInfo:['accounting','visaExpenseConfiguration','getSearchInfo'].join('/'),
  saveExpensesCharges:['accounting','visaExpensePaymentTypeDetails','updateAllRecords'].join('/'),
  addExpenseCharge:['accounting','visaExpensePaymentTypeDetails','create'].join('/'),
  deleteExpenseCharge:['accounting','visaExpensePaymentTypeDetails','delete'].join('/'),
  getVisaExpenseConfiguration:['accounting','visaExpenseConfiguration','page','search'].join('/'),
  addVisaExpenseConfiguration:['accounting','visaExpenseConfiguration','create'].join('/'),
  updateVisaExpenseConfiguration:['accounting','visaExpenseConfiguration','update'].join('/'),
  deleteVisaExpenseConfiguration:['accounting','visaExpenseConfiguration','delete'].join('/'),
  getAllStatements:['accounting','visaStatement','allStatements'].join('/'),
  uploadStatement:['accounting','visaStatement','startParsing'].join('/'),
  deleteStatement:['accounting','visaStatement','delete'].join('/'),
  checkCanBeConfirm:['accounting','visaStatement','canBeConfirm'].join('/'),
  confirmStatement:['accounting','visaStatement','confirm'].join('/'),
  checkStatus:['accounting','visaStatement'].join('/'),
  getMatchedTransaction:['accounting','visaStatementTransaction','getMatchedTransaction'].join('/'),
  WrongMatchedTransaction:['accounting','visaStatementTransaction','wrongMatch'].join('/'),
  confirmMatched:['accounting','visaStatementTransaction','confirm'].join('/'),
  confirmMatchedAllSelected:['accounting','visaStatementTransaction','confirmSelected'].join('/'),
  getMissingERPTransaction:['accounting','visaStatementTransaction','getMissingFromERPTransaction'].join('/'),
  rematchMissingERPTransaction:['accounting','visaStatementTransaction','rematch'].join('/'),
  getMissingStatementTransaction:['accounting','visaStatementTransaction','getMissingFromStatementTransaction'].join('/'),
  dismissStatementTransaction:['accounting','visaStatementTransaction','dismiss'].join('/'),
  getDifferentAmountTransaction:['accounting','visaStatementTransaction','getSameReferenceNumberButDifferentAmountTransaction'].join('/'),
  fixSelectedERPAmount:['accounting','visaStatementTransaction','fixSelectedERPAmount'].join('/'),
  canBeConfirmStatement:['accounting','visaStatement','canBeConfirm'].join('/'),
  VisaStatementsearchBuckets:['accounting','buckets','page','searchBuckets'].join('/'),
  VisaStatementsearchExpenses:['accounting','expenses','page','searchExpenses'].join('/'),
  confirmAll:['accounting','visaStatementTransaction','confirmAll'].join('/'),
  rematchAll:['accounting','visaStatementTransaction','rematchAll'].join('/'),
  //#endregion
  //#region direct-debit-applications
  directDebitApplicationMetaSearch: ['accounting','directdebitfile','meta','directdebitfiles_management_new',].join('/'),
  directDebitApplicationList: ['accounting','directdebitfile','advancesearch2New','page',].join('/'),
  directDebitApplicationGetFile: ['accounting','directdebitfile','advancesearch2New','file',].join('/'),
  exportCsv: ['accounting', 'directdebitfile', 'advancesearch2', 'csv'].join('/'),
  exportCsvMail: ['accounting', 'directdebitfile', 'advancesearch2', 'mail'].join('/'),
  updateNotes: ['accounting', 'directdebitfile', 'update'].join('/'),
  //#endregion
  //#region contract-payments-files
  getContractPaymentsFiles: ['accounting', 'contractpaymentterm', 'getddfiles'].join('/'),
  getMergedDDFiles: ['accounting', 'contractpaymentterm', 'getmergedddfiles'].join('/'),
  sendDDFilesToClient: ['accounting', 'contractpaymentterm', 'sendddfilestoclient'].join('/'),
  updateContractPaymentTerm: ['accounting', 'contractpaymentterm', 'update'].join('/'),
  getPdfOfImages: ['accounting', 'contractpaymentterm', 'getpdfofimages'].join('/'),
  directDebitFileUpdate: ['accounting', 'directdebitfile', 'update'].join('/'),
  directDebitFileCreateAll: ['accounting', 'directdebitfile', 'createAll'].join('/'),
  //#endregion
  // #Region DD Messaging Setup
  DDMessagingSetupEvent: ['accounting', 'DDMessaging', 'getDdMessagingEvent'].join('/'),
  DDMessagingSubTypeOptions: ['accounting', 'DDMessaging', 'getDdMessagingSubEvent'].join('/'),
  BouncedPaymentStatusOptions: ['public', 'picklist', 'items', 'BouncedPaymentStatus'].join('/'),
  SearchMessageSetup: ['accounting', 'DDMessaging', 'advanceSearch'].join('/'),
  UpdateMessageSetup: ['accounting', 'DDMessaging', 'update'].join('/'),
  //message form
  getMessageForm: ['accounting', 'DDMessaging'].join('/'),
  BankNames: ['public', 'picklist', 'items', 'BankName'].join('/'),
  DDBankMessaging: ['accounting', 'ddBankMessaging', 'getDdBankMessaging'].join('/'),
  CreateAllDDBankMessaging: ['accounting', 'ddBankMessaging', 'createAll'].join('/'),
  UpdateAllDDBankMessaging: ['accounting', 'ddBankMessaging', 'updateAll'].join('/'),
  DDBankMessagingDelete: ['accounting', 'ddBankMessaging', 'delete'].join('/'),
  DDMessageCreateUpdate: ['accounting', 'DDMessaging'].join('/'),
  PaymentStructureOption: ['accounting', 'DDMessaging', 'getDdMessagingPaymentStructure'].join('/'),
  // #End Region
  //#region Expense Requests
  searchExpenseRequests: [environment.apiBase, 'accounting', 'expenseRequestTodo', 'search'].join('/'),
  searchExpensesRequests: [environment.apiBase, 'accounting', 'expenses', 'page', 'searchExpenses'].join('/'),
  searchBucketsRequests: [environment.apiBase, 'accounting', 'buckets', 'page', 'searchBuckets'].join('/'),
  searchHousemaid: [environment.apiBase, 'accounting', 'housemaid', 'searchHousemaid'].join('/'),
  searchOfficeStaff: [environment.apiBase, 'payroll', 'officestaff', 'search', 'page'].join('/'),
  searchSuppliers: [environment.apiBase, 'accounting', 'supplier', 'page'].join('/'),
  searchRequestors: [environment.apiBase, 'public', 'user', 'page'].join('/'),
  getCandidateByName: [environment.apiBase, 'recruitment', 'maidsAtCandidateWA', 'getCandidateByName'].join('/'),
  refundExpense: [environment.apiBase, 'accounting', 'expenseRequestTodo', 'addrefund'].join('/'),
  deleteExpense: [environment.apiBase, 'accounting', 'expenseRequestTodo', 'deleteEntityWithNotes'].join('/'),
  getExpensePayment: [environment.apiBase, 'accounting', 'expense-payment', 'getExpensePayment'].join('/'),
  exportCSVExpenseRequestsMail: [environment.apiBase, 'accounting', 'expenseRequestTodo', 'search', 'csv', 'mail'].join('/'),
  //#endregion Expense Requests

  //region upload statement
  createFileStatement:['accounting','bankStatementFile','create'].join('/'),
  deleteFileStatement:['accounting','bankStatementFile','deleteFileStatement'].join('/'),
  getFileStatement:['accounting','bankStatementFile','filterStatement'].join('/'),
  getbankStatementFileSummary:['accounting','bankStatementFile'].join('/'),
  confirmTransactions:['accounting','bankStatementFile','confirmTransactions'].join('/'),
  confirmAllTransactions:['accounting','bankStatementFile','confirmAllTransactions'].join('/'),
  USclientsOptions:['accounting','clients','clients','name'].join('/'),
  UScontractsOptions:['accounting','clients','clientcontracts'].join('/'),
  USpdcByContract:['accounting','payments','pdcByContract'].join('/'),
  matchPaymentToTransaction:['accounting','bankStatementFile','matchPaymentToTransaction'].join('/'),
  addNoteToTransaction:['accounting','bankStatementFile','addNoteToTransaction'].join('/'),
  USgetRevenueOptions:['accounting','revenues','page','searchRevenues'].join('/'),
  USgetExpensesOptions:['accounting','expenses','page','searchExpenses'].join('/'),
  USrefOfficeStaffOptions:['staffmgmt','officestaff','page'].join('/'),
  USrefHousemaidOptions:['accounting','housemaid','searchHousemaid'].join('/'),
  USrefClientOptions:['staffmgmt','client','status','client'].join('/'),
  USrefApplicantOptions:['recruitment','maidsAtCandidateWA','getCandidateByName'].join('/'),
  USrefProspectOptions:['staffmgmt','client','status','prospect'].join('/'),
  USrefContractOptions:['accounting','contract','searchContract'].join('/'),
  USrefFreedomOptions:['accounting','freedomoperator','search','page'].join('/'),
  getPayrollTransfers:['accounting','bankStatementFile'].join('/'),
  getPayrollTransfersCSV:['accounting','bankStatementFile'].join('/'),
  //endregion
};
