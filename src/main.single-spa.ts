import { enableProdMode, NgZone } from '@angular/core';

import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { Router, NavigationStart } from '@angular/router';
import { from } from 'rxjs';

import { singleSpaAngular, getSingleSpaExtraProviders } from 'single-spa-angular';


import { AppModule } from './app/app.module';
import { environment } from './environments/environment';
import { publicPath } from './single-spa/asset-url';
import { singleSpaPropsSubject } from './single-spa/single-spa-props';

if (environment.production) {
  enableProdMode();
}

const lifecycles = singleSpaAngular({
  bootstrapFunction: (singleSpaProps:any) => {
    singleSpaPropsSubject.next(singleSpaProps);
    return platformBrowserDynamic(getSingleSpaExtraProviders()).bootstrapModule(AppModule);
  },
  template: '<microfrontend-accounting />',
  domElementGetter:()=>document.getElementById('single-spa-custom-container')!,
  Router,
  NavigationStart,
  NgZone,
});


const rootPath :string=publicPath();

export const mount = lifecycles.mount;
export const unmount = lifecycles.unmount;

export const bootstrap = [
  lifecycles.bootstrap,
  () =>
    Promise.all([
      loadStyle(
        'maindAAccounting', `${rootPath}main.css`,
      ),
    ]),
];

/**
 * function to load style file
 * @param styleId stylesheet uniqe id wihout #
 * @param url url where the file is
 * @returns promise
 */
function loadStyle(styleId:string,url: string) {
  const oldStyle = document.head.querySelectorAll('#'+styleId);//id should be without #
  const themeLoaded$ = from(new Promise<any>((resolve, reject) => {
    const themeLink = document.createElement("link");
    themeLink.onload = (ev) => {
      // oldThemeLinkElement && oldThemeLinkElement.remove();
      oldStyle.forEach(item=>item.remove());
      resolve(url)
    }
    themeLink.onerror = ev => reject({error: ev, message: "an error occured while fetching the sylesheet"});

    themeLink.id = styleId;
    themeLink.rel = 'stylesheet';
    themeLink.href =url;
    document.head.appendChild(themeLink);
  }));
  return themeLoaded$;
}
