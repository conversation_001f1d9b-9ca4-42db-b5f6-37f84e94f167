/* You can add global styles to this file, and also import other style files */
label {
  color: black;
  display: flex !important;
}
a {
  cursor: pointer;
}
.acc-7932 {
  .red-text {
    color: #92000a !important;
    cursor: pointer;
  }
  .red-text:hover {
    text-decoration: underline;
  }
}
.acc-7877 {
  .icon {
    position: absolute;
    margin-top: 7px;
    left: 0.5em;
  }
  .mat-radio-label {
    display: flex;
  }
}
.acc-7712 {
  .required-label::after {
    content: " *";
    color: red;
  }
}
.acc-7904 {
  .icon {
    position: absolute;
    margin-top: 6px;
    right: 0.3em;
  }

  .required-label::after {
    content: "*";
    color: red;
  }
  .error-msg {
    position: absolute;
    font-size: 11px;
    color: red;
    bottom: 0.05em;
  }
  .green {
    background-color: green;
    color: white;
  }
}
.acc-7738 {
  .icon {
    position: absolute;
    margin-top: 6px !important;
    padding-right: 10px;
  }

  .required-label::after {
    content: "*";
    color: red;
  }
  .icon-2 {
    position: absolute;
    margin-top: 4px;
    left: 1em;
  }
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type="number"] {
    -moz-appearance: textfield;
  }
  .max {
    white-space: nowrap;
  }
}
.acc-7904 {
  .icon {
    position: absolute;
    margin-top: 6px;
    right: 0.3em;
  }

  .required-label::after {
    content: "*";
    color: red;
  }
  .error-msg {
    position: absolute;
    font-size: 11px;
    color: red;
    bottom: 0.05em;
  }
  .green {
    background-color: green;
    color: white;
  }
}
.acc-7712 {
  .required-label::after {
    content: " *";
    color: red;
  }
}
.acc-7932 {
  .red-text {
    color: #92000a !important;
    cursor: pointer;
  }
  .red-text:hover {
    text-decoration: underline;
  }
}
.acc-7877 {
  .icon {
    position: absolute;
    margin-top: 7px;
    left: 0.5em;
  }
  .mat-radio-label {
    display: flex;
  }
}
.panel-icon .mat-icon {
  position: relative;
  top: 0.3em;
  margin-right: 0.3em;
}

.document-gov-acc {
  margin: 2em !important;
}

.row-warning {
  background-color: orange !important;
}

.label-visit {
  position: relative !important;
  top: -0.2em !important;
}
.active-status {
  border: 1px solid #535353;
  border-radius: 20px;
  padding: 0.5em;
}

.visit-status {
  padding: 0.1em;
  border: 1px solid #535353;
  border-radius: 20px;
  display: block !important;
}

.blink {
  background-color: #f4c4c8 !important;
  color: black !important;
  animation: blinker 1s cubic-bezier(0.5, 0, 1, 1) infinite alternate;
}

@keyframes blinker {
  from {
    opacity: 1;
  }
  to {
    opacity: 0.5;
  }
}

.mat-radio-label-content {
  top: -0.3em !important;
  position: relative !important;
}

.working-on-user {
  font-size: 10px !important;
}

.cc-tab-label {
  font-weight: bold !important;
}

/* Make all file uploaders 100% width */
cc-file-uploader {
  display: block;
  width: 100% !important;
}

/* Target internal elements of the file uploader */
cc-file-uploader .dropzone,
cc-file-uploader .dz-wrapper,
cc-file-uploader .dz-message,
cc-file-uploader dropzone {
  width: 100% !important;
  max-width: 100% !important;
}

.cc-tab-label-content {
  color: var(--cc-primary-500) !important;
}
.acc-8047 {
  .icon {
    position: absolute;
    margin-top: 7px;
    left: 0.5em;
  }
}
.acc-8044 {
  .icon {
    position: absolute;
    margin-top: 6px;
    left: 0.6em;
  }
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type="number"] {
    -moz-appearance: textfield;
  }
}
.acc-8620 {
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type="number"] {
    -moz-appearance: textfield;
  }
}
.acc-7935 {
  .max {
    white-space: nowrap;
  }
  .icon {
    position: absolute;
    margin-top: 6px;
    left: 0.7em;
  }
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  input[type="number"] {
    -moz-appearance: textfield;
  }
}
.ACC-7845 {
  .panel-icon .mat-icon {
    position: relative;
    top: 0.2em;
  }

  table caption {
    caption-side: top !important;
  }
}
.acc-8011 {
  .icon {
    position: absolute;
    margin-top: 7px;
    left: 0.5em;
  }

  .required-label::after {
    content: "*";
    color: red;
  }
}
.acc-8904 {
  .filter_icon {
    position: absolute;
    margin-top: -2px;
  }
  .icon {
    position: absolute;
    margin-top: 5px;
    left: 0.5em;
  }
  .link {
    color: #a92222;
    cursor: pointer;
  }
}.acc-7912 {
  .icon {
    position: absolute;
    margin-top: 5px;
    left: 0.5em;
  }

  .bold {
    font-weight: 500;
  }
  .max {
    width: max-content;
    white-space: nowrap;
  }
  .filter_icon {
    position: absolute;
    margin-top: -2px;
  }
  .font-weight-bold {
    font-weight: 700 !important;
  }
  .dynamic-search-compare-to-field {
    display: none !important;
  }
}

.acc-7799 {
  .icon {
    position: absolute;
    margin-top: 5px !important;
    left: 0.5em;
  }
  .icon_filter {
    margin-top: -3px;
    position: absolute;
    left: 2em;
  }
}
.acc-7905 {
  .date {
    white-space: nowrap;
    width: fit-content;
  }
  .required-label::after {
    content: "*";
    color: red;
  }
  .description {
    max-width: 400px !important;
  }
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type="number"] {
    -moz-appearance: textfield;
  }
  .description-width {
    max-width: 300px;
    white-space: normal;
  }
}
.acc-7953 {
  .icon-edit-save-cancel {
    mat-icon {
      font-size: 16px !important;
      margin-bottom: 16px !important;
    }
  }
}
.acc-7859 {
  .max {
    width: fit-content;
    white-space: nowrap;
  }
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type="number"] {
    -moz-appearance: textfield;
  }
  .icon {
    position: absolute;
    top: 10px;
  }
  .desc-row {
    max-width: 400px;
    white-space: normal;
  }
}
.acc-7779 {
  .bold-font {
    font-weight: 500;
  }
  .info-button {
    background-color: #0d6efd !important;
    color: white;
  }
  .success-button {
    background-color: #198754 !important;
    color: white;
  }
  .desc-row {
    max-width: 400px;
    white-space: normal;
  }
}
.acc-7735 {
  .icon {
    position: absolute;
    margin-top: 6px;
    left: 0.5em;
  }
  .error-msg {
    position: absolute;
    font-size: 11px;
    color: red;
    bottom: 0.05em;
  }
}
.acc-7757 {
  .icon {
    position: absolute;
    margin-top: 6px;
    left: 0.5em;
  }
}
.acc-7691 {
  .required-label::after {
    content: "*";
    color: red;
  }
}
.acc-7824 {
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type="number"] {
    -moz-appearance: textfield;
  }
}
.acc-6302 {
  .bg-gray-light {
    background-color: #bcbcbc;
  }
  .text-transparent {
    color: transparent !important;
    user-select: none;
  }
  .grid-container {
    height: 400px;
    overflow-y: auto;
  }
  .bordered-bottom-line {
    border-bottom: 2px solid #000;
    margin: 10px 0px;
  }
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type="number"] {
    -moz-appearance: textfield;
  }
}
.acc-7767 {
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type="number"] {
    -moz-appearance: textfield;
  }
}
.acc-7767 {
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type="number"] {
    -moz-appearance: textfield;
  }
}
.acc-7873 {
  .titlePaymentValidation {
    font-weight: 500;
    font-size: 16px;
  }
}
.acc-8078 {
  .icon_filter {
    position: absolute;
    margin-top: -2px;
    left: 0.7em;
  }

  .icon {
    position: absolute;
    margin-top: 6px;
    left: 0.5em;
  }
}
.acc-7825 {
  .icon {
    position: absolute;
    margin-top: 5px;
    left: 0.5em;
  }
  .error-msg {
    position: absolute;
    font-size: 11px;
    color: red;
    bottom: 0.05em;
  }
}
.acc-7805 {
  .icon {
    position: absolute;
    left: 0.3em;
    top: 0.4em;
  }
}
.acc-7849 {
  .card-design {
    background: linear-gradient(135deg, #adadad, #646464);
    border-radius: 15px;
    color: rgb(255, 255, 255);
    padding: 20px;
    text-align: center;
    width: 100%;
    position: relative;
    /* Needed for the pseudo-element */
    transition: transform 0.3s ease, background 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-bottom: 4px solid #f8f9fa;
    /* First border */
  }

  .card-design::after {
    content: "";
    position: absolute;
    bottom: -8px;
    /* Adjust to control spacing between the borders */
    left: 10px;
    right: 10px;
    height: 4px;
    background-color: #dee2e6;
    /* Color of the second border */
    border-radius: 2px;
  }

  .card-design:hover {
    transform: translateZ(-5px) scale(1.02);
    background: linear-gradient(135deg, #f8f9fa, #dee2e6);
    cursor: pointer;
    color: #646464;
  }

  .card-design span {
    font-size: 1em;
    font-weight: bold;
  }
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type="number"] {
    -moz-appearance: textfield;
  }
}
.acc-7855 {
  .grid-container {
    max-height: 400px;
    overflow-y: auto;
  }
  .icon {
    position: absolute;
    margin-top: 6px !important;
    left: 0.5em;
  }
  .green-btn {
    background-color: green;
    color: white;
  }
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type="number"] {
    -moz-appearance: textfield;
  }
}
.acc-7851,
.acc-7848,
.acc-7849 {
  .required-label::after {
    content: "*";
    color: red;
  }
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type="number"] {
    -moz-appearance: textfield;
  }
}
.acc-8362 {
  .bold-font {
    font-weight: bold;
  }
}
.acc-7830 {
  cc-datagrid th,
  cc-datagrid td {
    white-space: nowrap;
    padding: 8px;
  }
  .icon {
    position: absolute;
    margin-top: 5px !important;
    left: 0.5em;
  }
  .icon_filter {
    margin-top: -3px;
    position: absolute;
    left: 2em;
  }
}
.acc-7922 {
  .desc-row {
    max-width: 400px;
    white-space: normal;
  }
}
.acc-7798 {
  .icon {
    position: absolute;
    margin-top: 6px;
    left: 0.6em;
  }
  .filter-icon {
    mat-icon {
      font-size: 18px !important;
    }
    position: absolute;
    padding-right: 0.6em;
  }
  .section {
    border-radius: 6px;
    box-shadow: 1px 1px 3px 3px #cecece;
    padding: 5px;
    margin-bottom: 10px;
  }
  .dynamic-search-compare-to-field {
    display: none !important;
  }
  .input-wrapper {
    position: relative;
    display: inline-block;
    width: 100%;
    margin-top: 1em;
  }

  .error-msg {
    position: absolute;
    top: 100%;
    left: 1em;
    color: #f44336;
    font-size: 12px;
    margin-top: -1.6em;
  }
  .background {
    background-color: lightgray;
    padding: 5px;
    margin-bottom: 10px;
    border-radius: 4px;
    span {
      margin-left: 5px;
    }
  }
  .description-width {
    max-width: 200px;
    white-space: normal;
  }
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type="number"] {
    -moz-appearance: textfield;
  }
  .photo-container {
    width: 700px;
    max-width: 100%;
  }
  .main-container {
    width: fit-content;
  }
  .download-btn {
    margin-top: auto;
  }
}
.acc-9760{
  .required-label::after{
      content: '*';
      color: red;
      margin-left: 2px;
  }
}
.acc-8902 {
  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type="number"] {
    -moz-appearance: textfield;
  }
  .desc-row {
    max-width: 400px;
    white-space: normal;
  }
}
.acc-7878 {
  .spinner{
      max-width: 20px;
      max-height: 20px;
  }
  .max{
      white-space: nowrap;
      width: max-content;
  }
  .desc-row{
      max-width: 600px;
      white-space: normal;
  }
}

.acc-7912 {
  .icon {
    position: absolute;
    margin-top: 5px;
    left: 0.5em;
  }

  .bold {
    font-weight: 500;
  }
  .max {
    width: max-content;
    white-space: nowrap;
  }
  .filter_icon {
    position: absolute;
    margin-top: -2px;
  }
  .font-weight-bold {
    font-weight: 700 !important;
  }
  .dynamic-search-compare-to-field {
    display: none !important;
  }
}

.acc-8152 {
  .state {
    font-size: 20px;
    font-weight: 100;
  }
  .bg {
    margin: 5px 0px;
    background-color: lightgray;
    padding: 5px;
    border-radius: 6px;
  }
  .upload {
    font-size: 20px;
    font-weight: 100;
  }
  .icon {
    position: absolute;
    margin-top: 6px;
    left: 0.6em;
  }
  .input-wrapper {
    position: relative;
    display: inline-block;
    width: 100%;
    margin-top: 1em;
  }

  .error-msg {
    position: absolute;
    top: 100%;
    left: 1em;
    color: #f44336;
    font-size: 12px;
    margin-top: -1.6em;
  }
  .summaryRow {
    background-color: lightgray;
    padding: 0.5em;
    margin: 0.5em 0.06em -0.3em;
    border-radius: 6px 6px 0px 0px;
  }
  .summaryTitle {
    font-size: 16px;
    font-weight: 400;
  }
}
