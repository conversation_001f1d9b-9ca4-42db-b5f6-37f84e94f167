{"compileOnSave": false, "compilerOptions": {"baseUrl": "", "outDir": "./dist/out-tsc", "paths": {"@core/*": ["src/app/core/*"], "@admin/*": ["src/app/admin-module/*"]}, "forceConsistentCasingInFileNames": true, "strict": false, "noPropertyAccessFromIndexSignature": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "declaration": false, "downlevelIteration": true, "experimentalDecorators": true, "moduleResolution": "node", "importHelpers": true, "target": "es2017", "module": "es2020", "lib": ["es2020", "dom"], "types": ["node", "jasmine", "jest"], "typeRoots": ["node_modules/@types"], "skipLibCheck": true}, "angularCompilerOptions": {"enableI18nLegacyMessageIdFormat": false, "strictInjectionParameters": true, "strictInputAccessModifiers": true, "strictTemplates": true}}